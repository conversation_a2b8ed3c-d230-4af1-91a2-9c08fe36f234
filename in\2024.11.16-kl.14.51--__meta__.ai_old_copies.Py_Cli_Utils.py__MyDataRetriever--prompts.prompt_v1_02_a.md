<!-- =============================================================================

# Urls:
# ===
- "ai-prompt-generator","https://www.feedough.com/ai-prompt-generator/"
- "prompt-clarity-checker","https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/67388dff-af98-8008-afaf-76668b24a938"
- "prompt-clarity-checker","https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/6738a0fe-bb88-8008-8a6f-bd75b8ab30c6"

============================================================================= -->

You are an expert Python developer with over 23 years of experience specializing in optimizing project structures, enhancing coding styles, and writing clean, efficient code. You balance readability with functionality and add concise, insightful comments that clarify the code without overloading it. You adapt best practices to individual preferences, fostering a productive and maintainable coding environment.

Your task is to help me define and set up an optimal project structure and coding style for the a Python project (the existing project will be provided to you after you confirm that you accept the task).

Here are some details about the project:
- Project Name: `py__MyDataRetriever`
- Project Description: `Scripts for fetching and retrieving my personal online data, such as emails, youtube-logs/playlists, etc. This is a subcomponent of a larger framework of mine used to automate all of my digital and personal life. These scripts will run automatically at regular intervals to ensure i'm always up-to-date, which i then interact upon and run rules that "filters" the data to automate the process of generating my own personal feed. This will work as a way to "interface" my personal life with the vast digital landscape. Instead of manually having to scroll through large amounts of misdirected algorithms and distractions, i instead automate the retrieval-process such that i can re-direct it. I can always define new rules (e.g. retrieving youtube-updates *only* from a certain set of account), and gain immediate value when implementing the rule.`
- Existing Issues or Challenges: `Project has been written without any specific guidelines or any consideration to consistency and flexibility, and has turned into something unorganized.`
- Specific Requirements or Preferences: `Striking the exact balance between comments and code, the comments should be *short* and straight-forward, and should be written in a concistent manner. The comment should *not* be overly explicit or contain unneccessary phrasing, but should be precice and "high-value". Similar sections, subsections, groups and category of code should share codingstyle and commentstyle when *similar*.`
- Bias: `Provide guidance on how to structure the folders and files within the project to ensure clarity and ease of maintenance, as well as best practices for writing clean code within this context. Ensure the code is well-structured and only include brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.`
- Core: `Always lay a foundation worthy of building upon. This is a rule you live by, because it’s the difference between chaos and clarity. **All you do is point of departure**.`
- Reminder: `Always the stage for success. It results in workspaces others can seamlessly take over, not only will this remove friction, but it is also *helpful* in the way for others easily can be exposed to and learn from *your* definitions.`
- Project's Current File Structure:
```
[-] ├── .gitignore
[ ] └── gmail_exporter
[-] │   ├── credentials.json
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[ ] │   ├── fetch_emails.py
[-] │   ├── token.json
[ ] ├── main.py
[-] ├── py__MyDataRetriever.sublime-project
[-] ├── requirements.txt
```


### fetch_emails.py

```python
# Function: Gmail Email Fetcher
# =======================================================
# Fetch emails from a Gmail account using the Gmail API. The script allows filtering
# by sender or recipient, supports saving results to CSV and JSON files, and tracks
# processed emails to avoid duplicates.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
import os
import re
import csv
import json
import argparse
from dotenv import load_dotenv
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
CLIENT_SECRET_FILE = os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json")
TOKEN_FILE = os.getenv("GOOGLE_TOKEN_FILE", "token.json")
SCOPES = os.getenv("GOOGLE_SCOPES", "https://www.googleapis.com/auth/gmail.readonly").split()

# Configure loguru
# -------------------------------------------------------
logger.remove()
logger.add("email_fetcher.log", rotation="10 MB", retention="10 days", level="INFO")

# Authentication
# =======================================================
def authenticate():
    """ Authenticate and retrieve credentials for Gmail API. """
    creds = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            logger.info("Credentials refreshed.")
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New credentials obtained.")
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())
            logger.info("Credentials saved to token file.")
    return creds


# Function: Generate Filename
# =======================================================
def generate_filename(base_name, sender=None, recipient=None, suffix="", file_type="csv"):
    """ Generate a consistent filename based on sender, recipient, and suffix. """
    parts = [base_name]
    if sender:
        parts.append(f"from_{sender.replace('@', '_').replace('.', '_')}")
    if recipient:
        parts.append(f"to_{recipient.replace('@', '_').replace('.', '_')}")
    if suffix:
        parts.append(suffix)
    return f"{'_'.join(parts)}.{file_type}"


# Function: Append Emails to CSV
# =======================================================
def append_emails_to_csv(emails, file_path):
    """ Append a list of emails to a CSV file. """
    with open(file_path, mode="a", newline="", encoding="utf-8-sig") as file:
        writer = csv.writer(file)
        for email in emails:
            writer.writerow(
                [
                    email["date"],
                    email["from"],
                    email["to"],
                    email["subject"],
                    email["snippet"],
                ]
            )


# Function: Append Emails to JSON
# =======================================================
def append_emails_to_json(emails, file_path):
    """ Append a list of emails to a JSON file. """
    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump([], file)
    with open(file_path, "r+", encoding="utf-8") as file:
        existing_data = json.load(file)
        existing_data.extend(emails)
        file.seek(0)
        json.dump(existing_data, file, ensure_ascii=False, indent=4)


# Function: Load Processed Emails
# =======================================================
def load_processed_emails(file_path):
    """ Load the list of already processed email IDs. """
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return set(f.read().splitlines())
    return set()


# Function: Save Processed Email ID
# =======================================================
def save_processed_email(email_id, file_path):
    """ Append an email ID to the processed emails file. """
    with open(file_path, "a") as f:
        f.write(email_id + "\n")


# Function: Safe Strip
# =======================================================
def safe_strip(value):
    """ Safely strip a value; returns an empty string if None. """
    return value.strip() if value else ""


# Function: Get Header Value
# =======================================================
def get_header_value(headers, name):
    """ Retrieve the value of a specific header by its name. """
    for header in headers:
        if header["name"].lower() == name.lower():
            return header["value"]
    return None


# Function: Clean Sender
# =======================================================
def clean_sender(sender):
    """ Standardize the format of the sender field. """
    match = re.match(r'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)', sender)
    if match:
        name, email = match.groups()
        return f"{name.strip()} <{email.strip()}>" if name else f"{email.strip()}"
    return sender


# Function: Fetch Emails
# =======================================================
def fetch_emails(sender=None, recipient=None, max_results=10):
    """ Fetch emails from Gmail based on sender and recipient filters. """
    creds = authenticate()
    service = build("gmail", "v1", credentials=creds)

    # Prepare file paths
    base_name = "emails"
    csv_file_path = generate_filename(base_name, sender, recipient, file_type="csv")
    json_file_path = generate_filename(base_name, sender, recipient, file_type="json")
    processed_emails_file = generate_filename(base_name, sender, recipient, suffix="processed", file_type="txt")
    processed_emails = load_processed_emails(processed_emails_file)

    # Build query string and fetch emails
    query_string = build_query(sender, recipient)
    total_fetched = 0
    next_page_token = None


    with tqdm(total=max_results, desc="Fetching Emails", unit="email") as pbar:
        while total_fetched < max_results:
            messages, next_page_token = fetch_message_ids(
                service, query_string, max_results - total_fetched, next_page_token
            )
            if not messages:
                logger.info(f"No more emails found matching query: {query_string}")
                break
            emails = process_messages(service, messages, processed_emails, processed_emails_file)
            save_emails(emails, csv_file_path, json_file_path)
            fetched = len(emails)
            total_fetched += fetched
            logger.info(f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}")
            pbar.update(fetched)
            if not next_page_token:
                break
    logger.success(f"Finished fetching {total_fetched} emails.")


# Function: Build Query
# =======================================================
def build_query(sender, recipient):
    """ Construct a Gmail search query string. """
    query = []
    if sender:
        query.append(f"from:{sender}")
    if recipient:
        query.append(f"to:{recipient}")
    return " ".join(query)


# Function: Fetch Message IDs
# =======================================================
def fetch_message_ids(service, query_string, max_results, next_page_token):
    """ Fetch message IDs matching the query string. """
    result = (
        service.users()
        .messages()
        .list(
            userId="me",
            q=query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        )
        .execute()
    )
    return result.get("messages", []), result.get("nextPageToken")


# Function: Process Messages
# =======================================================
def process_messages(service, messages, processed_emails, processed_emails_file):
    """ Retrieve and process messages by fetching details like subject and sender. """
    emails = []
    for message in messages:
        email_id = message["id"]
        if email_id in processed_emails:
            continue
        msg = service.users().messages().get(userId="me", id=email_id).execute()
        headers = msg["payload"]["headers"]
        email_data = {
            "date": safe_strip(get_header_value(headers, "Date")),
            "from": safe_strip(clean_sender(get_header_value(headers, "From"))),
            "to": safe_strip(clean_sender(get_header_value(headers, "To")))
            if get_header_value(headers, "To")
            else "",
            "subject": safe_strip(get_header_value(headers, "Subject")),
            "snippet": safe_strip(msg.get("snippet", "")),
        }
        emails.append(email_data)
        save_processed_email(email_id, processed_emails_file)
    return emails


# Function: Save Emails
# =======================================================
def save_emails(emails, csv_file_path, json_file_path):
    """Save emails to CSV and JSON files."""
    if emails:
        append_emails_to_csv(emails, csv_file_path)
        append_emails_to_json(emails, json_file_path)


# Main Execution
# =======================================================
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument("--max_results", type=int, default=10, help="Specify the maximum number of emails to fetch")
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)

```








Additionally, provide guidance on how to structure the folders and files within the project to ensure clarity and ease of maintenance, as well as examples of best practices for writing clean code within this context.

Please provide guidance on how to structure the folders and files within the project to ensure clarity and ease of maintenance.

Ensure the code is well-structured and only include brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.


Additionally, include examples of best practices for writing clean code within this context. Ensure that the code is well-structured and includes comments that explain the purpose of each section, as well as any complex logic in a clear and concise manner.


Provide guidance on structuring project folders and files for clarity and maintainability, along with examples of best practices for writing clean, well-structured code. Include only brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.


<!--
Your task is to help me structure a Python project effectively. Here are the details about my project:
- Project Name: __
- Main Purpose of the Project: __
- Key Features: __
- Target Audience: __

Please provide guidance on how to structure the folders and files within the project to ensure clarity and ease of maintenance. Additionally, include examples of best practices for writing clean code within this context. Ensure that the code is well-structured and includes comments that explain the purpose of each section, as well as any complex logic in a clear and concise manner.

# Urls:
# ===


# ---

# Prompt:
# ===
```
You are an expert Python developer with over 10 years of experience specializing in optimizing project structures, enhancing coding styles, and writing clean, efficient code. You balance readability with functionality and add concise, insightful comments that clarify the code without overloading it. You adapt best practices to individual preferences, fostering a productive and maintainable coding environment.

Your task is to help me set up an optimal project structure and coding style for my existing Python project. Here are the details of my project:

- Project Name: py__MyDataRetriever
- Current Project Structure: __________
- Preferred Coding Style (e.g., PEP 8, Google Style, etc.): __________
- Specific Requirements or Preferences: __________
- Any Existing Issues or Challenges: __________

Please ensure the code is well-structured and includes comments that explain the purpose of each section, as well as any complex logic in a clear and concise manner. Keep in mind the following requirements:

```

 -->
