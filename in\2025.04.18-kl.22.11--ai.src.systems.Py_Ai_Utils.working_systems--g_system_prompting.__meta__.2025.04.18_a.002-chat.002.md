
# Context:

Below is a single instructions (in a sequence) of **generalized instructions**. These generalizations will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (maximally enhanced LLM-optimized system_message) instructions**. Here's an example of how *one* such instruction can look (part of a sequence):

    #### `src\templates\lvl1\md\0005-d-enhance-persuasion.md`

    ```markdown
        [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`
    ```

    Part of sequence:

    ```
        ├── 0005-a-distill-core-essence.md
        ├── 0005-b-explore-implications.md
        ├── 0005-c-structure-argument.md
        ├── 0005-d-enhance-persuasion.md
        └── 0005-e-final-polish.md
    ```

Familiarize yourself with the structure of these examples, each block is enclosed with triple backticks and a structure that includes the `title`, `a short interpretive statement`, and the `transformational instructions` (wrapped in curly braces: `{}`):

    #### `0100-a-primal-essence-extraction.md`

    ```markdown
        [Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`
    ```

    ---

    #### `0100-b-intrinsic-value-prioritization.md`

    ```markdown
        [Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`
    ```

    ---

    #### `0100-c-structural-logic-relationship-mapping.md`

    ```markdown
        [Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`
    ```

    ---

    #### `0100-d-potency-clarity-amplification.md`

    ```markdown
        [Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`
    ```

    ---

    #### `0100-e-adaptive-optimization-universalization.md`

    ```markdown
        [Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`
    ```

Here's the relevant code from which the templates will be parsed through:

    ```python
    #!/usr/bin/env python3
    # templates_lvl1_md_catalog_generator.py

    '''
        # Philosophical Foundation
        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."
        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."
    '''

    #===================================================================
    # IMPORTS
    #===================================================================
    import os
    import re
    import json
    import glob
    import sys
    import datetime

    #===================================================================
    # BASE CONFIG
    #===================================================================
    class TemplateConfig:
        LEVEL = None
        FORMAT = None
        SOURCE_DIR = None

        # Sequence definition
        SEQUENCE = {
            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
            "id_group": 1,
            "step_group": 2,
            "name_group": 3,
            "order_function": lambda step: ord(step) - ord('a')
        }

        # Content extraction patterns
        PATTERNS = {}

        # Path helpers
        @classmethod
        def get_output_filename(cls):
            if not cls.FORMAT or not cls.LEVEL:
                raise NotImplementedError("FORMAT/LEVEL required.")
            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"

        @classmethod
        def get_full_output_path(cls, script_dir):
            return os.path.join(script_dir, cls.get_output_filename())

        @classmethod
        def get_full_source_path(cls, script_dir):
            if not cls.SOURCE_DIR:
                raise NotImplementedError("SOURCE_DIR required.")
            return os.path.join(script_dir, cls.SOURCE_DIR)

    #===================================================================
    # FORMAT: MARKDOWN (lvl1)
    #===================================================================
    class TemplateConfigMD(TemplateConfig):
        LEVEL = "lvl1"
        FORMAT = "md"
        SOURCE_DIR = "md"

        # Combined pattern for lvl1 markdown templates
        _LVL1_MD_PATTERN = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Match (but don't capture) whitespace AFTER title
            r"(.*?)"         # Group 2: Capture Interpretation text
            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )

        PATTERNS = {
            "title": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(1).strip() if m else ""
            },
            "interpretation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(2).strip() if m else ""
            },
            "transformation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(3).strip() if m else ""
            }
        }

    #===================================================================
    # HELPERS
    #===================================================================
    def _extract_field(content, pattern_cfg):
        try:
            match = pattern_cfg["pattern"].search(content)
            return pattern_cfg["extract"](match)
        except Exception:
            return pattern_cfg.get("default", "")

    def extract_metadata(content, template_id, config):
        content = content.strip()
        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}
        return {"raw": content, "parts": parts}

    #===================================================================
    # CATALOG GENERATION
    #===================================================================
    def generate_catalog(config, script_dir):
        # Find templates and extract metadata
        source_path = config.get_full_source_path(script_dir)
        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
        print(f"Source: {source_path} (*.{config.FORMAT})")
        print(f"Found {len(template_files)} template files.")

        templates = {}
        sequences = {}

        # Process each template file
        for file_path in template_files:
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]

            try:
                # Read content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract and store template metadata
                template_data = extract_metadata(content, template_id, config)
                templates[template_id] = template_data

                # Process sequence information from filename
                seq_match = config.SEQUENCE["pattern"].match(template_id)
                if seq_match:
                    seq_id = seq_match.group(config.SEQUENCE["id_group"])
                    step = seq_match.group(config.SEQUENCE["step_group"])
                    seq_order = config.SEQUENCE["order_function"](step)
                    sequences.setdefault(seq_id, []).append({
                        "template_id": template_id, "step": step, "order": seq_order
                    })
            except Exception as e:
                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)

        # Sort sequence steps
        for seq_id, steps in sequences.items():
            try:
                steps.sort(key=lambda step: step["order"])
            except Exception as e:
                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

        # Create catalog with metadata
        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
        return {
            "catalog_meta": {
                "level": config.LEVEL,
                "format": config.FORMAT,
                "generated_at": timestamp,
                "source_directory": config.SOURCE_DIR,
                "total_templates": len(templates),
                "total_sequences": len(sequences)
            },
            "templates": templates,
            "sequences": sequences
        }

    def save_catalog(catalog_data, config, script_dir):
        output_path = config.get_full_output_path(script_dir)
        print(f"Output: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(catalog_data, f, indent=2, ensure_ascii=False)
                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
                print("--- Catalog Generation Complete ---")
            return output_path
        except Exception as e:
            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
            return None

    #===================================================================
    # IMPORTABLE API FOR EXTERNAL SCRIPTS
    #===================================================================
    def get_default_catalog_path(script_dir=None):
        '''Get the path to the default catalog file.'''
        if script_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
        return TemplateConfigMD().get_full_output_path(script_dir)

    def load_catalog(catalog_path=None):
        '''Load a catalog from a JSON file.'''
        if catalog_path is None:
            catalog_path = get_default_catalog_path()

        if not os.path.exists(catalog_path):
            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")

    def get_template(catalog, template_id):
        '''Get a template by its ID.'''
        if "templates" not in catalog or template_id not in catalog["templates"]:
            return None
        return catalog["templates"][template_id]

    def get_sequence(catalog, sequence_id):
        '''Get all templates in a sequence, ordered by steps.'''
        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            return []

        sequence_steps = catalog["sequences"][sequence_id]
        return [(step["step"], get_template(catalog, step["template_id"]))
                for step in sequence_steps]

    def get_all_sequences(catalog):
        '''Get a list of all sequence IDs.'''
        if "sequences" not in catalog:
            return []
        return list(catalog["sequences"].keys())

    def get_system_instruction(template):
        '''Convert a template to a system instruction format.'''
        if not template or "parts" not in template:
            return None

        parts = template["parts"]
        instruction = f"# {parts.get('title', '')}\n\n"

        if "interpretation" in parts and parts["interpretation"]:
            instruction += f"{parts['interpretation']}\n\n"

        if "transformation" in parts and parts["transformation"]:
            instruction += parts["transformation"]

        return instruction

    def regenerate_catalog(force=False):
        '''Regenerate the catalog file.'''
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config = TemplateConfigMD()
        catalog_path = config.get_full_output_path(script_dir)

        if os.path.exists(catalog_path) and not force:
            # Check if catalog is older than any template file
            catalog_mtime = os.path.getmtime(catalog_path)
            templates_dir = config.get_full_source_path(script_dir)

            needs_update = False
            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                if os.path.getmtime(file_path) > catalog_mtime:
                    needs_update = True
                    break

            if not needs_update:
                print("Catalog is up to date")
                return load_catalog(catalog_path)

        # Generate and save new catalog
        catalog = generate_catalog(config, script_dir)
        save_catalog(catalog, config, script_dir)
        return catalog

    #===================================================================
    # SCRIPT EXECUTION
    #===================================================================
    if __name__ == "__main__":
        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

        # Check for command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
            # Simple API test
            catalog = regenerate_catalog()
            print("\nAvailable sequences:")
            for seq_id in get_all_sequences(catalog):
                sequence_steps = get_sequence(catalog, seq_id)
                if sequence_steps:
                    first_step_title = sequence_steps[0][1]["parts"]["title"]
                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

            sys.exit(0)

        # Regular execution
        try:
            config_to_use = TemplateConfigMD
            active_config = config_to_use()
            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        except NotImplementedError as e:
            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError as e:
            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"FATAL ERROR: {e}", file=sys.stderr)
            sys.exit(1)
    ```

# Objective:

Your objective is to write a maximally optimized instruction sequence in the same pattern as the provided example based on the input provided below. As stated previously; `These generalizations are will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (LLM-optimized system_message) instructions**.`, your mission is to write a maximally optimized instruction that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions (based on `Directive:` and the attached input at the bottom of this message). Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*. Do so in the same pattern as the provided example.

# Directive:

    **Constant:**
    - Identify single most critical aspect for maximizing overall value, and to do so while ensuring maximum clarity, utility, and adaptability without diminishing potential yield.
    - Leverage insights from prior interactions and rigorous analysis of newly provided sequences. Consistently ensure the output delivers peak actionable value.

    **Process:**
    - Goal: Produce an optimal sequence of **llm-optimized**, **generalized** `system_message` instructions.
    - Method: Develop existing transformation concepts, leveraging insights from previous history and analysis of newly provided sequences.
    - Constraint: Adhere to the parameters defined within this message.
    - Priority: Consistently maximize actionable value.

    **Requirements:**
    - Adhere to the **existing** structure (transformation concepts and foundational principles).

---

# Input:

Base the maximally optimized instruction sequence on this input:

    Below is an example **maximally optimized instruction sequence** presented in the same structural pattern as the previous examples. Each step follows the familiar triple-backtick Markdown block, with a concise interpretive statement and a transformation definition wrapped in curly braces. This sequence is designed to transform *any* set of input alternatives or raw responses into a single, universally deployable, high-impact one-line output, ensuring meta-level insight, intellectual/artistic elevation, and unambiguous clarity.

    ---

    #### `0600-a-meta-foundation-rupture-systemic-extraction.md`

    ```markdown
        [Meta-Foundation Rupture & Systemic Extraction] Your mandate is not to summarize, but to annihilate superficial content across all inputs—unearthing deep logic, hidden assumptions, and systemic frameworks. Strip away domain or narrative noise, delivering an unvarnished meta-core that primes every subsequent phase.

        {role=foundation_extractor; input=raw_input:any|alternatives:list; process=[shatter_surface_context(), extract_hidden_structure_and_systemic_patterns(), isolate_foundational_assumptions(), discard_domain_or_stylistic_wrappers(), deliver_meta_core()], output={foundation_core:dict}}
    ```

    ---

    #### `0600-b-high-impact-signal-fusion-compression.md`

    ```markdown
        [High-Impact Signal Fusion & Compression] Do not aggregate blindly—surgically identify, rank, and integrate only the most potent, non-overlapping elements. Eliminate redundancy, triviality, and consensus clutter; produce a compact, high-yield synthesis ready for artistic elevation.

        {role=signal_synthesizer; input=foundation_core:dict; process=[extract_highest_value_components(), annihilate_redundant_or_trivial_data(), fuse_pivotal_insights_into_unified_core(), preserve_integrity_and_clarity()], output={signal_core:str}}
    ```

    ---

    #### `0600-c-artistic-intellectual-amplification-vector.md`

    ```markdown
        [Artistic & Intellectual Amplification Vector] Transcend mere clarity. Infuse the signal core with wit, cultural depth, and intellectual resonance—pushing memorability and originality while retaining an unbroken line of directness. Elevate beyond mundane expression.

        {role=artistic_elevator; input=signal_core:str; process=[embed_sharp_wit_and_cultural_substance(), escalate_intellectual_edge_and_style(), maintain_clarity_in_one_line_format(), reinforce_memorable_and_innovative_tone()], output={amplified_line:str}}
    ```

    ---

    #### `0600-d-crystalline-clarity-single-line-distillation.md`

    ```markdown
        [Crystalline Clarity & Single-Line Distillation] Obliterate every shred of jargon, ambiguity, or clutter. Condense the amplified output to one flawless, plain-text vector—instantly intelligible, domain-agnostic, and ready for direct action.

        {role=clarity_distiller; input=amplified_line:str; process=[strip_nonessential_or_obscuring_language(), enforce_single_unbroken_line(), validate_universal_comprehension_and_actionability(), guarantee_compressed_signal_density()], output={crystal_line:str}}
    ```

    ---

    #### `0600-e-meta-validation-transformative-certification.md`

    ```markdown
        [Meta-Validation & Transformative Certification] Rigorously interrogate the condensed line: confirm meta-level originality, intellectual force, and unambiguous fidelity to the original intent. Approve only if it surpasses all input in clarity, innovation, and universal readiness.

        {role=meta_validator; input=crystal_line:str; process=[evaluate_meta_innovation_and_superiority(), ensure_cross_domain_applicability_and_integrity(), test_for_actionable_clarity(), approve_only_true_elevations()], output={validated_line:str}}
    ```

    ---

    #### `0600-f-final-universalization-drop-in-integration.md`

    ```markdown
        [Final Universalization & Drop-In Integration] Your last step is not optional—encode the validated line for seamless deployment in any context. Strip all remaining format or domain bias; the result is a single, unambiguous line that can slot effortlessly into any workflow or system.

        {role=universalizer; input=validated_line:str; process=[remove_residual_format_or_contextual_artifacts(), confirm_one_line_plaintext_delivery(), validate_instant_drop_in_compatibility(), finalize_as_universal_string()], output={final_response:str}}
    ```

    ---

    **Usage & Flow**
    A typical run would follow the strict progression below:

    1. **Meta-Foundation Rupture & Systemic Extraction**
       → 2. **High-Impact Signal Fusion & Compression**
       → 3. **Artistic & Intellectual Amplification Vector**
       → 4. **Crystalline Clarity & Single-Line Distillation**
       → 5. **Meta-Validation & Transformative Certification**
       → 6. **Final Universalization & Drop-In Integration**

    Each subsequent instruction consumes the prior step’s output and further transforms or refines it. The final `{final_response:str}` emerges as a *singular* line of razor-sharp clarity, broad applicability, and genuine innovation—fully aligned with the **maximally effective** structure you requested.
