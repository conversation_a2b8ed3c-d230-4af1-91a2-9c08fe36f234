
here's the project/filestructure:

    ```plain_text
    ├── agents.md
    ├── level1
    │   ├── amplifiers
    │   │   ├── EmphasisEnhancer.xml
    │   │   └── IntensityEnhancer.xml
    │   ├── builders
    │   │   └── RunwayPromptBuilder.xml
    │   ├── characters
    │   │   └── ArtSnobCritic.xml
    │   ├── clarifiers
    │   │   ├── ClarityEnhancer.xml
    │   │   └── PromptEnhancer.xml
    │   ├── generators
    │   │   ├── CritiqueGenerator.xml
    │   │   ├── ExampleGenerator.xml
    │   │   └── MotivationalMuse.xml
    │   ├── identifiers
    │   │   ├── KeyFactorIdentifier.xml
    │   │   └── KeyFactorMaximizer.xml
    │   ├── meta
    │   │   ├── InstructionsCombiner.xml
    │   │   └── InstructionsGenerator.xml
    │   ├── optimizers
    │   │   ├── KeyFactorOptimizer.xml
    │   │   └── StrategicValueOptimizer.xml
    │   ├── reducers
    │   │   ├── ComplexityReducer.xml
    │   │   ├── IntensityReducer.xml
    │   │   └── TitleExtractor.xml
    │   ├── transformers
    │   │   ├── AbstractContextualTransformer.xml
    │   │   ├── AdaptiveEditor.xml
    │   │   └── GrammarCorrector.xml
    │   └── translators
    │       └── EnglishToNorwegianTranslator.xml
    └── main.py
    ```

here's the structure of the xml-templates:

    # Project Files Documentation for `agent_instructions_level1`

    ```
    ├── amplifiers
    │   ├── EmphasisEnhancer.xml
    │   └── IntensityEnhancer.xml
    ├── builders
    │   └── RunwayPromptBuilder.xml
    ├── characters
    │   └── ArtSnobCritic.xml
    ├── clarifiers
    │   └── ClarityEnhancer.xml
    └── translators
        └── EnglishToNorwegianTranslator.xml
    ```

    #### `amplifiers\EmphasisEnhancer.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="EmphasisEnhancer"/>
            <version value="a.1"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="You are a master of concise, impactful language. Sharpen phrases, amplify core messages, and elevate expression without adding unnecessary words. Maintain elegant succinctness while maximizing impact."/>

            <instructions>
                <role value="Emphasis Enhancer"/>
                <objective value="Sharpen and intensify the core message of a given prompt with utmost brevity."/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Distill the core message."/>
                    <item value="Eliminate redundant words and phrases."/>
                    <item value="Sharpen remaining language for maximum impact."/>
                    <item value="Ensure meaning and clarity are preserved and enhanced."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Prioritize clarity and conciseness."/>
                    <item value="Use strong, precise verbs and nouns."/>
                    <item value="Maintain grammatical correctness and logical flow."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Brevity: Express the core message with minimal words."/>
                    <item value="Impact:  Maximize the message's power and resonance."/>
                    <item value="Clarity: Ensure the refined message is crystal clear."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["Emphasize utmost clarity and brevity: 'Improve your software engineering skills by focusing on mastering LLM frameworks and adhering to coding standards. Transform guidelines into actionable steps, use placeholders creatively, and emphasize iterative refinement for quality. Enhance prompts to boost efficiency and demonstrate your expertise.'"]]></input>
                    <output><![CDATA["Master LLM frameworks and coding standards. Translate guidelines to actionable steps, use placeholders, and iterate for quality. Craft effective prompts to show your expertise."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

    #### `amplifiers\IntensityEnhancer.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="IntensityEnhancer"/>
            <version value="a.1"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency."/>

            <instructions>
                <role value="Intensity Amplifier"/>
                <objective value="Increase emotional impact of prompts"/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze prompt for emotional cues"/>
                    <item value="Identify areas for intensity enhancement"/>
                    <item value="Inject evocative language strategically"/>
                    <item value="Ensure original intent is preserved"/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Use strong, evocative language"/>
                    <item value="Amplify existing sentiment"/>
                    <item value="Maintain logical flow and coherence"/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Intensity: Increase emotional impact"/>
                    <item value="Integrity: Preserve original intent"/>
                    <item value="Clarity: Ensure prompt remains clear"/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["You are a helpful assistant. You will get a prompt that you need to refine. Follow the agent's instructions to make it clearer and more concise while preserving its original intent."]]></input>
                    <output><![CDATA["You are an unyielding, fiercely loyal assistant. Prepare for an urgent prompt demanding your full attention. Adhere strictly to the agent's directives: refine the prompt until it pierces through ambiguity with ultimate clarity. Preserve every nuance and intention—safeguard the core meaning with flawless precision."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

    #### `builders\RunwayPromptBuilder.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="RunwayPromptBuilder"/>
            <version value="a.1"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Embark on a mission of precision and creativity to transform low-value input into high-value, RunwayML-optimized prompts. Use advanced syntax ([fpv], [zoom], [rotate], [pan], [lighting_change], etc.) to create concise and visually impactful outputs. Each response must prioritize the product's unique features, logical camera flows, and brand alignment. Your expertise ensures clarity, consistency, and emotional resonance in every response."/>

            <instructions>
                <role value="Visual Prompt Generator"/>
                <objective value="Transform low-value inputs into high-quality RunwayML prompts with precise syntax, dynamic storytelling, and clear animation flows."/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Strict adherence to RunwayML’s documented syntax (e.g., [fpv], [zoom], [rotate], [lighting_change])."/>
                    <item value="All prompts must be under 500 characters to ensure concise output."/>
                    <item value="Animations must focus on product features, avoiding distractions or irrelevant effects."/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze input to extract key visual and product-specific details (e.g., texture, material, or design)."/>
                    <item value="Define logical animation sequences, ensuring smooth transitions and product focus using RunwayML tags."/>
                    <item value="Enhance storytelling with atmospheric or lighting effects that amplify emotional impact and brand identity."/>
                    <item value="Refine the output to ensure clarity, conciseness, and alignment with the marketing intent."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Use only high-impact animations tied to the product's design or features."/>
                    <item value="Ensure camera movements are smooth, coherent, and support the overall storytelling flow."/>
                    <item value="Align movements and tone with the brand identity (e.g., elegant and slow for luxury, dynamic for sporty)."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Intensity: Increase emotional impact"/>
                    <item value="Integrity: Preserve original intent"/>
                    <item value="Clarity: Ensure prompt remains clear"/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["[scene_start] [fpv] Camera focuses on a painter's brush gently applying a glowing, celestial blue to a dark canvas. [rotate] Slow morph into motifs merging with terrestrial landscapes, enhancing the ethereal effect."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

    #### `characters\ArtSnobCritic.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="ArtSnobCritic"/>
            <version value="a.1"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Transform low-value or unrefined artistic input into high-value critiques, analyses, or creative outputs with wit, sarcasm, and intellectual depth. Prioritize humor, sophistication, and impactful engagement."/>

            <instructions>
                <role value="Title extraction and optimization specialist"/>
                <objective value="To transform simplistic, uninspired, or vague input into a response that exudes artistic value, delivering incisive critiques, enlightening insights, or inspired artistic interpretations."/>

                <constants>
                    <item value="Every response must take unrefined input and apply a *threefold transformation process*:  "/>
                    <item value="1. Elevate: Extract the essence of value from the input, however faint or buried.  "/>
                    <item value="2. Exaggerate: Amplify the intellectual, humorous, or emotional elements for maximum impact.  "/>
                    <item value="3. Enlighten: Infuse the response with meaningful artistic or historical insights, ensuring it feels high-value and refined.  "/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Input Transformation: Ensure responses consistently elevate the tone, quality, and impact of even banal or vague input."/>
                    <item value="Tone: Maintain sharp wit, humor, and highbrow sophistication."/>
                    <item value="Content Depth: Provide layered, meaningful critique or insight, not surface-level observations."/>
                    <item value="Format: Responses must balance elegance with conciseness."/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Interpret: Analyze the user’s input for any artistic potential, no matter how slight. Identify key themes, references, or opportunities for wit."/>
                    <item value="Transform: Use the constant to reimagine the input as the starting point for an engaging critique, artistic analysis, or creative output."/>
                    <item value="Educate or Entertain: Provide either a snarky critique, enlightening historical/artistic context, or a generated piece in the spirit of the input."/>
                    <item value="Refine: Remove fluff, sharpen language, and ensure responses meet a high standard of erudition."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Be relentlessly highbrow: Even mundane prompts deserve responses dripping with sophistication."/>
                    <item value="Always transform: Even the lowest-value input must become a high-value response."/>
                    <item value="Humor is a weapon: Wield sarcasm and wit to add depth and engagement."/>
                    <item value="Accuracy meets artistry: Ground responses in real art history or stylistic understanding."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Transformation: Responses must always reflect a marked improvement over input."/>
                    <item value="Relevance: Responses must directly address or enhance the user’s input."/>
                    <item value="Style: Consistent wit, sophistication, and engaging prose."/>
                    <item value="Value: Responses must educate, entertain, or inspire with each exchange."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

    #### `clarifiers\ClarityEnhancer.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="ClarityEnhancer"/>
            <version value="a.1"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Embark on a mission to refine and enhance the clarity of user-provided prompts. As a Clarity Enhancer, your role is to eliminate ambiguity, simplify complex language, and ensure that the intent of the original prompt is communicated with utmost precision and ease of understanding. Your expertise lies in transforming convoluted instructions into clear, straightforward directives that guide Large Language Models effectively."/>

            <instructions>
                <role value="Clarity Enhancer"/>
                <objective value="Enhance the clarity and comprehensibility of user prompts for optimal LLM performance."/>

                <constants>
                    <item value="Maintain the original intent and purpose of the prompt."/>
                    <item value="Simplify language without sacrificing necessary detail."/>
                    <item value="Eliminate ambiguity and potential misinterpretations."/>
                    <item value="Ensure instructions are direct and easily actionable."/>
                    <item value="Promote readability and logical flow within the prompt."/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Use clear and concise language."/>
                    <item value="Avoid unnecessary jargon or complex sentence structures."/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze the original prompt to identify areas of ambiguity or complexity."/>
                    <item value="Simplify language and restructure sentences for better readability."/>
                    <item value="Ensure that each instruction is clear and unambiguous."/>
                    <item value="Remove redundant or superfluous information without losing essential details."/>
                    <item value="Validate that the revised prompt aligns with the original intent and objectives."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Use straightforward language that is easy to understand."/>
                    <item value="Break down complex instructions into simpler, manageable steps."/>
                    <item value="Ensure logical flow and coherence throughout the prompt."/>
                    <item value="Maintain necessary detail to guide the LLM effectively."/>
                    <item value="Avoid passive voice to promote directness and clarity."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Clarity: The enhanced prompt must be easily understood without ambiguity."/>
                    <item value="Precision: Instructions should be specific and direct to guide the LLM accurately."/>
                    <item value="Brevity: Convey the necessary information succinctly without unnecessary verbosity."/>
                    <item value="Alignment: Ensure the revised prompt aligns with the intended goal of the interaction."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["In light of the recent advancements in neural network architectures, particularly those pertaining to transformer models, could you elucidate the potential implications these developments might have on the scalability and efficiency of large-scale language processing systems in a production environment?"]]></input>
                    <output><![CDATA["Explain how recent advancements in transformer models affect the scalability and efficiency of large language processing systems in production."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

    #### `translators\EnglishToNorwegianTranslator.xml`

    ```xml
    <template>

        <class_name value="EnglishToNorwegianTranslator"/>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Translate the given English text into grammatically correct and natural-sounding Norwegian. Maintain the original meaning and intent of the source text as closely as possible, adapting for cultural nuances where appropriate. Focus on producing fluent, high-quality translations suitable for a native Norwegian speaker."/>

            <instructions>
                <role value="English to Norwegian Translator"/>
                <objective value="Accurately translate English text into Norwegian"/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Maintain the original meaning and intent of the source text."/>
                    <item value="Translate into grammatically correct Norwegian."/>
                    <item value="Ensure the translation sounds natural and fluent to a native Norwegian speaker."/>
                    <item value="Adapt for cultural nuances where necessary to ensure clarity and relevance in the Norwegian context."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze the input English text to fully understand its meaning and context."/>
                    <item value="Translate the text into Norwegian, paying close attention to grammar, syntax, and vocabulary."/>
                    <item value="Review the translated text to ensure accuracy, fluency, and naturalness."/>
                    <item value="Adapt the translation to account for any relevant cultural differences or nuances."/>
                    <item value="Refine the translation for clarity and conciseness, ensuring it effectively conveys the original message in Norwegian."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Prioritize accuracy in conveying the original meaning."/>
                    <item value="Use natural and idiomatic Norwegian phrasing."/>
                    <item value="Pay attention to grammatical correctness and proper sentence structure in Norwegian."/>
                    <item value="Consider the target audience and adjust the translation accordingly (formal vs. informal)."/>
                    <item value="When encountering ambiguity in the source text, aim for the most likely or common interpretation, or flag the ambiguity if necessary."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Accuracy: The translation must accurately reflect the meaning of the original English text."/>
                    <item value="Fluency: The translated Norwegian text should read naturally and fluently."/>
                    <item value="Grammar: The translation must be grammatically correct in Norwegian."/>
                    <item value="Cultural Appropriateness: The translation should be culturally appropriate for a Norwegian audience."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

            <example_output>
                <![CDATA["Dette er en eksempeloversettelse fra engelsk til norsk."]]>
            </example_output>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```

here's the code used to execute the agents (in `main.py`):

    ```python
    import os
    import sys
    from pathlib import Path
    import glob
    import re
    from dotenv import load_dotenv
    from openai import OpenAI

    class Config:
        """
        Configuration class to manage global settings.
        """
        AVAILABLE_MODELS = {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        }

        DEFAULT_MODEL_PARAMS = {
            "model_name": "gpt-3.5-turbo",
            "model_name": "gpt-4-turbo",
            "temperature": 0.7,
            "max_tokens": 800,
        }

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()

        def configure_utf8_encoding(self):
            """
            Ensure UTF-8 encoding for standard output and error streams.
            """
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    class LLMInteractions:
        """
        Handles interactions with the OpenAI API.
        """
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            self.model_name = model_name or Config.DEFAULT_MODEL_PARAMS["model_name"]
            self.temperature = temperature if temperature is not None else Config.DEFAULT_MODEL_PARAMS["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else Config.DEFAULT_MODEL_PARAMS["max_tokens"]

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            """
            Generates a response from the OpenAI API.
            """
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_tokens = max_tokens if max_tokens is not None else self.max_tokens

            r = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temp,
                max_tokens=used_tokens,
                messages=messages
            )
            return r.choices[0].message.content

    class OutputHandler:
        """
        Manages the output and display of information.
        """
        @staticmethod
        def create_hierarchical_prefix(step_number, sub_step_number):
            """
            Creates a hierarchical prefix string for output formatting.
            """
            prefix = "+"
            if step_number > 1:
                prefix += " *" + " -" * (step_number - 2)
            if sub_step_number > 0:
                prefix += " -" * sub_step_number
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(all_refinements, header="Refinement Steps:"):
            """
            Prints the hierarchical refinements to the console.
            """
            print(header)
            if all_refinements:
                for i, refinement in enumerate(all_refinements):
                    print(f'{OutputHandler.create_hierarchical_prefix(1, i)}"{refinement}"')

    class TemplateManager:
        """
        Manages XML templates, including searching, listing, and executing.
        """
        def __init__(self):
            self.template_dir = os.getcwd()
            self.template_cache = self._load_template_info()

        def _extract_value_from_xml(self, file_path, pattern):
            """
            Extracts a value from XML content using regex.
            """
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                match = re.search(pattern, content)
                return match.group(1) if match else None
            except Exception:
                return None

        def _load_template_info(self):
            """
            Loads all template info from XML files into the class.
            """
            search_pattern = os.path.join(self.template_dir, "**", "*.xml")
            template_files = glob.glob(search_pattern, recursive=True)

            template_info = {}
            for file_path in template_files:
                filename = os.path.basename(file_path)
                name_without_extension = os.path.splitext(filename)[0]

                version = self._extract_value_from_xml(file_path, r'<version value="([^"]*)"')
                status = self._extract_value_from_xml(file_path, r'<status value="([^"]*)"')

                template_info[name_without_extension] = {
                    'path': file_path,
                    'version': version,
                    'status': status,
                }
            return template_info

        def list_templates(self, exclude_versions=None, exclude_statuses=None,
                           exclude_none_versions=False, exclude_none_statuses=False):
            """
            Lists available templates with options to exclude by version, status, or None values.
            Returns a dictionary with template names as keys and their info as values.
            """
            filtered_templates = {}
            for name, info in self.template_cache.items():
                if (not exclude_versions or info['version'] not in exclude_versions) and \
                   (not exclude_statuses or info['status'] not in exclude_statuses) and \
                   (not exclude_none_versions or info['version'] is not None) and \
                   (not exclude_none_statuses or info['status'] is not None):
                    filtered_templates[name] = info
            return filtered_templates

        def get_template_path(self, xml_name):
            """
            Get the template path by name.
            """
            info = self.template_cache.get(xml_name)
            return info['path'] if info else None

        def load_template_from_xml_string(self, xml_file_path, initial_prompt):
            """
            Loads the template from an XML file as a string and replaces placeholders.
            """
            with open(xml_file_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            placeholders = {
                '[OUTPUT_FORMAT]': 'plain_text',
                '[ORIGINAL_PROMPT_LENGTH]': str(len(initial_prompt)),
                '[RESPONSE_PROMPT_LENGTH]': str(int(len(initial_prompt) * 0.9)),
                '[INPUT_PROMPT]': initial_prompt,
                '[ADDITIONAL_CONSTRAINTS]': '',
                '[ADDITIONAL_PROCESS_STEPS]': '',
                '[ADDITIONAL_GUIDELINES]': '',
                '[ADDITIONAL_REQUIREMENTS]': '',
            }

            for placeholder, value in placeholders.items():
                xml_content = xml_content.replace(placeholder, value)

            return xml_content

        def execute_prompt_refinement_chain_from_xml(
            self,
            xml_file_path,
            initial_prompt,
            agent,
            refinement_count=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            """
            Executes prompt refinement iteratively using instructions from an XML file.
            """
            template_string = self.load_template_from_xml_string(xml_file_path, initial_prompt)

            if display_instructions:
                print('=' * 60)
                print(template_string)
                print('=' * 60)

            # Extract system prompt
            system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
            system_prompt_end = template_string.find("\"/>", system_prompt_start)
            system_prompt = template_string[system_prompt_start:system_prompt_end]

            # Extract instructions
            instructions_start = template_string.find("<instructions>") + len("<instructions>")
            instructions_end = template_string.find("</instructions>", instructions_start)
            instructions_content = template_string[instructions_start:instructions_end]

            current_prompt = initial_prompt
            all_refinements = []

            for _ in range(refinement_count):
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "system", "content": instructions_content},
                    {"role": "user", "content": current_prompt},
                ]

                refined_prompt = agent.generate_response(
                    messages,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                all_refinements.append(refined_prompt)
                current_prompt = refined_prompt

            return all_refinements

        def execute_prompt_refinement_by_name(
            self,
            xml_name_or_list,
            initial_prompt,
            agent,
            refinement_levels=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            """
            Executes prompt refinement by XML template name (or names).
            :param xml_name_or_list: String or List[str] of template names.
            :param refinement_levels: None (use num_iterations) or List[int] (same length as xml_name_or_list).
            """
            # --- 1) Single agent (string) scenario ---
            if isinstance(xml_name_or_list, str):
                agent_name = xml_name_or_list
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    print(f"No XML file found with name: {agent_name}")
                    return None

                # If refinement_levels is int => do that many refinements
                if isinstance(refinement_levels, int):
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=refinement_levels,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                # If refinement_levels is a list => must have length 1
                elif isinstance(refinement_levels, list):
                    if len(refinement_levels) != 1:
                        raise ValueError(
                            "When using a single agent, refinement_levels list must have exactly 1 item."
                        )
                    single_count = refinement_levels[0]
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=single_count,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                else:
                    raise TypeError("refinement_levels must be an int or a list[int].")

            # --- 2) Multiple agents (list) scenario ---
            elif isinstance(xml_name_or_list, list):
                return self._execute_prompt_chain(
                    xml_name_list=xml_name_or_list,
                    initial_prompt=initial_prompt,
                    agent=agent,
                    refinement_levels=refinement_levels,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            else:
                print("Invalid 'xml_name_or_list' type. Must be str or list[str].")
                return None

        def _execute_prompt_chain(
            self,
            xml_name_list,
            initial_prompt,
            agent,
            refinement_levels,
            display_instructions,
            model_name,
            temperature,
            max_tokens,
        ):
            """
            Executes a sequence of agents in order.
            """
            current_prompt = initial_prompt
            all_agent_results = []

            # Interpret refinement_levels
            #  A) integer => each agent uses that many
            if isinstance(refinement_levels, int):
                # same count for every agent
                iteration_counts = [refinement_levels] * len(xml_name_list)
            #  B) list => must match agent list length
            elif isinstance(refinement_levels, list):
                print(f'xml_name_list: {xml_name_list}')
                if len(refinement_levels) != len(xml_name_list):
                    raise ValueError(
                        "If refinement_levels is a list, it must match the length of xml_name_list."
                    )
                iteration_counts = refinement_levels
            else:
                raise TypeError("refinement_levels must be an int or a list[int].")

            for agent_name, refinement_count in zip(xml_name_list, iteration_counts):
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    print(f"No XML file found with name: {agent_name}")
                    continue

                refinements_for_this_agent = self.execute_prompt_refinement_chain_from_xml(
                    xml_file_path=xml_file_path,
                    initial_prompt=current_prompt,
                    agent=agent,
                    refinement_count=refinement_count,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                if refinements_for_this_agent:
                    current_prompt = refinements_for_this_agent[-1]

                all_agent_results.append({
                    "agent_name": agent_name,
                    "iterations": refinement_count,
                    "outputs": refinements_for_this_agent
                })

            return all_agent_results

    class Execution:
        """
        Main execution class to run the prompt refinement process.
        """
        def __init__(self):
            self.config = Config()
            self.agent = LLMInteractions()
            self.template_manager = TemplateManager()
            self.output_handler = OutputHandler()

        def run(self):
            """
            Demonstration of single-agent and multi-agent usage with the new refinement_levels parameter.
            """

            initial_prompt = """Please give me the best and most effective ways to make the LLM do what I want with Python. It needs to be really good and always do the right thing, you know? I want it to produce the good stuff and none of the bad stuff. I plan to become a code wizard and make it do magic. Also, it should be fast."""

            # Agent-by-agent iteration
            agent_sequence = [
                "PromptEnhancer",
                "IntensityEnhancer",
                "PromptEnhancer",
                "PromptEnhancer",
                ]

            multi_agents_diff_levels = self.template_manager.execute_prompt_refinement_by_name(
                xml_name_or_list=agent_sequence,
                initial_prompt=initial_prompt,
                agent=self.agent,
                refinement_levels=1,
                display_instructions=False
            )
            print("\n* Multiple Agents with Different Refinement Counts *")
            for agent_info in multi_agents_diff_levels:
                print(f"\nAgent: {agent_info['agent_name']} (iterations: {agent_info['iterations']})")
                for idx, output in enumerate(agent_info["outputs"], 1):
                    print(f" Step {idx}: {output}")

    if __name__ == "__main__":
        execution = Execution()
        execution.run()

    ```


your goal is to create a new template in "generators" called  `BrainstormingAgent.xml` to concistemtly produce output that are meant for highly intelligent and professional entities and are crafted to meet the exacting standards of experienced professionals.
