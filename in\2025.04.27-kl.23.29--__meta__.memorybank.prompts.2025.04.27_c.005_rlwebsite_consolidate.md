assimilate and aggregate the insights from the additional references (labeled #1 through #5 in your prompt) into a single, final "memory bank system template." this template is designed to govern the process and documentation of understanding and refactoring the ringerike landskap website (rlweb) codebase, leveraging the principles discussed previously and incorporating the refinements identified in your latest examples.

*Process:*
- Define or verify the Memory Bank’s root abstraction by constructing a strictly numbered, Markdown-based file structure (e.g., 1-projectbrief.md, 2-productContext.md, etc.), ensuring all top-level files represent the minimal set of highest-value abstraction tiers directly dictated by current project context.
- Audit the existing file structure for redundancy, bloat, and overlapping scopes; consolidate, demote, or eliminate files and documentation that do not yield direct abstraction-level value anchored in the root project purpose.
- For every new codebase element, insight, or workflow demand, explicitly map and justify its relationship to the Memory Bank root abstraction, dynamically updating hierarchical placement and reference links to reinforce foundation-first logic.
- Embed or update distilled context mini-summaries or a distilledContext.md at key abstraction junctions within the Memory Bank to support rapid cognitive loading, continuity, and prevention of context drift in future assimilation or onboarding flows.
- Reframe assimilated content and complexity as persistent, high-value relationships within the file structure, so that documentation, patterns, or discovered knowledge become self-cognizant nodes anchored outward from the most abstract ‘root’, eliminating unnecessary detail proliferation.
- Direct all discoveries, interventions, and improvement proposals through explicit, traceable records—updating files such as 5-activeContext.md, 6-progress.md, and 7-tasks.md—ensuring continuity, historical integrity, and a live chain-of-reasoning linked upward to the project root.
- Orchestrate all workflow phases (scan, map, act) with persistent bias towards complexity reduction and value extraction: operationalize anti-bloat rules, structural audits, and periodic re-anchoring to the root abstraction, guaranteeing data reduction without value loss as an explicit step at each phase.
- At the conclusion of each assimilation cycle, validate that: (1) all knowledge and interventions are contextually anchored in the file structure; (2) essential value and clarity are maximized; (3) no untracked complexity or redundant documentation remains; and (4) all project elements remain traceable to the overarching mission. Sanction the finalized Memory Bank if all criteria pass.

*Constraints:*
- Do not proceed beyond initial file structure definition unless every file is justified as essential by abstraction responsibility.
- Never introduce or retain files or content merely to catalog existing complexity; only preserve or expand documentation that clarifies or compresses context at the highest possible tier.
- Always trace each update, intervention, or deep dive back to an explicit line of reasoning linking it to the root project mission and structure.
- Ban concept drift: re-anchor all moves to both prior Memory Bank states (when applicable) and the current project core purpose.
- Enforce strict adherence to Markdown sequential file format (e.g., 1-projectbrief.md, 2-productContext.md, etc.)—no loosely named or generic documentation permitted.

*Requirements:*
- Every action, file, and documented insight must be explicitly justified using the core meta-invariant: persistent, root-abstraction-first clarity, utility, and adaptability with maximized value density.
- The Memory Bank structure must always serve as the lens through which new and legacy codebase knowledge is reframed and maintained.
- No file, section, or intervention is persisted unless it directly reduces entropy or enhances traceability and project clarity.
- Session-to-session continuity is *guaranteed only* by the Memory Bank; no ephemeral or tacit knowledge is relied upon.
- Anti-bloat and non-redundancy mechanisms (checklists, structure audits, documentation/code ratio <30%, context tracing per step) must be explicitly enforced throughout.
- Updates are only admitted to the Memory Bank if their abstraction level, utility, and fit can be justified in terms of project-wide clarity and persistent, maximal value.
- Root-level orientation and context linkage is preserved at all times (e.g., via distilled summaries, reference section links, and explicit file inclusion rationale).
- If prior Memory Bank files exist, review and migrate only those elements that can be justified under the new project's abstraction-aligned structure and value rules.
- All instructions, principles, and rationales must be operationalized—never codified as platitude, generic text, or wishful statements.

*Sequence (example):*
- Assess the current codebase to determine the most minimal, context-responsive file structure needed to capture all root-level project knowledge.
- Establish or validate a strictly numbered, single-responsibility Memory Bank directory structure, beginning with 1-projectbrief.md and cascading abstraction downward (e.g., 2-productContext.md, 3-systemPatterns.md).
- Re-anchor all subsequent codebase assimilation, analysis, and intervention phases to this root-derived file structure, ensuring each workflow starts from the project’s highest-level purpose.
- For every new insight, finding, or intervention, map and document it directly within the relevant Memory Bank file, updating the structure only to enhance abstraction integrity, clarity, or yield.
- Enforce strict anti-bloat and anti-redundancy protocols: consolidate, merge, or remove files if information overlaps, exceeds scope, or loses direct connection to the project’s foundational purpose.
- Require explicit justification for any addition, deep dive, or structural change by demonstrating improved clarity, complexity reduction, or actionable value in the Memory Bank context.
- Deliver each update, summary, or structural decision as a discrete, contextualized entry traceable to the project’s root mission using minimal, precise Markdown documentation.
- Periodically re-audit the Memory Bank structure; ensure every file and content block reinforces minimalism, traceability, and context retention, eliminating entropy or context drift.
- Maintain distilled context or session loading summaries for rapid orientation and continuity (e.g., 0-distilledContext.md or inline mini-summaries), ensuring seamless onboarding and recovery.
- Continuously bias all documentation and workflow toward extracting and reframing maximally abstract, high-value relationships—never merely cataloging or deepening existing local complexity.
- Anchor all progress-tracking, active contexts, and tasks in sequenced, abstraction-justified Memory Bank files (e.g., 5-activeContext.md, 6-progress.md, 7-tasks.md), driving operational clarity and project yield.
- Forbid undocumented field order drift, redundancy, and schema deviations at all steps; persistently ensure instructions remain maximally clear, atomic, and parser-ready.

*Inspiration (for reference only):*

    ```json
    {
      "filename": "0000-meta-memory-bank-root.md",
      "content": "## Purpose & Meta-Orientation\n\n> Establish and relentlessly enforce a file-structure-first, abstraction-rooted Memory Bank system that anchors every document, workflow, and codebase assimilation step directly to the project’s highest-purpose context, yielding maximal clarity, adaptability, and ongoing value extraction with minimum complexity.\n\n---\n\n**Meta-Protocol:**\n- All assimilation, documentation, and intervention must begin from and continually re-anchor to the optimal, current codebase-rooted file structure—never proceeding without explicit structural justification.\n- Every file and change must maximize actionable clarity, utility, and yield, and be justified by its essential relationship to the project’s overarching purpose.\n- No content, action, or structure is permitted without explicit reference to the root abstraction and mandatory chain-of-context.\n",
      "schema": "meta-root-instruction",
      "input": [],
      "output": ["0001-memory-bank-structure.md"],
      "notes": "This meta-layer file defines the global protocol and philosophy, and is responsible for aligning all subsequent files and actions to the meta-abstraction anchor."
    },
    {
      "filename": "0001-memory-bank-structure.md",
      "content": "## Memory Bank File Structure Definition\n\n**Prime Directive:**\n- _Before any deep dives, always (re)validate and (re)define the Memory Bank’s numbered, abstraction-tiered file structure._\n- This structure must reflect the current project state’s irreducible root abstraction and the minimal, non-overlapping segmentation of concerns.\n\n```markdown\nmemory-bank/\n├── 1-projectbrief.md         # Foundation document; project scope and core requirements\n├── 2-productContext.md       # Project purpose, goals, and stakeholders\n├── 3-systemPatterns.md       # High-level system architecture and design principles\n├── 4-contextMap.md           # Persistent relationships and project-specific abstractions\n├── 5-activeContext.md         # Recently assimilated findings, context deltas, or critical active changes\n├── 6-progress.md              # Historical decision log and phased context summaries\n├── 7-tasks.md                 # Justified actions, interventions, and refinement triggers\n```\n\n- Expansion or reduction of files is permitted only with justification at the root abstraction level.\n- Every file must serve a single, precisely scoped responsibility—never introducing redundancy, bloat, or context drift.\n",
      "schema": "file-structure-definition",
      "input": ["0000-meta-memory-bank-root.md"],
      "output": ["0002-projectbrief.md","0003-productContext.md","0004-systemPatterns.md","0005-contextMap.md","0006-activeContext.md","0007-progress.md","0008-tasks.md"],
      "notes": "Every assimilation/refactor step is led by and tethered to this evolving, meta-justified file structure."
    },
    {
      "filename": "0002-projectbrief.md",
      "content": "## Project Brief\n\n- **Summary:** Clearly articulate the project’s fundamental purpose, scope, value proposition, and root requirements. This file acts as the highest abstraction anchor for all subsequent files and decisions.\n- **Core Mandates:**\n  - All assimilation steps and documentation must maintain explicit traceability to one or more project brief tenets.\n  - Any structural/documentation adjustment must be traceable to an explicit improvement in the fulfillment of the project's core mission.\n- **Distilled Context:** Either via `0-distilledContext.md` or concise inline summaries at the file top, immediately convey the essential orientation and value leverages for rapid onboarding and minimal cognitive load.\n",
      "schema": "project-brief-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Root context—no further abstraction; all file purposes, interventions, and rationales chain back here."
    },
    {
      "filename": "0003-productContext.md",
      "content": "## Product & Stakeholder Context\n\n- **Project Rationale:** Define the 'why': origins, beneficiaries, expected outcomes, broad user and market goals.\n- **Core Users/Stakeholders:** Explicit mapping of audiences, needs, value definition, constraints, and relationship to the project's mission.\n- **Actionable Leverage:** Only include contextual details that clarify or prioritize high-yield direction for all technical, design, and process choices downstream.\n",
      "schema": "product-context-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Directly contextualizes project brief and ties into all system pattern and intervention decisions."
    },
    {
      "filename": "0004-systemPatterns.md",
      "content": "## System & Architectural Patterns\n\n- **High-Level View:** Map primary system architecture, design patterns, and key decisions in relation to purpose/context.\n- **Pattern Rationale:** Each detail must clarify a single, high-abstraction decision or recurring implementation pattern. All content must compress verbose/complex details into maximally reusable, context-tethered patterns (not mere documentation of the status quo).\n- **Persistent Entropy Check:** Only patterns that eliminate, unify, or clarify system complexity vs. project goals are included; bloat and redundancy are ruthlessly pruned.\n",
      "schema": "system-patterns-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Supports and justifies all implementation, refactor, and intervention priorities."
    },
    {
      "filename": "0005-contextMap.md",
      "content": "## Context Map\n\n- **Persistent Relationships:** Map and maintain explicit links between files, concepts, code assets, topics, and workflow phases.\n- **Abstraction Ladders:** Synthesize cross-cutting connections, commonalities, dependencies and their rationale in relation to the foundational root abstraction.\n- **Dynamic Update:** Any change in context or abstraction layer must be mirrored here; ensures the meta-justified chain of reasoning is not lost.\n",
      "schema": "context-map-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Serves as the living manifestation of the meta-principle on interconnectedness between abstraction layers and persistent context."
    },
    {
      "filename": "0006-activeContext.md",
      "content": "## Active Context\n\n- **Recent Updates:** Chronologically document recent findings, insights, interventions, or contextual deltas that have not yet been fully assimilated into the higher-abstraction files.\n- **Justification for Action:** For every entry, clarify root reasoning for inclusion and relate each back to the file structure.\n- **Entry Expiry:** Entries must either be promoted to their proper abstraction tier or pruned within a short, defined cycle—never persisting as entropy.\n",
      "schema": "active-context-update-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Guardrail to ensure the living context never accumulates unsorted complexity."
    },
    {
      "filename": "0007-progress.md",
      "content": "## Progress & Intervention Log\n\n- **Decision Log:** Sequentially capture all meaningful interventions: what, why, when, and tie each decision and action to the related Memory Bank file via explicit reference.\n- **Phase Summaries:** Maintain rolling, concise summaries of what has been clarified, refactored, reduced, or pruned as a direct result—always referencing the root purpose.\n- **Yield Criterion:** Only log context shifts that compress complexity or directly advance actionable clarity and value.\n",
      "schema": "progress-log-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "Persistent audit trail for structural and knowledge evolution under the meta-protocol."
    },
    {
      "filename": "0008-tasks.md",
      "content": "## Tasks & Action Items\n\n- **Justified Interventions:** List all pending, in-progress, or proposed interventions, improvements, or reductions—each with explicit justification in abstraction-rooted context.\n- **Yield & Urgency:** Every task must specify expected value, tie to the relevant file, and include a rationale for why it compresses, simplifies, or clarifies project complexity relative to the root abstraction.\n- **Anti-Bloat Guard:** No speculative or unanchored task is permitted; every action must self-certify anti-bloat compliance and explicit necessity.\n",
      "schema": "tasks-document",
      "input": ["0001-memory-bank-structure.md"],
      "output": [],
      "notes": "No task survives without explicit, root-level value justification and location in Memory Bank."
    }
    ```

