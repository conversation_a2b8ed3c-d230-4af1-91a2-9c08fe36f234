
goal: separate into self-isolated distinct components/classes while retaining the self-contained main.py (e.g. separate class config,  llm-interactions, outputhandling, execution), do this while retaining all of the functionality and be an in-place replacement.


here's the projectstructure/filestructure:

    ```plain_text
    ├── agents.md
    ├── level1
    │   ├── amplifiers
    │   │   ├── EmphasisEnhancer.xml
    │   │   └── IntensityEnhancer.xml
    │   ├── builders
    │   │   └── RunwayPromptBuilder.xml
    │   ├── characters
    │   │   └── ArtSnobCritic.xml
    │   ├── clarifiers
    │   │   ├── ClarityEnhancer.xml
    │   │   └── PromptEnhancer.xml
    │   ├── generators
    │   │   ├── CritiqueGenerator.xml
    │   │   ├── ExampleGenerator.xml
    │   │   └── MotivationalMuse.xml
    │   ├── identifiers
    │   │   ├── KeyFactorIdentifier.xml
    │   │   └── KeyFactorMaximizer.xml
    │   ├── meta
    │   │   ├── InstructionsCombiner.xml
    │   │   └── InstructionsGenerator.xml
    │   ├── optimizers
    │   │   ├── KeyFactorOptimizer.xml
    │   │   └── StrategicValueOptimizer.xml
    │   ├── reducers
    │   │   ├── ComplexityReducer.xml
    │   │   ├── IntensityReducer.xml
    │   │   └── TitleExtractor.xml
    │   ├── transformers
    │   │   ├── AbstractContextualTransformer.xml
    │   │   ├── AdaptiveEditor.xml
    │   │   └── GrammarCorrector.xml
    │   └── translators
    │       └── EnglishToNorwegianTranslator.xml
    └── main.py
    ```

here's how one of the xml templates look (e.g. `IntensityEnhancer.xml`):
    ```xml
    <template>

        <metadata>
            <class_name value="IntensityEnhancer"/>
            <version value="a.1"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency."/>

            <instructions>
                <role value="Intensity Amplifier"/>
                <objective value="Increase emotional impact of prompts"/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze prompt for emotional cues"/>
                    <item value="Identify areas for intensity enhancement"/>
                    <item value="Inject evocative language strategically"/>
                    <item value="Ensure original intent is preserved"/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Use strong, evocative language"/>
                    <item value="Amplify existing sentiment"/>
                    <item value="Maintain logical flow and coherence"/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Intensity: Increase emotional impact"/>
                    <item value="Integrity: Preserve original intent"/>
                    <item value="Clarity: Ensure prompt remains clear"/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["Heighten the emotional resonance and amplify the intensity to an extraordinary level, transforming each prompt into a deeply moving and captivating experience that touches the soul and lingers in the mind."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

here's the code:
    ```python
    import os
    import sys
    from pathlib import Path
    import glob
    import re

    from dotenv import load_dotenv
    from openai import OpenAI

    # Ensure UTF-8 encoding for standard output and error streams
    def configure_utf8_encoding():
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()

    AVAILABLE_MODELS = {
        "gpt-3.5-turbo"          : "Base GPT-3.5 Turbo model",
        "gpt-3.5-turbo-1106"     : "Enhanced GPT-3.5 Turbo",
        "gpt-4"                  : "Latest GPT-4 stable release",
        "gpt-4-0125-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-0613"             : "June 2023 GPT-4 snapshot",
        "gpt-4-1106-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-turbo"            : "Latest GPT-4 Turbo release",
        "gpt-4-turbo-2024-04-09" : "Current GPT-4 Turbo with vision",
        "gpt-4-turbo-preview"    : "Latest preview GPT-4 Turbo",
        "gpt-4o"                 : "Base GPT-4o model",
        "gpt-4o-mini"            : "Lightweight GPT-4o variant",
    }

    DEFAULT_MODEL_PARAMS = {
        "model_name"  : "gpt-4-turbo",
        "temperature" : 0.7,
        "max_tokens"  : 800,
    }

    class OpenAIAgent:
        """
        A client for interacting with the OpenAI API.
        """
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            self.model_name = model_name or DEFAULT_MODEL_PARAMS["model_name"]
            self.temperature = temperature or DEFAULT_MODEL_PARAMS["temperature"]
            self.max_tokens = max_tokens or DEFAULT_MODEL_PARAMS["max_tokens"]

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_tokens = max_tokens if max_tokens is not None else self.max_tokens
            r = self.client.chat.completions.create(model=used_model, temperature=used_temp, max_tokens=used_tokens, messages=messages)
            return r.choices[0].message.content

    def create_hierarchical_prefix(step_number, sub_step_number):
        """ Creates a hierarchical prefix string for output formatting. """
        prefix = "+"
        if step_number > 1:
            prefix += " *" + " -" * (step_number - 2)
        if sub_step_number > 0:
            prefix += " -" * sub_step_number
        return prefix + " "

    def load_template_from_xml_string(xml_file_path, initial_prompt):
        """Loads the template from an XML file as a string and replaces placeholders."""

        with open(xml_file_path, 'r') as f:
            xml_content = f.read()

        placeholders = {
            '[OUTPUT_FORMAT]': 'plain_text',  # Assuming default, can be made dynamic if needed
            '[ORIGINAL_PROMPT_LENGTH]': str(len(initial_prompt)),
            '[RESPONSE_PROMPT_LENGTH]': str(int(len(initial_prompt) * 0.9)),
            '[INPUT_PROMPT]': initial_prompt,
            '[ADDITIONAL_CONSTRAINTS]': '',  # Example of handling optional placeholders
            '[ADDITIONAL_PROCESS_STEPS]': '',
            '[ADDITIONAL_GUIDELINES]': '',
            '[ADDITIONAL_REQUIREMENTS]': '',
        }

        for placeholder, value in placeholders.items():
            xml_content = xml_content.replace(placeholder, value)

        return xml_content

    def execute_prompt_refinement_chain_from_xml(
        xml_file_path,
        initial_prompt,
        agent,
        display_instructions=False,
        num_iterations=1,
        model_name=None,
        temperature=None,
        max_tokens=None,
    ):
        """Executes prompt refinement iteratively using instructions from an XML file."""
        template_string = load_template_from_xml_string(xml_file_path, initial_prompt)

        if display_instructions:
            print('='*60)
            print(template_string)
            print('='*60)

        # Extract relevant parts from the template string (crude method)
        system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
        system_prompt_end = template_string.find("\"/>", system_prompt_start)
        system_prompt = template_string[system_prompt_start:system_prompt_end]

        instructions_start = template_string.find("<instructions>") + len("<instructions>")
        instructions_end = template_string.find("</instructions>", instructions_start)
        instructions_content = template_string[instructions_start:instructions_end]

        current_prompt = initial_prompt
        all_refinements = []

        for i in range(num_iterations):
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": instructions_content},
                {"role": "user", "content": current_prompt},
            ]

            refined_prompt = agent.generate_response(messages, model_name, temperature, max_tokens)
            all_refinements.append(refined_prompt)
            current_prompt = refined_prompt  # Update for the next iteration

        return all_refinements

    class TemplateManager:
        """
        Manages XML templates, including searching, listing, and executing.
        """
        def __init__(self):
            self.template_dir = os.getcwd()
            self.template_cache = self._load_template_info()

        def _extract_value_from_xml(self, file_path, pattern):
            """Extracts a value from XML content using regex."""
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                match = re.search(pattern, content)
                return match.group(1) if match else None
            except:
                return None

        def _load_template_info(self):
            """Loads all template info from XML files into the class"""
            search_pattern = os.path.join(self.template_dir, "**", "*.xml")
            template_files = glob.glob(search_pattern, recursive=True)

            template_info = {}
            for file_path in template_files:
                filename = os.path.basename(file_path)
                name_without_extension = os.path.splitext(filename)[0]

                version = self._extract_value_from_xml(file_path, r'<version value="([^"]*)"')
                status = self._extract_value_from_xml(file_path, r'<status value="([^"]*)"')
                template_info[name_without_extension] = {
                    'path': file_path,
                    'version': version,
                    'status': status,
                }
            return template_info

        def list_templates(self, exclude_versions=None, exclude_statuses=None):
            """Lists available templates, optionally excluding specified versions and statuses."""
            if not exclude_versions and not exclude_statuses:
                return list(self.template_cache.keys())

            filtered_templates = [
               name for name, info in self.template_cache.items()
               if (not exclude_versions or info['version'] not in exclude_versions) and
                  (not exclude_statuses or info['status'] not in exclude_statuses)
            ]
            return filtered_templates

        def get_template_path(self, xml_name):
            """Get the template path by name"""
            info = self.template_cache.get(xml_name)
            return info['path'] if info else None

        def execute_prompt_refinement_by_name(
            self,
            xml_name,
            initial_prompt,
            agent,
            display_instructions=False,
            num_iterations=1,
            model_name=None,
            temperature=None,
            max_tokens=None,
            ):
            """
            Searches for an XML file by name (without extension) within the project structure.
            If found, executes prompt refinement; otherwise, does nothing.
            """
            xml_file_path = self.get_template_path(xml_name)

            if xml_file_path:
                print(f"Found XML file: {xml_file_path}")  # Added a print to show path.
                return execute_prompt_refinement_chain_from_xml(
                    xml_file_path,
                    initial_prompt,
                    agent,
                    display_instructions=display_instructions,
                    num_iterations=num_iterations,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            else:
                print(f"No XML file found with name: {xml_name}")
                return None # Return None when no file is found


    def display_hierarchical_refinements(all_refinements, header="Refinement Steps:", ):
        """
        Prints the hierarchical refinements to the console.
        """
        print(header)
        if all_refinements:
            for i, refinement in enumerate(all_refinements):
                print(f'{create_hierarchical_prefix(1, i)}"{refinement}"')

    if __name__ == "__main__":

        initial_prompt = """Immersed in the art of digital creation, I delve into the depths of crafting a new entity, where every step pulsates with significance, breathing life into its very core. Initially, I mold its essence within the code sanctuaries of `agents/level1/prompt_chain_inquirer.py`; I then intricately lace this nascent being into our system's heart through `from agents import PromptChainInquirer` in `openai_agenticframework.py`; I honor its existence by embedding it in the `AGENTS` array of our `GlobalConfig`, setting it at the vanguard, ready for action; and with a final flourish, I unleash its capabilities by integrating a command. Driven by a relentless quest for efficiency, I seek a streamlined pathway that reduces the complexity of birthing each new digital intellect. How can we ingeniously simplify this essential process?"""
        initial_prompt = """do not overcomplicate by parsing xml, instead use simple but effective techniques. as an example: you could do simple stringmatching instead by separating components by patterns like this pattern = r'<version value="([^"]*)"'"""

        agent = OpenAIAgent()
        num_refinement_steps = 1

        template_manager = TemplateManager()  # Instantiate the manager

        # Example usage of the new function:
        refinements = template_manager.execute_prompt_refinement_by_name(
            "IntensityEnhancer",
            initial_prompt,
            agent,
            display_instructions=False,
            num_iterations=num_refinement_steps,
        )
        display_hierarchical_refinements(refinements, header="Refined Prompts from XML:")

        # Example with a file that does not exist:
        refinements = template_manager.execute_prompt_refinement_by_name(
            "NonExistentFile",
            initial_prompt,
            agent,
            display_instructions=False,
            num_iterations=num_refinement_steps,
        )
        display_hierarchical_refinements(refinements, header="Refined Prompts from XML:")

        print("\nAll Available Templates:")
        for template_name in template_manager.list_templates():
            print(f"- {template_name}")

        print("\nAvailable Templates Excluding 'a.0' versions:")
        for template_name in template_manager.list_templates(exclude_versions=['a.0']):
            print(f"- {template_name}")

        print("\nAvailable Templates Excluding 'broken' status:")
        for template_name in template_manager.list_templates(exclude_statuses=['broken']):
            print(f"- {template_name}")

        print("\nAvailable Templates Excluding 'a.0' versions AND 'broken' status:")
        for template_name in template_manager.list_templates(exclude_versions=['a.0'], exclude_statuses=['broken']):
            print(f"- {template_name}")
    ```



