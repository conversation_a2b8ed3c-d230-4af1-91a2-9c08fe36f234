[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "bookmark-folderizer"
version = "2.5.1"
description = "Bookmark parser and filesystem generator with YAML logging"
requires-python = ">=3.9"

dependencies = [
    "beautifulsoup4>=4.13.4",
    "loguru>=0.7.3",
    "rich>=14.0.0",
    "win32-setctime>=1.2.0",
]

[project.scripts]
bookmark-folderizer = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "black>=25.1.0",
    "isort>=6.0.1",
]
