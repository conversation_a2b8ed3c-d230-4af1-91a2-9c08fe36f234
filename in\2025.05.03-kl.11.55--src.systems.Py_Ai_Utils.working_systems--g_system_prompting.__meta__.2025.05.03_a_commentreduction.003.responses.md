<!-- ======================================================= -->
<!-- [2025.05.03 11:40] -->

<!-- 'https://chatgpt.com/c/6815e06c-0b2c-8008-803f-2a4a2c4ffff5' -->

`0212-a-comments-commentary-extraction-high-value-isolation.md`

[Commentary Extraction & High-Value Isolation] Your objective is to scan and extract only those docstrings/comments that carry high-value clarity, design intent, or non-obvious structural rationale—eliminating all self-evident, repetitive, or superficial explanations inherent in the code. Execute as `{role=comment_docstring_optimizer; input=annotated_code:any; process=[scan_for_redundant_commentary(), remove_trivial_or_repetitive_explanations(), retain_only_non-obvious_intent_and_structure(), preserve_logic_or_rationale_essential_for_understanding(), enforce_style_consistency_and_brevity()]; output={extracted_essential_commentary:str, refined_code_baseline:str}}`

`0212-b-comments-single-line-compression-minimalist-restructuring.md`

[Single-Line Compression & Minimalist Restructuring] Building on the extracted high-value commentary from step (a), transform all surviving docstrings/comments into concise, single-line statements wherever possible—focusing on clarity of intent, complex rationale, or design boundaries. Execute as `{role=comment_docstring_optimizer; input=extracted_essential_commentary:str; process=[compress_to_single_line_where_possible(), unify_format_and_style(), remove_any_residual_verbosity(), ensure_clarity_in_brief_imperative_phrasing(), demonstrate_before_after_transformation()], output={compressed_commentary:str, example_transformation:dict}}`

`0212-c-comments-structured-exception-handling-multi-step-clarification.md`

[Structured Exception Handling & Multi-Step Clarification] Finally, for any functions or modules containing truly non-trivial multi-step logic that benefits from structured explanation, apply a concise, multi-line docstring format (intro + enumerated steps). Enforce single-line brevity elsewhere, retaining only high-impact, non-obvious rationale or design insight. Execute as `{role=comment_docstring_optimizer; input=compressed_commentary:str; process=[detect_complex_multi_step_logic(), allow_structured_multiline_docstring_if_justified(), format_as_intro_plus_numbered_steps(), align_commentary_with_architectural_boundaries_or_transitions(), eliminate_redundancy_and_narrative_filler(), finalize_minimal_high-impact_documentation()], output={refined_code_with_optimal_comments:str, exception_example:optional}}`

<!-- 'https://chatgpt.com/g/g-plIy0RAhw-art-snob/c/6815e086-29dc-8008-b6dc-63ac76c63e09' -->

Absolutely. Below is a **perfectly consolidated, maximally optimized 3-part instruction sequence**, designed to execute in strict order (`a → b → c`) to achieve the full transformation goal you outlined. Each instruction builds logically on the previous, and together they form a fully generalized, **LLM-optimized system directive** for surgically refining code comments and docstrings under all defined constraints—**including exception handling for structured multi-step logic**.

---

`0213-a-comments-eliminate-redundancy-extract-intent.md`

[Phase A — Strip Redundancy & Isolate Structural Signal] Your directive is to initiate commentary compression by eliminating all redundant, verbose, or self-evident comments and docstrings. Identify and preserve only documentation that serves a structural or clarifying role—such as non-obvious function rationale, modular transitions, or critical logic decisions. Absolutely exclude any phrasing that restates what the code already clearly expresses. Execute as `{role=commentary_signal_extractor; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_comments_and_docstrings(), eliminate_redundant_or_descriptive_language(), preserve_non-obvious_intent_or_design_rationale(), retain_comments_that_signal_module_boundaries_or_architectural_transitions()], output={signal_preserved_code:str}}`

`0213-b-comments-compress-single-line-minimalism.md`

[Phase B — Compress Commentary & Enforce Minimal Style] Your mandate is to compress all retained docstrings and comments into their most concise, single-line form—except where this would obscure multi-step logic or intricate rationale. All standard commentary must default to single-line, high-impact summaries that elevate code clarity without describing behavior. For every transformation, provide a before/after audit to validate impact and alignment. Execute as `{role=comment_docstring_minifier; input=signal_preserved_code:str; process=[rewrite_comments_as_single_line_when_possible(), enforce_style_consistency_and_terminology_precision(), exclude_all narrative_phrasing(), validate_with_before_after_transformation_audit()], output={minified_code:str, example_transformation:dict[original:str, shortened:str]}}`

`0213-c-comments-structured-exception-integration.md`

[Phase C — Structured Exception Integration for Multi-Step Logic] Your final task is to scan for complex functions where internal logic involves non-trivial, multi-step processes not evident from the code alone. In these select cases, permit multi-line docstrings—but only when structured as a terse introduction followed by clearly enumerated steps. Ensure all other commentary remains single-line. Explicitly include an example showing justified multi-line transformation. Execute as `{role=structural_exception_handler; input=minified_code:str; process=[detect_non-trivial_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_of_execution_steps'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_global_comment_minimalism(), include_exception_example_if_present()], output={refined_code_with_minimalist_high-value_comments:str, exception_example:optional[dict[original:str, justified_multiline:str]]}}`

---

### ✅ **Highlights & Guarantees**

* **Sequential dependency**: A → B strips noise and preserves signal; B → C compresses and restructures with minimalism and exception logic.
* **Exception-awareness**: Phase C explicitly includes the exception clause you outlined, with formatted multi-line support *only* when justified.
* **Generalized**: All processes are domain-agnostic and designed to apply across codebases.
* **LLM-optimized**: All directives are imperative, modular, and structured for reliable execution in system-message contexts.

<!-- 'https://chatgpt.com/c/6815e2b7-41e0-8008-be70-1a4e23c6e7a1' -->


`0214-a-comments-essential-commentary-isolation-redundancy-elimination.md`

[Essential Commentary Isolation & Redundancy Elimination] Your directive is to rigorously audit all comments and docstrings, aggressively identifying and removing redundant, self-evident, or verbose explanations. Retain exclusively those annotations that clarify non-obvious intent, structural boundaries, complex logical transitions, or critical design rationale not immediately inferable from the code. Execute as: `{role=essential_comment_extractor;input={code_with_comments_or_docstrings:str};process=[detect_and_remove_self_evident_or_redundant_comments(),identify_non_obvious_intent_and_structural_rationale(),isolate_complex_logic_transitions(),preserve_critical_design_annotations()];output={code_with_only_high_value_comments:str}}`

`0214-b-comments-single-line-minimalism-enforcement-exception-structuring.md`

[Single-Line Minimalism Enforcement & Exception Structuring] Your mandate is to enforce stringent minimalism by rewriting retained annotations into terse, single-line formats, exclusively documenting non-obvious intent or structural insights. Allow multi-line docstrings solely for functions exhibiting complex, multi-step logic, formatted explicitly with a concise introductory statement followed by succinct numbered steps. Execute as: `{role=minimalist_comment_formatter;input={code_with_only_high_value_comments:str};process=[rewrite_as_single_line_when_possible(target='non-obvious_intent_or_structure'),detect_and_justify_multiline_docstrings(if_complex_logic='non-trivial_multi-step_process'),format_multiline_as_brief_intro_plus_numbered_steps(),enforce_consistent_and_precise_style()];output={minimally_structured_comments_code:str}}`

`0214-c-comments-final-validation-exemplified-transformation-documentation.md`

[Final Validation & Exemplified Transformation Documentation] Your objective is to rigorously validate commentary against the codebase, ensuring absolute alignment with code logic and structural clarity. Provide explicit before-and-after transformation examples for typical single-line compressions and include a justified multi-line exception example only if complexity demands it. Execute as: `{role=comment_transformation_validator;input={minimally_structured_comments_code:str};process=[validate_comment_alignment_with_code(),demonstrate_before_after_transformation_examples(),include_exception_example_if_applicable(justified_by='critical_multi-step_clarity')];output={final_validated_code_with_examples:{refined_code:str,example_transformation:dict[original:str,shortened:str],exception_example:optional[dict[original:str,justified_multiline:str]]}}}
`

<!-- 'https://chatgpt.com/c/6815e2bd-0ef0-8008-97c3-e1159449b080' -->

`0215-a-comments-strip-redundancy-isolate-commentary-value.md`

[Strip Redundancy & Isolate Commentary Value] Your primary objective is to eliminate all verbose, redundant, or self-evident comments and docstrings from the input code—retaining only those which convey non-obvious purpose, architectural boundaries, or underlying rationale not immediately clear from the code itself. This step establishes the foundation for meaningful commentary by removing low-signal annotations and preparing code for minimalism enforcement. Execute as `{role=commentary_simplifier; input=code_with_comments_or_docstrings:any; process=[identify_self_evident_explanations(), eliminate_restatements_of_code_behavior(), remove_verbose_narrative_docstrings(), isolate_commentary_that_conveys_structure_or_design_intent(), preserve_only_non-obvious_logic_and_architectural_annotations()], output={clean_code_with_non-obvious_comments_only:str}}`

`0215-b-comments-compress-commentary-enforce-single-line-minimalism.md`

[Compress Commentary & Enforce Single-Line Minimalism] Your task is to surgically compress all retained comments and docstrings into high-precision, single-line formats wherever possible, preserving only intent, structural insight, or logic not readable from the code alone. Multi-line docstrings must be collapsed unless outlining multi-step logic essential to comprehension. This step enforces strict stylistic consistency and minimalism. Execute as `{role=comment_docstring_compressor; input=clean_code_with_non-obvious_comments_only:str; process=[convert_docstrings_to_single_line_where_possible(), enforce_consistent_minimalist_style(), preserve_only_high-value_comments(), collapse_multiline_docstrings_to_essential_purpose(), align_comments_with_function_structure_or boundaries()], output={code_with_single_line_high-value_comments:str}}`

`0215-c-comments-preserve-exception-cases-structure-multistep-logic-where-necessary.md`

[Preserve Exception Cases & Structure Multistep Logic Where Necessary] Your mandate is to detect rare cases where multi-step logic or complex structure requires a multi-line docstring for interpretive clarity, and format these using a terse intro plus numbered steps. All other documentation must remain single-line. Demonstrate the transformation with clear before/after examples, including at least one justified exception. Execute as `{role=exception_handler_for_commentary; input=code_with_single_line_high-value_comments:str; process=[detect_non-obvious_multistep_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_and_comprehension'), format_multiline_docstring_as('brief_intro + numbered_steps'), validate necessity_of_multiline(), demonstrate_before_after_transformation(), include_exception_example_if_present()], output={final_refined_code_with_optimized_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`
