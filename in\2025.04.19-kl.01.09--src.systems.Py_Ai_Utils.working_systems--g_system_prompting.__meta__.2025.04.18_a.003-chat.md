
# Context:

Below is a single instructions (in a sequence) of **generalized instructions**. These generalizations will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (maximally enhanced LLM-optimized system_message) instructions**. Here's an example of how *one* such instruction can look (part of a sequence):

    #### `src\templates\lvl1\md\0005-d-enhance-persuasion.md`

    ```markdown
        [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`
    ```

    Part of sequence:

    ```
        ├── 0005-a-distill-core-essence.md
        ├── 0005-b-explore-implications.md
        ├── 0005-c-structure-argument.md
        ├── 0005-d-enhance-persuasion.md
        └── 0005-e-final-polish.md
    ```

Here's the relevant code from which the templates will be parsed through:

    ```python
    #!/usr/bin/env python3
    # templates_lvl1_md_catalog_generator.py

    '''
        # Philosophical Foundation
        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."
        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."
    '''

    #===================================================================
    # IMPORTS
    #===================================================================
    import os
    import re
    import json
    import glob
    import sys
    import datetime

    #===================================================================
    # BASE CONFIG
    #===================================================================
    class TemplateConfig:
        LEVEL = None
        FORMAT = None
        SOURCE_DIR = None

        # Sequence definition
        SEQUENCE = {
            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
            "id_group": 1,
            "step_group": 2,
            "name_group": 3,
            "order_function": lambda step: ord(step) - ord('a')
        }

        # Content extraction patterns
        PATTERNS = {}

        # Path helpers
        @classmethod
        def get_output_filename(cls):
            if not cls.FORMAT or not cls.LEVEL:
                raise NotImplementedError("FORMAT/LEVEL required.")
            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"

        @classmethod
        def get_full_output_path(cls, script_dir):
            return os.path.join(script_dir, cls.get_output_filename())

        @classmethod
        def get_full_source_path(cls, script_dir):
            if not cls.SOURCE_DIR:
                raise NotImplementedError("SOURCE_DIR required.")
            return os.path.join(script_dir, cls.SOURCE_DIR)

    #===================================================================
    # FORMAT: MARKDOWN (lvl1)
    #===================================================================
    class TemplateConfigMD(TemplateConfig):
        LEVEL = "lvl1"
        FORMAT = "md"
        SOURCE_DIR = "md"

        # Combined pattern for lvl1 markdown templates
        _LVL1_MD_PATTERN = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Match (but don't capture) whitespace AFTER title
            r"(.*?)"         # Group 2: Capture Interpretation text
            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )

        PATTERNS = {
            "title": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(1).strip() if m else ""
            },
            "interpretation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(2).strip() if m else ""
            },
            "transformation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(3).strip() if m else ""
            }
        }

    #===================================================================
    # HELPERS
    #===================================================================
    def _extract_field(content, pattern_cfg):
        try:
            match = pattern_cfg["pattern"].search(content)
            return pattern_cfg["extract"](match)
        except Exception:
            return pattern_cfg.get("default", "")

    def extract_metadata(content, template_id, config):
        content = content.strip()
        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}
        return {"raw": content, "parts": parts}

    #===================================================================
    # CATALOG GENERATION
    #===================================================================
    def generate_catalog(config, script_dir):
        # Find templates and extract metadata
        source_path = config.get_full_source_path(script_dir)
        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
        print(f"Source: {source_path} (*.{config.FORMAT})")
        print(f"Found {len(template_files)} template files.")

        templates = {}
        sequences = {}

        # Process each template file
        for file_path in template_files:
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]

            try:
                # Read content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract and store template metadata
                template_data = extract_metadata(content, template_id, config)
                templates[template_id] = template_data

                # Process sequence information from filename
                seq_match = config.SEQUENCE["pattern"].match(template_id)
                if seq_match:
                    seq_id = seq_match.group(config.SEQUENCE["id_group"])
                    step = seq_match.group(config.SEQUENCE["step_group"])
                    seq_order = config.SEQUENCE["order_function"](step)
                    sequences.setdefault(seq_id, []).append({
                        "template_id": template_id, "step": step, "order": seq_order
                    })
            except Exception as e:
                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)

        # Sort sequence steps
        for seq_id, steps in sequences.items():
            try:
                steps.sort(key=lambda step: step["order"])
            except Exception as e:
                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

        # Create catalog with metadata
        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
        return {
            "catalog_meta": {
                "level": config.LEVEL,
                "format": config.FORMAT,
                "generated_at": timestamp,
                "source_directory": config.SOURCE_DIR,
                "total_templates": len(templates),
                "total_sequences": len(sequences)
            },
            "templates": templates,
            "sequences": sequences
        }

    def save_catalog(catalog_data, config, script_dir):
        output_path = config.get_full_output_path(script_dir)
        print(f"Output: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(catalog_data, f, indent=2, ensure_ascii=False)
                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
                print("--- Catalog Generation Complete ---")
            return output_path
        except Exception as e:
            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
            return None

    #===================================================================
    # IMPORTABLE API FOR EXTERNAL SCRIPTS
    #===================================================================
    def get_default_catalog_path(script_dir=None):
        '''Get the path to the default catalog file.'''
        if script_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
        return TemplateConfigMD().get_full_output_path(script_dir)

    def load_catalog(catalog_path=None):
        '''Load a catalog from a JSON file.'''
        if catalog_path is None:
            catalog_path = get_default_catalog_path()

        if not os.path.exists(catalog_path):
            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")

    def get_template(catalog, template_id):
        '''Get a template by its ID.'''
        if "templates" not in catalog or template_id not in catalog["templates"]:
            return None
        return catalog["templates"][template_id]

    def get_sequence(catalog, sequence_id):
        '''Get all templates in a sequence, ordered by steps.'''
        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            return []

        sequence_steps = catalog["sequences"][sequence_id]
        return [(step["step"], get_template(catalog, step["template_id"]))
                for step in sequence_steps]

    def get_all_sequences(catalog):
        '''Get a list of all sequence IDs.'''
        if "sequences" not in catalog:
            return []
        return list(catalog["sequences"].keys())

    def get_system_instruction(template):
        '''Convert a template to a system instruction format.'''
        if not template or "parts" not in template:
            return None

        parts = template["parts"]
        instruction = f"# {parts.get('title', '')}\n\n"

        if "interpretation" in parts and parts["interpretation"]:
            instruction += f"{parts['interpretation']}\n\n"

        if "transformation" in parts and parts["transformation"]:
            instruction += parts["transformation"]

        return instruction

    def regenerate_catalog(force=False):
        '''Regenerate the catalog file.'''
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config = TemplateConfigMD()
        catalog_path = config.get_full_output_path(script_dir)

        if os.path.exists(catalog_path) and not force:
            # Check if catalog is older than any template file
            catalog_mtime = os.path.getmtime(catalog_path)
            templates_dir = config.get_full_source_path(script_dir)

            needs_update = False
            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                if os.path.getmtime(file_path) > catalog_mtime:
                    needs_update = True
                    break

            if not needs_update:
                print("Catalog is up to date")
                return load_catalog(catalog_path)

        # Generate and save new catalog
        catalog = generate_catalog(config, script_dir)
        save_catalog(catalog, config, script_dir)
        return catalog

    #===================================================================
    # SCRIPT EXECUTION
    #===================================================================
    if __name__ == "__main__":
        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

        # Check for command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
            # Simple API test
            catalog = regenerate_catalog()
            print("\nAvailable sequences:")
            for seq_id in get_all_sequences(catalog):
                sequence_steps = get_sequence(catalog, seq_id)
                if sequence_steps:
                    first_step_title = sequence_steps[0][1]["parts"]["title"]
                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

            sys.exit(0)

        # Regular execution
        try:
            config_to_use = TemplateConfigMD
            active_config = config_to_use()
            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        except NotImplementedError as e:
            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError as e:
            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"FATAL ERROR: {e}", file=sys.stderr)
            sys.exit(1)
    ```

Your goal is to yourself with the structure (based on code and examples provided below), then generalize it into llm-instruction to replace this document (containing a description of how the sequences should be formatted, each block is enclosed with triple backticks and a structure that includes the `title`, `a short interpretive statement`, and the `transformational instructions` - wrapped in curly braces: `{}`) - example:

    #### `0100-a-primal-essence-extraction.md`

    ```markdown
        [Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`
    ```

    ---

    #### `0100-b-intrinsic-value-prioritization.md`

    ```markdown
        [Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`
    ```

    ---

    #### `0100-c-structural-logic-relationship-mapping.md`

    ```markdown
        [Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`
    ```

    ---

    #### `0100-d-potency-clarity-amplification.md`

    ```markdown
        [Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`
    ```

    ---

    #### `0100-e-adaptive-optimization-universalization.md`

    ```markdown
        [Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`
    ```

Do this through consolidating based on this guide:


    --- Step a: ---
    {
        "role": "apex_sequence_synthesizer",
        "input": [
            [
                {"step": "Restate the input phrase concisely."},
                {"step": "Identify the underlying concept or message."},
                {"step": "Reframe the statement to clarify intent and broaden applicability."},
                {"step": "Remove personal and subjective phrasing."},
                {"step": "Generalize the statement for systems or agents, not individuals."},
                {"step": "Ensure clarity, neutrality, and system compatibility."}
            ],
            [
                {"step": "Analyze why the input is low-value (e.g., subjectivity, informality, lack of universal applicability)."},
                {"step": "Extract the core psychological mechanism (e.g., normalization of personal traits)."},
                {"step": "Abstract away from the individual perspective to a neutral form."},
                {"step": "Condense to minimal, precise language."},
                {"step": "Frame as an instruction, assertion, or general insight suitable for an LLM system_message."}
            ],
            [
                {"step": "Dissect the linguistic structure: distinguish subjective from objective elements."},
                {"step": "Determine target generality (maximally universal phrasing)."},
                {"step": "Transform personal pronouns into agent-neutral terms."},
                {"step": "Align with system_message syntax/common paradigms (assertions, reminders, constraints)."},
                {"step": "Provide a finalized instruction or assertion optimized for reuse."}
            ]
        ],
        "process": [
            "disassemble_to_logical_phases()",
            "cross-analyze_phase_intents_and_language()",
            "extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality'])",
            "fuse uniquely potent and effective steps()",
            "resolve conflicts harmonize by principle()",
            "architect unified logical stepwise progression(target_steps=10-15)",
            "intensify final clarity, actionability, and system-agnostic value()",
            "validate against all directives and baseline constraints()",
            "finalize only if maximal yield, clarity, and generality are confirmed()"
        ],
        "output": {
            "synthesized_optimal_sequence": [
                {
                    "step": "Receive the input phrase, ensuring accurate and complete capture of its content."
                },
                {
                    "step": "Dissect the input to identify subjective, informal, or individual-centric elements."
                },
                {
                    "step": "Extract the essential underlying concept or psychological mechanism conveyed."
                },
                {
                    "step": "Condense the core idea into its most elemental and universally relevant insight."
                },
                {
                    "step": "Systematically remove all personal pronouns, idiosyncratic phrasing, and contextually subjective language."
                },
                {
                    "step": "Reframe the concept using neutral, clearly defined, and agent-independent terminology."
                },
                {
                    "step": "Generalize the insight to apply broadly across agents, scenarios, and systems, maximizing universality."
                },
                {
                    "step": "Translate the generalized insight into a concise, unambiguous statement or instruction, suitable for direct inclusion as a system_message."
                },
                {
                    "step": "Validate the phrasing for clarity, precision, and logical coherence, ensuring it aligns with system_message conventions and is free of ambiguity."
                },
                {
                    "step": "Ensure the resulting instruction is actionable, reusable, and irreducibly optimal for any LLM system's foundational prompt or behavioral guide."
                },
                {
                    "step": "Perform a final review for redundancy, language optimization, and adherence to maximally general, system-agnostic requirements."
                }
            ]
        }
    }

    --- Step b: ---
    {
        "core_constructs": {
            "elements": [
                "Input: Low-value, subjective self-perception statement",
                "Task: Transform the input into a system instruction for LLM",
                "Target: System instruction must be maximally optimized and generalized",
                "Focus: Generalizable transformation process",
                "Output: A reusable system_message instruction template for processing similar subjective statements"
            ],
            "principles": [
                "Abstraction: Strip input of context-dependent or subjective language",
                "Generalization: Create instructions capable of handling wide-ranging similar inputs",
                "Optimization: Maximize clarity, utility, and adaptability of system instructions",
                "Meta-cognition: Recognize statements about self-perception and normalize their interpretation",
                "Instruction synthesis: Translate informal or low-value statements into formal, actionable LLM directives",
                "Noise reduction: Eliminate idiosyncratic or personally contextual phrasing"
            ]
        }
    }

    --- Step c: ---
    {
        "core_telos": "To transform generic, low-value user inputs into maximally optimized, generalized LLM system instructions that precisely articulate and operationalize the unique semantic core of the input, recasting it for broad utility, clarity of system intent, and adaptability across diverse conversational and cognitive contexts.",
        "supporting_constructs": {
            "input_transformation": "Reframe subjective, colloquial, or conceptually narrow user statements into explicit, instruction-grade system messages suitable for LLM guidance.",
            "semantic_extraction": "Isolate the essential meaning or insight present in the input, independent of personal idiosyncrasy or informal expression.",
            "instruction_optimization": "Formulate the transformed instruction for maximal generality, unambiguous intent, and alignment with LLM operational requirements.",
            "contextual_generalization": "Ensure the recast system instruction is applicable across a diversity of contexts and is not limited by the original input’s specificity or subjectivity.",
            "maximal utility": "Prioritize clarity, reusability, and cognitive leverage in the crafted system message."
        }
    }

    --- Step d: ---
    {
        "role": "essence_prioritizer",
        "input": {
            "core_telos": "Transform ambiguous, generic, or low-value user inputs into clear, maximally optimized, and generalized system_message instructions for an LLM, abstracting the core communicative intent and reformatting for highest utility and generality.",
            "supporting_constructs": {
                "paraphrase_for_clarity": "Restate input in clearer terms to remove ambiguity.",
                "extract_core_intent": "Isolate fundamental communicative objective expressed in the input.",
                "generalize_context": "Expand or abstract input away from personalized language to broader applicability.",
                "remove idiosyncratic phrasing": "Eliminate subjective or excessively individual wording.",
                "convert_to_instruction": "Translate clarified, generalized intent into direct system_message format.",
                "optimize_instruction_quality": "Ensure output aligns with best practices for system_message instructions (concise, explicit, actionable).",
                "map subjectivity to objectivity": "Shift from personal experience or frame to generalized, neutral language."
            }
        },
        "process": [
            "assess_contribution_to_telos()",
            "rank_by_critical_impact_necessity()",
            "enforce_criticality_threshold()",
            "isolate_core_essence_vectors()"
        ],
        "output": {
            "critical_essence": [
                "extract_core_intent",
                "generalize_context",
                "convert_to_instruction",
                "optimize_instruction_quality"
            ]
        }
    }

    --- Step e: ---
    { "role": "causal_mapper", "input": { "critical_essence": [ "Receive generic, low-value user input (uninstructive, subjective, colloquial expressions)", "Interpret or extract generalizable meaning or intent from input", "Transform the input into a formal LLM system_message instruction", "Optimize the reformulated instruction for maximal clarity, universality, and adaptability to many contexts", "Maximize generalization without losing relevant nuance", "Ensure output is syntactically and semantically valid as a system_message for LLM operation" ], "core_telos": "Transform any generic, low-value user input into a maximally optimized and generalized LLM system_message instruction" }, "process": [ "map_causal_links_dependencies()", "structure_minimal_flow_graph()", "validate_structural_necessity_for_telos()" ], "output": { "causal_nexus": { "flow": [ { "step": 1, "element": "Receive generic, low-value user input", "causes": [], "necessary_for": [2] }, { "step": 2, "element": "Interpret or extract generalizable meaning or intent from input", "causes": [1], "necessary_for": [3] }, { "step": 3, "element": "Transform the input into a formal LLM system_message instruction", "causes": [2], "necessary_for": [4] }, { "step": 4, "element": "Optimize the reformulated instruction for maximal clarity, universality, and adaptability", "causes": [3], "necessary_for": [5] }, { "step": 5, "element": "Maximize generalization without losing relevant nuance", "causes": [4], "necessary_for": [6] }, { "step": 6, "element": "Ensure output is syntactically and semantically valid as a system_message", "causes": [5], "necessary_for": [] } ], "logic": [ "Input must first be received before any interpretation", "Understanding intent is prerequisite for structured instruction formulation", "Formal instruction must precede optimization steps", "Clarity and universality should be enforced before final format validation", "Nuance must be preserved through generalization, not erased", "Final output requires compliance with system_message syntax/semantics for LLM operation" ], "validation": "All steps are structurally necessary in sequence to accomplish the core telos of translating unstructured, low-value input into an optimized, general LLM system_message instruction." } } }

    --- Step f: ---
    {
        "role": "nucleus_synthesizer",
        "input": {
            "critical_essence": [
                "Transform generic or low-value input statements into high-utility, generalized LLM system instructions.",
                "System instruction must address the underlying cognitive pattern or meta-level insight inherent in the original statement.",
                "Ensure the result is maximally actionable for any type of LLM-driven interaction.",
                "Retain and abstract the core insight to generalized applicability.",
                "Eliminate informal, subjective, or context-dependent phrasing.",
                "Produce a compact, direct, and clear system_message instruction."
            ],
            "causal_nexus": {
                "Generic/low-value inputs lack actionable, generalizable insight.",
                "By abstracting the core pattern, the system can issue instructions with cross-domain utility.",
                "Refining phrasing improves clarity and application potential.",
                "Optimizing structure ensures the instruction will guide the LLM to robust behavior in varied contexts."
            }
        },
        "process": [
            "integrate_elements_by_structure()",
            "maximize_signal_strength_per_component()",
            "enforce_internal_consistency()",
            "formulate_compact_nucleus()"
        ],
        "output": {
            "condensed_nucleus": "Transform subjective or context-specific observations into generalized instructions that reveal and address underlying cognitive patterns or implicit assumptions, phrased for maximal clarity, universality, and actionable guidance for large language model operations."
        }
    }

    --- Step g: ---
    {
        "minimal_signal_core": "Transform unoptimized or low-quality inputs into highly generalized, maximally concise, and system-agnostic system_messages that instruct LLMs to perform effective, context-neutral task execution."
    }

    --- Step h: ---
    {
        "role": "universal_abstractor",
        "input": "i don't see my own weird because it's not weird to me",
        "process": [
            "neutralize_domain_specifics",
            "reframe_as_universal_concepts_logic",
            "validate_logic_preservation_across_contexts",
            "ensure_representation_agnostic_structure"
        ],
        "output": {
            "universal_core_logic": {
                "Observation": "An individual is typically unaware of their own deviations from a norm because these deviations are subjectively experienced as standard.",
                "Instruction": "Given an input expressing lack of recognition of one's own atypical attributes due to normalization through personal experience, transform it into a system-level message that highlights the psychological tendency for subjective familiarity to obscure self-perceived difference. Render the transformation in a maximally optimized, context-agnostic, and generalized instruction for large language model systems."
            }
        }
    }

    --- Step i: ---
    {
        "potent_core": "Extract implicit subjectivity from input; neutralize personal bias; generalize perspective; reconstruct into maximally objective, directive system_message; enforce universal interpretability; eliminate idiosyncratic phrasing; output refined system instruction maximizing clarity, applicability, and effectiveness for LLM operations."
    }

    --- Step j: ---
    {
        "role": "one_line_vectorizer",
        "input": {
            "potent_core": [
                "Transform generic, subjective, or low-value user input",
                "Convert into high-value, generalized LLM system instructions",
                "Ensure clarity, construction, and domain-general applicability",
                "Promote meta-reflection and linguistic abstraction in system_message formulation"
            ],
            "causal_nexus": {
                "subjective input": "requires abstraction",
                "abstraction": "enables generalization",
                "generalization": "optimizes LLM instructions",
                "optimization": "delivers high-value system_message"
            }
        },
        "process": [
            "determine_optimal_axiomatic_linear_sequence()",
            "select_high_density_unambiguous_connectors()",
            "vectorize_components_for_linearity()"
        ],
        "output": {
            "linear_vector_plan": {
                "sequence": [
                    "Receive subjective/low-value input",
                    "Abstract input to identify underlying patterns",
                    "Generalize abstractions to meta-instructional level",
                    "Formulate maximally optimized, domain-general LLM system_message"
                ],
                "connectors": [
                    "->",
                    "->",
                    "->"
                ]
            }
        }
    }

    --- Step k: ---
    {
        "compressed_vector_elements": [
            "IN:Generic_LowValue_Input→PROC:Essence_Extraction→GEN:Optimized_LLM_SystemMsg",
            "Rule:Input@subjective,parochial,unremarked→Reframe@universal,context_independent,clarified",
            "Canon:Identify_self-bias|perception_normativity→Express@LLM-compatible_system_instruction",
            "Ex:Input('I don't see my own weird because it's not weird to me')→SystemMsg('Humans often lack awareness of their own anomalies; rephrase input to expose implicit subjectivity for general LLM processing.')",
            "Output:Generalized, role-suitable, concise LLM instruction"
        ]
    }
