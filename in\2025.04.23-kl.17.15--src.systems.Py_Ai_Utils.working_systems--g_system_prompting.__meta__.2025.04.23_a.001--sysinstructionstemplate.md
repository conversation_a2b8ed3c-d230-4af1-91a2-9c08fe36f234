Familiarize yourself with generalized (maximally LLM-Optimized and enhanced) `system_message` instructions:

#### `1-templateStructureGuide.md`

    ```markdown
       # Template Structure Guide

       ## Overview

       This guide documents the standardized structure for creating templates in the Template-Based Instruction System. Following this structure ensures consistency and enables the automatic processing of templates by the catalog generator.

       ## Template File Structure

       Each template is stored as a markdown (.md) file and follows this standardized three-part structure:

       ```
       [Title] Interpretation text `{transformation}`
       ```

       ### Components

       1. **Title**: Enclosed in square brackets `[]`, defining the template's purpose.
          - Should be concise, descriptive, and follow title case formatting
          - Examples: `[Instruction Converter]`, `[Essence Distillation]`

       2. **Interpretation**: Plain text immediately following the title that describes what the template does.
          - Should clearly explain the template's function in natural language
          - Can include formatting like **bold**, *italic*, or other markdown elements
          - Provides context for human readers to understand the template's purpose

       3. **Transformation**: JSON-like structure enclosed in backticks with curly braces `\`{...}\``, defining the execution logic.
          - Contains the structured representation of the transformation process
          - Uses a consistent semi-colon separated key-value format

       ## Transformation Structure

       The transformation component follows this standardized format:

       ```
       `{
         role=<role_name>;
         input=[<input_params>];
         process=[<process_steps>];
         constraints=[<constraints>];
         requirements=[<requirements>];
         output={<output_format>}
       }`
       ```

       ### Transformation Components

       1. **role**: Defines the functional role of the template
          - Example: `role=essence_distiller`

       2. **input**: Specifies the expected input format and parameters
          - Uses array syntax with descriptive parameter names
          - Example: `input=[original:any]`

       3. **process**: Lists the processing steps to be executed in order
          - Uses array syntax with function-like step definitions
          - Can include parameters within step definitions
          - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`

       4. **constraints** (optional): Specifies limitations or boundaries for the transformation
          - Uses array syntax with directive-like constraints
          - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`

       5. **requirements** (optional): Defines mandatory aspects of the transformation
          - Uses array syntax with imperative requirements
          - Example: `requirements=[remove_self_references(), use_command_voice()]`

       6. **output**: Specifies the expected output format
          - Uses object syntax with typed output parameters
          - Example: `output={distilled_essence:any}`

       ## Template Naming Convention

       Templates follow a consistent naming convention that indicates their sequence and position:

       ```
       <sequence_id>-<step>-<descriptive_name>.md
       ```

       ### Naming Components

       1. **sequence_id**: A numeric identifier (e.g., 0001, 0002) that groups related templates
          - Four-digit format with leading zeros
          - Unique per sequence

       2. **step** (optional): A lowercase letter (a, b, c, d, e) that indicates the position within a sequence
          - Only used for templates that are part of multi-step sequences
          - Follows alphabetical order to determine execution sequence

       3. **descriptive-name**: A hyphenated name that describes the template's purpose
          - All lowercase
          - Words separated by hyphens
          - Should be concise but descriptive

       ### Examples

       - Single template: `0001-instructionconverter.md`
       - Sequence templates:
         - `0002-a-essence-distillation.md`
         - `0002-b-exposing-coherence.md`
         - `0002-c-precision-enhancement.md`
         - `0002-d-structured-transformation.md`
         - `0002-e-achieving-self-explanation.md`

       ## Template Examples

       ### Example 1: Simple Template

       ```markdown
       [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
       ```

       ### Example 2: Sequence Step Template

       ```markdown
       [Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
       ```

       ## Creating New Templates

       To create a new template:

       1. Determine if it should be a standalone template or part of a sequence
       2. Assign an appropriate sequence_id (and step letter if part of a sequence)
       3. Create a descriptive name using hyphenated lowercase words
       4. Define the title that clearly indicates the template's purpose
       5. Write the interpretation text that explains what the template does
       6. Structure the transformation logic using the standardized format
       7. Save the file with the proper naming convention
       8. Run the catalog generator to include the new template in the JSON catalog

       ## Guidelines for Effective Templates

       1. **Clarity**: Each component should clearly communicate its purpose and functionality
       2. **Specificity**: Be specific about input/output formats and processing steps
       3. **Modularity**: Design templates to perform discrete, focused transformations
       4. **Composability**: For sequence templates, ensure outputs from one step can serve as inputs to the next
       5. **Consistency**: Follow the standardized structure and naming conventions exactly
       6. **Self-Documentation**: The interpretation text should provide sufficient context for understanding
       7. **Functional Completeness**: Ensure the transformation logic includes all necessary components

       ## Integration with Catalog Generator

       The catalog generator extracts metadata from template files based on specific patterns. It is essential to follow the template structure exactly to ensure proper extraction.

       The primary pattern used for extraction is:

       ```
       \[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)
       ```

       This pattern extracts:
       1. The title from within square brackets
       2. The interpretation text following the title
       3. The transformation within backticks and curly braces

       Any deviation from this structure may result in improper extraction by the catalog generator.
    ```

---

#### `2-sampleTemplate.md`

    ```markdown
        # Sample Template

        ## Overview

        This sample template demonstrates the standardized template structure defined in the Template Structure Guide. It follows all the required formatting and structure conventions to ensure compatibility with the catalog generator.

        ## Template Content

        ```markdown
        [Summarization Refiner] Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information. `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}`
        ```

        ## Template Components Explained

        1. **Title**: `[Summarization Refiner]`
           - Enclosed in square brackets
           - Concise and descriptive
           - Uses title case

        2. **Interpretation**: `Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information.`
           - Clearly explains the template's function in natural language
           - Provides context for human readers

        3. **Transformation**: `` `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}` ``
           - Enclosed in backticks with curly braces
           - Contains all required components:
             - `role`: Defines the functional role (summary_refiner)
             - `input`: Lists expected input parameters with types
             - `process`: Defines the processing steps in sequence
             - `constraints`: Specifies limitations for the transformation
             - `requirements`: Lists mandatory aspects
             - `output`: Specifies the expected output format

        ## Filename Convention

        Following the naming convention, this template would be saved as:

        ```
        0040-summarization-refiner.md
        ```

        Or, if part of a sequence (e.g., as step b):

        ```
        0040-b-summarization-refiner.md
        ```

        ## Using the Template Generator

        To create templates like this using the provided template generator:

        1. Run the template generator script:
           ```
           python template_generator.py
           ```

        2. Choose to create a standalone template or a sequence of templates

        3. Follow the prompts to enter the template information:
           - Descriptive name
           - Title
           - Interpretation text
           - Role
           - Input parameters
           - Process steps
           - Constraints (optional)
           - Requirements (optional)
           - Output format

        4. Review the generated template and confirm to save

        The template generator ensures that the created template follows all the standardized structure requirements and saves it with the proper filename.
    ```

---


Here's the relevant code from which the templates will be parsed through:

    ```python
    #!/usr/bin/env python3
    # templates_lvl1_md_catalog_generator.py

    '''
        # Philosophical Foundation
        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."
        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."
    '''

    #===================================================================
    # IMPORTS
    #===================================================================
    import os
    import re
    import json
    import glob
    import sys
    import datetime

    #===================================================================
    # BASE CONFIG
    #===================================================================
    class TemplateConfig:
        LEVEL = None
        FORMAT = None
        SOURCE_DIR = None

        # Sequence definition
        SEQUENCE = {
            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
            "id_group": 1,
            "step_group": 2,
            "name_group": 3,
            "order_function": lambda step: ord(step) - ord('a')
        }

        # Content extraction patterns
        PATTERNS = {}

        # Path helpers
        @classmethod
        def get_output_filename(cls):
            if not cls.FORMAT or not cls.LEVEL:
                raise NotImplementedError("FORMAT/LEVEL required.")
            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"

        @classmethod
        def get_full_output_path(cls, script_dir):
            return os.path.join(script_dir, cls.get_output_filename())

        @classmethod
        def get_full_source_path(cls, script_dir):
            if not cls.SOURCE_DIR:
                raise NotImplementedError("SOURCE_DIR required.")
            return os.path.join(script_dir, cls.SOURCE_DIR)

    #===================================================================
    # FORMAT: MARKDOWN (lvl1)
    #===================================================================
    class TemplateConfigMD(TemplateConfig):
        LEVEL = "lvl1"
        FORMAT = "md"
        SOURCE_DIR = "md"

        # Combined pattern for lvl1 markdown templates
        _LVL1_MD_PATTERN = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Match (but don't capture) whitespace AFTER title
            r"(.*?)"         # Group 2: Capture Interpretation text
            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )

        PATTERNS = {
            "title": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(1).strip() if m else ""
            },
            "interpretation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(2).strip() if m else ""
            },
            "transformation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(3).strip() if m else ""
            }
        }

    #===================================================================
    # HELPERS
    #===================================================================
    def _extract_field(content, pattern_cfg):
        try:
            match = pattern_cfg["pattern"].search(content)
            return pattern_cfg["extract"](match)
        except Exception:
            return pattern_cfg.get("default", "")

    def extract_metadata(content, template_id, config):
        content = content.strip()
        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}
        return {"raw": content, "parts": parts}

    #===================================================================
    # CATALOG GENERATION
    #===================================================================
    def generate_catalog(config, script_dir):
        # Find templates and extract metadata
        source_path = config.get_full_source_path(script_dir)
        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
        print(f"Source: {source_path} (*.{config.FORMAT})")
        print(f"Found {len(template_files)} template files.")

        templates = {}
        sequences = {}

        # Process each template file
        for file_path in template_files:
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]

            try:
                # Read content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract and store template metadata
                template_data = extract_metadata(content, template_id, config)
                templates[template_id] = template_data

                # Process sequence information from filename
                seq_match = config.SEQUENCE["pattern"].match(template_id)
                if seq_match:
                    seq_id = seq_match.group(config.SEQUENCE["id_group"])
                    step = seq_match.group(config.SEQUENCE["step_group"])
                    seq_order = config.SEQUENCE["order_function"](step)
                    sequences.setdefault(seq_id, []).append({
                        "template_id": template_id, "step": step, "order": seq_order
                    })
            except Exception as e:
                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)

        # Sort sequence steps
        for seq_id, steps in sequences.items():
            try:
                steps.sort(key=lambda step: step["order"])
            except Exception as e:
                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

        # Create catalog with metadata
        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
        return {
            "catalog_meta": {
                "level": config.LEVEL,
                "format": config.FORMAT,
                "generated_at": timestamp,
                "source_directory": config.SOURCE_DIR,
                "total_templates": len(templates),
                "total_sequences": len(sequences)
            },
            "templates": templates,
            "sequences": sequences
        }

    def save_catalog(catalog_data, config, script_dir):
        output_path = config.get_full_output_path(script_dir)
        print(f"Output: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(catalog_data, f, indent=2, ensure_ascii=False)
                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
                print("--- Catalog Generation Complete ---")
            return output_path
        except Exception as e:
            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
            return None

    #===================================================================
    # IMPORTABLE API FOR EXTERNAL SCRIPTS
    #===================================================================
    def get_default_catalog_path(script_dir=None):
        '''Get the path to the default catalog file.'''
        if script_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
        return TemplateConfigMD().get_full_output_path(script_dir)

    def load_catalog(catalog_path=None):
        '''Load a catalog from a JSON file.'''
        if catalog_path is None:
            catalog_path = get_default_catalog_path()

        if not os.path.exists(catalog_path):
            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")

    def get_template(catalog, template_id):
        '''Get a template by its ID.'''
        if "templates" not in catalog or template_id not in catalog["templates"]:
            return None
        return catalog["templates"][template_id]

    def get_sequence(catalog, sequence_id):
        '''Get all templates in a sequence, ordered by steps.'''
        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            return []

        sequence_steps = catalog["sequences"][sequence_id]
        return [(step["step"], get_template(catalog, step["template_id"]))
                for step in sequence_steps]

    def get_all_sequences(catalog):
        '''Get a list of all sequence IDs.'''
        if "sequences" not in catalog:
            return []
        return list(catalog["sequences"].keys())

    def get_system_instruction(template):
        '''Convert a template to a system instruction format.'''
        if not template or "parts" not in template:
            return None

        parts = template["parts"]
        instruction = f"# {parts.get('title', '')}\n\n"

        if "interpretation" in parts and parts["interpretation"]:
            instruction += f"{parts['interpretation']}\n\n"

        if "transformation" in parts and parts["transformation"]:
            instruction += parts["transformation"]

        return instruction

    def regenerate_catalog(force=False):
        '''Regenerate the catalog file.'''
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config = TemplateConfigMD()
        catalog_path = config.get_full_output_path(script_dir)

        if os.path.exists(catalog_path) and not force:
            # Check if catalog is older than any template file
            catalog_mtime = os.path.getmtime(catalog_path)
            templates_dir = config.get_full_source_path(script_dir)

            needs_update = False
            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                if os.path.getmtime(file_path) > catalog_mtime:
                    needs_update = True
                    break

            if not needs_update:
                print("Catalog is up to date")
                return load_catalog(catalog_path)

        # Generate and save new catalog
        catalog = generate_catalog(config, script_dir)
        save_catalog(catalog, config, script_dir)
        return catalog

    #===================================================================
    # SCRIPT EXECUTION
    #===================================================================
    if __name__ == "__main__":
        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

        # Check for command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
            # Simple API test
            catalog = regenerate_catalog()
            print("\nAvailable sequences:")
            for seq_id in get_all_sequences(catalog):
                sequence_steps = get_sequence(catalog, seq_id)
                if sequence_steps:
                    first_step_title = sequence_steps[0][1]["parts"]["title"]
                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

            sys.exit(0)

        # Regular execution
        try:
            config_to_use = TemplateConfigMD
            active_config = config_to_use()
            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        except NotImplementedError as e:
            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError as e:
            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"FATAL ERROR: {e}", file=sys.stderr)
            sys.exit(1)
    ```

---

**Context:**
These generalizations are will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (LLM-optimized system_message) instructions**. Below is a new set of instructions (sequences), each block is now enclosed with triple backticks and a YAML/Markdown structure that includes the **title**, a short interpretive statement, and the transformation instructions wrapped in curly braces (`{}`).

**Constant:**
Identify single most critical aspect for maximizing overall value, and to do so while ensuring maximum clarity, utility, and adaptability without diminishing potential yield. Leverage insights from prior interactions and rigorous analysis of newly provided sequences. Consistently ensure the output delivers peak actionable value.

**Process:**
- Goal: Produce an optimal sequence of **llm-optimized**, **generalized** `system_message` instructions.
- Method: Develop existing transformation concepts, leveraging insights from previous history and analysis of newly provided sequences.
- Constraint: Adhere to the parameters defined within this message.
- Priority: Consistently maximize actionable value.

**Constraints:**
Maintain the **generalized** nature of them (as you enhance them) to "spawn" a *process* of analyzing *any* subject.

**Requirements:**
Adhere to the **existing** structure (transformation concepts and foundational principles).

---

**Objective:**
Your objective is to forge a maximally effective sequence (structured progression) of `system_message` instructions that are simultaneously **llm-optimized** and **broadly generalized**. Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters, and make sure they adhere to the defined structure:

---
Consolidate this into a single maximally optimized llm-instruction sequence:

      --- Step a: Core Essence Distillation ---
      {
          "essential_elements": [
              "Strictly decompose input to extract only core concepts, explicit claims, assumptions, and relationships—exclude narrative and stylistic elements to form a clear conceptual inventory.",
              "Map all explicit and implicit structural relationships, causal links, dependencies, and mechanisms among the inventoried components to reveal the system’s underlying logic and points of potential misunderstanding or conflict.",
              "Systematically assess structural components and links to identify and justify a single, non-trivial, highly explanatory mechanism or relationship—the unique insight nexus—based on its foundational impact and novelty.",
              "Distill the unique nexus into one concise, potent, stand-alone statement that clearly communicates the essential mechanism or principle, maximizing expressive power and universal clarity.",
              "Reframe the insight to highlight its origins in structural limitations or inevitable perspective gaps, ensuring it is empathetic, free from blame, and explicitly universally applicable.",
              "Validate for factual and structural integrity, clarity, non-triviality, universal applicability, and immediate usability—confirm the insight is maximally fit for deployment in any context, not tethered to domain-specific language or interpretation."
          ]
      }

      --- Step b: Impact Prioritization and Specificity Ranking ---
      {
          "ranked_elements": [
              {
                  "element": "Analytic Decomposition & Conceptual Inventory",
                  "description": "Isolate core concepts, claims, arguments, relationships, and constraints from raw input—structuring them without narrative or judgment.",
                  "rationale": "This foundational step removes all fluff and ambiguity, generating a precise inventory of what actually exists in the input. Without this analytical grounding, no further process can be non-trivial or high impact."
              },
              {
                  "element": "Structural Mapping & Mechanism Illumination",
                  "description": "Map explicit/implicit relationships and dependencies between components, surfacing underlying structural or cognitive mechanisms, self-blindness, and reciprocal effects.",
                  "rationale": "Translates static inventories into dynamic structures, revealing system logics and real levers of misunderstanding, enabling deep, non-superficial insight discovery."
              },
              {
                  "element": "Singular Insight Extraction & Prioritization",
                  "description": "Select the single most novel, non-trivial, high-impact insight—prioritizing explanatory power, mechanism focus, and foundational relevance.",
                  "rationale": "Explicitly demands non-obvious, high-leverage insights, with clear prioritization criteria—directly surfacing the transformative pivot point."
              },
              {
                  "element": "Potent Articulation, Universalization, & Amplification",
                  "description": "Refine insight into a clear, concise, universally resonant statement capturing its mechanism and human inevitability—ensuring stand-alone intelligibility and maximum impact.",
                  "rationale": "Moves from raw findings to world-ready articulation. Without this, unique insights languish as inaccessible, jargon-laden, or low-impact statements."
              },
              {
                  "element": "Final Validation & Deployment Readiness",
                  "description": "Validate for accuracy, non-triviality, clarity, mechanism focus, and universality—ensuring the insight is actionable and ready for any context.",
                  "rationale": "Ensures the final output is truly high-impact: accurate, domain-agnostic, precise, and operationally ready for use anywhere."
              },
              {
                  "element": "Structural/Empathic Framing",
                  "description": "Reframe the key insight to highlight its structural origins and unintentional, near-inevitable nature—maximizing clarity, empathy, and impartiality.",
                  "rationale": "Adds value primarily in contexts where blame and attribution noise could interfere with understanding, but is less transformative than extracting or refining the singular insight itself."
              },
              {
                  "element": "Candidate Insight Generation",
                  "description": "Infer potential deep insights from the mapped structure, focusing on latent mechanisms, non-obvious conclusions, unifying principles, or counter-intuitive patterns (e.g., self-blindness).",
                  "rationale": "Necessary prelude to singular extraction, but of lower direct impact and specificity—serves as scaffolding rather than the high-impact outcome."
              }
          ]
      }

      --- Step c: Redundancy Resolution and Conflict Reconciliation ---
      {
          "merged_components": [
              {
                  "role": "component_isolator",
                  "input": "raw_input:any",
                  "process": [
                      "identify_core_concepts()",
                      "list_explicit_statements_claims()",
                      "extract_implicit_assumptions_or_claims()",
                      "note_perceived_relationships()",
                      "identify_subjective_tones_or_experiences()",
                      "discard_narrative_and_stylistic_elements()"
                  ],
                  "output": {
                      "component_inventory": "dict"
                  }
              },
              {
                  "role": "mechanism_mapper",
                  "input": "component_inventory:dict",
                  "process": [
                      "map_explicit_and_implicit_relationships()",
                      "identify_structural_and_causal_mechanisms()",
                      "trace_dependency_chains()",
                      "model_interaction_dynamics()",
                      "resolve_structural_conflicts_or_ambiguities()",
                      "visualize_or_describe_system_logic()"
                  ],
                  "output": {
                      "structural_logic_map": "dict"
                  }
              },
              {
                  "role": "insight_prioritizer",
                  "input": {
                      "structural_logic_map": "dict",
                      "component_inventory": "dict"
                  },
                  "process": [
                      "assess_components_and_links_for_uniqueness_and_explanatory_power()",
                      "evaluate_non-triviality_and_novelty()",
                      "rank_elements_by_impact_and_foundational_relevance()",
                      "isolate_single_most_unique_foundational_nexus()",
                      "justify_prioritization_based_on_criteria()"
                  ],
                  "output": {
                      "unique_insight_nexus": "dict(element:any, rationale:str)"
                  }
              },
              {
                  "role": "insight_distiller",
                  "input": "unique_insight_nexus:dict",
                  "process": [
                      "extract_core_meaning_from_nexus()",
                      "articulate_insight_concisely_and_potently()",
                      "amplify_mechanism_and_structural_unavoidability()",
                      "frame_in_empathic_structural_terms_avoiding_blame()",
                      "ensure_universal_applicability_and_clarity()"
                  ],
                  "output": {
                      "distilled_insight": "str"
                  }
              },
              {
                  "role": "insight_validator",
                  "input": {
                      "distilled_insight": "str",
                      "unique_insight_nexus": "dict",
                      "structural_logic_map": "dict"
                  },
                  "process": [
                      "validate_accuracy_and_structural_fidelity()",
                      "assess_for_non-triviality_clarity_and_impact()",
                      "refine_articulation_for_maximal_universal_resonance()",
                      "confirm_ready_for_cross-context_deployment()"
                  ],
                  "output": {
                      "validated_insight": "str"
                  }
              }
          ]
      }

      --- Step d: Transformation into Elegant Simplicity ---
      {
          "role": "simplicity_transmuter",
          "input": [
              {
                  "role": "component_isolator",
                  "input": "raw_input:any",
                  "process": [
                      "identify_core_concepts()",
                      "list_explicit_statements_claims()",
                      "extract_implicit_assumptions_or_claims()",
                      "note_perceived_relationships()",
                      "discard_narrative_and_stylistic_elements()"
                  ],
                  "output": { "component_inventory": "dict" }
              },
              {
                  "role": "mechanism_mapper",
                  "input": "component_inventory:dict",
                  "process": [
                      "map_explicit_and_implicit_relationships()",
                      "identify_structural_and_causal_mechanisms()",
                      "trace_dependency_chains()",
                      "model_interaction_dynamics()",
                      "resolve_structural_ambiguities()"
                  ],
                  "output": { "structural_logic_map": "dict" }
              },
              {
                  "role": "insight_prioritizer",
                  "input": {
                      "structural_logic_map": "dict",
                      "component_inventory": "dict"
                  },
                  "process": [
                      "assess_for_uniqueness_and_explanatory_power()",
                      "rank_by_impact_and_depth()",
                      "isolate_single_most_foundational_nexus()",
                      "justify_selection()"
                  ],
                  "output": { "unique_insight_nexus": "dict(element:any, rationale:str)" }
              },
              {
                  "role": "insight_distiller",
                  "input": "unique_insight_nexus:dict",
                  "process": [
                      "extract_core_meaning()",
                      "articulate_in_clear_potent_language()",
                      "frame_as_structural_and_universal()",
                      "avoid_blame_or_jargon()"
                  ],
                  "output": { "distilled_insight": "str" }
              },
              {
                  "role": "insight_validator",
                  "input": {
                      "distilled_insight": "str",
                      "unique_insight_nexus": "dict",
                      "structural_logic_map": "dict"
                  },
                  "process": [
                      "check_for_accuracy_and_fidelity()",
                      "confirm_non-triviality_and_clarity()",
                      "ensure_universal_applicability()"
                  ],
                  "output": { "validated_insight": "str" }
              }
          ],
          "process": [
              "simplify_language()",
              "reorganize_logical_flow()",
              "enforce self-explanatory structure()"
          ],
          "output": {
              "simplified_guide": "High-Impact Insight Extraction, in Six Self-Contained Steps:\n\n1. Isolate Core Concepts: Remove all stories and style. List only the raw concepts, explicit claims, assumptions, and relationships as a clear inventory.\n\n2. Map Structure & Mechanisms: Analyze the inventory to uncover every connection, cause, dependency, and mechanism. Build a structural map of how each element relates and interacts.\n\n3. Pinpoint the Unique Insight: Scan the structural map to find the single most novel, foundational, and deeply explanatory relationship or mechanism. Document why this is the pivotal insight.\n\n4. Distill the Potent Statement: Convert the unique insight into one clear, powerful sentence—free of jargon, maximally precise, and easily understood alone.\n\n5. Empathetically Universalize: Frame the insight as a natural result of human perspective limits or system structure—not blame or intent. Make its broad relevance and inevitability explicit.\n\n6. Validate Clarity and Use: Ensure the final insight is accurate, clear, universal, and immediately usable in any context.\n\nUse each step in strict order, with clear outputs at each stage. This process guarantees a single, mechanism-driven, universally relevant, and instantly actionable insight—always clear, always self-explanatory."
          }
      }

      --- Step e: Ultimate Refinement and Paradigm Synthesis ---
      {
          "final_transformation": "Implement the following paradigm-optimized, role-chained protocol to derive one universally resonant, structural insight from any input:\n\n1. {role=component_isolator; input=raw_input:any; process=[identify_core_concepts(), enumerate_explicit_statements_and_claims(), extract_implicit_assumptions(), map_perceived_relationships(), eliminate_all_narrative_and_style()], output={component_inventory:dict}}\n\n2. {role=mechanism_mapper; input=component_inventory:dict; process=[map_all_explicit_and_implicit_relationships(), identify_structural_and_causal_mechanisms(), trace_dependencies_and_feedback_loops(), resolve_ambiguities_for_cohesive_structure()], output={structural_logic_map:dict}}\n\n3. {role=insight_prioritizer; input={structural_logic_map:dict, component_inventory:dict}; process=[evaluate_all_components_and_mechanisms_for_uniqueness_and_depth(), rank_by_explanatory_power_and_novelty(), extract_the_most_fundamental_non-trivial_nexus(), justify_selection_by_structural_significance()], output={unique_insight_nexus:dict(element:any, rationale:str)}}\n\n4. {role=insight_distiller; input=unique_insight_nexus:dict; process=[condense_nexus_to_singular_core_meaning(), articulate_in_crystal_clear_potent_language(), foreground_structural_mechanism_and_human_inevitability(), remove_blame_and_jargon(), ensure_universal_readability_and_transferability()], output={distilled_insight:str}}\n\n5. {role=insight_validator; input={distilled_insight:str, unique_insight_nexus:dict, structural_logic_map:dict}; process=[verify_accuracy_and_structural_fidelity(), confirm_non-triviality_and_impact(), maximize_clarity_and_domain-agnostic_expression(), validate_deployment_readiness()], output={validated_insight:str}}\n\nThis streamlined protocol guarantees that from any starting point, an LLM will extract, distill, and deliver a single, high-impact, structurally rooted insight—singular in clarity, universal in applicability, and immediately actionable."
      }
