@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: yt-dlp Upgrade Tool
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  yt-dlp Upgrade Tool
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Show current yt-dlp version
ECHO [INFO] Current yt-dlp version:
uv run python -c "import yt_dlp; print(f'yt-dlp {yt_dlp.version.__version__}')" 2>nul || ECHO Not installed or not accessible
ECHO.

:: Upgrade yt-dlp
ECHO [INFO] Upgrading yt-dlp to latest version...
uv sync --upgrade-package yt-dlp

IF ERRORLEVEL 1 (
    ECHO [WARNING] uv sync failed, trying alternative method...
    uv pip install --upgrade yt-dlp
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to upgrade yt-dlp
        PAUSE>NUL & EXIT /B
    ) ELSE (
        ECHO [SUCCESS] yt-dlp upgraded successfully
    )
) ELSE (
    ECHO [SUCCESS] yt-dlp upgraded successfully
)

:: Show new version
ECHO.
ECHO [INFO] New yt-dlp version:
uv run python -c "import yt_dlp; print(f'yt-dlp {yt_dlp.version.__version__}')"

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
