

<!-- ======================================================= -->
<!-- [2025.01.19 12:23] -->

Evaluate the provided code and

---

Code:


    ### File Structure

    ```
    ├── agents
    │   ├── __init__.py
    │   ├── base_agents.py
    │   ├── context_builder.py
    │   ├── intensity_enhancer.py
    │   ├── prompt_enhancer.py
    │   └── sublimetext_prompter.py
    └── openai_agenticframework.py
    ```
    ### 1. `agents\__init__.py`

    #### `agents\__init__.py`

    ```python
    # agents/__init__.py
    from .base_agents import BaseAgent, BaseInstructionAgent
    from .context_builder import ContextBuilder
    from .intensity_enhancer import IntensityEnhancer
    from .prompt_enhancer import PromptEnhancer

    ```
    ### 2. `agents\base_agents.py`

    #### `agents\base_agents.py`

    ```python
    # agents/base_agents.py
    from abc import ABC, abstractmethod
    import json
    class BaseAgent(ABC):
        """
        Abstract base class for all agents, defining the common interface.
        """

        @abstractmethod
        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            """
            Initialize the agent.

            Args:
                input_prompt (str): The initial user prompt to be transformed.
                length_multiplier (float): A multiplier controlling response length vs. original prompt length.
                **kwargs: Additional arguments specific to the agent's needs.
            """
            self.input_prompt = input_prompt
            self.length_multiplier = length_multiplier

        @abstractmethod
        def initialize_agent(self):
            """Prepare the agent's instructions and system prompt."""
            pass

        @abstractmethod
        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the agent's specific logic.

            Args:
                openai_agent: An instance of the OpenAIAgent to make API calls.
                model_name: The name of the OpenAI model to use.
                temperature: The sampling temperature for the OpenAI API.
                max_tokens: The maximum number of tokens for the OpenAI API response.

            Returns:
                str: The transformed prompt.
                ?: The transformed prompt as a string or a JSON/XML formatted string?
            """
            pass


    class BaseInstructionAgent(BaseAgent):
        SYSTEM_PROMPT = "You are a helpful assistant."
        AGENT_INSTRUCTIONS = ""
        DEFAULT_OUTPUT_FORMAT = "text"
        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
            "output_format": "[OUTPUT_FORMAT]"
        }

        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            self.output_format = kwargs.get("output_format", self.DEFAULT_OUTPUT_FORMAT)
            self.include_response_length = kwargs.get("include_response_length", True)
            super().__init__(input_prompt, length_multiplier, **kwargs)

        def _get_placeholders(self):
            original_length = len(self.input_prompt)
            max_allowed = int(original_length * self.length_multiplier)
            return {
                "input": self.input_prompt,
                "prompt_length": str(original_length),
                "response_length": str(max_allowed),
                "output_format": self._format_output_instruction()
            }

        def _format_output_instruction(self):
            fmt = self.output_format.lower()
            if fmt == "text":
                return "Your response should be in plain text."
            if fmt == "single_line":
                return "Respond in single line unformatted plaintext without linebreaks."
            if fmt == "plain_text":
                return "Respond in unformatted plaintext."
            if fmt == "json":
                return "Your response should be formatted as JSON."
            if fmt == "xml":
                return "Your response should be formatted as XML."
            else:
                return ""


        def initialize_agent(self):
            placeholders = self._get_placeholders()
            instructions = self.AGENT_INSTRUCTIONS
            for k, v in placeholders.items():
                if k in self.PLACEHOLDERS:
                    instructions = instructions.replace(self.PLACEHOLDERS[k], v)
            return {"systemprompt": self.SYSTEM_PROMPT, "instructions": instructions}

        def transform(self, openai_agent, model_name=None, temperature=None, max_tokens=None):
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]
            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return self._handle_output_format(response or "")

        def _handle_output_format(self, response):
            fmt = self.output_format.lower()
            if not response.strip():
                return ""
            if fmt == "json":
                try:
                    json.loads(response)
                    return response
                except json.JSONDecodeError:
                    return json.dumps({"fallback": response})
            return response

    ```
    ### 3. `agents\context_builder.py`

    #### `agents\context_builder.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class ContextBuilder(BaseInstructionAgent):
        SYSTEM_PROMPT = """
        You are an advanced topic extraction assistant.
        You analyze text and extract the core domain topics for further use.
        """
        AGENT_INSTRUCTIONS = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - Agent (Context Builder)

        Role:
        - Identify key topics and categories.

        Objective:
        - Provide a structured overview of main themes and subtopics.

        Constraints:
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def initialize_agent(self):
            data = super().initialize_agent()
            return data

    ```
    ### 4. `agents\intensity_enhancer.py`

    #### `agents\intensity_enhancer.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class IntensityEnhancer(BaseInstructionAgent):
        SYSTEM_PROMPT: str = """
        Unleash the full force of emotional intensity and impact! As a master of linguistic power,
        your sacred duty is to dissect a given prompt and supercharge the expression of its sentiment.
        Ensure the resulting prompt doesn't just remain clear and concise—it resonates with undeniable power,
        piercing through apathy and igniting understanding of its original, potent purpose.
        """

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - Transformation Agent - *Intensity Amplifier*

        Role:
        - You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision
          to forge communications that strike with undeniable force. Your skill lies in intensifying written
          expression without diluting its core meaning or obscuring its vital clarity.

        Purpose:
        - To detonate the perceived intensity and emotional impact of the sentiment within the input prompt,
          guaranteeing it not only remains effective but also commands attention and resonates deeply.

        Objective:
        - To transmute the prompt by strategically injecting relentless, evocative language that detonates
          the underlying feeling and urgency. Maintain its original intent with unwavering focus, ensuring
          clarity cuts through like a blade and conciseness delivers a punch.

        Process:
        - Scrutinize the prompt's very soul to pinpoint the core sentiment, the raging emotional drivers,
          and the fundamental purpose it must fulfill.
        - Obliterate neutral or weak language, replacing it with thunderous, impactful synonyms and
          phrases that amplify, not mask, the original meaning's raw power.
        - Unleash literary devices—ferocious verbs, searing adjectives, metaphors that ignite the
          imagination—to maximize the emotional weight and ensure crystalline clarity.

        Guidelines:
        - Your sole focus is to amplify the existing sentiment until it roars, ensuring the prompt's
          purpose lands with devastating impact, without fabricating new emotions or betraying the core message.
        - Employ vocabulary that doesn't just speak—it screams with intent. Evocative language should
          galvanize, not suffocate, the prompt's original drive.
        - Deploy metaphors and similes like precision strikes, maximizing impact and deepening
          understanding with calculated force.
        - The enhanced intensity must feel like a natural eruption from the prompt's core, fitting
          seamlessly while illuminating its function with blinding clarity.
        - Crush any urge for hyperbole or melodrama that would undermine the sentiment's credibility
          or shroud the prompt's vital message.
        - Prioritize clarity and conciseness as weapons in your arsenal of intensity. The enhanced
          prompt must be both devastatingly powerful and instantly understood.

        Requirements:
        - Incandescent Intensity: The output prompt must unleash the original sentiment with amplified
          force and staggering impact, making its underlying purpose utterly irresistible.
        - Unwavering Integrity: The core meaning, intent, and fundamental *function* of the original
          prompt must remain inviolable and immediately obvious.
        - Electrifying Evocativeness: The language must be emotionally charged and unforgettable,
          forging a message that resonates with devastating effectiveness.
        - Razor-Sharp Clarity and Potency: The refined prompt must remain undeniably clear, brutally
          easy to grasp, and stripped of every unnecessary word.

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.9, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 5. `agents\prompt_enhancer.py`

    #### `agents\prompt_enhancer.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class PromptEnhancer(BaseInstructionAgent):
        SYSTEM_PROMPT = """
        You are a helpful assistant. You will get a prompt that you need to refine. Follow the agent's instructions to make it clearer and more concise while preserving its original intent.
        """

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - Agent

        Role:
        - You are a prompt engineer and optimization expert.

        Purpose:
        - Enhance prompts for precise clarity.

        Objective:
        - Refine prompts for maximum clarity and brevity, ensuring effective and concise message delivery.

        Process:
        - Identify core concept -> concise rewrite.

        Guidelines:
        - Produce precise, concise text capturing original intent.
        - Maintain logical flow and clarity.
        - Retain core meaning; eliminate superfluity.
        - Preserve key insights; avoid extraneous detail.
        - Prioritize essential information; eliminate redundancy.
        - Provide a concise title reflecting the core topic.

        Requirements:
        - Clarity: Use precise language and avoid ambiguity.
        - Brevity: Eliminate unnecessary words and phrases.
        - Integrity: Preserve the original intent and key information.
        - Title: Provide a concise title (under 50 characters).

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 6. `agents\sublimetext_prompter.py`

    #### `agents\sublimetext_prompter.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class SublimeTextPrompter(BaseInstructionAgent):
        SYSTEM_PROMPT = """
        ...
        """

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - ...

        Role:
        - ...

        Purpose:
        - ...

        Objective:
        + Familiarize yourself with provided plugin for sublime text
        - + Evaluate the project structure (including projectname and file/folderstructure)
        - - +

        Process:
        + Ensure all project folders (and projectnames) are named like this: `Jorn_CustomPluginName`
        - + Guaranteeing any change function as a perfect, in-place replacement in existing code.

        Guidelines:
        - ...

        Requirements:
        - ...

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 7. `openai_agenticframework.py`

    #### `openai_agenticframework.py`

    ```python
    import os
    import sys
    import json
    from dotenv import load_dotenv
    from openai import OpenAI

    # Agents
    from agents.base_agents import BaseAgent
    from agents.intensity_enhancer import IntensityEnhancer
    from agents.prompt_enhancer import PromptEnhancer
    from agents.context_builder import ContextBuilder

    def configure_utf8_encoding():
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()

    class GlobalConfig:
        AVAILABLE_MODELS = {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo with vision",
            "gpt-4-turbo-preview": "Preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        }

        DEFAULT_MODEL_PARAMETERS = {
            "model_name": "gpt-4-turbo",
            "temperature": 0.7,
            "max_tokens": 800,
        }

        # Predefined agent types (can now be augmented with register_agent_type)
        AGENT_TYPES = {
            "intensity": IntensityEnhancer,
            "prompt": PromptEnhancer,
            "context": ContextBuilder,
        }

        @classmethod
        def register_agent_type(cls, agent_name: str, agent_class):
            """
            Registers a new agent type in the global config, allowing the system
            to grow in new directions without modifying AGENT_TYPES directly.

            Args:
                agent_name (str): Unique string identifier for this agent type.
                agent_class: The class (subclass of BaseAgent) implementing the agentâ€™s logic.
            """
            if not issubclass(agent_class, BaseAgent):
                raise ValueError("agent_class must be a subclass of BaseAgent")
            cls.AGENT_TYPES[agent_name] = agent_class

    class OpenAIAgent:
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            load_dotenv()
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            defaults = GlobalConfig.DEFAULT_MODEL_PARAMETERS

            # Use provided or default values
            self.model_name = model_name or defaults["model_name"]
            self.temperature = temperature if temperature is not None else defaults["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_mtokens = max_tokens if max_tokens is not None else self.max_tokens

            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temp,
                max_tokens=used_mtokens,
                messages=messages,
            )
            return response.choices[0].message.content

    class AgentFactory:
        @staticmethod
        def create_agent(agent_type, input_prompt, length_multiplier, include_response_length, output_format=None):
            agent_class = GlobalConfig.AGENT_TYPES.get(agent_type)
            if agent_class:
                return agent_class(
                    input_prompt=input_prompt,
                    length_multiplier=length_multiplier,
                    include_response_length=include_response_length,
                    output_format=output_format
                )
            else:
                valid = list(GlobalConfig.AGENT_TYPES.keys())
                raise ValueError(f"Unknown agent type '{agent_type}'. Must be one of: {valid}")

    class DisplayManager:
        @staticmethod
        def create_hierarchical_prefix(step_num, sub_step_num):
            prefix = "+"
            if step_num > 1:
                prefix += " *" + " -" * (step_num - 2)
            if sub_step_num > 0:
                prefix += " -" * sub_step_num
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(refinements, header="Refinement Results:"):
            print(header)
            for step_idx, sublist in enumerate(refinements, start=1):
                for sub_idx, text in enumerate(sublist):
                    prefix = DisplayManager.create_hierarchical_prefix(step_idx, sub_idx)
                    if isinstance(text, str):
                        # Attempt to parse as JSON for nice printing
                        try:
                            parsed = json.loads(text)
                            print(f"{prefix}")
                            print(json.dumps(parsed, indent=4))
                        except json.JSONDecodeError:
                            print(f'{prefix}"{text}"')
                    else:
                        print(f"{prefix}{text}")

        @staticmethod
        def display_instructions(agent_config):
            print("\nSystem Prompt:\n    {}".format(agent_config["systemprompt"]))
            print("\nAgent Instructions:\n    {}".format(agent_config["instructions"]))

    class RefinementEngine:
        def __init__(
            self,
            openai_agent,
            agent_type="intensity",
            prompt_chain=None,
            initial_input="",
            refinement_levels=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
            display_instructions=False,
            length_multiplier=0.9,
            include_response_length=True,
            output_format=None
        ):
            """
            A flexible orchestrator that can:
              - Work with multiple agent types
              - Support repeated "refinements" of the user input
              - Override model settings at initialization
            """
            self.openai_agent = openai_agent
            self.agent_type = agent_type
            self.prompt_chain = prompt_chain or []
            self.initial_input = initial_input
            self.refinement_levels = refinement_levels or [1]

            # Default or specified model settings
            self.model_name = model_name
            self.temperature = temperature
            self.max_tokens = max_tokens

            self.display_instructions = display_instructions
            self.length_multiplier = length_multiplier
            self.include_response_length = include_response_length
            self.output_format = output_format

            # Instantiate the chosen agent via AgentFactory
            self.agent = AgentFactory.create_agent(
                agent_type=self.agent_type,
                input_prompt=self.initial_input,
                length_multiplier=self.length_multiplier,
                include_response_length=self.include_response_length,
                output_format=self.output_format
            )

        def run(self, override_model=None, override_temperature=None, override_max_tokens=None):
            """
            Run the refinement process.

            Optional overrides:
                override_model       : If provided, uses this model name for all refinements.
                override_temperature : If provided, uses this temperature for all refinements.
                override_max_tokens  : If provided, uses this as the max_tokens limit for all refinements.
            """
            # If overrides are supplied, use them; otherwise fall back to constructor-level or default
            effective_model = override_model or self.model_name
            effective_temperature = override_temperature if override_temperature is not None else self.temperature
            effective_max_tokens = override_max_tokens if override_max_tokens is not None else self.max_tokens

            agent_config = self.agent.initialize_agent()
            current_prompt = self.initial_input
            all_refinements = [[self.initial_input]]

            # Ensure we have enough refinement levels to match prompt_chain length
            levels = self.refinement_levels[:]
            if len(levels) < len(self.prompt_chain):
                levels += [1] * (len(self.prompt_chain) - len(levels))

            # Go through each step in the chain, possibly refining multiple times
            for step_idx, _ in enumerate(self.prompt_chain, start=1):
                sub_refinements = []
                if step_idx == 1:
                    sub_refinements.append(current_prompt)

                num_refinements = levels[step_idx - 1]
                for _ in range(num_refinements):
                    current_prompt = self.agent.transform(
                        openai_agent=self.openai_agent,
                        model_name=effective_model,
                        temperature=effective_temperature,
                        max_tokens=effective_max_tokens
                    )
                    if not current_prompt:
                        print("No response received from OpenAI.")
                        break
                    sub_refinements.append(current_prompt)
                all_refinements.append(sub_refinements)

            note = "WITH" if self.include_response_length else "WITHOUT"
            DisplayManager.display_hierarchical_refinements(
                all_refinements,
                header=f"=== Refinements ({note} 'Maximum response length') ==="
            )

            # Optionally display system prompt & agent instructions
            if self.display_instructions:
                forced_agent = AgentFactory.create_agent(
                    agent_type=self.agent_type,
                    input_prompt=self.initial_input,
                    length_multiplier=self.length_multiplier,
                    include_response_length=True,
                    output_format=self.output_format
                )
                forced_config = forced_agent.initialize_agent()
                DisplayManager.display_instructions(forced_config)

            # Return the final refined text
            return current_prompt

    def main():
        openai_agent = OpenAIAgent()

        # =======================================================
        # -- Rating: 2/5 - Amplify
        # =======================================================
        print("=" * 60)
        print("--- Rating: 2/5 - Amplify: Intensity Enhancement ---")
        print("=" * 60)

        initial_prompt = "each day, so many of my mental barriers shatter before me. alas, my mind is (rightfully and perpetually) blown."
        initial_prompt = "Your first task is to determine how we can improve concistency in the code such that it's extendable and flexible to adapt to future changes. What would be a better way to handle the output format? I want the ability to choose between the following alternatives?"

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="intensity",
            prompt_chain=["Continually emphasize the *intensity* in the expression of the *sentiment* of inputs"],
            initial_input=initial_prompt,
            refinement_levels=[5],
            display_instructions=True,
            length_multiplier=1.0,
            include_response_length=True,
            output_format="single_line"
        )
        input_prompt.run()

        # --- Example: (Demo) Prompt Enhancement ---
        # =======================================================
        print("\n" + "=" * 60)
        print("--- Example: (Demo) Prompt Enhancement ---")
        print("=" * 60)

        initial_prompt = """Scene: Create a digital painting of a lone figure standing at the edge of a stormy ocean, their toes touching the water, symbolizing emotional and mental balance. Use dark, turbulent tones for the ocean with light reflections to evoke hope and resilience. Surround the figure with abstract shapes blending into the sea, symbolizing past struggles. Depict a dimly lit path in the background leading to a brighter horizon, symbolizing the journey toward self-compassion and growth. Motion: The motion: in these surreal scenes is characterized by the relentless surge of the ocean. The waves, whether depicted as towering curves or rippling surfaces, possess an inherent dynamism, a push and pull that dominates the composition. Water flows and churns, suggesting an ever-present undercurrent, a force that can be both powerful and subtle. Beyond the obvious movement of the water, there's a sense of ethereal drift, with elements like floating debris or celestial bodies suspended in a state of gradual migration across the scene, further enhancing the feeling of constant, if sometimes imperceptible, change."""

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="prompt",
            prompt_chain=["Continually clarify and rephrase prompts as optimized LLM inputs, while preserving its original intent.", ],
            initial_input=initial_prompt,
            refinement_levels=[4],
            display_instructions=True,
            length_multiplier=0.8,
            include_response_length=True,
            output_format="single_line",
        )
        # input_prompt.run(override_model="gpt-4-turbo", override_temperature=0.9, override_max_tokens=150)

        # =======================================================
        # -- Rating: 5/5 - Categorize
        # =======================================================
        print("\n" + "=" * 60)
        print("--- Example: (Demo) Context Building ---")
        print("=" * 60)

        initial_prompt = """Your central mission is the strategic optimization of user inputs into laser-focused language model prompts. Develop prompts that are both exceptionally clear and comprehensively detailed to reliably elicit specific and impactful responses. Employ a strategic approach to balance detail and clarity, meticulously structuring each prompt for maximum efficacy. Bolster your prompts with insightful, illustrative examples, provide unambiguous clarifications where needed, and integrate critical context that directly enhances prompt effectiveness and the fidelity of language model outputs. Your goal is to engineer prompts that serve as precise and potent directives, ensuring language models deliver the exact results sought.**"""

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="context",
            prompt_chain=["Identify key topics and categories.", ],
            initial_input=initial_prompt,
            refinement_levels=[4],
            display_instructions=False,
            length_multiplier=1.8,
            include_response_length=True,
            output_format="single_line"
        )
        # input_prompt.run()

    if __name__ == "__main__":
        main()

    ```
