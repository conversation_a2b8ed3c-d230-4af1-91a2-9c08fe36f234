# CONTEXT

RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).

# SOLUTION

i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__. you will understand exactly why i mean by this as you start understanding the fundamental concepts (especially with regards to the workflow itself) utilized in the utility, it's **extremely adaptable** (the workflow/concepts/methods for how it works is *meticiously crafted*; there's *a reason for everything*).

# CONSIDERATIONS

since our company use internal github repos for all development we do this utility will also be uploaded to github's company page (accessible for everyone within the company), i want to use this as an opportunity to impress (with unique and original simplicity and elegance). although 99.99% of developers in similar situations will choose to adhere to what they perceive as the concencus best practices and restructure (organize and refactor) the utility's codebase/project/filestructure based on these "rules", **i no longer blindly adhere to established concepts**, i *build* on them through *inherent understanding*. as an example, best practices are *great* for large and complex systems, and in some cases even **neccessary** - but such standardized practices does *not apply to small utilities the same way*, small utilities can often be transformed from a working utility within a single file of <500 lines, and after transforming it to "established best practices" will transform it into ~30 files and thousands of lines of code; __I WANT TO DO THE OPPOSITE OF THIS__. i want **balance** and **cognizeant and inherent understanding**, i want to leverage my own artistic vision onto the code. i find reduction of complexity through perpetually evaluation everything through different contexts through simulating (internally within my mind) different visual representations of components/sections/files in relation to each other.

# FILESTRUCTURE

this is the current filestructure:
```
RigOfficeDownloader
├── .cmd
│   ├── py_venv_pip_install.bat
│   ├── py_venv_run_script.bat
│   ├── py_venv_terminal.bat
│   ├── py_venv_upgrade_requirements.bat
│   └── py_venv_write_requirements.bat
├── memory-bank
│   ├── 01_foundation.md
│   ├── 02_context.md
│   ├── 03_patterns.md
│   ├── 04_tech.md
│   ├── 05_activity.md
│   ├── 06_progress.md
│   ├── 07_tasks.md
│   ├── 08_objective.md
│   └── memory-bank.md
├── outputs
│   ├── data
│   │   ├── R5385.010-a-docs.json
│   │   ├── R5385.010-a-docs.md
│   │   ├── R5385.010-b-files.json
│   │   └── R5385.010-b-files.md
│   └── downloads
│       └── R5385.dirtree.md
├── .gitignore
├── RigOfficeDownloader-v1.bat
├── RigOfficeDownloader-v1.py
├── RigOfficeDownloader-v2.bat
├── RigOfficeDownloader-v2.py
├── RigOfficeDownloader-v3.bat
├── RigOfficeDownloader-v3.py
├── RigOfficeDownloader-v4.bat
├── RigOfficeDownloader-v4.py
├── RigOfficeDownloader.sublime-project
├── notes.md
├── py_venv_init.bat
└── requirements.txt
```

# OBJECTIVE

start by writing a short `README.md` **IN LESS THAN <50 LINES OF CODE** based on all parameters inherently defined within this message, and based on the current memory-bank (provided below). the memory-bank is automatically generated and is therefore extremely (unneccessarily) verbose, so don't try to extract *everything* from it; instead transform subsets into (gradually more) consolidated (and condensed) ESSENTIALS.

## Memorybank

    ### RigOfficeDownloader

    #### Memorybank File Structure

    ```
    ├── 01_foundation.md
    ├── 02_context.md
    ├── 03_patterns.md
    ├── 04_tech.md
    ├── 05_activity.md
    ├── 06_progress.md
    ├── 07_tasks.md
    └── 08_objective.md
    ```

    ---

    ##### `01_foundation.md`

    ```markdown
        ## Distilled Highlights
        - RigOfficeDownloader automates document retrieval from NOV's RigDoc system
        - Three-step workflow: documents → files → downloads
        - Designed to save engineering time during document gathering phase
        - Primary purpose is to streamline 3D modeling preparation workflow

        # 01_foundation.md
        ## Core Mission

        RigOfficeDownloader exists to eliminate the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigOffice system. The mission is to free engineers from repetitive document retrieval tasks, allowing them to focus on their primary responsibility: high-quality 3D modeling.

        ## Primary Intent

        Engineering productivity is severely constrained by poorly functioning document retrieval systems that require excessive manual navigation and intervention. By automating this process, we can dramatically reduce the preparation phase for 3D modeling work, enabling faster project delivery without compromising quality.

        ## Project Identity

        This utility is characterized by:
        - **Pragmatic automation** that focuses on solving a single problem extremely well
        - **Progressive workflow** that divides complex tasks into manageable stages with human review points
        - **Preservation of context** by maintaining consistent naming and organizational patterns
        - **Prioritization of reliability** over speed, ensuring accurate document retrieval

        These guiding principles shape all design and implementation decisions in the project.
    ```

    ---

    ##### `02_context.md`

    ```markdown
        ## Distilled Highlights
        - Engineers waste significant time navigating poorly designed document retrieval systems
        - NOV's RigDoc system requires extensive manual interaction and repetitive tasks
        - Document retrieval is a prerequisite for the actual 3D modeling work
        - Target users are simulation engineers working on oil rig models

        # 02_context.md
        ## Problem Space

        3D simulation engineers working on oil rig visualizations face a significant challenge before they can begin their actual modeling work: gathering the necessary technical documentation. The NOV RigDoc system (https://rigdoc.nov.com) serves as the central repository for this documentation but presents several workflow challenges:

        1. **Fragmented Search Process**: Engineers must construct complex search queries to locate relevant documents
        2. **Manual Review Overhead**: Each search result requires manual review to determine relevance
        3. **Multi-Step Document Retrieval**: Accessing the actual files requires navigating through multiple interfaces
        4. **Inconsistent Naming Conventions**: Documents and files have inconsistent naming patterns
        5. **Inefficient Organization**: Downloaded files lack a clear organizational structure

        The time spent navigating these challenges can often exceed the time required for the actual 3D modeling work, creating a significant bottleneck in the overall workflow.

        ## Stakeholders

        - **Primary Users**: 3D simulation engineers responsible for creating accurate rig models
        - **Project Managers**: Team leaders who need to allocate resources efficiently
        - **NOV Documentation System**: The external service providing the technical documentation
        - **End Clients**: Those who will ultimately use the 3D simulations for training or analysis

        ## User Needs

        3D engineers require:
        1. **Efficient Document Discovery**: The ability to quickly locate relevant technical drawings
        2. **Selective Retrieval**: Options to choose specific documents rather than downloading everything
        3. **Contextual Organization**: Downloaded files organized in a way that preserves their relationships
        4. **Progress Visibility**: Clear feedback on lengthy operations
        5. **Workflow Control**: The ability to pause, review, and continue the process as needed

        ## Environmental Constraints

        - **Authentication Requirements**: Access to RigDoc requires proper credentials
        - **Session Timeouts**: Browser sessions expire after periods of inactivity
        - **Download Limitations**: The system may have rate limits or connection constraints
        - **File Format Variability**: Documents may be available in multiple formats (PDF, DWG, etc.)
        - **Network Dependencies**: The process requires stable internet connectivity

        ## Success Criteria

        The solution will be considered successful if it:
        1. **Reduces Documentation Gathering Time**: Cuts the time required by at least 75%
        2. **Minimizes Manual Intervention**: Automates the most repetitive aspects of the process
        3. **Maintains Engineer Control**: Allows for human judgment at key decision points
        4. **Organizes Results Logically**: Creates a clear structure for downloaded materials
        5. **Handles Edge Cases Gracefully**: Manages errors, timeouts, and unusual document formats
    ```

    ---

    ##### `03_patterns.md`

    ```markdown
        ## Distilled Highlights
        - Three-stage workflow: document retrieval → file metadata → selective download
        - User-editable Markdown interfaces between stages for review and selection
        - JSON storage with consistent structure and field ordering
        - Intelligent naming pattern with subfolder support for downloaded files

        # 03_patterns.md
        ## System Design

        RigOfficeDownloader follows a **linear multi-stage pipeline** architecture with human checkpoints between stages. This design prioritizes reliability, user control, and an incremental approach to data processing.

        ### Core Architectural Pattern

        The system operates through a three-stage workflow:

        ```mermaid
        flowchart TD
            A[Document Metadata Retrieval] --> B[Markdown Export]
            B --> C[User Selection]
            C --> D[Document Import]
            D --> E[File Metadata Retrieval]
            E --> F[Markdown Export]
            F --> G[User Selection]
            G --> H[File Import]
            H --> I[File Download]
            I --> J[Organized Storage]
        ```

        Each stage:
        1. **Collects necessary data** through web scraping
        2. **Transforms the data** into a human-readable format
        3. **Allows for user interaction** to make selection decisions
        4. **Processes the results** based on user choices

        This separation of concerns enables:
        - Independent testing and verification of each processing step
        - The ability to re-run specific stages without repeating the entire process
        - Clear progress tracking and resumption capabilities

        ## Data Representation

        The system employs a consistent data representation pattern across all stages:

        ### JSON as Internal Storage

        ```mermaid
        flowchart TD
            A[Web Scraping] --> B[JSON Documents]
            B --> C[JSON Files]
            C --> D[Downloaded Files]
        ```

        All metadata uses JSON with consistent field schemas:

        1. **Document Schema**:
           - `item_type`: Always "doc"
           - `item_title`: Document title
           - `item_drawing_type`: Drawing type classification
           - `item_include`: Boolean flag for inclusion in the file retrieval stage
           - `item_generated_name`: Constructed name for organization

        2. **File Schema**:
           - `item_type`: Always "file"
           - `item_download`: Boolean flag for download selection
           - `item_generated_name`: Inherits from document with additional identifiers
           - `item_file_ext`: File extension for type identification

        ### Markdown as User Interface

        The system converts JSON to Markdown tables for user editing, providing:
        - A familiar, readable format for reviewing data
        - Easy editing of inclusion/download flags
        - Consistent visualization of metadata
        - Preservation of all fields in a structured layout

        ## Naming and Organization Pattern

        A key pattern is the **hierarchical generated naming** system:

        ```
        <DocNumber>_REV<Revision>--<CaseNumber>-<DocTitle>.<FileIdentifier>.<FileTitle>
        ```

        This pattern:
        - Embeds critical metadata in the filename
        - Supports hierarchical organization through slash (/) characters
        - Maintains consistency between source documents and files
        - Automatically sanitizes invalid characters for filesystem compatibility

        ## Filter Chain Pattern

        The system implements a **sequential filter chain** for intelligent automation:

        ```mermaid
        flowchart LR
            A[Input Data] --> B[Filter 1]
            B --> C[Filter 2]
            C --> D[Filter 3]
            D --> E[Filter N]
            E --> F[Processed Data]
        ```

        Filters are:
        - **Sequentially applied** with later filters potentially overriding earlier ones
        - **Pattern-based** using glob-style syntax
        - **Type-specific** targeting either documents or files
        - **Field-targeted** applying to specific metadata fields
        - **Toggle-capable** with enable/disable functionality

        This approach balances automation with flexibility for special cases.

        ## Browser Interaction Pattern

        The system employs a consistent pattern for browser automation:

        1. **Session Context Management**: Using contextmanager for cleanup
        2. **Smart Waiting Strategy**: Progressive scrolling with dynamic waits
        3. **Deduplication**: Ensuring unique downloads and metadata
        4. **Progress Feedback**: Visibility for long-running operations
    ```

    ---

    ##### `04_tech.md`

    ```markdown
        ## Distilled Highlights
        - Python-based implementation with Selenium web automation
        - Modular architecture with clear separation of concerns
        - JSON and Markdown for data storage and user interaction
        - Browser automation with smart waiting strategies and error handling

        # 04_tech.md
        ## Technology Stack

        RigOfficeDownloader is built with a focused set of technologies to balance reliability, maintainability, and functionality:

        ### Core Technologies

        - **Python**: Primary implementation language
        - **Selenium**: Web automation framework
        - **BeautifulSoup**: HTML parsing and extraction
        - **ChromeDriver**: Browser control interface
        - **JSON**: Data storage format
        - **Markdown**: User interaction format

        ### Key Dependencies

        ```
        selenium         # Browser automation
        beautifulsoup4   # HTML parsing
        webdriver_manager # WebDriver installation automation
        colorama         # Terminal color support
        ansimarkup       # Enhanced terminal formatting
        dateutil         # Date parsing and formatting
        ```

        ## Implementation Architecture

        The system follows a modular implementation pattern with the following components:

        ### RigDocScraper Class

        The central class implementing the scraping workflow with methods for:
        - `fetch_docs()`: Document metadata retrieval
        - `fetch_files()`: File metadata retrieval
        - `download_files()`: File downloading with smart naming

        ### Browser Session Management

        ```python
        @contextmanager
        def browser_session(self, download_mode=False):
            # Setup browser with appropriate configuration
            try:
                # Initialize browser with options
                yield self.driver
            finally:
                # Clean up resources
                if self.driver:
                    self.driver.quit()
        ```

        This pattern ensures proper cleanup of browser resources regardless of execution path.

        ### Smart Waiting Strategy

        The system implements a progressive waiting approach:

        ```python
        class SleepCondition:
            # Callable class for WebDriverWait that sleeps for specified seconds then returns True
            def __init__(self, s):
                self.s = s
            def __call__(self, _):
                time.sleep(self.s)
                return True
        ```

        Combined with explicit conditions:

        ```python
        WebDriverWait(driver, max_wait).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "a.search-result-link"))
        )
        ```

        This ensures reliable extraction even with variable page load times.

        ### Data Transformation Pipeline

        Data flows through a consistent transformation pipeline:
        1. **Web Extraction**: Selenium + BeautifulSoup scraping
        2. **Data Structuring**: Consistent field mapping
        3. **Storage**: JSON persistence
        4. **Transformation**: JSON to Markdown conversion
        5. **User Interaction**: Markdown editing
        6. **Reversal**: Markdown to JSON conversion
        7. **Processing**: Filtered downloading based on flags

        ### File Organization System

        Downloads are managed through a structured file system:

        ```
        outputs/
        ├── data/
        │   ├── <rig>-a-docs.json   # Document metadata
        │   ├── <rig>-a-docs.md     # User-editable document selection
        │   ├── <rig>-b-files.json  # File metadata
        │   └── <rig>-b-files.md    # User-editable file selection
        └── downloads/
            └── <rig>/              # Downloaded files with subfolder organization
        ```

        ### Sanitization and Path Management

        File paths and names are sanitized with a robust approach:

        ```python
        def sanitize_filename(s):
            # Clean string for valid filename: replace invalid chars, preserve paths
            replacements = {
                "'": "", '"': "", '`': "",  # Remove quotes and backticks
                '\\': "_", '|': "_", '?': "_", '*': "_", '<': "_", '>': "_", ':': "_"  # Replace with underscore
            }

            # Apply replacements and clean up
            result = s
            for char, replacement in replacements.items():
                result = result.replace(char, replacement)

            result = ''.join(c for c in result if c.isprintable() or c == '/')  # Keep printable chars and path separators
            result = re.sub(r'\s+', ' ', result)  # Normalize spaces

            return result.strip()
        ```

        This allows for:
        - Safe filesystem operations
        - Preservation of subfolder structure
        - Consistent naming patterns

        ## Configuration System

        The system uses a structured configuration approach:

        ```python
        CONFIG = {
            "rig_number": f"{PROJECT_RIG_ID}.020",
            "search_urls": PROJECTINFO_GAD,
            "show_progress": True,
            "filters": [
                # Sequential filter definitions
            ]
        }
        ```

        This enables:
        - Easy modification of search parameters
        - Fine-tuning of filter chains
        - Toggle-based control of functionality
        - Project-specific customization

        ## Technical Constraints

        - **Browser Compatibility**: Requires Chrome browser
        - **Network Access**: Must have connectivity to rigdoc.nov.com
        - **Authentication**: User must handle authentication through browser session
        - **File System Access**: Requires write permissions for outputs directory
        - **Memory Consumption**: Can handle large document sets with incremental processing
    ```

    ---

    ##### `05_activity.md`

    ```markdown
        ## Distilled Highlights
        - Currently working on v4 of RigOfficeDownloader with enhanced file organization
        - Implementing subfolder support via path separators in generated names
        - Improved configuration system with filter chains
        - Working on progress visibility for lengthy operations

        # 05_activity.md
        ## Current Focus

        The development team is currently focused on enhancing RigOfficeDownloader with several key improvements:

        ### Version 4 Implementation

        Version 4 represents a significant maturation of the tool with emphasis on:

        1. **Enhanced File Organization**
           - Implementing subfolder support through path separator handling
           - Improving the item_generated_name pattern for more consistent file organization
           - Ensuring proper sanitization of filenames while preserving path structure

        2. **Smarter Configuration System**
           - Refining the filter chain mechanism for more precise document/file selection
           - Supporting pattern-based filtering with glob syntax
           - Enabling field-specific matching for greater flexibility

        3. **User Experience Improvements**
           - Enhancing progress visibility during long-running operations
           - Providing clearer feedback on the staging process
           - Making the interactive menu more intuitive

        ## Recent Decisions

        The team has made several key decisions that are shaping current development:

        1. **Single-File Approach**
           - Maintaining the utility as a single Python file for simplicity
           - Ensuring all functionality is self-contained for easy deployment
           - Using modular internal design for maintainability

        2. **Three-Stage Workflow**
           - Confirming the document → file → download workflow as optimal
           - Reinforcing user decision points between stages
           - Enhancing the JSON ↔ Markdown conversion for better user interaction

        3. **Progressive Enhancement**
           - Adding features incrementally without disrupting core functionality
           - Prioritizing stability and reliability over feature expansion
           - Focusing on refinements that enhance the engineer's workflow

        ## Technical Hypotheses

        The team is currently exploring several technical approaches:

        1. **Smart Waiting Strategy**
           - Hypothesis: Combining explicit waits with scrolling will improve scraping reliability
           - Implementation: Using WebDriverWait with both sleep conditions and element presence checks
           - Testing: Comparing success rates across different document types and quantities

        2. **Configurable Field Ordering**
           - Hypothesis: Customizable field order will improve user experience
           - Implementation: Separate field order definitions for documents and files
           - Testing: User feedback on Markdown table readability and editing experience

        3. **Deduplication Approach**
           - Hypothesis: Priority-based deduplication will handle conflicting data appropriately
           - Implementation: Using hashable key generation with exclusion lists
           - Testing: Verifying correct handling of duplicates with different attribute values

        ## Ongoing Discussions

        Current topics under active discussion include:

        1. **Session Management**
           - How to handle authentication when sessions expire
           - Options for persisting cookies between runs
           - Balancing security with convenience

        2. **Error Recovery**
           - Strategies for handling network interruptions
           - Approaches to resuming interrupted downloads
           - Error reporting format and detail level

        3. **Filter Chain Refinement**
           - Making filter creation more user-friendly
           - Potential for saving and loading filter configurations
           - Visualization of filter effects before application
    ```

    ---

    ##### `06_progress.md`

    ```markdown
        ## Distilled Highlights
        - Version 4 is the current active build with enhanced organization features
        - Subfolder support successfully implemented for downloaded files
        - Filter chain mechanism functioning with pattern-based selection
        - Interactive configuration menu implemented for runtime adjustments

        # 06_progress.md
        ## State of the Build

        The RigOfficeDownloader utility has evolved through several versions, with each iteration bringing focused improvements:

        ### Version History

        | Version | Status | Key Features |
        |---------|--------|-------------|
        | v1 | Complete | Basic document retrieval and download |
        | v2 | Complete | JSON/Markdown conversion and user selection |
        | v3 | Complete | Improved error handling and field organization |
        | v4 | Active | Subfolder support, filter chains, field ordering |

        ### Current Version Capabilities

        Version 4 represents the current stable build with several key capabilities:

        1. **Complete Document Retrieval Pipeline**
           - ‚úÖ Automated document metadata scraping from RigDoc
           - ‚úÖ Structured JSON storage with consistent field schemas
           - ‚úÖ Markdown export for user review and selection
           - ‚úÖ Re-import of user selections for processing

        2. **Enhanced File Organization**
           - ‚úÖ Hierarchical file naming with metadata embedding
           - ‚úÖ Subfolder support through path separator handling
           - ‚úÖ Filename sanitization with path preservation
           - ‚úÖ Collision detection and avoidance

        3. **Intelligent Selection System**
           - ‚úÖ Sequential filter chain with pattern-based matching
           - ‚úÖ Type-specific filters (documents vs. files)
           - ‚úÖ Field-targeted matching for precise filtering
           - ‚úÖ Enable/disable toggles for filter customization

        4. **User Experience Improvements**
           - ‚úÖ Interactive configuration menu for runtime adjustments
           - ‚úÖ Progress feedback during lengthy operations
           - ‚úÖ Color-coded terminal output for status clarity
           - ‚úÖ Modular workflow allowing partial process execution

        ## Recent Development Progress

        Recent development efforts have focused on several key areas:

        1. **Subfolder Organization**
           - Completed implementation of path separator handling in generated names
           - Validated correct directory creation and file placement
           - Ensured proper path sanitization while preserving structure
           - Tested with complex nested folder structures

        2. **Filter System Enhancement**
           - Implemented sequential filter chain with override capability
           - Added support for match field specification beyond default item_generated_name
           - Created interactive filter configuration interface
           - Built pattern-based filtering with glob syntax support

        3. **Browser Automation Improvements**
           - Enhanced waiting strategy with explicit condition checks
           - Implemented progressive scrolling for complete page loading
           - Added contextmanager for reliable resource cleanup
           - Improved download handling with timeout protection

        4. **Data Structure Refinement**
           - Standardized field ordering for consistent presentation
           - Enhanced deduplication with priority-based conflict resolution
           - Improved JSON and Markdown conversion fidelity
           - Added generated name consistency between documents and files

        ## Current Blockers

        Several challenges remain to be addressed in ongoing development:

        1. **Session Management**
           - ‚ö†Ô∏è Browser sessions can expire during lengthy operations
           - ‚ö†Ô∏è Authentication flow needs more robust handling
           - üîÑ Investigating options for session persistence

        2. **Progress Visibility**
           - ‚ö†Ô∏è Long-running operations lack detailed progress indicators
           - ‚ö†Ô∏è Users have limited insight into incremental completion
           - üîÑ Exploring approaches for finer-grained progress reporting

        3. **Download Verification**
           - ‚ö†Ô∏è No checksum validation for downloaded files
           - ‚ö†Ô∏è Incomplete downloads may not be detected
           - üîÑ Considering file integrity verification approaches

        ## Next Development Priorities

        The team has identified the following priorities for immediate focus:

        1. **Hash-based File Verification**
           - Implement checksum calculation for downloaded files
           - Add verification step to confirm download integrity
           - Provide retry mechanism for failed or corrupted downloads

        2. **Enhanced Progress Reporting**
           - Add percentage-based progress indicators
           - Implement estimated time remaining calculations
           - Create logging system for detailed operation tracking

        3. **Search Result Optimization**
           - Refine search URL templates for more precise results
           - Implement automatic deduplication across search queries
           - Improve handling of "VOID" documents and duplicates

        4. **Configuration Persistence**
           - Add ability to save and load filter configurations
           - Create presets for common search patterns
           - Implement project-specific configuration profiles
    ```

    ---

    ##### `07_tasks.md`

    ```markdown
        ## Distilled Highlights
        - Implement hash-based file verification to prevent redundant downloads
        - Add enhanced progress reporting with completion percentage and time estimates
        - Improve session management with automatic re-authentication
        - Create configuration persistence for filter chains and search templates

        # 07_tasks.md
        ## Current Task List

        The following tasks have been identified as priorities for near-term development:

        | Task ID | Priority | Complexity | Status | Description |
        |---------|----------|------------|--------|-------------|
        | T001 | High | Medium | Pending | Implement hash-based file verification |
        | T002 | High | Low | Pending | Enhance progress reporting for long operations |
        | T003 | Medium | High | Pending | Improve session management and re-authentication |
        | T004 | Medium | Medium | Pending | Add configuration persistence for filters |
        | T005 | Low | Medium | Pending | Refactor filter UI for improved usability |

        ## Task Details

        ### T001: Implement Hash-based File Verification

        **Description:**
        Implement a hash-based verification system to identify duplicate files and prevent redundant downloads. This will significantly improve efficiency for large document sets and enable smart resumption of interrupted processes.

        **Specific Requirements:**
        1. Calculate MD5 or SHA-256 hash for each downloaded file
        2. Store hashes in a persistent cache file
        3. Check existing files against stored hashes before downloading
        4. Implement a verification step after download to confirm integrity
        5. Add option to force re-download regardless of hash status

        **Justification:**
        This task directly addresses a current performance bottleneck and will eliminate wasted time downloading files that have already been retrieved in previous runs. This aligns with the project's goal of maximizing engineer productivity.

        **Dependencies:**
        - None

        ---

        ### T002: Enhance Progress Reporting

        **Description:**
        Implement more detailed progress reporting for long-running operations, particularly during document scraping, file metadata retrieval, and download phases.

        **Specific Requirements:**
        1. Add percentage-based completion indicators
        2. Display current item and total count during processing
        3. Implement estimated time remaining calculations
        4. Create a persistent log file of operations
        5. Add visual progress bar for terminal display

        **Justification:**
        Improved progress visibility will enhance user confidence during lengthy operations and provide better feedback on system status. This addresses a known pain point in the current implementation.

        **Dependencies:**
        - None

        ---

        ### T003: Improve Session Management

        **Description:**
        Enhance the browser session handling to better manage authentication, session timeouts, and recovery from interruptions.

        **Specific Requirements:**
        1. Detect session expiration conditions
        2. Implement graceful re-authentication flow
        3. Add session persistence between program runs (optional)
        4. Handle network interruptions with automatic retry
        5. Provide clear user feedback during authentication issues

        **Justification:**
        More robust session management will reduce failures during long-running operations and minimize the need for user intervention, furthering the automation goals of the project.

        **Dependencies:**
        - None

        ---

        ### T004: Add Configuration Persistence

        **Description:**
        Implement a system to save and load filter configurations, search templates, and other user preferences between program runs.

        **Specific Requirements:**
        1. Create a configuration file format (JSON) for storing settings
        2. Implement save/load functionality for filter chains
        3. Add support for named configuration profiles
        4. Create preset templates for common search patterns
        5. Maintain backward compatibility with direct configuration

        **Justification:**
        Configuration persistence will allow users to reuse successful search and filter strategies across projects, reducing setup time and improving consistency.

        **Dependencies:**
        - None

        ---

        ### T005: Refactor Filter UI

        **Description:**
        Improve the user interface for filter configuration to make it more intuitive and provide better visualization of filter effects.

        **Specific Requirements:**
        1. Redesign the interactive filter configuration menu
        2. Add preview functionality to show filter effects before applying
        3. Implement drag-and-drop reordering of filters
        4. Add grouping capability for related filters
        5. Provide filter templates for common use cases

        **Justification:**
        A more intuitive filter interface will reduce the learning curve for new users and improve efficiency for experienced users, supporting broader adoption of the tool.

        **Dependencies:**
        - T004: Add Configuration Persistence

        ## Task Assignments

        These tasks are currently unassigned and available for development. Engineers should select tasks based on:

        1. **Priority**: Higher priority items address more immediate needs
        2. **Dependencies**: Tasks with no dependencies can be started immediately
        3. **Expertise**: Match tasks to individual strengths and interests

        When picking up a task, document your ownership in the `05_activity.md` file and update status here as you progress.

        ## Success Criteria

        Each task should be considered complete when it meets these general criteria:

        1. Full implementation of all specific requirements
        2. Maintains compatibility with existing functionality
        3. Includes appropriate error handling
        4. Follows established code style and patterns
        5. Is documented in the appropriate memory bank files
    ```

    ---

    ##### `08_objective.md`

    ```markdown
        ## Distilled Highlights
        - Create a reliable automation tool that reduces documentation gathering time by 75%+
        - Maintain a three-stage pipeline with user control points for maximum flexibility
        - Ensure robust error handling and progress visibility during lengthy operations
        - Implement smart file organization with hash-based verification to prevent redundant work

        # 08_objective.md
        ## Current Definition of Success

        The RigOfficeDownloader project will be considered successful when it achieves these core objectives:

        ### Primary Objectives

        1. **Time Efficiency**
           - Reduce documentation gathering time by at least 75% compared to manual methods
           - Eliminate repetitive manual steps in the document retrieval workflow
           - Allow engineers to focus on 3D modeling rather than preparation tasks

        2. **Reliability and Robustness**
           - Handle session timeouts and network interruptions gracefully
           - Verify file integrity with hash-based validation
           - Provide clear error feedback and recovery options
           - Maintain data consistency across interrupted operations

        3. **User Control and Flexibility**
           - Preserve the three-stage workflow with human checkpoints
           - Support both automated and manual selection processes
           - Enable configuration of search parameters and filter chains
           - Allow execution of individual workflow stages as needed

        4. **Intelligent Organization**
           - Implement hierarchical file naming with metadata preservation
           - Support subfolder organization based on document relationships
           - Prevent redundant downloads through smart verification
           - Maintain consistent naming patterns for improved discoverability

        ## Success Metrics

        The project's success will be measured against these specific metrics:

        | Metric | Target | Current Status |
        |--------|--------|----------------|
        | Documentation gathering time | 75% reduction | ~60% reduction |
        | File organization consistency | 100% accuracy | 90% accuracy |
        | Error recovery capability | Automatic for common issues | Manual intervention required |
        | Configuration flexibility | Save/load support | Runtime only |
        | Progress visibility | Real-time with estimates | Basic progress indication |

        ## Immediate Focus

        To achieve our objectives, the immediate focus is on:

        1. **Hash-based Verification (Highest Priority)**
           - Implement file fingerprinting to identify duplicates
           - Store hash data for future reference
           - Verify file integrity after download
           - Enable smart resumption of interrupted processes

        2. **Enhanced Progress Reporting**
           - Provide detailed progress indicators for all operations
           - Implement estimated time remaining calculations
           - Create visual progress representation
           - Maintain log of completed operations

        3. **Session Management Improvements**
           - Detect and handle authentication timeouts
           - Implement automatic re-authentication
           - Provide clear user feedback during session issues
           - Add resilience to network interruptions

        ## Vision Alignment

        These objectives directly support the core mission of RigOfficeDownloader:

        - **Engineer Time Optimization**: By automating document retrieval, we free skilled engineers to focus on value-adding 3D modeling work.
        - **Quality Improvement**: Consistent file organization and verification improve the quality of inputs to the modeling process.
        - **Process Reliability**: Robust error handling and session management create a more dependable workflow.
        - **Knowledge Preservation**: Metadata-rich file naming and organization maintain important context about documents and their relationships.

        The utility will ultimately serve as a critical bridge between the NOV RigDoc system and the 3D modeling workflow, transforming a tedious, error-prone process into an efficient, reliable, and user-friendly experience.
    ```

# REQUIREMENTS

i want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the "canvas" with unusefull noise (unneccessary bloat)
