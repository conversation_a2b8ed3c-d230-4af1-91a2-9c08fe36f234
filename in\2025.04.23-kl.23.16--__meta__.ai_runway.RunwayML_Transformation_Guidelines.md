# RunwayML Transformation Guidelines

This document outlines the process for transforming extracted RunwayML-specific prompts into the standardized, generalized, LLM-optimized format required for the final output.

## Target Format Overview

All transformed prompts must adhere to the following standardized format:

```
[RunwayML Prompt Generator] Your objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications. `{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`
```

## Required Components

Each transformed prompt must include these specific sections:

1. **Role Declaration**: `[RunwayML Prompt Generator]` with a clear objective statement
2. **Input Parameters**: Defining the expected input type and format
3. **Process Steps**: A sequence of functions that process the input, with parameters in parentheses
4. **Constraints**: Specific limitations that must be adhered to
5. **Requirements**: Essential qualities the output must possess
6. **Output Specification**: The expected format and type of the result

## Transformation Process

### Stage 1: Source Analysis

1. **Content Extraction**:
   - Identify the core functionality of the source prompt
   - Extract key RunwayML syntax patterns and features
   - Document the purpose, expected inputs, and intended outputs
   - Note any unique features or specialized behaviors

2. **Intent Mapping**:
   - Categorize the prompt's primary purpose (e.g., camera movement, scene description, style enhancement)
   - Identify what visual elements or transformations it focuses on
   - Document any mood, style, or aesthetic components

3. **Syntax Inventory**:
   - Catalog all RunwayML-specific syntax used in the source
   - Identify camera movements, dynamic elements, and scene descriptors
   - Note any specialized or custom syntax patterns

### Stage 2: Component Preparation

1. **Role Definition**:
   - Create a clear, concise role statement that communicates the prompt's purpose
   - Format: `[RunwayML Prompt Generator] Your objective is to...`
   - Focus on the core transformation or visual effect

2. **Input Parameters**:
   - Define what input the prompt expects (text description, scene elements, etc.)
   - Format: `input=[source_description:any]` or other appropriate type
   - Add any required sub-parameters or nested structures

3. **Process Functions**:
   - Create a sequence of functions that represent the processing steps
   - Start with analysis functions, then transformation, then validation
   - Include parameters for functions when appropriate
   - Format each function as a distinct step with a clear purpose
   - Example: `analyze_input_for_visual_essence_intent_and_key_elements()`

4. **Constraints**:
   - Identify the key limitations and requirements
   - Always include RunwayML-specific constraints:
     * Single unbroken line of text
     * Maximum 500 characters
     * Valid RunwayML syntax
   - Add any prompt-specific constraints
   - Format: `constraints=[constraint1(), constraint2()]`

5. **Requirements**:
   - Define qualitative aspects the output must possess
   - Include standard visual impact and syntax requirements
   - Add prompt-specific requirements
   - Format: `requirements=[requirement1(), requirement2()]`

6. **Output Definition**:
   - Specify the output format
   - For RunwayML prompts, use: `output={runwayml_prompt:str}`

### Stage 3: Integration

1. **Template Construction**:
   - Compile all components into the standardized format
   - Ensure proper formatting with backticks and curly braces
   - Verify all required sections are included
   - Check for proper nesting of elements

2. **Syntax Verification**:
   - Ensure all RunwayML syntax is correctly represented
   - Verify camera movements are properly formatted (e.g., `[pan:direction]`)
   - Check for accurate representation of dynamic elements
   - Validate scene descriptor formatting

3. **Coherence Check**:
   - Ensure the transformed prompt maintains the source's intent
   - Verify the process functions accurately represent the transformation logic
   - Check that constraints and requirements match the source's purpose
   - Test for logical flow from input to output

### Stage 4: Optimization

1. **Conciseness Refinement**:
   - Eliminate redundant or verbose descriptions
   - Use precise, impactful language
   - Focus on essential elements only
   - Ensure the final prompt fits within character limits

2. **Clarity Enhancement**:
   - Improve readability of function names and parameters
   - Ensure constraints and requirements are clearly stated
   - Make input and output definitions explicit
   - Resolve any ambiguous or unclear elements

3. **Final Format Check**:
   - Verify the prompt is a single unbroken line
   - Check character count (must be ≤500)
   - Ensure all sections are properly delineated
   - Validate proper use of backticks, curly braces, and brackets

## Transformation Examples

### Example 1: Camera Movement Prompt

**Original:**
```xml
<template>
    <metadata>
        <class_name value="CameraMovementInfuser"/>
        <description value="Infuses scene descriptions with camera keywords"/>
    </metadata>
    <agent>
        <system_prompt value="You are a camera movement expert for AI image generation. Your task is to enhance scene descriptions by integrating camera movements using RunwayML-compatible keywords."/>
        <instructions>
            <constraints>
                <item value="Format: plain_text"/>
                <item value="Maximum response length: 288 chars"/>
            </constraints>
        </instructions>
    </agent>
</template>
```

**Transformed:**
```
[RunwayML Prompt Generator] Your objective is to enhance scene descriptions by strategically infusing RunwayML-compatible camera movement tags that elevate visual storytelling. Analyze the input scene for composition, focus, and narrative flow, then select appropriate camera techniques (such as `[pan]`, `[zoom]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`) that best showcase its elements. Position these tags to create a cohesive visual journey while maintaining the scene's original intent and atmosphere. `{role=camera_movement_infuser; input=[scene_description:str]; process=[analyze_scene_composition_and_focal_elements(), identify_narrative_flow_and_key_moments(), select_optimal_camera_movements_for_visual_impact(), position_camera_tags_at_strategic_points(), preserve_original_scene_details_and_atmosphere()]; constraints=[must_use_valid_runwayml_camera_syntax(), output_must_be_a_single_text_line(), final_prompt_length_must_not_exceed_500_characters(), camera_movements_must_enhance_not_distort_scene_intent()]; requirements=[achieve_natural_cinematic_flow(), ensure_camera_movements_highlight_key_elements(), maintain_scene_coherence_and_atmosphere()]; output={enhanced_scene:str}}`
```

### Example 2: Intensity Enhancer Prompt

**Original:**
```xml
<template>
    <metadata>
        <class_name value="IntensityEnhancer"/>
    </metadata>
    <agent>
        <system_prompt value="Transform ordinary descriptions into visceral, intense experiences using powerful language and evocative imagery."/>
        <instructions>
            <constraints>
                <item value="Maximum length: 300 chars"/>
                <item value="No linebreaks allowed"/>
            </constraints>
        </instructions>
    </agent>
</template>
```

**Transformed:**
```
[RunwayML Prompt Generator] Your objective is to transform ordinary visual descriptions into extraordinarily vivid, emotionally resonant RunwayML prompts. Amplify the sensory details, dramatic tension, and visual impact while maintaining strict adherence to RunwayML syntax requirements. Replace mundane language with powerful, evocative terms that intensify the scene's emotional and visual depth. `{role=intensity_enhancer; input=[scene_description:str]; process=[analyze_emotional_tone_and_core_impact(), identify_key_visual_elements(), replace_mundane_descriptors_with_high_impact_alternatives(), amplify_sensory_details_and_atmospheric_elements(), integrate_runwayml_camera_and_lighting_tags_for_emphasis(), ensure_dramatic_coherence_and_flow(), validate_syntax_and_length()]; constraints=[must_use_valid_runwayml_syntax(), output_must_be_a_single_text_line(), final_prompt_length_must_not_exceed_500_characters(), must_maintain_original_visual_concept()]; requirements=[achieve_maximum_emotional_and_visual_intensity(), maintain_narrative_coherence(), enhance_without_distorting_original_intent(), produce_cinematic_quality_descriptions()]; output={enhanced_prompt:str}}`
```

## Implementation Checklist

For each prompt to be transformed, follow this checklist:

1. **Preparation**
   - [ ] Source prompt fully analyzed
   - [ ] Core intent and functionality identified
   - [ ] RunwayML syntax elements cataloged
   - [ ] Special features or behaviors noted

2. **Component Creation**
   - [ ] Role statement crafted
   - [ ] Input parameters defined
   - [ ] Process functions created (minimum 5)
   - [ ] Constraints specified (minimum 3)
   - [ ] Requirements articulated (minimum 3)
   - [ ] Output format defined

3. **Integration & Validation**
   - [ ] All components assembled in standardized format
   - [ ] Syntax properly formatted with backticks and braces
   - [ ] RunwayML-specific syntax correctly represented
   - [ ] Character count verified (≤500)
   - [ ] Single unbroken line format confirmed
   - [ ] Original intent preserved

4. **Documentation**
   - [ ] Transformation decisions documented
   - [ ] Any special considerations noted
   - [ ] Source-to-target mapping recorded

## Common Transformation Patterns

### System Prompt to Role Conversion

Convert system prompts into concise role statements:

**System Prompt:**
```
You are a camera movement expert for AI image generation. Your task is to enhance scene descriptions by integrating camera movements using RunwayML-compatible keywords.
```

**Role Statement:**
```
[RunwayML Prompt Generator] Your objective is to enhance scene descriptions by strategically infusing RunwayML-compatible camera movement tags that elevate visual storytelling.
```

### Constraints to Process Functions

Convert constraint descriptions into process functions:

**Constraint:**
```
<item value="Maximum response length: 288 chars"/>
```

**Process Function:**
```
validate_character_limit(max=288)
```

### XML Class to Role Type

Map XML class names to role types:

| XML Class Name | Role Type |
|----------------|-----------|
| RunwayPromptBuilder | runwayml_prompt_generator |
| CameraMovementInfuser | camera_movement_infuser |
| IntensityEnhancer | intensity_enhancer |
| MultiResponseSelector | variant_selector |
| PoeticRefiner | poetic_enhancer |

## Final Quality Check

Before finalizing any transformation, verify:

1. **Completeness**: All required sections are included
2. **Syntax Compliance**: All RunwayML syntax is correctly represented
3. **Character Limit**: Final prompt is under 500 characters
4. **Format Compliance**: Single unbroken line with proper delimiters
5. **Intent Preservation**: Original functionality and purpose maintained
6. **Generalization**: Prompt is applicable to a wide range of inputs
7. **Clarity**: Clear, unambiguous instructions and process flow
