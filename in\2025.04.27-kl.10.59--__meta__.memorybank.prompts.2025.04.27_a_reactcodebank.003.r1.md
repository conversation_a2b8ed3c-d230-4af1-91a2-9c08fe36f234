<!-- ======================================================= -->
<!-- [2025.04.27 10:30] -->
<!-- 'https://gemini.google.com/app/012571600a2d6122' -->

Okay, let's break down the process of cleaning up and consolidating this project structure sequentially, focusing on best practices, simplicity, and adaptability.

The current structure shows signs of evolution, leading to significant duplication and unclear boundaries between different concepts (UI components, shared utilities, features, data, configuration). We'll aim for a clearer, more maintainable structure, possibly leaning towards a simplified Feature-Sliced Design (FSD) or a similar layered approach.

Here's a recommended sequential order:

**Phase 0: Preparation & Safety**

1.  **Version Control:** Ensure your project is under Git and all current changes are committed. Create a dedicated branch for this refactoring work (e.g., `git checkout -b refactor/structure`).
2.  **Backup:** Although Git is great, making a separate file backup of the entire project directory before starting major changes is wise.
3.  **Tooling Check:** Verify that your linter (ESLint), formatter (e.g., Prettier), TypeScript checker (`tsc --noEmit`), and build process (`vite build`) are all working correctly. This helps catch errors introduced during refactoring.

**Phase 1: Root Level & Configuration Cleanup**

4.  **Consolidate Dependency Cruiser Config:** You have both `.dependency-cruiser.cjs` and `depcruise-config.cjs`. Decide which one to keep (usually the one referenced in `package.json` scripts). Migrate any unique settings from the redundant file, then delete it. Update `package.json` scripts if necessary.
5.  **Review `package.json`:**
    * Audit dependencies: Remove any unused packages (`npm prune` or use `depcheck`).
    * Update key dependencies: Consider updating Vite, React, TypeScript, Tailwind CSS, ESLint, etc., to their latest stable versions if feasible. Run `npm install` (or `yarn`).
6.  **Define `.gitignore`:** Ensure generated output and sensitive files are ignored. Critically, add the `screenshots/` directory unless the images inside are *required* as baseline assets for visual regression testing. Add `dist/` (Vite build output) and potentially IDE/OS files (`.idea/`, `.vscode/`, `.DS_Store`).
7.  **Clarify Root `index.html`:** You have `index.html` at the root *and* in `src/`. Vite typically uses the one specified in `vite.config.ts` (often defaulting to the root one, or one configured via `root` or `build.rollupOptions.input`). Determine which `index.html` is the actual entry point for Vite. If the one in `src/` is the template, the root one might be redundant or used by `capture-website.js`. If the root one is redundant for the *main application build*, remove it. If it's needed for scripts, keep it but understand its purpose.
8.  **Review Core Config Files:** Briefly check `vite.config.ts`, `tsconfig.*.json`, `tailwind.config.js`, `postcss.config.js`, `eslint.config.js`. Ensure paths and settings are consistent and make sense. Pay attention to aliases defined in Vite/TSConfig as they will be crucial later.

**Phase 2: Consolidating Shared Logic & UI Primitives**

This is where the bulk of the duplication lies. The goal is to create a single source of truth for reusable code. A common pattern is a `src/shared` directory.

9.  **Establish `src/shared` Structure:** Create base directories within `src/shared/` like:
    * `src/shared/ui/` (for primitive, context-agnostic UI components: Button, Input, Card, Icon, Logo, etc.)
    * `src/shared/lib/` (for helper functions, generic hooks, constants)
    * `src/shared/config/` (for app-wide settings: routes, site metadata, constants)
    * `src/shared/types/` (for truly global types)
    * `src/shared/api/` (if applicable, for API client setup)
    * `src/shared/assets/` (optional, for static assets not in `/public`)
10. **Consolidate UI Primitives:**
    * Identify the most basic UI components scattered across `src/components/common`, `src/components/shared/Elements`, `src/components/ui`, `src/ui`.
    * Choose the best implementation for each (e.g., for `Button`), move it to `src/shared/ui/Button/Button.tsx` (using component folders is good practice), and delete the duplicates.
    * Systematically go through all components suspected of being primitive UI and move/consolidate them into `src/shared/ui/`.
    * Update all import paths across the codebase to point to the new location in `src/shared/ui/`. This is tedious but essential. Your IDE's find/replace or refactoring tools can help.
11. **Consolidate Utilities:**
    * Gather all general-purpose utility functions from `src/lib/utils`, `src/utils`, `src/shared/utils`.
    * Move them into `src/shared/lib/`, potentially organizing them by domain (e.g., `src/shared/lib/dateUtils.ts`, `src/shared/lib/imageUtils.ts`).
    * Remove duplicates and update all imports.
12. **Consolidate Generic Hooks:**
    * Identify hooks that are truly generic (e.g., `useDebounce`, `useLocalStorage`, `useMediaQuery`, `useEventListener`, `useIntersectionObserver`) from `src/hooks`, `src/lib/hooks`.
    * Move them to `src/shared/lib/hooks/` (or directly under `src/shared/lib/`).
    * Remove duplicates and update imports. Feature-specific hooks (`useServiceFilter`, `useFilteredData`) should *not* be moved here yet.
13. **Consolidate Global Types:**
    * Examine types in `src/types`, `src/lib/types`. Move only the types that are truly global or used across many different features/modules into `src/shared/types/`.
    * Remove duplicates and update imports. Keep feature-specific or entity-specific types closer to their usage for now.
14. **Consolidate Configuration:**
    * Merge configuration constants/objects from `src/config` and `src/lib/config` into `src/shared/config/`. Ensure a single source for things like site metadata, image paths, route definitions.
    * Remove duplicates and update imports.
15. **Cleanup Old Shared Folders:** Once confident, delete the now-empty (or fully migrated) top-level `src/hooks`, `src/lib`, `src/types`, `src/ui`, `src/utils`, `src/shared` (the one at `src/shared`, not the new `src/shared/` subdirectories). Also, review `src/components/common`, `src/components/shared`, `src/components/ui` and remove them if their contents are now primitives in `src/shared/ui`.

**Phase 3: Structuring Application Code (Features, Entities, Widgets, Pages)**

Now focus on organizing the application logic itself, using the consolidated `shared` resources.

16. **Standardize `src` Root & App Entry:**
    * Consider moving application setup files (`main.tsx`, `App.tsx`, `vite-env.d.ts`) and global styles (`index.css`, `styles/`) into a dedicated `src/app/` directory (e.g., `src/app/main.tsx`, `src/app/App.tsx`, `src/app/styles/`).
    * Update `index.html` (the Vite entry one) and `vite.config.ts` (`build.rollupOptions.input`) to point to the new entry point if you moved `main.tsx`.
    * Ensure `src/index.html` is clean and primarily serves as the mount point for React.
17. **Refactor `pages`:**
    * Ensure consistency. Use subdirectories for each page (e.g., `src/pages/HomePage/index.tsx`, `src/pages/AboutPage/index.tsx`, `src/pages/ProjectsPage/index.tsx`, `src/pages/ProjectDetailPage/index.tsx`).
    * Remove duplicate page definitions (e.g., `Projects.tsx` vs `projects/index.tsx`).
    * Pages should primarily orchestrate the layout by importing Widgets and Features.
18. **Identify and Structure Entities:**
    * Entities represent core business data (e.g., Project, Service, Testimonial). Create `src/entities/Project/`, `src/entities/Service/`, etc.
    * Consolidate data definitions: Merge content from `src/data/`, `src/content/`, and relevant parts of `src/features/*/data/` into the respective entity folder (e.g., `src/entities/Service/model/services.data.ts`, `src/entities/Service/model/types.ts`).
    * Move components that *primarily display* entity data (like a simple `ServiceCard`, `ProjectCard`) into the entity's `ui` subfolder (e.g., `src/entities/Service/ui/ServiceCard/ServiceCard.tsx`).
    * Update imports in pages/features/widgets.
19. **Identify and Structure Features:**
    * Features represent user interactions or slices of business logic (e.g., FilterServices, SubmitContactForm, ViewProjectGallery). Create `src/features/FilterServices/`, `src/features/ProjectGallery/`, etc.
    * Move components, hooks (`useServiceFilter`), API calls, and state management specific to a feature into its folder.
    * Consolidate logic from the old `src/features` and relevant parts of `src/components` (like `ContactForm`, `ProjectFilter`).
    * Features often use Entities and Shared UI.
    * Update imports.
20. **Identify and Structure Widgets:**
    * Widgets are compositional UI blocks, often assembling multiple features and entities (e.g., SiteHeader, SiteFooter, TestimonialsSection, ServiceList). Create `src/widgets/SiteHeader/`, `src/widgets/ServiceList/`, etc.
    * Move components from `src/components/layout` (like Header, Footer) and other compositional components (like `TestimonialsSection`) here.
    * Widgets compose Features, Entities, and use Shared UI. They are used directly by Pages.
    * Update imports.
21. **Cleanup Old Structure:** Once components, data, hooks, etc., are migrated to the new `entities`, `features`, `widgets` structure, delete the old `src/components`, `src/features`, `src/data`, `src/content` directories.

**Phase 4: Scripts and Final Polish**

22. **Review and Organize `scripts/`:**
    * Analyze each script (`capture-website.js`, `auto-snapshot.js`, etc.). Determine its purpose and if it's still needed.
    * Consider converting JavaScript (`.js`) scripts to TypeScript (`.ts`) for consistency, using `tsconfig.node.json`.
    * Group related scripts if possible (e.g., under subdirectories or consolidate into fewer files with command-line arguments).
    * Ensure scripts use correct paths, especially if source code locations changed.
    * If `capture-website.js` or the screenshot scripts rely on the root `index.html`, ensure that relationship is clear or refactor if possible.
23. **Run Dependency Analysis:** Use `dependency-cruiser` (with the single config) to visualize the new structure and enforce rules (e.g., `shared` cannot import `features`, `features` should ideally not import other `features` directly).
24. **Thorough Testing:**
    * Run all checks: `tsc --noEmit`, linters, formatters.
    * Run the development server (`npm run dev`) and manually test *all* application functionality and pages.
    * Run a production build (`npm run build`) to catch build-time errors.
    * Run automated tests if you have them.
25. **Documentation:** Update the project's `README.md` to explain the new structure, how to run the project, and any conventions developers should follow.

**Key Principles During Refactoring:**

* **Iterate and Test:** Refactor one section (e.g., consolidating UI primitives), then test thoroughly before moving to the next. Use Git commits frequently.
* **Simplicity First:** Don't over-engineer the structure. The goal is clarity and maintainability. If FSD seems too complex, a simpler `pages`/`components`/`hooks`/`utils`/`types` split done *consistently* is better than the current duplicated structure. However, the presence of `features` suggests an intent that FSD-like layering could clarify.
* **Consistency:** Use consistent naming conventions for files, folders, and components. Decide on a pattern (e.g., component folders with `index.ts` exports) and stick to it.
* **Single Responsibility:** Aim for modules/components that do one thing well.

This sequential approach breaks down a large task into manageable steps, starting with foundational cleanup and progressively reorganizing the core application code. Good luck!
