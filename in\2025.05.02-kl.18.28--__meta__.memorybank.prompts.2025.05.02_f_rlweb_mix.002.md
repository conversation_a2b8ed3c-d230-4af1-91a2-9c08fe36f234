What's the single most *constructive* tip you could give based on the current projectstructure with regards to consolidation (currently has unnessecarily many build envs and overall chaotic filestructure)? It's cruical that the website directory is completely self-contained (independent from the dev environment). Here's the current filestructure (i've excluded imagefiles):


    ├── config
    │   ├── env
    │   │   ├── .env.development
    │   │   ├── .env.example
    │   │   ├── .env.production
    │   │   ├── .env.staging
    │   │   └── README.md
    │   ├── README.md
    │   ├── eslint.config.js
    │   ├── postcss.config.js
    │   ├── tailwind.config.js
    │   ├── tsconfig.json
    │   ├── tsconfig.node.json
    │   └── vite.config.ts
    ├── dist
    │   ├── assets
    │   │   ├── index-DFrQxn5i.js
    │   │   ├── index-DvDYalx4.css
    │   │   ├── ui-OiFBRudS.js
    │   │   └── vendor-DfY6ie7V.js
    │   ├── images
    │   │   ├── categorized
    │   │   │   ├── belegg
    │   │   │   ├── ferdigplen
    │   │   │   ├── hekk
    │   │   │   ├── kantstein
    │   │   │   ├── platting
    │   │   │   ├── stål
    │   │   │   ├── støttemur
    │   │   │   ├── trapp-repo
    │   │   ├── site
    │   │   ├── team
    │   │   └── metadata.json
    │   ├── index.html
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── vite.svg
    ├── docs
    │   └── dependency-visualization.md
    ├── scripts
    │   ├── dev
    │   │   └── README.md
    │   ├── utils
    │   │   └── validate-env-files.js
    │   └── README.md
    ├── tools
    │   ├── depcruise
    │   │   ├── config
    │   │   │   └── dependency-cruiser.config.cjs
    │   │   ├── outputs
    │   │   │   ├── data
    │   │   │   │   ├── d3-data.json
    │   │   │   │   ├── d3.json
    │   │   │   │   ├── dependency-analysis.json
    │   │   │   │   ├── dependency-data.json
    │   │   │   │   ├── import-analysis.json
    │   │   │   │   └── module-metrics.json
    │   │   │   ├── graphs
    │   │   │   │   ├── circular-graph.svg
    │   │   │   │   ├── clustered-graph.svg
    │   │   │   │   ├── dependency-graph.svg
    │   │   │   │   ├── hierarchical-graph.svg
    │   │   │   │   └── tech-filtered.svg
    │   │   │   ├── interactive
    │   │   │   │   ├── archi-interactive.html
    │   │   │   │   ├── bubble-chart.html
    │   │   │   │   ├── bubble.html
    │   │   │   │   ├── circle-packing.html
    │   │   │   │   ├── circle.html
    │   │   │   │   ├── d3-graph.html
    │   │   │   │   ├── d3.html
    │   │   │   │   ├── dependency-graph.html
    │   │   │   │   ├── flow-diagram.html
    │   │   │   │   ├── flow.html
    │   │   │   │   ├── high-level-dependencies.html
    │   │   │   │   └── validation.html
    │   │   │   └── index.html
    │   │   ├── scripts
    │   │   │   ├── check-dependencies.js
    │   │   │   ├── check-graphviz.js
    │   │   │   ├── cleanup-directory.js
    │   │   │   ├── cleanup-old-configs.js
    │   │   │   ├── cleanup-redundant-files.js
    │   │   │   ├── create-bubble-chart.js
    │   │   │   ├── create-circle-packing.js
    │   │   │   ├── create-d3-graph.js
    │   │   │   ├── create-dependency-dashboard.js
    │   │   │   ├── create-flow-diagram.js
    │   │   │   ├── dependency-manager.js
    │   │   │   ├── fix-depcruise-paths.js
    │   │   │   ├── fix-missing-files.js
    │   │   │   ├── remove-old-directories.js
    │   │   │   ├── run-visualizations.js
    │   │   │   └── visualize.js
    │   │   └── README.md
    │   ├── screenshots
    │   │   ├── config
    │   │   │   └── screenshot-config.json
    │   │   ├── outputs
    │   │   │   ├── ai
    │   │   │   │   ├── latest
    │   │   │   │   │   ├── about.png
    │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   ├── contact.png
    │   │   │   │   │   ├── home.png
    │   │   │   │   │   ├── projects.png
    │   │   │   │   │   └── services.png
    │   │   │   │   ├── snapshot-1746089722250
    │   │   │   │   │   ├── about.png
    │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   ├── contact.png
    │   │   │   │   │   ├── projects.png
    │   │   │   │   │   └── services.png
    │   │   │   │   ├── snapshot-1746091880957
    │   │   │   │   │   └── ai-summary.md
    │   │   │   │   ├── snapshot-1746092062562
    │   │   │   │   │   ├── about.png
    │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   ├── contact.png
    │   │   │   │   │   ├── home.png
    │   │   │   │   │   ├── projects.png
    │   │   │   │   │   └── services.png
    │   │   │   │   ├── snapshot-1746113922576
    │   │   │   │   │   ├── about.png
    │   │   │   │   │   ├── home.png
    │   │   │   │   │   └── services.png
    │   │   │   │   └── metadata.json
    │   │   │   ├── captured
    │   │   │   │   ├── 2025-04-19_15-18-45
    │   │   │   │   │   ├── desktop
    │   │   │   │   │   │   ├── about-desktop.html
    │   │   │   │   │   │   ├── about-desktop.png
    │   │   │   │   │   │   ├── contact-desktop.html
    │   │   │   │   │   │   ├── contact-desktop.png
    │   │   │   │   │   │   ├── home-desktop.html
    │   │   │   │   │   │   ├── home-desktop.png
    │   │   │   │   │   │   ├── projects-desktop.html
    │   │   │   │   │   │   ├── projects-desktop.png
    │   │   │   │   │   │   ├── services-desktop.html
    │   │   │   │   │   │   └── services-desktop.png
    │   │   │   │   │   ├── mobile
    │   │   │   │   │   │   ├── about-mobile.html
    │   │   │   │   │   │   ├── about-mobile.png
    │   │   │   │   │   │   ├── contact-mobile.html
    │   │   │   │   │   │   ├── contact-mobile.png
    │   │   │   │   │   │   ├── home-mobile.html
    │   │   │   │   │   │   ├── home-mobile.png
    │   │   │   │   │   │   ├── projects-mobile.html
    │   │   │   │   │   │   ├── projects-mobile.png
    │   │   │   │   │   │   ├── services-mobile.html
    │   │   │   │   │   │   └── services-mobile.png
    │   │   │   │   │   └── tablet
    │   │   │   │   │       ├── about-tablet.html
    │   │   │   │   │       ├── about-tablet.png
    │   │   │   │   │       ├── contact-tablet.html
    │   │   │   │   │       ├── contact-tablet.png
    │   │   │   │   │       ├── home-tablet.html
    │   │   │   │   │       ├── home-tablet.png
    │   │   │   │   │       ├── projects-tablet.html
    │   │   │   │   │       ├── projects-tablet.png
    │   │   │   │   │       ├── services-tablet.html
    │   │   │   │   │       └── services-tablet.png
    │   │   │   │   ├── latest
    │   │   │   │   │   ├── desktop
    │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   ├── about-desktop.html
    │   │   │   │   │   │   ├── about-desktop.png
    │   │   │   │   │   │   ├── contact-desktop.html
    │   │   │   │   │   │   ├── contact-desktop.png
    │   │   │   │   │   │   ├── home-desktop.html
    │   │   │   │   │   │   ├── home-desktop.png
    │   │   │   │   │   │   ├── projects-desktop.html
    │   │   │   │   │   │   ├── projects-desktop.png
    │   │   │   │   │   │   ├── services-desktop.html
    │   │   │   │   │   │   └── services-desktop.png
    │   │   │   │   │   ├── mobile
    │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   ├── about-mobile.html
    │   │   │   │   │   │   ├── about-mobile.png
    │   │   │   │   │   │   ├── contact-mobile.html
    │   │   │   │   │   │   ├── contact-mobile.png
    │   │   │   │   │   │   ├── home-mobile.html
    │   │   │   │   │   │   ├── home-mobile.png
    │   │   │   │   │   │   ├── projects-mobile.html
    │   │   │   │   │   │   ├── projects-mobile.png
    │   │   │   │   │   │   ├── services-mobile.html
    │   │   │   │   │   │   └── services-mobile.png
    │   │   │   │   │   ├── tablet
    │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   ├── about-tablet.html
    │   │   │   │   │   │   ├── about-tablet.png
    │   │   │   │   │   │   ├── contact-tablet.html
    │   │   │   │   │   │   ├── contact-tablet.png
    │   │   │   │   │   │   ├── home-tablet.html
    │   │   │   │   │   │   ├── home-tablet.png
    │   │   │   │   │   │   ├── projects-tablet.html
    │   │   │   │   │   │   ├── projects-tablet.png
    │   │   │   │   │   │   ├── services-tablet.html
    │   │   │   │   │   │   └── services-tablet.png
    │   │   │   │   │   └── .gitkeep
    │   │   │   │   ├── .gitignore
    │   │   │   │   └── .gitkeep
    │   │   │   ├── reports
    │   │   │   │   └── screenshot-report.html
    │   │   │   ├── .gitignore
    │   │   │   └── .gitkeep
    │   │   ├── scripts
    │   │   │   ├── capture.js
    │   │   │   ├── cleanup-old-files.js
    │   │   │   ├── dev-snapshots.js
    │   │   │   ├── manage.js
    │   │   │   └── run-capture.bat
    │   │   └── README.md
    │   ├── tools
    │   │   └── screenshots
    │   │       └── outputs
    │   │           └── ai
    │   │               ├── latest
    │   │               │   └── ai-summary.md
    │   │               ├── snapshot-1746091735987
    │   │               │   └── ai-summary.md
    │   │               └── metadata.json
    │   ├── www
    │   │   ├── cleanup-duplicates.js
    │   │   ├── config.js
    │   │   ├── deploy.js
    │   │   ├── environments.js
    │   │   ├── init.js
    │   │   ├── move-to-website.js
    │   │   └── setup-dev-env.js
    │   └── package.json
    ├── website
    │   ├── config
    │   │   └── env
    │   │       ├── .env.development
    │   │       ├── .env.example
    │   │       ├── .env.production
    │   │       ├── .env.staging
    │   │       └── README.md
    │   ├── public
    │   │   ├── images
    │   │   │   ├── categorized
    │   │   │   │   ├── belegg
    │   │   │   │   ├── ferdigplen
    │   │   │   │   ├── hekk
    │   │   │   │   ├── kantstein
    │   │   │   │   ├── platting
    │   │   │   │   ├── stål
    │   │   │   │   ├── støttemur
    │   │   │   │   ├── trapp-repo
    │   │   │   ├── site
    │   │   │   ├── team
    │   │   │   └── metadata.json
    │   │   ├── robots.txt
    │   │   ├── site.webmanifest
    │   │   └── sitemap.xml
    │   ├── scripts
    │   │   └── validate-env-files.js
    │   ├── src
    │   │   ├── app
    │   │   │   └── index.tsx
    │   │   ├── content
    │   │   │   ├── locations
    │   │   │   │   └── index.ts
    │   │   │   ├── projects
    │   │   │   │   └── index.ts
    │   │   │   ├── services
    │   │   │   │   └── index.ts
    │   │   │   ├── team
    │   │   │   │   └── index.ts
    │   │   │   ├── testimonials
    │   │   │   │   └── index.ts
    │   │   │   └── index.ts
    │   │   ├── data
    │   │   │   ├── projects.ts
    │   │   │   ├── services.ts
    │   │   │   └── testimonials.ts
    │   │   ├── docs
    │   │   │   └── SEO_USAGE.md
    │   │   ├── layout
    │   │   │   ├── Footer.tsx
    │   │   │   ├── Header.tsx
    │   │   │   ├── Meta.tsx
    │   │   │   └── index.ts
    │   │   ├── lib
    │   │   │   ├── api
    │   │   │   │   └── index.ts
    │   │   │   ├── config
    │   │   │   │   ├── images.ts
    │   │   │   │   ├── index.ts
    │   │   │   │   ├── paths.ts
    │   │   │   │   └── site.ts
    │   │   │   ├── context
    │   │   │   │   └── AppContext.tsx
    │   │   │   ├── hooks
    │   │   │   │   ├── index.ts
    │   │   │   │   ├── useAnalytics.ts
    │   │   │   │   ├── useData.ts
    │   │   │   │   ├── useEventListener.ts
    │   │   │   │   └── useMediaQuery.ts
    │   │   │   ├── types
    │   │   │   │   ├── components.ts
    │   │   │   │   ├── content.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── utils
    │   │   │   │   ├── analytics.ts
    │   │   │   │   ├── dom.ts
    │   │   │   │   ├── formatting.ts
    │   │   │   │   ├── images.ts
    │   │   │   │   ├── index.ts
    │   │   │   │   ├── seasonal.ts
    │   │   │   │   ├── seo.ts
    │   │   │   │   └── validation.ts
    │   │   │   ├── README.md
    │   │   │   ├── config.ts
    │   │   │   └── constants.ts
    │   │   ├── sections
    │   │   │   ├── 10-home
    │   │   │   │   ├── FilteredServicesSection.tsx
    │   │   │   │   ├── SeasonalProjectsCarousel.tsx
    │   │   │   │   └── index.tsx
    │   │   │   ├── 20-about
    │   │   │   │   └── index.tsx
    │   │   │   ├── 30-services
    │   │   │   │   ├── components
    │   │   │   │   │   ├── ServiceCard.tsx
    │   │   │   │   │   ├── ServiceFeature.tsx
    │   │   │   │   │   └── ServiceGrid.tsx
    │   │   │   │   ├── data.ts
    │   │   │   │   ├── detail.tsx
    │   │   │   │   └── index.tsx
    │   │   │   ├── 40-projects
    │   │   │   │   ├── ProjectCard.tsx
    │   │   │   │   ├── ProjectFilter.tsx
    │   │   │   │   ├── ProjectGallery.tsx
    │   │   │   │   ├── ProjectGrid.tsx
    │   │   │   │   ├── ProjectsCarousel.tsx
    │   │   │   │   ├── detail.tsx
    │   │   │   │   └── index.tsx
    │   │   │   ├── 50-testimonials
    │   │   │   │   ├── AverageRating.tsx
    │   │   │   │   ├── Testimonial.tsx
    │   │   │   │   ├── TestimonialFilter.tsx
    │   │   │   │   ├── TestimonialSlider.tsx
    │   │   │   │   ├── TestimonialsSchema.tsx
    │   │   │   │   ├── TestimonialsSection.tsx
    │   │   │   │   ├── index.ts
    │   │   │   │   └── index.tsx
    │   │   │   └── 60-contact
    │   │   │       └── index.tsx
    │   │   ├── styles
    │   │   │   ├── animations.css
    │   │   │   ├── base.css
    │   │   │   └── utilities.css
    │   │   ├── ui
    │   │   │   ├── Form
    │   │   │   │   ├── Input.tsx
    │   │   │   │   ├── Select.tsx
    │   │   │   │   ├── Textarea.tsx
    │   │   │   │   └── index.ts
    │   │   │   ├── Button.tsx
    │   │   │   ├── Card.tsx
    │   │   │   ├── Container.tsx
    │   │   │   ├── ContentGrid.tsx
    │   │   │   ├── Hero.tsx
    │   │   │   ├── Icon.tsx
    │   │   │   ├── Intersection.tsx
    │   │   │   ├── Loading.tsx
    │   │   │   ├── Logo.tsx
    │   │   │   ├── Notifications.tsx
    │   │   │   ├── PageSection.tsx
    │   │   │   ├── SeasonalCTA.tsx
    │   │   │   ├── SectionHeading.tsx
    │   │   │   ├── ServiceAreaList.tsx
    │   │   │   ├── Skeleton.tsx
    │   │   │   ├── Transition.tsx
    │   │   │   └── index.ts
    │   │   ├── index.css
    │   │   ├── lineage.md
    │   │   ├── main.tsx
    │   │   └── vite-env.d.ts
    │   ├── README.md
    │   ├── eslint.config.js
    │   ├── index.html
    │   ├── package.json
    │   ├── tailwind.config.js
    │   ├── tsconfig.json
    │   ├── tsconfig.node.json
    │   ├── vite.config.ts
    │   ├── vite.svg
    │   └── website.dirtree.md
    ├── www
    │   ├── assets
    │   │   ├── index-DFrQxn5i.js
    │   │   ├── index-DvDYalx4.css
    │   │   ├── ui-OiFBRudS.js
    │   │   └── vendor-DfY6ie7V.js
    │   ├── images
    │   │   ├── categorized
    │   │   │   ├── belegg
    │   │   │   ├── ferdigplen
    │   │   │   ├── hekk
    │   │   │   ├── kantstein
    │   │   │   ├── platting
    │   │   │   ├── stål
    │   │   │   ├── støttemur
    │   │   │   ├── trapp-repo
    │   │   ├── site
    │   │   ├── team
    │   │   └── metadata.json
    │   ├── .gitignore
    │   ├── README.md
    │   ├── env.js
    │   ├── index.html
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   ├── version.json
    │   └── vite.svg
    ├── .cursorignore
    ├── .env
    ├── .gitignore
    ├── .gitkeep
    ├── README.md
    ├── RulesForAI.md
    ├── eslint.config.js
    ├── lineage.md
    ├── package-lock.json
    ├── package.json
    ├── postcss.config.js
    ├── rl-website-initial-notes.md
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    └── vite.config.ts
