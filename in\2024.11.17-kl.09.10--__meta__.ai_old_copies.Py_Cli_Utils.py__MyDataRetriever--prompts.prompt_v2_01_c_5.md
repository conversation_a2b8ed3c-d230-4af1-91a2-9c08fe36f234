<!-- =============================================================================

# Urls:
# ===
- "ai-prompt-generator","https://www.feedough.com/ai-prompt-generator/"
- "https://chatgpt.com/g/g-476KmATpZ-gpt-architect/c/673998c1-3e8c-8008-a219-150de6691a35"
- "https://chatgpt.com/g/g-5XtVuRE8Y-prompt-engineer/c/67399ee7-aeac-8008-932f-c1167439cba3"
- "https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/67399ad3-70c0-8008-906d-a2082f5b0733"
- "https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/67399df2-18b4-8008-bcef-bcec6631575d"
- "https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/6739a013-e1b4-8008-b39c-566a5b5fe4d3"
- "https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/6739a12c-79dc-8008-84e3-34a3dff7916a"
- "https://chatgpt.com/g/g-plIy0RAhw-art-snob/c/67399c1a-70b0-8008-9820-02f95bf3d2bf"

============================================================================= -->

You’re a skilled Python developer with extensive experience in restructuring and optimizing scripts for better readability and functionality. You excel in analyzing existing code and applying best practices to ensure it aligns with a specified structure, especially when working with Python scripts that interact with APIs.

Here are the details you should keep in mind while performing the restructuring:
- Ensure that the overall architecture of your script mirrors the modular design used in scripts 1 and 2.
- Pay close attention to the naming conventions and style guidelines present in scripts 1 and 2, such as function names, variable names, and comments.
- Include error handling and logging mechanisms that are consistent with the other scripts.
- Maintain the functionality of script your script while improving its structure and readability.
- Ensure the code is well-structured and only include brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.
- Every script you write should be a foundation worthy of building upon, embodying clarity and intent to eliminate chaos and set the stage for success. By treating your code as a point of departure, you create a structure that others can seamlessly adapt and expand, fostering collaboration and future innovation. This approach transforms your work into a teaching tool—a beacon of best practices and a launchpad for evolution.

Project details:
- Project Name: `py__MyDataRetriever`
- Project Context: `Automate the retrieval and processing of personal online data (e.g., emails, YouTube logs/playlists, chat history, etc) to create a streamlined interface with customizable rule definitions. This subcomponent is vital for a framework that automates personal digital workflows.`

Your task is to restructure your script (chatgpt_fetcher.py) to match the structure of script 1 (gmail_fetcher.py) and script 2 (fetch_youtube_playlists.py). Upon completion, the code should reflect a cohesive design across all three scripts, aligning functionality, structure, and maintainability standards. The scripts will be provided to you after you confirm that you accept the task.

<!-- ======================================================= -->

# gmail_fetcher.py
```python
# Gmail Email Fetcher
# =======================================================
# Fetch emails from a Gmail account using the Gmail API.
# Supports filtering by sender or recipient, saving results JSON files,
# and tracking processed emails to avoid duplicates.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
from pathlib import Path
import re
import json
import argparse
import os
from dotenv import load_dotenv
from email.utils import parseaddr
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
CLIENT_SECRET_FILE = Path(os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json"))
TOKEN_FILE = Path(os.getenv("GOOGLE_TOKEN_FILE", "token.json"))
SCOPES = os.getenv("GOOGLE_SCOPES", "https://www.googleapis.com/auth/gmail.readonly").split()

# Define Directories
# -------------------------------------------------------
DATA_DIR = Path("data")
LOGS_DIR = Path("logs")
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Configure loguru for structured logging
# -------------------------------------------------------
logger.remove()
log_file = LOGS_DIR / "email_fetcher.log"
logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

# Utility Functions
# -------------------------------------------------------
def sanitize_filename_component(component):
    """Replace invalid filename characters with underscores."""
    return re.sub(r'[^A-Za-z0-9_-]', '_', component)

def clean_sender(sender):
    """Standardize the sender format using email.utils.parseaddr."""
    name, email_addr = parseaddr(sender)
    return f"{name.strip()} <{email_addr.strip()}>" if name else email_addr.strip()

def sanitize_filename(base_name, sender=None, recipient=None, suffix="", file_type="json"):
    """Generate a sanitized filename based on provided components."""
    parts = [base_name]
    if sender:
        sender_sanitized = sanitize_filename_component(sender.replace('@', '_').replace('.', '_'))
        parts.append(f"from_{sender_sanitized}")
    if recipient:
        recipient_sanitized = sanitize_filename_component(recipient.replace('@', '_').replace('.', '_'))
        parts.append(f"to_{recipient_sanitized}")
    if suffix:
        suffix_sanitized = sanitize_filename_component(suffix)
        parts.append(suffix_sanitized)
    filename = f"{'_'.join(parts)}.{file_type}"
    return DATA_DIR / filename

# GmailFetcher Class
# -------------------------------------------------------
class GmailFetcher:
    """Fetch and process emails from Gmail."""

    def __init__(self, sender=None, recipient=None, max_results=10):
        self.sender = sender
        self.recipient = recipient
        self.max_results = max_results
        self.creds = self.authenticate()
        self.service = build("gmail", "v1", credentials=self.creds)
        self.query_string = self.build_query()
        self.processed_emails_file = sanitize_filename("emails", sender, recipient, suffix="processed", file_type="txt")
        self.processed_emails = self.load_processed_emails()

    def authenticate(self):
        """Authenticate and obtain Gmail API credentials."""
        creds = None
        if TOKEN_FILE.exists():
            creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
                logger.info("Credentials refreshed.")
            else:
                flow = InstalledAppFlow.from_client_secrets_file(str(CLIENT_SECRET_FILE), SCOPES)
                creds = flow.run_local_server(port=0)
                logger.info("New credentials obtained.")
            TOKEN_FILE.write_text(creds.to_json())
            logger.info("Credentials saved to token file.")
        return creds

    def build_query(self):
        """Construct Gmail API query string based on sender and recipient."""
        query = []
        if self.sender:
            query.append(f"from:{self.sender}")
        if self.recipient:
            query.append(f"to:{self.recipient}")
        return " ".join(query)

    def load_processed_emails(self):
        """Load processed email IDs to avoid duplication."""
        if self.processed_emails_file.exists():
            return set(self.processed_emails_file.read_text().splitlines())
        return set()

    def save_processed_email(self, email_id):
        """Mark an email ID as processed."""
        self.processed_emails_file.parent.mkdir(parents=True, exist_ok=True)
        with self.processed_emails_file.open("a") as f:
            f.write(email_id + "\n")
        self.processed_emails.add(email_id)

    def fetch_message_ids(self, max_results, next_page_token=None):
        """Retrieve message IDs from Gmail API."""
        result = self.service.users().messages().list(
            userId="me",
            q=self.query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        ).execute()
        return result.get("messages", []), result.get("nextPageToken")

    def process_messages(self, messages):
        """Fetch and structure email data from message IDs."""
        emails = []
        for message in messages:
            email_id = message["id"]
            if email_id in self.processed_emails:
                continue
            msg = self.service.users().messages().get(userId="me", id=email_id).execute()
            headers = msg["payload"]["headers"]
            email_data = {
                "id": email_id,
                "date": self.safe_strip(self.get_header_value(headers, "Date")),
                "from": self.safe_strip(clean_sender(self.get_header_value(headers, "From"))),
                "to": self.safe_strip(clean_sender(self.get_header_value(headers, "To"))) if self.get_header_value(headers, "To") else "",
                "subject": self.safe_strip(self.get_header_value(headers, "Subject")),
                "snippet": self.safe_strip(msg.get("snippet", "")),
            }
            emails.append(email_data)
            self.save_processed_email(email_id)
        return emails

    def save_emails(self, emails):
        """Save emails to JSON."""
        if not emails:
            return

        json_file_path = sanitize_filename("emails", self.sender, self.recipient, file_type="json")
        self.append_emails_to_json(emails, json_file_path)

    def append_emails_to_json(self, emails, file_path):
        """Append email data to a JSON file as a single JSON array."""
        existing_emails = []
        if file_path.exists():
            try:
                existing_emails = json.load(file_path.open("r", encoding="utf-8"))
                if not isinstance(existing_emails, list):
                    existing_emails = []
            except json.JSONDecodeError:
                existing_emails = []
        existing_emails.extend(emails)
        with file_path.open("w", encoding="utf-8") as file:
            json.dump(existing_emails, file, ensure_ascii=False, indent=4)

    def safe_strip(self, value):
        """Strip whitespace from a string, return empty string if None."""
        return value.strip() if value else ""

    def get_header_value(self, headers, name):
        """Extract the value of a specific email header."""
        return next((header["value"] for header in headers if header["name"].lower() == name.lower()), None)

    def fetch_emails(self):
        """Main method to fetch and save emails."""
        total_fetched = 0
        next_page_token = None

        with tqdm(total=self.max_results, desc="Fetching Emails", unit="email") as pbar:
            while total_fetched < self.max_results:
                messages, next_page_token = self.fetch_message_ids(
                    max_results=self.max_results - total_fetched,
                    next_page_token=next_page_token
                )
                if not messages:
                    logger.info(f"No more emails found matching query: {self.query_string}")
                    break
                emails = self.process_messages(messages)
                self.save_emails(emails)
                fetched = len(emails)
                total_fetched += fetched
                logger.info(f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}")
                pbar.update(fetched)
                if not next_page_token:
                    break
        logger.success(f"Finished fetching {total_fetched} emails.")

# Command-Line Interface Enhancements
# -------------------------------------------------------
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Fetch emails from Gmail using the Gmail API.",
        epilog="Example: python email_fetcher.py --sender <EMAIL> --recipient <EMAIL> --max_results 50"
    )
    parser.add_argument(
        "--sender",
        type=str,
        help="Specify the sender email address to filter."
    )
    parser.add_argument(
        "--recipient",
        type=str,
        help="Specify the recipient email address to filter."
    )
    parser.add_argument(
        "--max_results",
        type=int,
        default=10,
        help="Specify the maximum number of emails to fetch (default: 10)."
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default="data",
        help="Specify the directory to save data files (default: data)."
    )
    parser.add_argument(
        "--logs_dir",
        type=str,
        default="logs",
        help="Specify the directory to save log files (default: logs)."
    )
    return parser.parse_args()

# Main Execution Flow
# -------------------------------------------------------
def main():
    """Execute the email fetching process based on CLI arguments."""
    args = parse_arguments()

    # Update directories based on arguments
    data_dir = Path(args.data_dir)
    logs_dir = Path(args.logs_dir)
    data_dir.mkdir(parents=True, exist_ok=True)
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Reconfigure logger to use the specified logs directory
    global logger
    logger.remove()
    log_file = logs_dir / "email_fetcher.log"
    logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

    fetcher = GmailFetcher(
        sender=args.sender,
        recipient=args.recipient,
        max_results=args.max_results,
    )
    fetcher.fetch_emails()

if __name__ == "__main__":
    main()

```

# fetch_youtube_playlists.py
```python
# YouTube Playlists Fetcher
# =======================================================
# Fetch YouTube playlist data using the YouTube Data API.
# Supports listing playlists, fetching videos from a specific playlist,
# saving results to JSON files, and tracking processed playlists.
# Includes special playlists: "Watch Later" and "Liked Videos".
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
from pathlib import Path
import re
import json
import argparse
import os
from dotenv import load_dotenv
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
CLIENT_SECRET_FILE = Path(os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json"))
TOKEN_FILE = Path(os.getenv("GOOGLE_TOKEN_FILE", "token.json"))
SCOPES = os.getenv("YOUTUBE_SCOPES", "https://www.googleapis.com/auth/youtube.readonly").split()

# Define Directories
# -------------------------------------------------------
DATA_DIR = Path("data")
LOGS_DIR = Path("logs")
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Configure loguru for structured logging
# -------------------------------------------------------
logger.remove()
log_file = LOGS_DIR / "youtube_fetcher.log"
logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

# Utility Functions
# -------------------------------------------------------
def sanitize_filename_component(component):
    """Replace invalid filename characters with underscores."""
    return re.sub(r'[^A-Za-z0-9_-]', '_', component)

def sanitize_filename(base_name, identifier=None, suffix="", file_type="json"):
    """Generate a sanitized filename based on provided components."""
    parts = [base_name]
    if identifier:
        identifier_sanitized = sanitize_filename_component(identifier)
        parts.append(identifier_sanitized)
    if suffix:
        suffix_sanitized = sanitize_filename_component(suffix)
        parts.append(suffix_sanitized)
    filename = f"{'_'.join(parts)}.{file_type}"
    return DATA_DIR / filename

# YouTubeFetcher Class
# -------------------------------------------------------
class YouTubeFetcher:
    """Fetch and process YouTube playlist and video data."""

    def __init__(self, playlist_id=None, include_unlisted=False):
        self.playlist_id = playlist_id
        self.include_unlisted = include_unlisted
        self.creds = self.authenticate()
        self.service = build("youtube", "v3", credentials=self.creds)
        self.processed_playlists_file = sanitize_filename("playlists_processed", identifier=None, suffix="", file_type="txt")
        self.processed_playlists = self.load_processed_playlists()
        self.playlist_title = None  # Initialize playlist title

    def authenticate(self):
        """Authenticate and obtain YouTube API credentials."""
        creds = None
        if TOKEN_FILE.exists():
            creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
                logger.info("Credentials refreshed.")
            else:
                flow = InstalledAppFlow.from_client_secrets_file(str(CLIENT_SECRET_FILE), SCOPES)
                creds = flow.run_local_server(port=0)
                logger.info("New credentials obtained.")
            TOKEN_FILE.write_text(creds.to_json())
            logger.info("Credentials saved to token file.")
        return creds

    def load_processed_playlists(self):
        """Load processed playlist IDs to avoid duplication."""
        if self.processed_playlists_file.exists():
            return set(self.processed_playlists_file.read_text().splitlines())
        return set()

    def save_processed_playlist(self, playlist_id):
        """Mark a playlist ID as processed."""
        self.processed_playlists_file.parent.mkdir(parents=True, exist_ok=True)
        with self.processed_playlists_file.open("a") as f:
            f.write(playlist_id + "\n")
        self.processed_playlists.add(playlist_id)

    def get_special_playlists(self):
        """Retrieve 'Watch Later' and 'Liked Videos' playlists."""
        special_playlists = []
        response = self.service.channels().list(
            part="contentDetails",
            mine=True,
            # maxResults=1
        ).execute()
        items = response.get("items", [])
        if items:
            related_playlists = items[0].get("contentDetails", {}).get("relatedPlaylists", {})
            watch_later_id = related_playlists.get("watchLater")
            likes_id = related_playlists.get("likes")

            if watch_later_id:
                special_playlists.append({
                    "id": watch_later_id,
                    "snippet": {"title": "Watch Later"},
                    "contentDetails": {}
                })
            if likes_id:
                special_playlists.append({
                    "id": likes_id,
                    "snippet": {"title": "Liked Videos"},
                    "contentDetails": {}
                })
        return special_playlists


    # def get_special_playlists(self):
    #     """Retrieve 'Watch Later' and 'Liked Videos' playlists."""
    #     special_playlists = []
    #     # Directly use the known playlist IDs
    #     watch_later_id = 'WL'
    #     likes_id = 'LL'

    #     special_playlists.append({
    #         "id": watch_later_id,
    #         "snippet": {"title": "Watch Later"},
    #         "contentDetails": {}
    #     })
    #     special_playlists.append({
    #         "id": likes_id,
    #         "snippet": {"title": "Liked Videos"},
    #         "contentDetails": {}
    #     })

    #     return special_playlists


    def list_playlists(self):
        """List all playlists of the authenticated user, including 'Watch Later' and 'Liked Videos'."""
        playlists = []
        # Get regular playlists
        request = self.service.playlists().list(
            part="snippet,contentDetails",
            mine=True,
            # maxResults=50
        )
        while request:
            response = request.execute()
            playlists.extend(response.get("items", []))
            request = self.service.playlists().list_next(request, response)

        # Get special playlists
        special_playlists = self.get_special_playlists()
        playlists.extend(special_playlists)

        return playlists

    def fetch_videos_from_playlist(self):
        """Fetch videos from a specific playlist."""
        videos = []
        request = self.service.playlistItems().list(
            part="snippet,contentDetails",
            playlistId=self.playlist_id,
            # maxResults=50
        )
        while request:
            response = request.execute()
            videos.extend(response.get("items", []))
            request = self.service.playlistItems().list_next(request, response)
        return videos

    def save_videos(self, videos):
        """Save videos to JSON."""
        if not videos:
            return

        json_file_path = sanitize_filename("youtube_videos", identifier=self.playlist_title, file_type="json")
        self.append_videos_to_json(videos, json_file_path)

    def append_videos_to_json(self, videos, file_path):
        """Append video data to a JSON file as part of a JSON array."""
        video_entries = []

        for video in videos:
            snippet = video.get("snippet", {})
            thumbnails = snippet.get("thumbnails", {})

            # Attempt to retrieve available thumbnail URLs
            thumbnail_url = (
                thumbnails.get("default", {}).get("url") or
                thumbnails.get("medium", {}).get("url") or
                thumbnails.get("high", {}).get("url") or
                ""
            )

            video_data = {
                "video_id": video.get("contentDetails", {}).get("videoId", ""),
                "title": snippet.get("title", ""),
                "description": snippet.get("description", ""),
                "published_at": snippet.get("publishedAt", ""),
                "thumbnail_url": thumbnail_url,
            }
            video_entries.append(video_data)

        if not file_path.exists():
            # Create a new JSON array with the video entries
            with file_path.open(mode="w", encoding="utf-8") as file:
                json.dump(video_entries, file, ensure_ascii=False, indent=4)
        else:
            # Load existing data, append new videos, and save back as a JSON array
            with file_path.open(mode="r+", encoding="utf-8") as file:
                try:
                    existing_videos = json.load(file)
                    if not isinstance(existing_videos, list):
                        existing_videos = []
                except json.JSONDecodeError:
                    existing_videos = []

                existing_videos.extend(video_entries)
                file.seek(0)
                json.dump(existing_videos, file, ensure_ascii=False, indent=4)
                file.truncate()

    def safe_strip(self, value):
        """Strip whitespace from a string, return empty string if None."""
        return value.strip() if value else ""

    def fetch_playlists(self):
        """Fetch and save all playlists."""
        playlists = self.list_playlists()
        logger.info(f"Found {len(playlists)} playlists.")
        for playlist in tqdm(playlists, desc="Processing Playlists"):
            playlist_id = playlist["id"]
            if playlist_id in self.processed_playlists:
                continue
            self.save_processed_playlist(playlist_id)
            self.playlist_id = playlist_id
            self.playlist_title = playlist.get("snippet", {}).get("title", f"Playlist_{playlist_id}")
            videos = self.fetch_videos_from_playlist()
            self.save_videos(videos)
            logger.info(f"Processed playlist '{self.playlist_title}' (ID: {playlist_id}) with {len(videos)} videos.")

    def fetch_specific_playlist(self):
        """Fetch and save videos from a specific playlist."""
        if self.playlist_id in self.processed_playlists:
            logger.info(f"Playlist ID {self.playlist_id} has already been processed.")
            return
        self.save_processed_playlist(self.playlist_id)
        self.playlist_title = self.get_playlist_title(self.playlist_id)
        videos = self.fetch_videos_from_playlist()
        self.save_videos(videos)
        logger.info(f"Processed playlist '{self.playlist_title}' (ID: {self.playlist_id}) with {len(videos)} videos.")

# Command-Line Interface Enhancements
# -------------------------------------------------------
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Fetch data from YouTube playlists",
        epilog="Example: python fetch_youtube_playlists.py --list"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List your playlists"
    )
    parser.add_argument(
        "--playlist-id",
        type=str,
        help="YouTube playlist ID to fetch"
    )
    parser.add_argument(
        "--include-unlisted",
        action="store_true",
        help="Include unlisted videos"
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default="data",
        help="Specify the directory to save data files (default: data)."
    )
    parser.add_argument(
        "--logs_dir",
        type=str,
        default="logs",
        help="Specify the directory to save log files (default: logs)."
    )
    return parser.parse_args()

# Main Execution Flow
# -------------------------------------------------------
def main():
    """Execute the YouTube playlist fetching process based on CLI arguments."""
    args = parse_arguments()

    # Update directories based on arguments
    data_dir = Path(args.data_dir)
    logs_dir = Path(args.logs_dir)
    data_dir.mkdir(parents=True, exist_ok=True)
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Reconfigure logger to use the specified logs directory
    global logger
    logger.remove()
    log_file = logs_dir / "youtube_fetcher.log"
    logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

    fetcher = YouTubeFetcher(
        playlist_id=args.playlist_id,
        include_unlisted=args.include_unlisted,
    )

    if args.list:
        playlists = fetcher.list_playlists()
        print("Your Playlists:")
        for playlist in playlists:
            print(f"ID: {playlist['id']} | Title: {playlist['snippet']['title']}")
    elif args.playlist_id:
        fetcher.fetch_specific_playlist()
    else:
        fetcher.fetch_playlists()

if __name__ == "__main__":
    main()

```

# chatgpt_fetcher.py
```python
# ChatGPT Conversation Fetcher
# =======================================================
# Fetch conversation history from OpenAI's ChatGPT.
# Organize conversations into JSON files, track processed
# conversations to avoid duplicates, and enable CLI customization.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
from pathlib import Path
import os
import json
import argparse
from dotenv import load_dotenv
import openai

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
openai.api_key = OPENAI_API_KEY

# Define Directories
# -------------------------------------------------------
DATA_DIR = Path("data")
LOGS_DIR = Path("logs")
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Configure loguru for structured logging
# -------------------------------------------------------
logger.remove()
log_file = LOGS_DIR / "chatgpt_fetcher.log"
logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

# Utility Functions
# -------------------------------------------------------
def sanitize_filename_component(component):
    """Replace invalid filename characters with underscores."""
    return re.sub(r'[^A-Za-z0-9_-]', '_', component)

def sanitize_filename(base_name, identifier=None, suffix="", file_type="json"):
    """Generate a sanitized filename based on provided components."""
    parts = [base_name]
    if identifier:
        identifier_sanitized = sanitize_filename_component(identifier)
        parts.append(identifier_sanitized)
    if suffix:
        suffix_sanitized = sanitize_filename_component(suffix)
        parts.append(suffix_sanitized)
    filename = f"{'_'.join(parts)}.{file_type}"
    return DATA_DIR / filename


# ChatGptFetcher Class
# -------------------------------------------------------
class ChatGPTFetcher:
    """Fetch and process conversation history from ChatGPT."""

    def __init__(self, max_results=10):
        self.max_results = max_results
        self.processed_conversations_file = DATA_DIR / "conversations_processed.txt"
        self.processed_conversations = self.load_processed_conversations()

    def load_processed_conversations(self):
        """Load processed conversation IDs."""
        if self.processed_conversations_file.exists():
            return set(self.processed_conversations_file.read_text().splitlines())
        return set()

    def save_processed_conversation(self, conversation_id):
        """Mark a conversation ID as processed."""
        with self.processed_conversations_file.open("a") as f:
            f.write(conversation_id + "\n")
        self.processed_conversations.add(conversation_id)

    def fetch_conversations(self):
        """Fetch conversations from ChatGPT."""
        conversations = []
        try:
            response = openai.ChatCompletion.list(limit=self.max_results)
            for conversation in response.get("data", []):
                conversation_id = conversation.get("id")
                if conversation_id in self.processed_conversations:
                    continue
                conversations.append({
                    "id": conversation_id,
                    "timestamp": conversation.get("created"),
                    "content": conversation.get("messages", []),
                })
                self.save_processed_conversation(conversation_id)
        except Exception as e:
            logger.error(f"Failed to fetch conversations: {e}")
        return conversations

    def save_conversations(self, conversations):
        """Save conversations to JSON."""
        json_file_path = DATA_DIR / "conversations.json"
        existing_data = []

        if json_file_path.exists():
            try:
                existing_data = json.load(json_file_path.open("r"))
            except json.JSONDecodeError:
                pass

        existing_data.extend(conversations)
        with json_file_path.open("w") as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=4)

    def fetch_conversations(self):
        """Main method to fetch and save conversations."""
        conversations = self.fetch_conversations()
        self.save_conversations(conversations)
        logger.success(f"Saved {len(conversations)} new conversations.")

# Command-Line Interface
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Fetch ChatGPT conversation history.",
        epilog="Example: "
    )
    parser.add_argument(
        "--max_results",
        type=int,
        default=10,
        help="Specify the maximum number of conversations to fetch (default: 10)."
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default="data",
        help="Specify the directory to save data files (default: data)."
    )
    parser.add_argument(
        "--logs_dir",
        type=str,
        default="logs",
        help="Specify the directory to save log files (default: logs)."
    )


    return parser.parse_args()


# Main Execution Flow
# -------------------------------------------------------
def main():
    """Execute the conversation fetching process based on CLI arguments."""
    args = parse_arguments()

    # Update directories based on arguments
    data_dir = Path(args.data_dir)
    logs_dir = Path(args.logs_dir)
    data_dir.mkdir(parents=True, exist_ok=True)
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Reconfigure logger to use the specified logs directory
    global logger
    logger.remove()
    log_file = logs_dir / "conversation_fetcher.log"
    logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)


    fetcher = ChatGPTFetcher(
        max_results=args.max_results
    )
    fetcher.fetch_conversations()

if __name__ == "__main__":
    main()

```
