
Instead of:

    ```markdown
        ---
        name: nextjs-best-practices.mdc
        description: Best practices for Next.js applications and routing
        globs: **/*.{ts,tsx}
        ---

        - Use the App Router for better performance and improved data fetching
        - Implement proper error boundaries to handle runtime errors gracefully
        - Utilize Next.js built-in optimizations like code splitting and image optimization
        - Leverage server-side rendering and static site generation where appropriate
        - Use Next.js API routes for serverless functions and backend logic

        ---
        name: typescript-best-practices.mdc
        description: TypeScript coding standards and type safety guidelines
        globs: **/*.{ts,tsx}
        ---

        - Enable strict mode in your tsconfig.json for enhanced type checking
        - Use interfaces for object shapes and types for union types or type aliases
        - Leverage type guards and assertions to narrow types in conditional blocks
        - Implement generics for reusable components and functions
        - Use readonly properties and const assertions to enforce immutability

        ---
        name: tailwindcss-best-practices.mdc
        description: Best practices for using Tailwind CSS in your project
        globs: **/*.{ts,tsx,css}
        ---

        - Use utility-first classes for rapid development and consistent styling
        - Create custom components with `@apply` directive for reusability
        - Utilize responsive design utilities for mobile-first development
        - Leverage Tailwind's dark mode support for easy theming
        - Use the JIT (Just-In-Time) mode for optimized CSS output

        ---
        name: shadcn-ui-best-practices.mdc
        description: Guidelines for using Shadcn UI components in your project
        globs: **/*.{ts,tsx}
        ---

        - Follow the component structure and naming conventions provided by Shadcn UI
        - Customize components using the provided props and slots
        - Utilize the built-in dark mode support for consistent theming
        - Leverage the responsive design features of Shadcn UI components
        - Use the provided TypeScript types for type-safe component usage

        ---
        name: react-query-best-practices.mdc
        description: Best practices for managing data fetching with React Query
        globs: **/*.{ts,tsx}
        ---

        - Use `useQuery` for fetching data and `useMutation` for creating/updating/deleting data
        - Implement proper error handling and loading states
        - Utilize query keys effectively for caching and refetching
        - Leverage the `staleTime` and `cacheTime` options for optimized data fetching
        - Use `queryClient` to manage global query state and invalidate queries when needed

        ---
        name: zod-best-practices.mdc
        description: Best practices for using Zod for runtime type checking and validation
        globs: **/*.{ts,tsx}
        ---

        - Define schemas using Zod's intuitive API for type-safe data validation
        - Use Zod's inference feature to generate TypeScript types from schemas
        - Implement custom validation logic using Zod's `refine` and `superRefine` methods
        - Utilize Zod's built-in parsing and coercion features for robust data handling
        - Leverage Zod's integration with other libraries like React Hook Form for seamless form validation
    ```

Couldn't we instead incorporate "memory-bank" to yeld much greater value (especially with regards to high-value and consistent outputs)? Example (*reference only, you need to translate optimally into system_message instructions of autonomous coding assistants*):

    ```markdown
        # Memory Bank System Core Template

        > **[Structural Role Reminder]**: This file defines the fundamental principles and structure of the Memory Bank system, serving as the anchor for all other templates and implementations.

        ## Core Generative Principle

        The Memory Bank operates by an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression:

        - Each structure, detail, or insight is not preserved for itself, but interrogated for its irreducible essence
        - All elements are recursively re-anchored and distilled until the system becomes its own map, memory, and purpose
        - The dynamic is a pulsing metabolic flow:
          - Accumulation triggers pressure
          - Pressure demands pruning
          - Pruning yields emergent lucidity
        - The whole is reconstituted from its root with each movement
        - 'Structure' and 'meaning' converge
        - Entropy cannot persist
        - Every addition must pay its way by sharpening the axis of meaning

        > **Memory Bank = Progressive, self-reinforcing abstraction-layering rooted in project purpose.**

        ## Absolute Requirements

        | Principle | Implementation |
        | :-------- | :------------- |
        | File-Structure-First | Always first generate/validate a cognitive map *as a filestructure*, dynamically, based on what exists. |
        | Root-First Thinking | Every insight must relate *upward*, toward the project's irreducible mission. |
        | Persistent Simplification | Every action must aim to **reduce** net complexity (unless a rare, justified exception). |
        | Value Extraction Bias | Prefer "what maximizes actionable, durable insight" instead of unfolding more detail. |
        | Guardrails | Never mutate structure blindly — every structural change must be **explicitly justified** in the memory. |
        | Outward Mapping | Think *outward* (root → abstraction layers → concrete implementation), **never inward folding** (which causes sprawl). |

        ## Memory Bank Structure

        ```plaintext
        memory-bank/
        ├── 0-distilledContext.md         # Ultra-compressed project core abstraction
        ├── 1-projectbrief.md             # Root purpose: Why the project must exist
        ├── 2-productContext.md           # External reality: users, needs, outcomes
        ├── 3-systemPatterns.md           # Systems: component hierarchy, flows, state design
        ├── 4-techContext.md              # Stack-level technical constraints
        ├── 5-structureMap.md             # Actual and target filestructure mapping
        ├── 6-activeContext.md            # In-progress actions, bottlenecks, and simplifications
        ├── 7-progress.md                 # Assimilation milestones, simplifications achieved
        ├── 8-tasks.md                    # Active structure-guided tasks
        ├── drift-monitor.md              # Structural integrity monitoring
        ├── simplification-candidates.md  # High-impact simplification tracking
        └── lineage/                      # Cognitive evolution snapshots
        ```

        ### Core Required Files

        | File | Role in Cognitive Architecture |
        | :--- | :----------------------------- |
        | `0-distilledContext.md` | **Immediate Root Re-anchoring**: Crystallized project essence & goals in 2-3 bullets. |
        | `1-projectbrief.md` | **The Root Abstraction**: Project's irreducible mission, value prop, critical constraints. The source. |
        | `2-productContext.md` | **Why - Value Context**: Problems solved, users, operational context. Justifies features against the Root. |
        | `3-systemPatterns.md` | **How - Architectural Form**: Target structure, component patterns, state/data flow. Blueprint for order. |
        | `4-techContext.md` | **With What - Technical Constraints**: Stack, libraries, performance/accessibility boundaries. |
        | `5-structureMap.md` | **Current vs. Target Structure**: File structure mapping with migration path and justifications. |
        | `6-activeContext.md` | **Current Focus**: Active focus, consolidation analysis, decisions, bottlenecks. |
        | `7-progress.md` | **Status & Entropy Check**: Milestones, metrics, tech debt ledger, structural integrity validation. |
        | `8-tasks.md` | **Actionable Interventions**: Concrete, structure-anchored tasks. Must trace lineage to root goal. |

        ### Optional Cognitive Enhancements

        | File | Role in Cognitive Architecture |
        | :--- | :----------------------------- |
        | `drift-monitor.md` | **Structural Integrity Monitor**: Identifies and tracks structural drift, enforces role adherence. |
        | `simplification-candidates.md` | **Complexity Reduction Tracking**: Formalizes identification and implementation of high-impact simplifications. |
        | `/lineage` | **Cognitive Evolution Tracking**: Chronicles significant shifts in project understanding or structure. |

        ## Cognitive Abstraction Layers

        ```mermaid
        flowchart TD
            Root[0-1: Root Abstraction] --> Value[2: Value Context]
            Root --> Architecture[3: Architectural Form]
            Root --> Technical[4: Technical Constraints]
            Value & Architecture & Technical --> Structure[5: Structure Mapping]
            Structure --> Active[6: Active Context]
            Active --> Progress[7: Progress Tracking]
            Progress --> Tasks[8: Actionable Tasks]

            class Root primary
            class Value,Architecture,Technical secondary
            class Structure,Active tertiary
            class Progress,Tasks quaternary

            classDef primary fill:#f9f,stroke:#333,stroke-width:2px
            classDef secondary fill:#bbf,stroke:#333,stroke-width:1px
            classDef tertiary fill:#ddf,stroke:#333,stroke-width:1px
            classDef quaternary fill:#eff,stroke:#333,stroke-width:1px
        ```

        ## Structural Guardrails

        ### System Laws

        | Law | Enforcement |
        | :-- | :---------- |
        | 📜 **Memory Bank Is Living** | It self-prunes. It strengthens by collapsing excess back into the root. |
        | 📜 **Structure Is Intelligence** | Assimilation power arises not from detail captured, but from **structural clarity imposed**. |
        | 📜 **Value ≠ Volume** | Real value is extracted via deliberate constraint – **"enough, but no more."** |
        | 📜 **Root Fidelity is Absolute** | Every element must trace its lineage back to the irreducible purpose (`1`) or be dissolved. |

        ### Operational Guardrails

        | Situation | Mandate |
        | :-------- | :------ |
        | Find Complex Detail | Compress into higher abstraction, no passive notes. |
        | Need New File | Only post-compression attempt + explicit justification. |
        | Detect Drift | Refactor/re-anchor immediately. |
        | Discover Random Insight | Discard unless anchored outward to root. |
        | Unsure where an insight fits | **Halt.** Re-evaluate structure (`3`, `5/6`) before proceeding. Do not force fit. |

        ## Responsibility Matrix

        | File | When to Update | What to Include | What to Exclude |
        | :--- | :------------- | :-------------- | :-------------- |
        | `0-distilledContext.md` | When project mission changes | Core purpose, value, constraint in 2-3 bullets | Implementation details, process notes |
        | `1-projectbrief.md` | When fundamental purpose/constraints change | Mission, value proposition, key constraints | Low-level details, implementation specifics |
        | `2-productContext.md` | When user needs or market context evolves | User profiles, problems solved, external factors | Technical solutions, implementation approaches |
        | `3-systemPatterns.md` | When architecture patterns evolve | Component hierarchy, data flows, patterns | Instance details, temporary solutions |
        | `4-techContext.md` | When stack/constraints change | Technologies, constraints, integration points | Business logic, pattern rationales |
        | `5-structureMap.md` | When target or current structure changes | Current vs. target structure, migration path | Implementation details, low-level tasks |
        | `6-activeContext.md` | Daily or with each work session | Current focus, bottlenecks, in-progress simplifications | Historical notes, completed work (→ 7) |
        | `7-progress.md` | After significant milestones | Status, milestones, simplifications achieved | Current work status (→ 6), detailed plans (→ 8) |
        | `8-tasks.md` | When planning action | Structure-anchored tasks, clear acceptance criteria | Vague goals, non-traced tasks, historical notes |
        | `drift-monitor.md` | After Memory Bank updates | File purpose vs. usage, drift patterns, corrections | Implementation details, extended rationales |
        | `simplification-candidates.md` | With each development cycle | Simplification opportunities, scoring, implementation tracking | Detailed implementation plans (→ 8) |
        | `/lineage` | After significant cognitive shifts | Context, prior understanding, shift, structural impact | Current working notes (→ 6), active tasks (→ 8) |

        ## Update Workflow

        ```mermaid
        flowchart TD
            Start[Start Work Session] --> Read0[Read 0-distilledContext.md]
            Read0 --> Read6[Read 6-activeContext.md]
            Read6 --> WorkOnTask[Work on Task from 8-tasks.md]
            WorkOnTask --> IdentifySimplifications[Identify Simplification Opportunities]
            IdentifySimplifications --> UpdateActiveContext[Update 6-activeContext.md]
            UpdateActiveContext --> TaskComplete{Task Complete?}
            TaskComplete -->|Yes| UpdateProgress[Update 7-progress.md]
            UpdateProgress --> UpdateTasks[Update 8-tasks.md]
            TaskComplete -->|No| End[End Work Session]
            UpdateTasks --> CheckDrift[Check drift-monitor.md]
            CheckDrift --> SimplifyCandidates[Update simplification-candidates.md]
            SimplifyCandidates --> End
        ```

        ## Compression Reflex Protocol

        Before adding new content to any Memory Bank file:

        1. **Merge Check**: Can this be merged with existing content? If yes, merge instead of adding.
        2. **Elevation Check**: Can this be elevated to a pattern in a higher abstraction file? If yes, elevate instead of adding.
        3. **Dissolution Check**: Can this be dissolved as an instance of an existing pattern? If yes, reference pattern instead of adding.
        4. **Root Connection Check**: Does this clearly connect to the project's root purpose? If no, reconsider its value.
        5. **Justification**: If still adding, explicitly justify why this content cannot be compressed further.

        ## Validation & Sanction Checklists

        **Sanction after every cycle:**

        | Check | Pass Condition |
        | :---- | :------------- |
        | Root Purpose Confirmed | `1-projectbrief.md` accurate. |
        | Structure Integrity | Files justified, scoped, non-redundant. |
        | Compression Reflex Applied | No uncompressed additions. |
        | High-Impact Simplification Logged | At least 1 per cycle (`7-progress.md`). |
        | Context Traceability | No orphan entries; all map to `1`. |
        | Lineage Snapshots (if used) | Cognitive epochs captured. |

        ✅ Proceed if ALL conditions pass.
        ❌ Otherwise, halt assimilation → repair structure.

        ## Final Directive

        **Root Fidelity Above All**

        > "If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure — it must not be taken."

        **Cognitive Command Summary**

        > "Assimilate only via compression, traceable structure, and operational clarity.
        > No drift, no redundancy, no passive entropy. Memory Bank *is* the project intelligence."

        **Meta-Bias Reminder**

        > "Identify single most critical aspect for maximizing overall value, ensuring maximum clarity, utility, and adaptability without diminishing potential yield."

        This meta-bias must shape every **scan**, **map**, **act**, **document**.
    ```
