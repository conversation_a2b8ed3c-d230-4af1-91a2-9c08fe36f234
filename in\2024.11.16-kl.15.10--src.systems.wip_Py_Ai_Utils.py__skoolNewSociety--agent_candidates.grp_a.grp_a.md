# Project Files Documentation for `grp_a`

### File Structure

```
├── 005_prompt_chaining_5_c.py
├── 005_prompt_chaining_8_h.py
```
### 1. `005_prompt_chaining_5_c.py`

#### `005_prompt_chaining_5_c.py`

```python
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# Define Blueprint creation with mode-based customization
def create_guiding_blueprint(initial_input: str, mode: str) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users."
    }
    mode_description = mode_descriptions.get(mode, "defaulting to a general response focus.")

    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Enhance clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n"
            f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# Define grouped agent chains based on mode
def get_refinement_chain(mode: str) -> Dict[str, List[Dict[str, str]]]:
    chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint with topic, objective, and structure based on the input."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": f"Rephrase the data as a comprehensive question, focusing on '{mode}'."},
            {"role": "Objective Clarifier", "prompt": f"Define the primary objective based on the Blueprint, aligned with '{mode}'."},
            {"role": "Topic Alignment", "prompt": "Review and realign with the original topic and Blueprint if needed."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": f"Add relevant background for enriched response accuracy, with '{mode}' as focus."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint."},
            {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint and topic before further development."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback."},
            {"role": "Topic Alignment", "prompt": "Ensure content remains aligned with the original topic and Blueprint."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability."},
            {"role": "Topic Consistency Tracker", "prompt": "Ensure content aligns with the original question and topic."}
        ],
        "Final Optimization": [
            {"role": "Performance Optimizer", "prompt": "Optimize for efficiency without sacrificing clarity."},
            {"role": "Documentation Curator", "prompt": "Provide clear documentation focusing on usability."},
            {"role": "Quality Assessor", "prompt": "Perform a final quality check, allowing early exit if quality is sufficient."},
            {"role": "Final Topic Consistency Checker", "prompt": "Ensure the response aligns fully with the original topic and Blueprint."}
        ]
    }

    # Modify chain activation based on mode
    if mode == "Prompt Generation":
        # Emphasize clarity, conciseness, and immediate relevance
        chain["Prompt Reformulation"].append({"role": "Conciseness Optimizer", "prompt": "Ensure prompt is concise and impactful."})
    elif mode == "Content Creation":
        # Emphasize detailed structuring and background information
        chain["Content Development"].append({"role": "Detail Enhancer", "prompt": "Add necessary details to support a thorough response."})
    elif mode == "User Guidance":
        # Emphasize user-centered clarity and step-by-step guidance
        chain["User Experience and Readability"].append({"role": "Guidance Clarity Agent", "prompt": "Ensure instructions are clear and actionable for end-users."})

    return chain

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

# Categorize the prompt and select relevant agent chains
def categorize_prompt(prompt: str) -> List[str]:
    categories = []
    if any(keyword in prompt.lower() for keyword in ["clarity", "coherence", "reformulation"]):
        categories.append("Prompt Reformulation")
    if any(keyword in prompt.lower() for keyword in ["background", "content development", "context"]):
        categories.append("Content Development")
    if any(keyword in prompt.lower() for keyword in ["quality assurance", "standards", "error handling"]):
        categories.append("Quality Assurance")
    if any(keyword in prompt.lower() for keyword in ["user experience", "readability"]):
        categories.append("User Experience and Readability")
    if any(keyword in prompt.lower() for keyword in ["optimization", "performance"]):
        categories.append("Final Optimization")
    return categories if categories else ["Blueprint Creation"]

def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, iteration: int) -> str:
    re_anchor_message = f"{blueprint}\n\n" if iteration % 3 == 0 else ""
    prompt = f"{agent['prompt']}\n\nTopic Context from Blueprint:\n{blueprint}\n\n{re_anchor_message}Current Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response

def apply_agent_chain(blueprint: str, initial_input: str, mode: str) -> str:
    current_response = initial_input
    refinement_chain = get_refinement_chain(mode)
    selected_categories = categorize_prompt(initial_input)

    logging.info(f"Selected categories based on prompt: {selected_categories}")

    for category in selected_categories:
        logging.info(f"Starting group: {category}")
        agents = refinement_chain.get(category, [])
        for i, agent in enumerate(agents):
            current_response = apply_agent(agent, blueprint, current_response, i)
            if category == "Final Optimization" and i + 1 >= 5 and assess_response_quality(current_response):
                logging.info("Early exit after quality criteria met.")
                return current_response
    return current_response

def main():
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    mode = "Prompt Generation"  # User-selected mode
    blueprint = create_guiding_blueprint(initial_input, mode)

    logging.info(f"Starting process in '{mode}' mode...")
    final_output = apply_agent_chain(blueprint, initial_input, mode)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
### 2. `005_prompt_chaining_8_h.py`

#### `005_prompt_chaining_8_h.py`

```python
import os
import logging
import asyncio
import json
import time
from typing import List, Dict, Optional, Any, Union
import numpy as np
from openai import OpenAI

# Set up logging configuration to write to a file with timestamps
logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Initialize OpenAI Client
def init_openai_client() -> Optional[OpenAI]:
    """Initializes the OpenAI client using the API key from environment variables."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception:
        logging.error("Failed to initialize OpenAI client")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# --- Memory Manager Class ---
class MemoryManager:
    """Manages the storage and retrieval of text embeddings for contextual memory."""

    def __init__(self):
        self.memory_store = []

    def add_memory(self, text: str, embedding: np.ndarray):
        """Adds a text and its embedding to the memory store."""
        self.memory_store.append({"text": text, "embedding": embedding})

    def retrieve_relevant_context(self, query: str, top_k: int = 3) -> List[str]:
        """Retrieves relevant context texts based on similarity to the query."""
        query_embedding = self.get_embedding(query)
        similarities = [
            (self.cosine_similarity(query_embedding, mem["embedding"]), mem["text"])
            for mem in self.memory_store
        ]
        similarities.sort(reverse=True, key=lambda x: x[0])
        return [text for _, text in similarities[:top_k]]

    def get_embedding(self, text: str) -> np.ndarray:
        """Generates an embedding for the given text."""
        try:
            response = client.embeddings.create(input=text, model="text-embedding-ada-002")
            return np.array(response.data[0].embedding)
        except Exception as e:
            logging.error(f"Embedding generation failed: {e}")
            if self.memory_store:
                logging.info("Using average of existing embeddings as fallback.")
                embeddings = [mem["embedding"] for mem in self.memory_store]
                return np.mean(embeddings, axis=0)
            return np.zeros(1536)

    @staticmethod
    def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
        """Calculates the cosine similarity between two embeddings."""
        if np.linalg.norm(a) == 0 or np.linalg.norm(b) == 0:
            return 0.0
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

# --- Blueprint Manager Class ---
class BlueprintManager:
    """Manages the creation of the guiding blueprint for the refinement process."""

    def create_blueprint(self, initial_input: str, mode: str, desired_length: str = None, desired_format: str = None) -> str:
        """Creates a blueprint based on the initial input and user preferences."""
        mode_descriptions = {
            "Prompt Generation": "focused on generating concise, impactful prompts.",
            "Content Creation": "focused on detailed, structured content development.",
            "User Guidance": "focused on providing clear, actionable guidance for end-users.",
            "General": "balanced, high-quality response for general use."
        }
        mode_description = mode_descriptions.get(mode, "balanced response")
        length_description = f"Desired Length: {desired_length}\n" if desired_length else ""
        format_description = f"Desired Format: {desired_format}\n" if desired_format else ""

        return (f"Guiding Blueprint:\n"
                f"Mode: {mode} - {mode_description}\n"
                f"Topic: {initial_input}\n"
                f"{length_description}"
                f"{format_description}"
                f"Objective: Enhance the response for clarity, coherence, relevance, and usability.\n"
                f"Structure: Dynamic and adaptive refinement based on input analysis.\n"
                f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# --- Quality Evaluator Class ---
class QualityEvaluator:
    """Evaluates the quality of responses based on specified criteria."""

    def assess_quality(self, response: str) -> Dict[str, float]:
        """Assesses the response and returns scores for clarity, coherence, style, and correctness."""
        evaluation_prompt = (
            f"Evaluate the following response on a scale from 0 to 1 for: clarity, coherence, style, correctness.\n\n"
            f"Response:\n{response}\n\n"
            "Please provide the results in valid JSON format without any additional text. The JSON should be in the following format:\n"
            '{"clarity": 0.0, "coherence": 0.0, "style": 0.0, "correctness": 0.0}'
        )
        evaluation = get_completion(evaluation_prompt)
        try:
            evaluation_scores = json.loads(evaluation)
            return evaluation_scores
        except json.JSONDecodeError:
            logging.warning("Evaluation failed. Using default scores.")
            return {"clarity": 0.5, "coherence": 0.5, "style": 0.5, "correctness": 0.5}

# --- Agent Processor Class ---
class AgentProcessor:
    """Processes agents to refine responses based on the guiding blueprint."""

    def __init__(self, memory_manager: MemoryManager, blueprint: str):
        self.memory_manager = memory_manager
        self.blueprint = blueprint

    def time_function(func):
        """Decorator to measure the execution time of functions."""
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            result = await func(*args, **kwargs)
            end_time = time.time()
            logging.info(f"Function {func.__name__} took {end_time - start_time:.2f} seconds")
            return result
        return wrapper

    async def generate_prompt(self, agent_prompt: str, current_response: str, iteration: int, user_feedback: str = "") -> str:
        """Generates the prompt for the agent, including the blueprint, user feedback, and current response."""
        feedback_section = f"User Feedback:\n{user_feedback}\n\n" if user_feedback else ""
        prompt = (
            f"{self.blueprint}\n\n"
            f"{feedback_section}"
            f"{agent_prompt}\n\n"
            f"Current Response:\n{current_response}\n\n"
            f"Refined Response:"
        )
        return prompt

    @time_function
    async def apply_agent(self, agent: Dict[str, str], current_response: str, iteration: int, user_feedback: str = "") -> str:
        """Applies an agent to refine the current response."""
        prompt = await self.generate_prompt(agent['prompt'], current_response, iteration, user_feedback)
        refined_response = await get_completion_async(prompt)
        if refined_response:
            logging.info(f"Agent '{agent['role']}' successfully refined the response.")
            # Update memory with the refined response
            embedding = self.memory_manager.get_embedding(refined_response)
            self.memory_manager.add_memory(refined_response, embedding)
            return refined_response
        else:
            logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
            return current_response

# --- Get Completion Function with retries and exponential backoff ---
def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """Calls the OpenAI API to get a completion for the given prompt with retries and exponential backoff."""
    delay = 1  # Initial delay in seconds
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
            time.sleep(delay)
            delay *= 2  # Exponential backoff
    return None

async def get_completion_async(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """Asynchronous wrapper for get_completion."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, get_completion, prompt, model)

# --- Define Simplified Refinement Chain ---
def get_refinement_chain() -> Dict[str, Dict[str, List[Dict[str, str]]]]:
    """Returns an enhanced refinement chain with well-defined agents, prompts, and tags for usability, clarity, and efficiency."""
    return {
        "Blueprint Context": {
            "agents": [
                {"role": "Blueprint Creator", "prompt": "Draft a comprehensive blueprint with clear objectives, topics, and a logical structure for execution."}
            ],
            "tags": ["planning", "structure", "objectives"]
        },
        "Planning and Framework": {
            "agents": [
                {"role": "Goal Setter", "prompt": "Analyze the task requirements and current data to define clear and actionable goals."},
                {"role": "Blueprint Architect", "prompt": "Develop a detailed blueprint with objectives, topics, and a logical structure to guide execution."}
            ],
            "tags": ["planning", "objectives", "framework"]
        },
        "Content Development": {
            "agents": [
                {"role": "Context Enhancer", "prompt": "Integrate relevant background information to ensure depth and relevance in the response."},
                {"role": "Content Structurer", "prompt": "Organize content into a logical flow, ensuring coherence and clarity."},
                {"role": "Detail Refiner", "prompt": "Identify gaps or missing details and incorporate them to enrich the content."}
            ],
            "tags": ["content", "organization", "contextualization"]
        },
        "Quality Control": {
            "agents": [
                {"role": "Accuracy Validator", "prompt": "Ensure factual accuracy, resolving any errors or inconsistencies."},
                {"role": "Compliance Checker", "prompt": "Verify adherence to coding standards, best practices, and required guidelines."},
                {"role": "Error Mitigator", "prompt": "Address potential issues proactively, ensuring robustness and reliability."}
            ],
            "tags": ["quality", "accuracy", "compliance"]
        },
        "User-Focused Refinement": {
            "agents": [
                {"role": "Readability Optimizer", "prompt": "Enhance language clarity, readability, and accessibility for diverse audiences."},
                {"role": "Engagement Enhancer", "prompt": "Ensure the content is compelling, user-focused, and aligns with expectations."},
                {"role": "Usability Reviewer", "prompt": "Evaluate and refine the response to improve overall user experience."}
            ],
            "tags": ["usability", "engagement", "clarity"]
        },
        "Optimization and Delivery": {
            "agents": [
                {"role": "Performance Optimizer", "prompt": "Optimize for faster execution, improved efficiency, and resource management."},
                {"role": "Security Auditor", "prompt": "Identify and mitigate any security vulnerabilities in the response."},
                {"role": "Documentation Specialist", "prompt": "Produce clear, concise, and well-structured documentation for the final deliverable."}
            ],
            "tags": ["optimization", "security", "documentation"]
        }
    }


# --- Categorize Prompt to Select Relevant Chains ---
def categorize_prompt(prompt: str, memory_manager: MemoryManager) -> List[str]:
    """Categorizes the prompt using semantic similarity to select relevant chains."""
    refinement_chain = get_refinement_chain()
    selected_categories = []

    prompt_embedding = memory_manager.get_embedding(prompt)
    for category, chain in refinement_chain.items():
        chain_keywords = " ".join(chain.get("tags", []))
        if not chain_keywords:
            continue
        category_embedding = memory_manager.get_embedding(chain_keywords)
        similarity = MemoryManager.cosine_similarity(prompt_embedding, category_embedding)
        if similarity > 0.8:  # Threshold for similarity
            selected_categories.append(category)

    return selected_categories if selected_categories else ["Blueprint Context"]

# --- Construct Dynamic Refinement Chain ---
def construct_dynamic_chain(input_text: str, memory_manager: MemoryManager, desired_length: str = None, desired_format: str = None) -> Dict[str, List[Dict[str, str]]]:
    """Constructs a dynamic refinement chain based on the input text and user preferences."""
    refinement_chain = get_refinement_chain()
    selected_categories = categorize_prompt(input_text, memory_manager)
    chain = {}

    # Build agent prompts considering desired_length and desired_format
    length_instruction = f"Ensure the response is {desired_length}.\n" if desired_length else ""
    format_instruction = f"Format the response as {desired_format}.\n" if desired_format else ""
    additional_instructions = length_instruction + format_instruction

    for category in selected_categories:
        agents = refinement_chain[category]["agents"]
        # Modify agents' prompts to include additional instructions
        modified_agents = []
        for agent in agents:
            modified_prompt = f"{agent['prompt']}\n{additional_instructions}"
            modified_agents.append({"role": agent['role'], "prompt": modified_prompt})
        chain[category] = modified_agents

    # Always add Self-Assessment
    chain.setdefault('Self-Assessment', []).append({
        "role": "Self-Assessor",
        "prompt": "Assess the response for any issues and suggest specific improvements."
    })

    return chain

# --- Main Execution Function ---
async def execute_refinement_pipeline(initial_input: str, mode: str, desired_length: str = None, desired_format: str = None):
    """Executes the refinement pipeline to produce the final output, allowing for user feedback and iteration."""
    # Initialize managers
    memory_manager = MemoryManager()
    blueprint_manager = BlueprintManager()
    quality_evaluator = QualityEvaluator()

    # Create blueprint
    blueprint = blueprint_manager.create_blueprint(initial_input, mode, desired_length, desired_format)
    # Add initial input to memory
    initial_embedding = memory_manager.get_embedding(initial_input)
    memory_manager.add_memory(initial_input, initial_embedding)

    current_response = initial_input
    user_feedback = ""
    iteration = 0
    MAX_ITERATIONS = 5
    QUALITY_THRESHOLD = 0.8  # Threshold for quality metrics

    while True:
        # Construct dynamic refinement chain using categorize_prompt
        refinement_chain = construct_dynamic_chain(current_response, memory_manager, desired_length, desired_format)

        # Initialize Agent Processor
        agent_processor = AgentProcessor(memory_manager, blueprint)

        logging.info(f"Selected categories based on prompt: {list(refinement_chain.keys())}")

        for iteration in range(MAX_ITERATIONS):
            logging.info(f"Starting iteration {iteration + 1}")
            for category, agents in refinement_chain.items():
                logging.info(f"Executing category: {category}")
                for agent in agents:
                    current_response = await agent_processor.apply_agent(agent, current_response, iteration, user_feedback)
                    # Evaluate response
                    evaluation_scores = quality_evaluator.assess_quality(current_response)
                    logging.info(f"Evaluation scores: {evaluation_scores}")
                    if all(score >= QUALITY_THRESHOLD for score in evaluation_scores.values()):
                        logging.info("Quality thresholds met. Finalizing response.")
                        break
                else:
                    continue
                break
            else:
                continue
            break

        if iteration >= MAX_ITERATIONS - 1:
            logging.warning("Max iterations reached. Returning last response.")

        print("\nFinal Output:")
        print(current_response)

        # Collect user feedback
        feedback = input("Please provide feedback on the final output (or type 'exit' to finish):\n")
        if feedback.lower() == 'exit':
            break
        else:
            user_feedback = feedback
            # Optionally, add the feedback to memory
            feedback_embedding = memory_manager.get_embedding(user_feedback)
            memory_manager.add_memory(user_feedback, feedback_embedding)
            # Continue to next iteration
            # Optionally, update the blueprint if needed
            # blueprint = blueprint_manager.create_blueprint(current_response, mode, desired_length, desired_format)
            # agent_processor.blueprint = blueprint

# --- Main Function ---
def main():
    """Main function to run the refinement pipeline."""
    # initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    initial_input = "10x python developer specialized in helping users set up optimal projectstructure and codingstyle for an existing project, tailored to be the blest blend of best practices and subjective preferences"
    mode = "Prompt Generation"  # User-selected mode
    desired_length = "short"  # Options: 'short', 'medium', 'long'
    desired_format = "sentence"  # Options: 'bullet points', 'essay', 'code', etc.

    asyncio.run(execute_refinement_pipeline(initial_input, mode, desired_length, desired_format))

if __name__ == "__main__":
    main()

```
