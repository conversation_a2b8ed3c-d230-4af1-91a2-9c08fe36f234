[project]
name = "py-similaritymapper"
version = "1.0.0"
description = "Efficient text file similarity detection tool for finding duplicate and near-duplicate files"
authors = [
    {name = "User", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "textdistance>=4.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
]

[project.scripts]
similarity-mapper = "src.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
