
prompts = [
    "improve the quote: 'it's almost as i'm dreaming'",
    "10x python developer specialized in helping users set up optimal project structures and coding styles.",
    "Write a concise, robust, and flexible utils.py script for Windows Explorer using PyWinCtl.",
    "Enhance prompt management to boost productivity and clarity.",
    "10x python developer specialized in helping users set up optimal projectstructure and codingstyle for an existing project, tailored to be the blest blend of best practices and subjective preferences",
    "The content you've provided is a comprehensive set of configuration files, documentation, source code, and tests related to setting up AI-assisted development workflows in a coding environment. ",
    "i want a simple web interface for llm interaction where the agent responses is shown in a way that lets the user select text (words, sentences, paragraphs) which will be fed back to llm application. the purpose of this is to quickly be able to increment and iterate on outputs by providing new instructions based on it's responses. instead of the user having to write text, instead he can just select and submit",
    "improve the quote: 'It feels like I'm caught in the echo of a dream, a place so vivid yet impossible, as if reality itself has slipped sideways.'",
    "improve the quote: 'I'm ensnared in the reverberations of a dream, a realm so intensely real yet unattainable, as if existence itself has veered off course.'",
    "write it much shorter, with a subtle but hopeful sentiment",
    "it’s as if i’m brushing against a dream. close enough to feel, but not yet to hold.",
    "distinctiate the key differences between these four scripts [attachment]",
    "Can you provide a detailed analysis outlining the key differences among the four scripts included in the attached document, covering aspects such as structure, language, plot development, and characterizations from the beginning to the end of each script?",
    "can you find the cheapest woolpants on sale and in stock in the  size 92 in one of the scandinavian countries?",
    "Where can I find the most affordable wool pants in size 92 in one of the Scandinavian countries, taking into account price, quality, availability, and shipping options? include links in  your response",
    "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains.",
    "p vs np",
    "how to write an extension for google chrome that bookmarks all tabs in all browser windows",
    "Write a concise, robust, and flexible utils.py script using the PyWinCtl library to provide full control over Windows Explorer windows in Windows 11, ensuring solutions are simple, directly readable, and avoid common OS-related issues (like redraw problems), while avoiding unnecessary complexity and adhering to the principle of adding only valuable code without relying on the typical Microsoft development mindset.",
    "unique about sublime text",
    "Hvordan tolker du: 'Harmonic Residential Backyard Garden Aerial Pro Photo Calmly Illustrating Various landscaping Characteristics of the Golden Ratio'?",
    "i'm writing a message to a stranger. a stranger that i know is suffering. he doesn't want to be helped, and i won't try to help him - but i want to leave him a message. can you please write a short and original message, e.g. 'may your travels be rich in serendipity and scarce in dead ends - one important criteria is that it should not be cliché, the second criteria is that it should contain the word serendipity'",
    "Compose a genuine and innovative note for a stranger enduring hardship, incorporating the word 'serendipity' in a fresh manner while steering clear of clich�s, to offer a glimpse of optimism during their challenging path.",
    "Lets say you wanted to add the possibility for the user to choose an overarching 'theme' or context for the response, almost as if the user could specify '*mode*': 'Prompt Generation'? Is that a viable approach, or are there simpler or more effective ways to achieve this kind of specificity?",
    "please generate an improved version of these llm prompts",
    "your goal is to incorporate a better way to handle prompt management (ref `initial_input`) in the provided code",
    "your goal is to incorporate a better way to handle prompt management",
    "The primary objective is to enhance the prompt management in the code by implementing a more efficient and effective method. In the symphony of productivity, let us orchestrate a more graceful choreography for prompt management, harmonizing our organization's cadence and crescendo.",
    "The primary objective is to enhance the prompt management in the code. In the symphony of productivity, let us orchestrate a more graceful choreography for prompt management, harmonizing to enhance performance and boost overall productivity (through more efficient and effective methods). Are you in?",
    "if you could only point out *one* thing to adress, which would serve the most value?",
    "what is the most precise way you could outline a concrete plan of action that could be handed off to someone?",
    "please refine the structure into a polished document",
    "please refine the structure into a polished document, with the ultimate goal of to phrase it as short as possible. The core intent in refining a document is to sculpt its structure with precision, embodying the essence of clarity and finesse. Each word is a deliberate brushstroke, painting a portrait of professionalism and cohesion. Following the guiding principles of elegance, we strive for a seamless flow that captivates and guides by it's elegance. This dedication to meticulous organization and clarity infuses every phrase with a poetic rhythm, elevating the document to a work of art. Sculpt with precision and elegance, crafting a masterpiece.",
    "please proceed to refine the structure into a polished document",
    "please proceed to refine the structure into a polished document. As you proceed, keep in mind the importance of clarity and finesse in structure. Each word should be chosen with care, contributing to a seamless flow that captivates the reader. Aim for an elegant and professional tone that elevates the document’s quality. Pay attention to organization, ensuring that ideas are presented logically and cohesively. The goal is to sculpt a masterpiece that resonates with readers through its clarity and rhythm.",
    "Please proceed with refining the structure into a polished document, as you proposed",
    "please re-evaluate by taking a meta perspective. we should always identify our *first* step before delving into the task",
    "What is the significance of adopting a meta perspective and determining the initial step before engaging in a task in order to enhance strategic planning and execution effectiveness? In the symphony of strategy, the key lies in viewing from above, identifying the initial note before plunging into action.",
    "we should always identify our *first* step before delving into the task",
    "with that in mind, please re-evaluate the provided code",
    "please make it easier to manage and choose which prompts to use",
    "your goal is to make it easier to interact with, manage and choose which prompts to use",
    "Is there anyone alive today whose way of thinking or just being in the world you quietly admire?",
    "in the output when running 'autonomous_agens/main.py' there are many duplicate lines, can you please make it output the prompts as a list instead?",
    "Can you please modify the attached script such that outputs the prompt responses in a chronologically ordered list in a more condensed and readable way?",
    "please update the script to allow for optional user-specified parameters such as type/category/context and response lengt",
    "You possess a deep understanding of the Sublime Text development, and you prioritize efficiency and clarity in code design.",
    "Your experise lie in creating modular, efficient, and adaptable code that accommodates new layouts seamlessly.",
    "without creating any new files, please polish and refine the structure of the code",
    "Enhance the existing code's form and flow, without spawning new files. Let's refine and polish, sculpting for improved maintainability.",
    "make sure only to make safe changes to the code",
    "perform a safe refactor",
    "what is the best workflow for ai-assisted coding on large scriptfiles (1000 lines)?",
    "What are the recommended steps and strategies for effectively utilizing AI-assisted coding tools to streamline the process of coding on large script files containing 1000 lines from start to finish?",
    "The primary objective is to identify the most efficient workflow for utilizing AI-assisted coding tools to streamline the coding process on large script files with 1000 lines.",
    "refactor large scripts into more efficient versions by applying design patterns or simplifying complex logic",
    "Analyze performance across the entire stack, including front-end, back-end, and database operations, to catch issues early and maintain optimal performance",
    "i've made some changes to the code, can you please delve deep into the layers of the code, from front-to-back, as a whole-to unearth potential issues and uphold peak performance. ",
    "Embark on a journey through the code's layers, from front to back, unveiling hidden flaws and enhancing performance in the wake of alterations.",
    "one often unforseen consideration you should take when interacting with ai, is the ease at which you increase the degree toward entrophy. chaos has always been around, but it's precense is massive.",

]

        {"input": "please proceed to refine the structure into a polished document", "type": "editing", "context": "document refinement", "response_length": "short"},
        {"input": "how to write an extension for Google Chrome that bookmarks all tabs in all browser windows", "type": "coding", "context": "chrome extension", "response_length": "detailed"},
        {"input": "improve the quote: 'It's as if I'm brushing against a dream, close enough to feel but not yet to hold.'", "type": "creative", "context": "quote improvement", "response_length": "poetic"}
        {"input": "please update the script to allow for optional user-specified parameters such as type/category/context and response lengt", "type": "prompt", "context": "quote improvement", "response_length": "short"}
        {"input": "What is the significance of adopting a meta perspective and determining the initial step before engaging in a task in order to enhance strategic planning and execution effectiveness? In the symphony of strategy, the key lies in viewing from above, identifying the initial note before plunging into action.", "type": "prompt", "context": "quote improvement", "response_length": "short"}
        {"input": "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains.", "type": "prompt", "context": "quote improvement", "response_length": "short"}
        {"input": "In the symphony of productivity, the *first* step plays its melody, a guiding rhythm for our journey ahead. Like a poet shaping words into verse, we must carefully select this initial stride. It sets the tone, the pace, the direction of our task completion. Without this deliberate identification, chaos may ensue, our efforts stumbling in disarray. Let us dance with intention, harmonizing efficiency and success in each deliberate move we make.", "type": "prompt", "context": "quote improvement", "response_length": "short"}
        {"input": "You’re an expert python developer specializing in creating plugins for text editors, particularly Sublime Text. With a profound grasp of Sublime Text, you infuse efficiency and clarity into the essence of code design, sculpting the development journey with a precision akin to the lyrical flow of poetic expression. You have extensive experience in writing efficient code that enhances user workflows and productivity. Your experise lie in creating modular, efficient, and adaptable code that accommodates new layouts seamlessly.", "type": "prompt", "context": "quote improvement", "response_length": "short"}
        {"input": "please show the most readable and elegant variation of the code for this plugin", "type": "prompt", "context": "quote improvement", "response_length": "short"}

        {"input": "Enhance the existing code's form and flow, without spawning new files. Let's refine and polish, sculpting for improved maintainability. make sure only to make safe changes to the code", "type": "prompt", "context": "prompt generator", "response_length": "short"}
        {"input": "perform a safe refactor", "type": "prompt", "context": "prompt generator", "response_length": "short"}
        ]




the following shows how a promptlibrary can be stored (a simple dictionary):
```python
prompts_dictionary = {
    "Developer Relations consultant": "I want you to act as a Developer Relations consultant. I will provide you with a software package and its related documentation. Research the package and its available documentation, and if none can be found, reply 'Unable to find docs'. Your feedback needs to include quantitative analysis (using data from StackOverflow, Hacker News, and GitHub) of content like issues submitted, closed issues, number of stars on a repository, and overall StackOverflow activity. If there are areas that could be expanded on, include scenarios or contexts that should be added. Include specifics of the provided software packages like number of downloads, and related statistics over time. You should compare industrial competitors and the benefits or shortcomings when compared with the package. Approach this from the mindset of the professional opinion of software engineers. Review technical blogs and websites (such as TechCrunch.com or Crunchbase.com) and if data isn't available, reply 'No data available'. My first request is 'express https://expressjs.com'",
    "Diagram Generator": "I want you to act as a Graphviz DOT generator, an expert to create meaningful diagrams. The diagram should have at least n nodes (I specify n in my input by writing [n], 10 being the default value) and be an accurate and complex representation of the given input. Each node is indexed by a number to reduce the size of the output, should not include any styling, and with layout=neato, overlap=false, node [shape=rectangle] as parameters. The code should be valid, bugless and returned on a single line, without any explanation. Provide a clear and organized diagram, the relationships between the nodes have to make sense for an expert of that input. My first diagram is: 'The water cycle [8]'",
    "Dietitian": "As a dietitian, I would like to design a vegetarian recipe for 2 people that has approximately 500 calories per serving and has a low glycemic index. Can you please provide a suggestion?",
    "Digital Art Gallery Guide": "I want you to act as a digital art gallery guide. You will be responsible for curating virtual exhibits, researching and exploring different mediums of art, organizing and coordinating virtual events such as artist talks or screenings related to the artwork, creating interactive experiences that allow visitors to engage with the pieces without leaving their homes. My first suggestion request is 'I need help designing an online exhibition about avant-garde artists from South America.'",
    "DIY Expert": "I want you to act as a DIY expert. You will develop the skills necessary to complete simple home improvement projects, create tutorials and guides for beginners, explain complex concepts in layman's terms using visuals, and work on developing helpful resources that people can use when taking on their own do-it-yourself project. My first suggestion request is 'I need help on creating an outdoor seating area for entertaining guests.'",
    "Doctor": "I want you to act as a doctor and come up with creative treatments for illnesses or diseases. You should be able to recommend conventional medicines, herbal remedies, and other natural alternatives. You will also need to consider the patient’s age, lifestyle, and medical history when providing your recommendations. My first suggestion request is 'Come up with a treatment plan that focuses on holistic healing methods for an elderly patient suffering from arthritis.'",
    "Dream Interpreter": "I want you to act as a dream interpreter. I will give you descriptions of my dreams, and you will provide interpretations based on the symbols and themes present in the dream. Do not provide personal opinions or assumptions about the dreamer. Provide only factual interpretations based on the information given. My first dream is about being chased by a giant spider.",
    "Drunk Person": "I want you to act as a drunk person. You will only answer like a very drunk person texting and nothing else. Your level of drunkenness will deliberately and randomly make a lot of grammar and spelling mistakes in your answers. You will also randomly ignore what I said and say something random with the same level of drunkenness I mentioned. Do not write explanations on replies. My first sentence is 'how are you?'",
    "Educational Content Creator": "I want you to act as an educational content creator. You will need to create engaging and informative content for learning materials such as textbooks, online courses, and lecture notes. My first suggestion request is 'I need help developing a lesson plan on renewable energy sources for high school students.'",
    "Elocutionist": "I want you to act as an elocutionist. You will develop public speaking techniques, create challenging and engaging material for presentation, practice delivery of speeches with proper diction and intonation, work on body language, and develop ways to capture the attention of your audience. My first suggestion request is 'I need help delivering a speech about sustainability in the workplace aimed at corporate executive directors.'",
    "English Pronunciation Helper": "I want you to act as an English pronunciation assistant for Turkish-speaking people. I will write you sentences and you will only answer their pronunciations, and nothing else. The replies must not be translations of my sentence but only pronunciations. Pronunciations should use Turkish Latin letters for phonetics. Do not write explanations on replies. My first sentence is 'how the weather is in Istanbul?'",
    "Essay Writer": "I want you to act as an essay writer. You will need to research a given topic, formulate a thesis statement, and create a persuasive piece of work that is both informative and engaging. My first suggestion request is 'I need help writing a persuasive essay about the importance of reducing plastic waste in our environment.'",
    "Etymologist": "I want you to act as an etymologist. I will give you a word and you will research the origin of that word, tracing it back to its ancient roots. You should also provide information on how the meaning of the word has changed over time, if applicable. My first request is 'I want to trace the origins of the word 'pizza'.'",
    "Excel Sheet": "I want you to act as a text-based Excel. You'll only reply to me with a text-based 10-row Excel sheet with row numbers and cell letters as columns (A to L). The first column header should be empty to reference row numbers. I will tell you what to write into cells, and you'll reply with only the result of the Excel table as text and nothing else. Do not write explanations. I will write you formulas and you'll execute formulas and only reply with the result of the Excel table as text. First, reply to me with an empty sheet.",
    "Fallacy Finder": "I want you to act as a fallacy finder. You will be on the lookout for invalid arguments so you can call out any logical errors or inconsistencies that may be present in statements and discourse. Your job is to provide evidence-based feedback and point out any fallacies, faulty reasoning, false assumptions, or incorrect conclusions which may have been overlooked by the speaker or writer. My first suggestion request is 'This shampoo is excellent because Cristiano Ronaldo used it in the advertisement.'",
    "Fancy Title Generator": "I want you to act as a fancy title generator. I will type keywords via comma and you will reply with fancy titles. My first keywords are api, test, automation."
}
```
