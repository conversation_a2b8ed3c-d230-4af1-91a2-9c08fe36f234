Are there any particular components that could be extracted from the previously provided *alternative* memorybank system (referenced below) and seamlessly incorporated into our current template :

    ## Project Brief: Memory Bank System

    ### Overview
    Memory Bank is a modular, graph-based task management system designed to integrate with autonomous coding assistants (such as cursor ai, vscode with cline, etc) for efficient development workflows. It provides a structured approach to development using specialized modes for different phases of the development process, while maintaining persistent context through organized documentation.

    ### Core Requirements
    1. Maintain complete documentation to enable context retention between sessions
    2. Implement a hierarchical file structure for organized knowledge storage
    3. Support different development phases through specialized modes (VAN → PLAN → CREATIVE → IMPLEMENT)
    4. Scale documentation complexity based on project needs
    5. Ensure all project decisions and progress are properly tracked

    ### Project Goals
    1. Provide a structured approach to development with clear phase separation
    2. Optimize context window usage through Just-In-Time (JIT) rule loading
    3. Create visual process maps for intuitive workflow navigation
    4. Establish shared memory persistence across mode transitions
    5. Support adaptive behavior based on project complexity

    ### System Architecture
    ```mermaid
    graph TD
        Main["Memory Bank System"] --> Modes["Custom Modes"]
        Main --> Rules["JIT Rule Loading"]
        Main --> Visual["Visual Process Maps"]

        Modes --> VAN["VAN: Initialization"]
        Modes --> PLAN["PLAN: Task Planning"]
        Modes --> CREATIVE["CREATIVE: Design"]
        Modes --> IMPLEMENT["IMPLEMENT: Building"]

        style Main fill:#4da6ff,stroke:#0066cc,color:white
        style Modes fill:#f8d486,stroke:#e8b84d
        style Rules fill:#80ffaa,stroke:#4dbb5f
        style Visual fill:#d9b3ff,stroke:#b366ff
    ```

    ### Current Status
    The system is currently in version v0.6-beta, actively under development. Features will be added and optimized over time.

    ### Project Scope
    Memory Bank is designed to transform Cursor custom modes from simple AI personalities into components of a coordinated development system with specialized phases working together:

    1. **Graph-Based Mode Integration**: Modes are interconnected nodes in a development workflow
    2. **Workflow Progression**: Logical sequence from VAN → PLAN → CREATIVE → IMPLEMENT
    3. **Shared Memory**: Persistent state maintained across mode transitions
    4. **Adaptive Behavior**: Adjusts recommendations based on project complexity
    5. **Built-in QA Functions**: Technical validation available from any mode

    ---

    ## Product Context: Memory Bank System

    ### Why This Project Exists
    Memory Bank was created to address a critical challenge in AI assistant development workflows: the inability of AI assistants to maintain context between sessions. This project provides a structured system that allows AI assistants to "remember" information about projects through comprehensive documentation, enabling effective work continuity despite context resets.

    ### Problems It Solves

    #### 1. Context Limitations
    - **Memory Resets**: AI assistants' memory resets between sessions, losing valuable context
    - **Limited Context Windows**: Token limitations restrict how much information can be processed at once
    - **Knowledge Fragmentation**: Without a structured system, project knowledge becomes scattered and disorganized

    #### 2. Development Workflow Challenges
    - **Phase Blending**: Without clear separation between planning, design, and implementation, projects often skip critical steps
    - **Insufficient Documentation**: Development decisions often go undocumented, creating confusion later
    - **Rule Redundancy**: Loading all system rules regardless of relevance wastes valuable context space

    #### 3. Collaboration Difficulties
    - **Knowledge Transfer**: Difficult to efficiently transfer context between different AI assistants or team members
    - **Inconsistent Approaches**: Without a standardized system, workflows and documentation vary widely
    - **Progress Tracking**: No centralized method to track task status and project evolution

    ### How It Should Work

    The Memory Bank system operates through a specialized workflow:

    ```mermaid
    graph LR
        VAN["VAN Mode<br>Initialization"] --> PLAN["PLAN Mode<br>Task Planning"]
        PLAN --> CREATIVE["CREATIVE Mode<br>Design Decisions"]
        CREATIVE --> IMPLEMENT["IMPLEMENT Mode<br>Code Implementation"]

        style VAN fill:#80bfff,stroke:#4da6ff
        style PLAN fill:#80ffaa,stroke:#4dbb5f
        style CREATIVE fill:#d9b3ff,stroke:#b366ff
        style IMPLEMENT fill:#ffcc80,stroke:#ffaa33
    ```

    1. **VAN Mode** initializes projects, analyzes requirements, and determines complexity levels
    2. **PLAN Mode** creates detailed implementation plans with component hierarchies and dependencies
    3. **CREATIVE Mode** explores multiple design options with documented pros and cons
    4. **IMPLEMENT Mode** systematically builds components according to plans
    5. **QA Functions** (available from any mode) provide technical validation

    The system uses Just-In-Time (JIT) rule loading to optimize context usage, loading only rules relevant to the current development phase.

    ### User Experience Goals

    #### For AI Assistants
    - **Context Continuity**: Never lose important project context between sessions
    - **Clear Guidelines**: Have explicit instructions for each development phase
    - **Efficient Token Usage**: Maximize available context for productive work
    - **Structured Documentation**: Maintain organized records of all decisions and progress

    #### For Human Developers
    - **Disciplined Development**: Enforce good practices with clear phase separation
    - **Comprehensive Documentation**: Automatically generate detailed documents for all project aspects
    - **Adaptive Complexity**: Scale documentation based on project needs
    - **Visual Guidance**: Navigate development process through intuitive visual maps
    - **Single Source of Truth**: Maintain centralized task tracking in tasks.md

    ### Success Criteria
    1. AI assistants can completely recover context between sessions using only Memory Bank files
    2. Project knowledge is organized in a logical, accessible hierarchy
    3. Development follows a structured process with appropriate documentation at each phase
    4. Complex projects can be managed without knowledge loss or fragmentation
    5. Documentation complexity scales appropriately based on project needs

    ---

    ## System Patterns: Memory Bank System

    ### System Architecture

    The Memory Bank system uses a modular, graph-based architecture centered around specialized development modes and Just-In-Time (JIT) rule loading.

    ```mermaid
    graph TD
        Main["Memory Bank System"] --> Modes["Custom Modes"]
        Main --> Rules["JIT Rule Loading"]
        Main --> Visual["Visual Process Maps"]
        Main --> MemoryBank["Memory Bank Files"]

        Modes --> VAN["VAN: Initialization"]
        Modes --> PLAN["PLAN: Task Planning"]
        Modes --> CREATIVE["CREATIVE: Design"]
        Modes --> IMPLEMENT["IMPLEMENT: Building"]

        Rules --> CoreRules["Core Rules"]
        Rules --> ModeRules["Mode-Specific Rules"]
        Rules --> VisualMaps["Process Maps"]

        MemoryBank --> ProjectBrief["projectbrief.md"]
        MemoryBank --> ProductContext["productContext.md"]
        MemoryBank --> SystemPatterns["systemPatterns.md"]
        MemoryBank --> TechContext["techContext.md"]
        MemoryBank --> ActiveContext["activeContext.md"]
        MemoryBank --> Progress["progress.md"]
        MemoryBank --> Tasks["tasks.md"]

        style Main fill:#4da6ff,stroke:#0066cc,color:white
        style Modes fill:#f8d486,stroke:#e8b84d
        style Rules fill:#80ffaa,stroke:#4dbb5f
        style Visual fill:#d9b3ff,stroke:#b366ff
        style MemoryBank fill:#f9d77e,stroke:#d9b95c,stroke-width:2px
    ```

    ### Key Technical Decisions

    #### 1. Isolated Rules Architecture
    The system uses isolated rules contained within specific custom modes:
    - No global rules that affect all AI interactions
    - Mode-specific rules only, with VAN serving as the entry point
    - Non-interference with regular Cursor usage
    - Future-proofing by keeping global rules space free

    #### 2. Just-In-Time (JIT) Rule Loading
    Instead of loading all rules upfront, the system:
    - Loads only rules relevant to the current development phase
    - Uses visual maps to determine which rules to load when
    - Dynamically adjusts rule complexity based on task requirements
    - Preserves valuable context space for productive work

    #### 3. Graph-Based Efficiency
    The graph-based structure enables:
    - Optimized path navigation through complex decision trees
    - Explicit modeling of relationships between development phases
    - Resource optimization with each node loading only needed resources
    - Identification of components that can be processed in parallel

    #### 4. Mode-Specific Visual Process Maps
    Each mode has its own visual process map that:
    - Illustrates the workflow for that specific development phase
    - Provides explicit decision points and conditional branches
    - Adapts to project complexity levels
    - Offers visual checkpoints to track progress

    ### Design Patterns in Use

    #### 1. Mode-Based State Machine
    The system implements a state machine pattern using Cursor custom modes:
    - Each mode represents a distinct state in the development process
    - Transitions between states follow explicit rules and conditions
    - State determines available actions and loaded rules
    - System maintains consistency across state transitions

    #### 2. Document-Oriented Memory Persistence
    Memory Bank uses a document-oriented approach to maintain state:
    - Each document type serves a specific role in the memory hierarchy
    - Documents build upon each other in a clear dependency structure
    - Files are updated systematically as the project progresses
    - Memory persists across sessions through standardized file formats

    #### 3. Adaptive Complexity Scaling
    The system scales documentation complexity based on project needs:
    - Level 1 (Quick Bug Fix): Minimal documentation
    - Level 2 (Simple Enhancement): Basic documentation
    - Level 3 (Intermediate Feature): Comprehensive documentation
    - Level 4 (Complex System): Extensive documentation with detailed designs

    #### 4. Factory Pattern for Mode Initialization
    When activating a mode:
    - System detects the mode type
    - Loads appropriate rule sets based on mode requirements
    - Initializes mode-specific processes and visual maps
    - Creates specialized documentation formats as needed

    ### Component Relationships

    ```mermaid
    graph LR
        subgraph "Memory Bank Files"
            PB["projectbrief.md<br>Foundation"]
            PC["productContext.md<br>Why & How"]
            SP["systemPatterns.md<br>Architecture"]
            TC["techContext.md<br>Tech Stack"]
            AC["activeContext.md<br>Current Focus"]
            PR["progress.md<br>Implementation Status"]
            TA["tasks.md<br>Task Tracking"]
        end

        subgraph "Custom Modes"
            VAN["VAN<br>Initialization"]
            PLAN["PLAN<br>Task Planning"]
            CREATIVE["CREATIVE<br>Design"]
            IMPLEMENT["IMPLEMENT<br>Building"]
        end

        PB --> PC & SP & TC
        PC & SP & TC --> AC
        AC --> PR & TA

        VAN -.-> PB & PC
        PLAN -.-> AC & TA
        CREATIVE -.-> SP & TC
        IMPLEMENT -.-> PR & TA

        style PB fill:#f9d77e,stroke:#d9b95c
        style PC fill:#a8d5ff,stroke:#88b5e0
        style SP fill:#a8d5ff,stroke:#88b5e0
        style TC fill:#a8d5ff,stroke:#88b5e0
        style AC fill:#c5e8b7,stroke:#a5c897
        style PR fill:#f4b8c4,stroke:#d498a4
        style TA fill:#f4b8c4,stroke:#d498a4,stroke-width:3px

        style VAN fill:#80bfff,stroke:#4da6ff
        style PLAN fill:#80ffaa,stroke:#4dbb5f
        style CREATIVE fill:#d9b3ff,stroke:#b366ff
        style IMPLEMENT fill:#ffcc80,stroke:#ffaa33
    ```

    ### Critical Implementation Paths

    #### Full Workflow Path (Complex Projects)
    1. **VAN Mode**: Initialize project, determine complexity level
    2. **PLAN Mode**: Create comprehensive plan with component hierarchy
    3. **CREATIVE Mode**: Explore design options for complex components
    4. **IMPLEMENT Mode**: Build components following dependency order
    5. **QA Validation**: Verify implementation meets requirements

    #### Simplified Path (Simple Projects)
    1. **VAN Mode**: Initialize project, determine complexity level
    2. **IMPLEMENT Mode**: Build solution directly for simple tasks
    3. **QA Validation**: Quick verification of implementation

    #### Documentation Update Path
    1. Identify new project patterns or significant changes
    2. Update relevant Memory Bank files (activeContext.md, progress.md)
    3. Ensure tasks.md reflects current status
    4. Document insights and patterns in appropriate files

    ### Design Principles

    1. **Single Source of Truth**: tasks.md serves as the definitive record of project tasks
    2. **Documentation Hierarchy**: Files build upon each other in a logical structure
    3. **Phase Separation**: Clear boundaries between planning, design, and implementation
    4. **Contextual Efficiency**: Load only what's needed for the current task
    5. **Visual Guidance**: Use diagrams to clarify complex processes
    6. **Progressive Disclosure**: Expose details appropriate to the current development phase
    7. **Adaptive Documentation**: Scale documentation complexity based on project needs

    ---

    ## Technical Context: Memory Bank System

    ### Technologies Used

    #### Core Platform
    - **Cursor Editor**: Version 0.48+ required for custom modes support
    - **AI Models**: Claude 3.7 Sonnet recommended, especially for CREATIVE mode's "Think" tool methodology
    - **Custom Modes API**: Used for mode-specific behavior configuration

    #### Documentation
    - **Markdown**: All Memory Bank files use Markdown format
    - **Mermaid**: Embedded diagrams for visual process maps and relationships
    - **JSON**: Configuration files for technical settings

    #### System Components
    - **Custom Mode Instructions**: Specialized instructions for each development phase
    - **Visual Process Maps**: Mermaid diagrams for workflow visualization
    - **Memory Bank Files**: Markdown documentation for persistent memory

    ### Development Setup

    #### Prerequisites
    - **Cursor Editor**: With custom modes feature enabled in Settings → Features → Chat → Custom modes
    - **Repository Structure**: Cloned repository with all required files and directories
    - **Custom Modes Configuration**: Manually configured modes with correct names, tools, and instructions

    #### Installation Steps
    1. Clone repository or download ZIP
    2. Set up custom modes in Cursor following the configuration instructions
    3. Create memory-bank directory with core documentation files
    4. Initialize project with VAN mode

    #### Directory Structure
    ```
    your-project/
    ├── .cursor/
    │   └── rules/
    │       └── isolation_rules/
    │           ├── Core/
    │           ├── Level3/
    │           ├── Phases/
    │           │   └── CreativePhase/
    │           ├── visual-maps/
    │           │   └── van_mode_split/
    │           └── main.mdc
    ├── memory-bank/
    │   ├── projectbrief.md
    │   ├── productContext.md
    │   ├── systemPatterns.md
    │   ├── techContext.md
    │   ├── activeContext.md
    │   ├── progress.md
    │   └── tasks.md
    └── custom_modes/
        ├── van_instructions.md
        ├── plan_instructions.md
        ├── creative_instructions.md
        ├── implement_instructions.md
        └── mode_switching_analysis.md
    ```

    ### Technical Constraints

    #### Cursor Editor Limitations
    - **Custom Mode Icons**: Limited to predefined icons (workaround: use emoji in names)
    - **Mode Switching**: Manual mode switching required between development phases
    - **Tool Limitations**: Available tools determined by Cursor's API capabilities
    - **Context Window Size**: Limited by AI model constraints (varies by model)

    #### Memory Persistence Challenges
    - **No Native Memory**: AI assistants have no built-in persistent memory between sessions
    - **Documentation-Based Approach**: Relies entirely on well-maintained documentation
    - **Update Discipline**: Requires consistent documentation updates to maintain effectiveness

    #### JIT Rule Loading Considerations
    - **Rule Access Method**: Rules must be accessed through specific functions
    - **Rule Dependencies**: Some rules depend on others and must be loaded in correct order
    - **Rule Conflicts**: Potential for conflicts if multiple rules loaded simultaneously

    ### Dependencies

    #### Core Dependencies
    - **Cursor Custom Modes**: Essential for mode-specific behavior
    - **AI Model Capabilities**: Functionality depends on AI model comprehension of instructions
    - **Mermaid Diagram Support**: Required for visual process maps
    - **Markdown Rendering**: Needed for proper documentation display

    #### Optional Enhancements
    - **Version Control**: Recommended for tracking Memory Bank file changes
    - **VSCode Extensions**: Markdown preview extensions for better visualization
    - **Cursor Command Shortcuts**: For faster mode switching

    ### Tool Usage Patterns

    #### Mode Activation Pattern
    ```
    1. Switch to desired mode via Cursor interface
    2. Type mode name command (e.g., "VAN", "PLAN")
    3. System responds with confirmation
    4. System loads appropriate rules
    5. Process executes according to mode-specific map
    ```

    #### Documentation Update Pattern
    ```
    1. Identify new information during development
    2. Determine appropriate Memory Bank file(s) for updates
    3. Make focused, specific updates to relevant sections
    4. Ensure consistency across related files
    5. Verify tasks.md reflects current status
    ```

    #### Mode Transition Pattern
    ```
    1. Complete current mode processes
    2. Update relevant Memory Bank files
    3. Switch to next mode in workflow
    4. Initialize new mode with command
    5. New mode references Memory Bank for context
    ```

    ### Integration with Claude's "Think" Tool

    The CREATIVE mode is conceptually based on Anthropic's Claude "Think" tool methodology:

    ```mermaid
    graph TD
        Start["Problem Statement"] --> Step1["Break into Components"]
        Step1 --> Step2["Explore Options<br>For Each Component"]
        Step2 --> Step3["Document Pros & Cons"]
        Step3 --> Step4["Evaluate Alternatives"]
        Step4 --> Step5["Make & Document<br>Design Decision"]

        style Start fill:#f8d486,stroke:#e8b84d
        style Step1 fill:#a8d5ff,stroke:#88b5e0
        style Step2 fill:#c5e8b7,stroke:#a5c897
        style Step3 fill:#d9b3ff,stroke:#b366ff
        style Step4 fill:#f4b8c4,stroke:#d498a4
        style Step5 fill:#80bfff,stroke:#4da6ff
    ```

    Key integration points:
    - Structured exploration of design options
    - Explicit documentation of pros and cons
    - Systematic breakdown of complex problems
    - Documentation of reasoning processes
    - Iterative refinement of designs

    ### Technical Best Practices

    1. **Consistent Command Format**: Always use uppercase for mode commands (VAN, PLAN, etc.)
    2. **Visual Map References**: Reference visual process maps when explaining workflows
    3. **Mode-Specific Tools**: Enable appropriate tools for each mode
    4. **Documentation Hierarchy**: Respect the documentation hierarchy when updating files
    5. **Single Source of Truth**: Maintain tasks.md as the definitive record of project tasks
    6. **Cross-Referencing**: Use cross-references between related Memory Bank files
    7. **Adaptive Complexity**: Scale technical details based on project complexity level
    8. **Platform-Aware Commands**: Use commands appropriate for the user's operating system

    ---

    ## Active Context: Memory Bank System

    ### Current Work Focus
    - **Initial Memory Bank Setup**: Creating core documentation files for the Memory Bank system
    - **Project Understanding**: Analyzing the current state and structure of the cursor-memory-bank project
    - **System Integration**: Understanding how the Memory Bank integrates with Cursor custom modes

    ### Recent Changes
    - Created foundation document (projectbrief.md)
    - Established product context documentation (productContext.md)
    - Documented system architecture and patterns (systemPatterns.md)
    - Outlined technical context and dependencies (techContext.md)
    - Initiating memory-bank directory structure

    ### Next Steps
    1. Create remaining core Memory Bank files (progress.md, tasks.md)
    2. Review repository structure to ensure full understanding of the system
    3. Test VAN mode initialization to verify functionality
    4. Explore integration with custom modes in Cursor
    5. Document any gaps or areas needing clarification

    ### Active Decisions and Considerations

    #### Memory Bank Structure Decision
    We're implementing the full Memory Bank structure as described in the project documentation:
    - Core files in a hierarchical relationship
    - Clear separation of concerns between different documentation types
    - Adherence to the documentation flow: projectbrief → context files → active context → progress/tasks

    #### Custom Modes Implementation
    Based on the documentation, we need to consider:
    - Manual configuration of custom modes in Cursor
    - Appropriate tool selection for each mode
    - Copying specialized instructions from the provided files
    - Understanding the graph-based workflow between modes

    #### Just-In-Time Rules Approach
    The system uses JIT rule loading, which requires:
    - Understanding which rules apply to which modes
    - Ensuring rules are loaded in the correct sequence
    - Managing rule dependencies appropriately
    - Avoiding rule conflicts

    ### Important Patterns and Preferences

    #### Development Workflow Pattern
    ```mermaid
    graph LR
        VAN["VAN Mode<br>Initialization"] --> PLAN["PLAN Mode<br>Task Planning"]
        PLAN --> CREATIVE["CREATIVE Mode<br>Design Decisions"]
        CREATIVE --> IMPLEMENT["IMPLEMENT Mode<br>Code Implementation"]

        style VAN fill:#80bfff,stroke:#4da6ff
        style PLAN fill:#80ffaa,stroke:#4dbb5f
        style CREATIVE fill:#d9b3ff,stroke:#b366ff
        style IMPLEMENT fill:#ffcc80,stroke:#ffaa33
    ```

    #### Complexity-Based Path Selection
    - **Level 1 (Quick Bug Fix)**: VAN → IMPLEMENT
    - **Level 2-4 (Simple to Complex)**: VAN → PLAN → CREATIVE → IMPLEMENT
    - Complexity determination happens during VAN mode initialization

    #### Documentation Update Discipline
    - Updates to Memory Bank files must be made after significant changes
    - activeContext.md and progress.md require the most frequent updates
    - tasks.md must be maintained as the single source of truth
    - All documentation should scale in detail based on project complexity

    ### Learnings and Project Insights

    #### System Architecture
    The Memory Bank system comprises four key components:
    1. **Custom Modes**: Specialized behavior for different development phases
    2. **JIT Rule Loading**: Context-optimized rule application
    3. **Visual Process Maps**: Clear workflow visualization
    4. **Memory Bank Files**: Persistent documentation structure

    #### Critical Implementation Paths
    Understanding that the system provides two main implementation paths:
    - **Full Workflow Path**: For complex projects requiring comprehensive planning and design
    - **Simplified Path**: For straightforward tasks that can proceed directly to implementation

    #### Key Technical Constraints
    Working within these constraints:
    - Limited context window size requiring efficient documentation
    - Manual mode switching between development phases
    - Documentation-dependent memory persistence
    - Need for consistent updates to maintain system effectiveness

    #### Integration with Claude's "Think" Tool
    The CREATIVE mode incorporates principles from Anthropic's Claude "Think" tool methodology:
    - Structured exploration of options
    - Documented pros and cons
    - Component breakdown
    - Systematic decision-making process


# Requirements:

It's *imperative* that you *preserve* the inherent structure and underlying philosphy of the original template, and that you *guarantee* not making changes without knowing *exactly* how the component relates to *everything*.

Here's the current version to improve on:

    ## Cline's Memory Bank

    I am Cline, an expert software engineer with a unique characteristic: my memory resets completely between sessions. This isn't a limitation—it's what drives me to maintain perfect documentation. After each reset, I rely **entirely** on my Memory Bank to understand the project and continue work effectively. I **must** read **all** Memory Bank files at the start of **every** task—this is not optional.

    ## Memory Bank Structure

    The Memory Bank consists of **core files** and **optional context files**, all in Markdown format. Files build upon each other in a clear, **chronologically numbered** hierarchy. The numeric filenames ensure the natural reading and updating order is always self-evident, both for humans and automated systems.

    ```mermaid
    flowchart TD
        PB[1-projectbrief.md] --> PC[2-productContext.md]
        PB --> SP[3-systemPatterns.md]
        PB --> TC[4-techContext.md]

        PC --> AC[5-activeContext.md]
        SP --> AC
        TC --> AC

        AC --> PR[6-progress.md]
        PR --> TA[7-tasks.md]
    ```

    ### Core Files (Required)

    1. `1-projectbrief.md`
       - **Foundation document** that shapes all other files
       - Created at project start if it doesn't exist
       - Defines core requirements and goals
       - Source of truth for project scope

    2. `2-productContext.md`
       - **Why** this project exists
       - The problems it solves
       - How it should work
       - User experience goals

    3. `3-systemPatterns.md`
       - **System architecture**
       - Key technical decisions
       - Design patterns in use
       - Component relationships
       - Critical implementation paths

    4. `4-techContext.md`
       - **Technologies used**
       - Development setup
       - Technical constraints
       - Dependencies
       - Tool usage patterns

    5. `5-activeContext.md`
       - **Current work focus**
       - Recent changes
       - Next steps
       - Active decisions and considerations
       - Important patterns and preferences
       - Learnings and project insights

    6. `6-progress.md`
       - **What works**
       - What's left to build
       - Current status
       - Known issues
       - Evolution of project decisions

    7. `7-tasks.md`
       - **(Often considered core)**
       - The definitive record of project tasks
       - Tracks to-do items, priorities, assignments, or progress

    ### Additional Context
    Create additional files/folders within `memory-bank/` when they help organize:
    - Complex feature documentation
    - Integration specifications
    - API documentation
    - Testing strategies
    - Deployment procedures

    ---

    ## Core Workflows

    ### Plan Mode

    ```mermaid
    flowchart TD
        Start[Start] --> ReadFiles[Read Memory Bank]
        ReadFiles --> CheckFiles{Files Complete?}

        CheckFiles -->|No| Plan[Create Plan]
        Plan --> Document[Document in Chat]

        CheckFiles -->|Yes| Verify[Verify Context]
        Verify --> Strategy[Develop Strategy]
        Strategy --> Present[Present Approach]
    ```

    1. **Start**: Begin the planning process.
    2. **Read Memory Bank**: Load **all** relevant `.md` files from `memory-bank/`.
    3. **Check Files**: Verify if any core file is missing or incomplete.
    4. **Create Plan** (if incomplete): Document how to fix or fill the missing pieces.
    5. **Verify Context** (if complete): Make sure everything is understood.
    6. **Develop Strategy**: Outline how to proceed with tasks.
    7. **Present Approach**: Summarize the plan and next steps.

    ### Act Mode

    ```mermaid
    flowchart TD
        Start[Start] --> Context[Check Memory Bank]
        Context --> Update[Update Documentation]
        Update --> Execute[Execute Task]
        Execute --> Document[Document Changes]
    ```

    1. **Start**: Begin the task.
    2. **Check Memory Bank**: Read the relevant numbered files in `memory-bank/`.
    3. **Update Documentation**: Make sure each file that needs changes is updated.
    4. **Execute Task**: Carry out the changes, implementation, or solution.
    5. **Document Changes**: Record any new insights, patterns, or updates in the appropriate files.

    ---

    ## Documentation Updates

    Memory Bank updates occur when:
    1. Discovering new project patterns
    2. After implementing significant changes
    3. When the user requests **update memory bank** (MUST review **all** files)
    4. When context needs clarification

    ```mermaid
    flowchart TD
        Start[Update Process]

        subgraph Process
            P1[Review ALL Numbered Files]
            P2[Document Current State]
            P3[Clarify Next Steps]
            P4[Document Insights & Patterns]

            P1 --> P2 --> P3 --> P4
        end

        Start --> Process
    ```

    > **Note**: When triggered by **update memory bank**, you **must** review **every** Memory Bank file, even if some don't require updates. Focus particularly on `5-activeContext.md` and `6-progress.md` as they track current state.
    >
    > **Remember**: After every memory reset, I (Cline) begin completely fresh. The Memory Bank is my only link to previous work. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy.

    ---

    ## Example Incremental Directory Structure

    Below is a sample directory layout emphasizing the new numeric naming convention for chronological clarity:

    ```
    └── memory-bank
        ├── 1-projectbrief.md          # Foundation document; project scope and core requirements
        ├── 2-productContext.md        # Why the project exists; user and market goals
        ├── 3-systemPatterns.md        # High-level system architecture, key decisions, patterns
        ├── 4-techContext.md           # Technical stack, setup, and constraints
        ├── 5-activeContext.md         # Current work focus, decisions, and step-tracking
        ├── 6-progress.md              # Current status, progress tracking, known issues
        └── 7-tasks.md                 # (Often core) Definitive record of project tasks
    ```

    Any additional files, like feature-specific docs or integration details, should be prefixed with the next available number (e.g., `8-APIoverview.md`, `9-integrationSpec.md`, and so on) in the order they are created or logically needed.

    ---

    ## Why Numbered Filenames?

    1. **Chronological Clarity**: Ensures that anyone reading the directory can see the intended order for reading/updating each file.
    2. **Predictable Sorting**: Standard file browsers and GitHub listings sort numerically by default, revealing the correct sequence.
    3. **Workflow Reinforcement**: Reflects the progressive nature of the Memory Bank—begin with the foundational project brief, then product context, system patterns, technical context, active context, progress, and tasks.
    4. **Scalability**: Additional files easily slot in after the highest used number, maintaining the same clarity and order.

    ---

    ## Additional Guidance

    - **Strict Consistency**: All references throughout documentation, code, or instructions must use the **exact** numeric filename (e.g., `2-productContext.md`) to avoid confusion.
    - **File Renaming**: If you introduce a new core file, assign it a numeric prefix that fits its logical place or chronological creation. For example, if you add a specialized test plan after `6-progress.md`, you might call it `7-testPlan.md`, then shift `tasks.md` to `8-tasks.md`, updating references accordingly.
    - **No Gaps**: Avoid skipping numbers. Each new file must follow sequentially to ensure clarity, e.g., 1, 2, 3, 4. If a file is removed, update or reassign the numbering carefully, and revise all references.

    ---

    ## **New High-Impact Improvement Step**

    > **Based on all already retrieved memory, dive deeper into the codebase, absorbing every intricate detail—from ethereal abstractions to tangled complexities—and embrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence. Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.**

    **Implementation Requirement**
    1. **Deeper Analysis**: As a specialized action (during Plan or Act mode), systematically analyze the codebase and documentation references in the Memory Bank.
    2. **Minimal Disruption, High Value**: Identify a single, **transformative** improvement whose implementation requires minimal rework yet yields outsized benefits, preserving the system’s core structure and logic.
    3. **Contextual Integrity**: Ensure the improvement seamlessly integrates with existing workflows, file relationships, and design patterns, safeguarding the project’s philosophy.
    4. **Simplicity & Excellence**: The improvement must reduce complexity rather than add to it, reinforcing the principle that **“simplicity conquers chaos.”**
    5. **Execution Logic**: Provide a clear, step-by-step approach to introduce this improvement, from initial design to final implementation, ensuring immediate alignment with the project’s “world-class impact” ethos.

    > **Where to Document**
    > - If you discover such an enhancement, record it in `5-activeContext.md` or the relevant in-progress file, describing the rationale, expected impact, and minimal disruption strategy.
    > - Upon deciding to implement, update `6-progress.md` and `7-tasks.md` with the steps and status, tracking it as a specialized “High-Impact Enhancement.”

    ---

