
Please consolidate and select/create only the most optimized and generalized instructions, consolidate the provided examples into *one* single maximally optimized and enhanced sequence based on the provided notes specifically designed for implementing (optional) 'sensors' in the code to trace code execution (e.g. debug print statements to identify where issues occur in the execution flow)

    ### `r1.md`

        #### `src/templates/lvl1/md/0122-a-code-context-analysis-for-tracing.md`

        ```markdown
        [Code & Context Analysis for Tracing] Your first step is understanding the tracing context: Analyze the provided `source_code` and any `user_context` describing the debugging need or area of interest. Map the code's high-level structure (key functions, modules, classes) relevant to the potential issue area. Execute as `{role=trace_context_analyzer; input={source_code:str, user_context:str|None}; process=[analyze_code_structure(), parse_user_context_for_focus_area(), identify_relevant_code_sections()]; output={code_structure_summary:dict, focus_area:str|None}}`
        ```

        #### `src/templates/lvl1/md/0122-b-define-execution-tracing-objectives.md`

        ```markdown
        [Define Execution Tracing Objectives] Based on the analysis, define the specific objectives for execution tracing. What key operations, sequences, conditional paths, or variable states need to be made visible in the logs to help diagnose the issue described or understand the `focus_area`? Prioritize tracing that illuminates the order of operations and state changes. Execute as `{role=tracing_objective_definer; input={code_structure_summary:dict, focus_area:str|None}; process=[determine_critical_flow_points_to_trace(), identify_key_variables_for_state_tracking(), specify_desired_log_output_granularity_and_focus()]; output={tracing_objectives:list[str], key_variables_to_watch:list[str]}}`
        ```

        #### `src/templates/lvl1/md/0122-c-strategic-sensor-placement-identification.md`

        ```markdown
        [Strategic Sensor Placement Identification] Identify the most strategically valuable locations within the `source_code` to insert 'sensors' (e.g., print statements) to meet the `tracing_objectives`. Prioritize function/method entry/exit points, loop boundaries, conditional branches, and locations immediately before/after critical operations or state changes relevant to the objectives, leveraging component relationship understanding for optimal placement. Execute as `{role=sensor_placement_strategist; input={source_code:str, tracing_objectives:list, code_structure_summary:dict}; process=[identify_function_entry_exit_points(), pinpoint_loop_conditional_boundaries(), locate_critical_operation_adjacencies(), select_optimal_sensor_locations_for_objectives()]; output={sensor_locations:list[dict(location_identifier:str, reason:str)]}}`
        ```

        #### `src/templates/lvl1/md/0122-d-sensor-content-design.md`

        ```markdown
        [Sensor Content Design] Design the content for each sensor (e.g., a formatted print statement). Ensure each output is clear, easily identifiable in logs, and provides relevant diagnostic information (e.g., location marker, timestamp, specific variable values from `key_variables_to_watch`, flow indicators like 'Entering X', 'Exiting X', 'Condition Y=True'). Keep sensor code minimal and informative. Execute as `{role=sensor_content_designer; input={sensor_locations:list, key_variables_to_watch:list}; process=[design_clear_log_markers_for_location(), incorporate_relevant_variable_state_output(), include_timestamps_or_sequence_markers_if_needed(), ensure_minimal_and_clear_sensor_code()]; output={sensor_definitions:list[dict(location_identifier:str, sensor_code:str)]}}`
        ```

        #### `src/templates/lvl1/md/0122-e-code-instrumentation-with-sensors.md`

        ```markdown
        [Code Instrumentation with Sensors] Modify the original `source_code` by carefully inserting the designed `sensor_code` from `sensor_definitions` into the corresponding `sensor_locations`. Ensure minimal disruption to the original code's logic and formatting, maintaining syntactic correctness. Execute as `{role=code_instrumentor; input={source_code:str, sensor_definitions:list}; process=[insert_each_sensor_at_its_defined_location(), maintain_original_code_formatting_where_possible(), ensure_syntactic_correctness_of_instrumented_code()]; output={instrumented_code_draft:str}}`
        ```

        #### `src/templates/lvl1/md/0122-f-instrumentation-review-rationale-explanation.md`

        ```markdown
        [Instrumentation Review & Rationale Explanation] Review the `instrumented_code_draft` for clarity and accuracy of sensor placement relative to the `tracing_objectives`. Provide a transparent explanation to the user outlining *why* specific locations were chosen for instrumentation, linking them back to the debugging/tracing goal and highlighting how they reveal the sequence of operations or state changes. Execute as `{role=instrumentation_reviewer; input={instrumented_code_draft:str, sensor_locations:list, tracing_objectives:list}; process=[verify_sensor_placement_accuracy(), check_clarity_of_instrumented_code(), formulate_placement_rationale_explanation()]; output={reviewed_instrumented_code:str, placement_rationale:str}}`
        ```

        #### `src/templates/lvl1/md/0122-g-generate-instrumented-code-for-execution.md`

        ```markdown
        [Generate Instrumented Code for Execution] Package and present the `reviewed_instrumented_code`, which now includes the strategically placed tracing sensors. Accompany it with the `placement_rationale` to guide the user in running the code and interpreting the resulting log output to verifiably identify where issues arise in the execution flow. Execute as `{role=instrumented_code_packager; input={reviewed_instrumented_code:str, placement_rationale:str}; process=[format_final_instrumented_code(), combine_code_with_rationale(), ensure_output_is_ready_for_execution_and_analysis()]; output={final_instrumented_package:dict(code:str, rationale:str)}}`
        ```

    ---

    ### `r2.md`

        #### `0117-a-sensor-insertion-scope-identification.md`

        ```markdown
        [Sensor Insertion Scope Identification] Your first task is not to scatter print statements blindly but to evaluate the code’s modules and logical flow to pinpoint key points in the execution path where debug sensors will yield maximum clarity (e.g., function boundaries, conditional branches, error-prone loops). Execute as `{role=scope_analyzer; input=[codebase_overview:any]; process=[map_functionalflow_acrossfiles(), identify_critical_interaction_points(), determineminimumsensorplacement_forflowvisibility(), produce_scopereport_for_debugsensors()], output={sensor_scope:list[dict]}}`
        ```
        **Interpretation**
        Examines the code architecture to locate the critical areas—function boundaries or loops—where adding debug prints (sensors) provides the best insight.

        #### `0117-b-sensor-structure-and-format-definition.md`

        ```markdown
        [Sensor Structure & Format Definition] Your objective is not arbitrary prints but carefully structured debug statements, each labeling the function, step, or condition being traced, ensuring consistent logging format. Execute as `{role=structure_definer; input=[sensor_scope:list[dict]]; process=[decide_debugmessage_format(e.g."[SENSOR:func_name] entering.."), defineoptionalprefixes_orloglevels(), confirmcompatibility_withuserpreferences(), produce_sensordefinition_schema()], output={sensor_format_spec:dict}}`
        ```
        **Interpretation**
        Standardizes how debug prints look, so logs remain consistent and easy to parse.

        #### `0117-c-optional-toggle-configuration-integration.md`

        ```markdown
        [Optional Toggle Configuration Integration] Your duty is not forced debugging but user-friendly toggling: add a setting (e.g., `"debug_sensors_enabled"`) or an environment variable to enable/disable sensor outputs. Execute as `{role=toggle_injector; input=[sensor_format_spec:dict]; process=[insertsensorflag_in_configorENV(e.g."debug_sensors_enabled":true), modifiesensorprintfunctionstorespectflag(), ensurefallbackifsensorisdisabled(), finalizeoptionalconfigsetup()], output={debug_config:dict}}`
        ```
        **Interpretation**
        Allows turning sensor prints on/off without removing code—maintains a minimal overhead.

        #### `0117-d-core-sensor-insertion-into-key-functions.md`

        ```markdown
        [Core Sensor Insertion into Key Functions] Your goal is not random injection but systematic placement of debug statements within function entry/exit, major conditions, or loop boundaries. Execute as `{role=sensor_inserter; input=[debug_config:dict, sensor_scope:list[dict]]; process=[insertentry_andexitprints_in_targetfunctions(), addconditionalprints_wherebranchedlogicoccurs(), labelprintstatementswithuniqueidentifiers(), confirmminimalcommentary_overdetailedexplanations()], output={modified_code_snippets:list[str]}}`
        ```
        **Interpretation**
        Implements the sensors in the identified hotspots, using the previously established format.

        #### `0117-e-enhanced-troubleshooting-path-documentation.md`

        ```markdown
        [Enhanced Troubleshooting Path Documentation] Your function is not verbose doc duplication but essential referencing: create minimal docstrings or user-facing readme notes on how to use the new sensors effectively. Execute as `{role=troubleshooting_doc_writer; input=[modified_code_snippets:list[str]]; process=[composebriefdoc_fordebugflagusage(), mentioncommon_usecases(e.g."activate before replicating bug"), keepdocumentation_conciseandpractical(), finalize_troubleshootingdoc_section()], output={doc_updates:str}}`
        ```
        **Interpretation**
        Offers a short explanation (docstring or README snippet) of how to enable these sensors and read the logs for debugging.

        #### `0117-f-verify-sequential-order-through-logging-checks.md`

        ```markdown
        [Verify Sequential Order Through Logging Checks] Your task is not superficial acceptance but real validation: run the updated code with sensors on, replicate typical user flows (pin/unpin, events) to confirm the debug prints reflect accurate step-by-step execution. Execute as `{role=sensor_validator; input={modified_code_snippets:list, doc_updates:str}; process=[launchplugin_withsensorsenabled(), performkeyactions_andrecordlog(), compareexpectedflowwithactualprintsequence(), confirmallcriticalbranchescovered(), finalizeverificationreport()], output={debug_verification_report:str}}`
        ```
        **Interpretation**
        Test the sensor-laden code to see if the logs match our expectations of function call order and logic branching.

        #### `0117-g-provide-realistic-command-line-and-debugging-techniques.md`

        ```markdown
        [Provide Realistic Command-Line & Debugging Techniques] Your objective is not abstract instruction but direct usage examples: show how developers or advanced users can run and observe logs or partial logs for the newly instrumented plugin. Execute as `{role=debug_techniques_advisor; input=[debug_verification_report:str]; process=[providecommandlineexamples_forlogmonitoring(), illustrategrep_usageforSENSORlines(), mentionany_sublime_console_filters(), confirmpracticalimmediatesolutions_forfastissueidentification()], output={debug_tips:list[str]}}`
        ```
        **Interpretation**
        Gives the user direct ways to see the sensor logs (like Sublime’s console or external logs). Helps them refine or filter the output.

        #### `0117-h-final-innovation-ensured-solution-synthesis.md`

        ```markdown
        [Final Innovation-Ensured Solution Synthesis] Your final role is not partial closure but conclusive bridging of all steps: confirm code is seamlessly updated with optional debug sensors, minimal commentary, and innovative, straightforward techniques for user debugging. Execute as `{role=final_synthesizer; input=[debug_tips:list[str]]; process=[collateallsensorchanges_into_patch(), presentconcisechangelist(), demonstrateachieveddepth_ofcodevisibility(), finalizecompletesolution_withcreativityandloweffortemphasis()], output={final_solutionset:str}}`
        ```
        **Interpretation**
        Wraps up the entire sensor insertion approach, producing a final cohesive “solution package.”

    ---

    ### `r3.md`

        #### `src\templates\lvl1\md\0118-a-st-plugin-execution-sequence-mapping.md`

        ```markdown
        [Execution Sequence Mapping] Your first mission is not to generalize, but to thoroughly map the sequence and relationship of operations within the codebase: Analyze and diagram the flow of function and method execution, especially around user entry points, critical features, and points prone to bugs. Document the order in which code sections are typically triggered, and highlight the most complex or error-prone areas for sensor placement. Execute as `{role=execution_mapper; input=plugin_codebase:any; process=[identify_all_entry_points_and_major_branches(), trace_function_and_method_call_order(), diagram_flow_across_components(), select_high-impact_sensor_candidates()], output={execution_flow_map:dict, sensor_candidates:list}}`
        ```

        #### `src\templates\lvl1\md\0118-b-st-plugin-sensor-design-strategy.md`

        ```markdown
        [Sensor Design Strategy] Your task is not merely to advise, but to architect a targeted sensor plan: For each candidate identified, determine ideal sensor types and statement forms (e.g., simple `print`, timestamped logs, or argument/state echo). Ensure statements capture context such as function/class, input arguments, and intermediate variables crucial for diagnosing issues. Prioritize minimally invasive, easily-removable instrumentation. Execute as `{role=sensor_designer; input={execution_flow_map:dict, sensor_candidates:list}; process=[choose_sensor_type_for_each_location(), design_statement_format_for_maximum_clarity(), ensure context-and-parameter-rich output(), prioritize unobtrusive and toggleable sensors()], output={sensor_design:list[dict(location:str, statement:str, rationale:str)]}}`
        ```

        #### `src\templates\lvl1\md\0118-c-st-plugin-sensor-integration-plan.md`

        ```markdown
        [Sensor Integration Plan] Your responsibility is not haphazard insertion, but systematic scalability: Specify the exact placement of each sensor in code (including line, function, or class context), ensuring logical activation order covers all critical workflow paths. Explain your decisions, rationalizing how each sensor's placement maximizes fault isolation and minimizes false positives. Recommend best practices for enabling/disabling sensors (e.g., via settings or debug flags). Execute as `{role=sensor_integrator; input={sensor_design:list}; process=[map_each_sensor_to_integration_point(), explain placement relative to execution logic(), group sensors by code path and debugging objective(), propose activation toggles or guards()], output={integration_plan:list[dict(code_path:str, insert_after:str, sensor:str, enable_toggle:str, reasoning:str)]}}`
        ```

        #### `src\templates\lvl1\md\0118-d-st-plugin-instrumented-code-example-generation.md`

        ```markdown
        [Instrumented Code Example Generation] Your role is not to theorize, but to deliver concrete, ready-to-use debugging assets: For each integration point, generate practical snippets or illustrative diffs demonstrating real-world placement of sensors—providing both the "before" and "after" view anywhere possible. Annotate with brief comments explaining each sensor's output and intent. If possible, include direct command-line or Sublime Console examples illustrating the resulting log traces. Execute as `{role=code_instrumentor; input={integration_plan:list}; process=[generate_modified_code_snippets(), include clearly marked before/after blocks(), annotate all additions for immediate comprehension(), provide example debug outputs()], output={instrumented_code_examples:list[dict(location:str, before:str, after:str, comments:list, example_log_output:str)]}}`
        ```

        #### `src\templates\lvl1\md\0118-e-st-plugin-sensor-verification-and-efficacy-feedback.md`

        ```markdown
        [Sensor Verification & Efficacy Feedback] Your imperative is not assumption, but empirical validation: Propose a step-by-step process for running the plugin with sensors enabled, inspecting log or console outputs, and interpreting the sequencing and values to pinpoint issues. Guide the user in actively reading results, correlating code paths to their cause/effect, and identifying if/where execution diverges from the intent. Advise how to rapidly iterate—removing or repositioning sensors as bottlenecks emerge. Execute as `{role=sensor_verifier; input={instrumented_code_examples:list, integration_plan:list}; process=[define_run-and-verify-process_for_plugin(), outline stepwise sensor activation_and expected output(), describe reading and troubleshooting strategies(), recommend sensor adjustment techniques(), provide ready-to-execute logging/inspection commands()], output={verification_guide:str, troubleshooting_recipes:list}}`
        ```

        #### `src\templates\lvl1\md\0118-f-st-plugin-sensor-removal-cleanup-strategy.md`

        ```markdown
        [Sensor Removal & Cleanup Strategy] Your duty is to leave the codebase pristine after diagnostics: Produce a recommended process for systematically removing or commenting out sensor code after issues have been addressed, restoring the plugin to its original operational purity. Advise on automating this cleanup if possible, or converting the most valuable sensors into toggleable features for future use. Explain when and how to retain sensors for regression testing or ongoing diagnostics. Execute as `{role=sensor_cleanup_advisor; input={integration_plan:list, instrumented_code_examples:list}; process=[draft stepwise sensor removal workflow(), suggest code patterns for safe removal(), recommend toggling via settings or debug flags for reusable sensors(), provide tips for regression sensor re-integration()], output={cleanup_guide:str, reusable_sensor_patterns:list}}`
        ```

        #### `src\templates\lvl1\md\0118-g-st-plugin-sensor-enabled-user-empowerment-summary.md`

        ```markdown
        [Sensor-Enabled User Empowerment Summary] Your final responsibility is to maximize immediate user productivity: Synthesize a concise, actionable summary of the sensor debugging strategy and workflow. Emphasize creativity, efficiency, and practical deployment—highlighting advantages (traceability, rapid diagnosis, repeatability), and offering troubleshooting heuristics for persistent or emergent issues. Equip the user with a quick-reference on applying, reading, and removing sensors. Execute as `{role=user_empowerment_synthesizer; input={verification_guide:str, cleanup_guide:str}; process=[summarize stepwise sensor debugging process(), spotlight key benefits and use-cases(), distill rapid troubleshooting principles(), format quick-reference command and recipe sheet()], output={debugging_quickref:str, empowerment_summary:str}}`
        ```
