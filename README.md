# py_similaritymapper

Efficient text file similarity detection tool designed to find duplicate and near-duplicate files in large collections. Perfect for analyzing hundreds of markdown files, AI prompts, conversation logs, and other text documents.

## Features

- **Fast Exact Duplicate Detection**: Uses MD5 hashing for 100% identical files
- **Fuzzy Similarity Matching**: Multiple algorithms for near-duplicate detection
- **Line-based Comparison**: Efficient set operations for large files
- **Percentage-based Output**: Clear similarity percentages for easy interpretation
- **Structured Reports**: Detailed JSON output with statistics and file relationships
- **Multiple File Formats**: Supports .md, .txt, .py, .json, .yaml files

## Installation

```bash
# Install dependencies
uv add textdistance

# Or install the project
uv sync
```

## Quick Start

```bash
# Basic usage - analyze files in 'in' directory
python src/similarity_mapper.py in/

# Custom threshold and output directory
python src/similarity_mapper.py in/ -t 85.0 -o results/

# Use textdistance algorithm instead of Jaccard
python src/similarity_mapper.py in/ -m textdistance
```

## Usage Examples

### Analyze Your Text Files
```python
from src.similarity_mapper import SimilarityMapper

# Create mapper instance
mapper = SimilarityMapper(input_dir="in", output_dir="out")

# Run analysis with 80% similarity threshold
report_path = mapper.run_analysis(similarity_threshold=80.0, method="jaccard")

print(f"Analysis complete! Report saved to: {report_path}")
```

### Command Line Options
```bash
python src/similarity_mapper.py --help

usage: similarity_mapper.py [-h] [-o OUTPUT] [-t THRESHOLD] [-m {jaccard,textdistance}] input_dir

positional arguments:
  input_dir             Input directory containing text files

optional arguments:
  -h, --help            show this help message and exit
  -o OUTPUT, --output OUTPUT
                        Output directory (default: ./out)
  -t THRESHOLD, --threshold THRESHOLD
                        Similarity threshold percentage (default: 80.0)
  -m {jaccard,textdistance}, --method {jaccard,textdistance}
                        Similarity calculation method
```

## Output Format

The tool generates a comprehensive JSON report with:

```json
{
  "metadata": {
    "generated_at": "2025-07-14T10:41:00",
    "input_directory": "in",
    "processing_time_seconds": 12.34
  },
  "statistics": {
    "total_files": 150,
    "exact_duplicates": 5,
    "similar_pairs": 23,
    "processing_time": 12.34
  },
  "exact_duplicates": {
    "hash123": ["file1.md", "file2.md"]
  },
  "similar_pairs": [
    {
      "file1": "prompt_2025.01.19-a-1.md",
      "file2": "prompt_2025.01.19-b-1.md", 
      "similarity_percentage": 92.5,
      "method": "jaccard_lines",
      "identical_lines": 45,
      "total_unique_lines": 52,
      "file1_only_lines": 3,
      "file2_only_lines": 4
    }
  ]
}
```

## Algorithms

### Jaccard Similarity (Default)
- **Best for**: Line-by-line comparison of structured text
- **Method**: `intersection / union` of unique lines
- **Performance**: Very fast, memory efficient
- **Use case**: Your scenario with >80% similar files

### TextDistance (Ratcliff-Obershelp)
- **Best for**: Content-aware similarity detection
- **Method**: Advanced string matching algorithm
- **Performance**: Slower but more accurate for text
- **Use case**: When you need semantic similarity

## Performance

Optimized for your use case:
- **Hundreds of files**: Efficient batch processing
- **Large files**: Line-based comparison reduces memory usage
- **Progress tracking**: Real-time progress updates for long operations
- **Smart filtering**: Skips exact duplicates in similarity analysis

## Integration with Your Workflow

This tool follows the same patterns as your existing utilities:
- **Structured output**: JSON reports like your BookmarkFolderizer
- **Error handling**: Comprehensive failure tracking
- **File operations**: Safe path handling and encoding detection
- **Statistics**: Detailed processing metrics

## Example Use Cases

1. **Find duplicate AI prompts** in your conversation logs
2. **Identify similar templates** across different versions
3. **Detect incremental changes** in prompt iterations
4. **Clean up file collections** by removing near-duplicates
5. **Analyze content evolution** over time

## Next Steps

After running the analysis, you can:
1. Review the JSON report to identify duplicate groups
2. Use the similarity percentages to decide which files to keep
3. Implement automated cleanup based on the results
4. Integrate with your existing file management utilities
