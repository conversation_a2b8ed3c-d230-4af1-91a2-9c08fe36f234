
Here's the code i'm working on:


    ```python
    import os
    import sys
    import json
    from pathlib import Path
    from loguru import logger
    import glob
    import yaml
    import re
    from dotenv import load_dotenv
    from openai import OpenAI
    import logging
    from datetime import datetime
    from typing import List, Dict, Optional, Union
    from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT


    class Config:
        """
        Global settings.
        """
        PROVIDER_OPENAI = "openai"
        PROVIDER_DEEPSEEK = "deepseek"
        PROVIDER_ANTHROPIC = "anthropic"

        AVAILABLE_MODELS = {
            PROVIDER_OPENAI: {
                "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
                "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
                "gpt-4": "Latest GPT-4 stable release",
                "gpt-4-0125-preview": "Preview GPT-4 Turbo",
                "gpt-4-0613": "June 2023 GPT-4 snapshot",
                "gpt-4-1106-preview": "Preview GPT-4 Turbo",
                "gpt-4-turbo": "Latest GPT-4 Turbo release",
                "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",
                "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
                "gpt-4o": "Base GPT-4o model",
                "gpt-4o-mini": "Lightweight GPT-4o variant",
            },
            PROVIDER_DEEPSEEK: {
                "deepseek-chat": "DeepSeek Chat model",
                "deepseek-reasoner": "Specialized reasoning model",
            },
            PROVIDER_ANTHROPIC: {
                "claude-2": "Base Claude 2 model",
                "claude-2.0": "Enhanced Claude 2.0",
                "claude-2.1": "Latest Claude 2.1 release",
                "claude-3-opus-20240229": "Claude 3 Opus",
                "claude-3-sonnet-20240229": "Claude 3 Sonnet",
                "claude-3-haiku-20240307": "Claude 3 Haiku",
            },
        }

        DEFAULT_MODEL_PARAMS = {
            PROVIDER_OPENAI: {
                "model_name": "gpt-4-turbo-preview",  # (4) used on rare occasions
                "model_name": "gpt-4o",               # (3) debugging
                "model_name": "gpt-4-turbo",          # (2) used often
                "model_name": "gpt-3.5-turbo",        # (1) most used
                "model_name": "gpt-3.5-turbo-1106",   # (1) most used
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek-reasoner",
                "model_name": "deepseek-coder",
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-2.1",
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.7,
                "max_tokens": 800,
            },
        }

        API_KEY_ENV_VARS = {
            PROVIDER_OPENAI: "OPENAI_API_KEY",
            PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
            PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        }

        BASE_URLS = {
            PROVIDER_DEEPSEEK: "https://api.deepseek.com",
        }

        # We reorder these lines to switch default providers easily
        DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
        DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
        DEFAULT_PROVIDER = PROVIDER_OPENAI

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()
            self.provider = self.DEFAULT_PROVIDER.lower()
            self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
            self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
            self.setup_logger()

        def configure_utf8_encoding(self):
            """
            Ensure UTF-8 encoding for standard output and error streams.
            """
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")

        def setup_logger(self):
            """
            YAML logging via Loguru: clears logs, sets global context, and configures sinks
            """
            log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
            log_filepath = os.path.join(self.log_dir, log_filename)
            open(log_filepath, "w").close()
            logger.remove()
            logger.configure(extra={"provider": self.provider, "model": self.model_params.get("model_name")})

            def yaml_sink(log_message):
                log_record = log_message.record
                formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
                formatted_level = f"!{log_record['level'].name}"
                logger_name = log_record["name"]
                formatted_function_name = f"*{log_record['function']}"
                line_number = log_record["line"]
                extra_provider = log_record["extra"].get("provider")
                extra_model = log_record["extra"].get("model")
                log_message_content = log_record["message"]

                if "\n" in log_message_content:
                    formatted_message = "|\n" + "\n".join(f"  {line}" for line in log_message_content.splitlines())
                else:
                    formatted_message = f"'{log_message_content}'" if ":" in log_message_content else log_message_content

                log_lines = [
                    f"- time: {formatted_timestamp}",
                    f"  level: {formatted_level}",
                    f"  name: {logger_name}",
                    f"  funcName: {formatted_function_name}",
                    f"  lineno: {line_number}",
                ]
                if extra_provider is not None:
                    log_lines.append(f"  provider: {extra_provider}")
                if extra_model is not None:
                    log_lines.append(f"  model: {extra_model}")

                log_lines.append(f"  message: {formatted_message}")
                log_lines.append("")
                with open(log_filepath, "a", encoding="utf-8") as log_file:
                    log_file.write("\n".join(log_lines) + "\n")

            logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")


    class LLMInteractions:
        """
        Handles interactions with OpenAI, DeepSeek, and Anthropic APIs in a generalized way.
        """

        CLIENT_FACTORIES = {
            Config.PROVIDER_OPENAI:  lambda key: OpenAI(api_key=key),
            Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),
            Config.PROVIDER_ANTHROPIC:lambda key: Anthropic(api_key=key),
        }

        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):
            self.provider = provider or Config().provider
            cfg = Config()
            defaults = cfg.DEFAULT_MODEL_PARAMS.get(self.provider, cfg.DEFAULT_MODEL_PARAMS[cfg.DEFAULT_PROVIDER])

            self.model_name = model_name or defaults["model_name"]
            self.temperature = temperature if temperature is not None else defaults["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

            api_key_env_var = Config.API_KEY_ENV_VARS.get(self.provider)
            api_key_to_use = api_key or os.getenv(api_key_env_var)
            try:
                self.client = self.CLIENT_FACTORIES[self.provider](api_key_to_use)
            except KeyError:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")

            # -----------------------------------------------------
            # LOGGING HELPERS
            # -----------------------------------------------------
        def _log_api_response(self, response):
            extra = {"prompt_tokens": getattr(getattr(response, 'usage', None), 'prompt_tokens', "N/A")}
            logger.bind(**extra).debug(response)

        def _log_api_error(self, exception, model_name, messages):
            logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")
            logger.error(f"Exception type: {type(exception).__name__}")
            logger.error(f"Detailed exception: {exception}")
            logger.error(f"Input messages: {messages}")

        def _execute_api_call(self, call_fn, model_name, messages):
            try:
                response = call_fn()
                self._log_api_response(response)
                return response
            except Exception as e:
                self._log_api_error(e, model_name, messages)
                return None

        # -----------------------------------------------------
        # PROVIDER-SPECIFIC CALLS
        # -----------------------------------------------------
        def _openai_call(self, messages, model_name, temperature, max_tokens):
            return self.client.chat.completions.create(
                model=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )

        def _anthropic_call(self, messages, model_name, temperature, max_tokens):
            user_msgs = []
            system_content = ""
            for msg in messages:
                if msg['role'] == 'system':
                    system_content += msg['content'] + "\n"
                elif msg['role'] == 'user':
                    user_msgs.append({"role": "user", "content": msg['content']})
            return self.client.messages.create(
                model=model_name,
                max_tokens=max_tokens,
                temperature=temperature,
                system=system_content.strip(),
                messages=user_msgs
            )

        def _deepseek_call(self, messages, model_name, temperature, max_tokens):
            sys_prompt = ""
            instructions_content = ""
            user_prompt = ""
            for msg in messages:
                if msg['role'] == 'system':
                    if not sys_prompt:
                        sys_prompt = msg['content']
                    else:
                        instructions_content += msg['content'] + "\n"
                elif msg['role'] == 'user':
                    user_prompt = msg['content']

            combined_prompt = f"{sys_prompt}\n{instructions_content}\n{user_prompt}"
            return self.client.chat.completions.create(
                model=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=[{"role": "user", "content": combined_prompt}]
            )

        # -----------------------------------------------------
        # UNIFIED DISPATCH
        # -----------------------------------------------------
        def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):
            provider_map = {
                Config.PROVIDER_OPENAI:  lambda: self._openai_call(messages, model_name, temperature, max_tokens),
                Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),
                Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),
            }

            call_fn = provider_map.get(self.provider)
            if call_fn is None:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")

            response = self._execute_api_call(call_fn, model_name, messages)
            if not response:
                return None

            # Return text only
            if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:
                if hasattr(response, 'choices') and response.choices:
                    return response.choices[0].message.content
            elif self.provider == Config.PROVIDER_ANTHROPIC:
                if hasattr(response, 'content') and response.content:
                    return response.content[0].text
            return None

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_tokens = max_tokens if max_tokens is not None else self.max_tokens
            return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)


    class OutputHandler:
        """
        Manages output display.
        """
        @staticmethod
        def create_hierarchical_prefix(step_number, sub_step_number):
            prefix = "+"
            if step_number > 1:
                prefix += " *" + " -" * (step_number - 2)
            if sub_step_number > 0:
                prefix += " -" * sub_step_number
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(
            all_refinements,
            header="Refinement Steps:",
            replace_quotes=True,
            replace_newlines="\n"
        ):
            print('\n')
            print(f"Here's the input prompt that was used:\n`{header}`")
            print('\n')
            print("Here's the result when executed with your version:")
            print('```')
            for refinement in all_refinements:
                agent_name = refinement['agent_name']
                for step_number, response in enumerate(refinement["outputs"], 1):
                    if response is not None:
                        # Convert to string, replace quotes/newlines
                        response_str = str(response)
                        if replace_quotes:
                            response_str = response_str.replace('\"', "'")
                        if replace_newlines:
                            response_str = response_str.replace('\n', replace_newlines)
                        prefix = f"{agent_name}: {step_number}/{refinement['iterations']}"
                        print(f'{OutputHandler.create_hierarchical_prefix(1, step_number)}{prefix}: "{response_str}"')
                    else:
                        prefix = f"{agent_name}: {step_number}/{refinement['iterations']}"
                        print(f'{OutputHandler.create_hierarchical_prefix(1, step_number)}{prefix}: "--- API Error - No Response ---"')
            print('```')
            print('\n')

        @staticmethod
        def display_evaluation_result(template_name, prompt_to_evaluate, evaluation_result):
            print("\n--- Evaluation Result ---")
            print(f"Evaluator: {template_name}")
            print(f"Prompt Evaluated: '{prompt_to_evaluate}'")
            print(f"Evaluation Result: {evaluation_result}")
            print("\n")


    class TemplateFileManager:
        """
        Manages prompt templates of various file types, performing lazy loading,
        placeholder replacement, and refinement execution. Also can combine
        or repeat chains in a flexible way via 'recipes'.
        """

        ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".txt", ".md")

        def __init__(self):
            self.template_dir = os.getcwd()
            self.template_cache = {}

        def get_template_metadata(self, template_name):
            file_path = self.get_template_path(template_name)
            if not file_path:
                return {}
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {
                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"'),
                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"'),
                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"'),
                }
            except Exception as e:
                logger.error(f"Error reading template metadata from {file_path}: {e}")
                return {}

        def refresh_template_cache(self, force_reload=False):
            self.template_cache.clear()
            if force_reload:
                pattern = os.path.join(self.template_dir, "**", "*.*")
                all_files = glob.glob(pattern, recursive=True)
                for file_path in all_files:
                    _, ext = os.path.splitext(file_path)
                    if ext.lower() in self.ALLOWED_FILE_EXTS:
                        name = os.path.splitext(os.path.basename(file_path))[0]
                        self.template_cache[name] = file_path

        def extract_placeholders(self, template_name):
            file_path = self.get_template_path(template_name)
            if not file_path:
                return []
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                # strip out CDATA
                no_cdata = re.sub(r'<!\[CDATA\[.*?\]\]>', '', content, flags=re.DOTALL)
                found = re.findall(r'\[(.*?)\]', no_cdata)
                return list(set(found))
            except Exception as e:
                logger.error(f"Error extracting placeholders from {file_path}: {e}")
                return []

        def find_similar_templates(self, partial_name):
            from difflib import get_close_matches
            all_names = list(self.template_cache.keys())
            return get_close_matches(partial_name, all_names, n=5, cutoff=0.5)

        def prefetch_templates(self, template_name_list):
            for name in template_name_list:
                _ = self.get_template_path(name)

        def _extract_value_from_content(self, content, pattern):
            match = re.search(pattern, content)
            return match.group(1) if match else None

        def _find_template(self, template_name):
            for ext in self.ALLOWED_FILE_EXTS:
                pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")
                files = glob.glob(pattern, recursive=True)
                if files:
                    return files[0]
            return None

        def get_template_path(self, template_name):
            if template_name not in self.template_cache:
                file_path = self._find_template(template_name)
                if file_path:
                    self.template_cache[template_name] = file_path
            return self.template_cache.get(template_name)

        def list_templates(self, exclude_versions=None, exclude_statuses=None, exclude_none_versions=False, exclude_none_statuses=False):
            pattern = os.path.join(self.template_dir, "**", "*.*")
            candidate_files = glob.glob(pattern, recursive=True)
            all_info = {}
            for fpath in candidate_files:
                base_name = os.path.splitext(os.path.basename(fpath))[0]
                ext = os.path.splitext(fpath)[1].lower()
                if ext in self.ALLOWED_FILE_EXTS:
                    try:
                        with open(fpath, 'r', encoding='utf-8') as f:
                            content = f.read()
                        version = self._extract_value_from_content(content, r'<version value="([^"]*)"')
                        status = self._extract_value_from_content(content, r'<status value="([^"]*)"')
                        description = self._extract_value_from_content(content, r'<description value="([^"]*)"')
                        all_info[base_name] = {
                            'path': fpath,
                            'version': version,
                            'status': status,
                            'description': description,
                        }
                    except Exception as e:
                        logger.error(f"Error loading template from {fpath}: {e}")
            filtered = {}
            for tname, info in all_info.items():
                if (not exclude_versions or info['version'] not in exclude_versions) and \
                   (not exclude_statuses or info['status'] not in exclude_statuses) and \
                   (not exclude_none_versions or info['version'] is not None) and \
                   (not exclude_none_statuses or info['status'] is not None):
                    filtered[tname] = info
            return filtered

        def load_template_from_file(self, template_file_path, initial_prompt=""):
            try:
                with open(template_file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                initial_prompt = str(initial_prompt)
                prompt_length = len(initial_prompt)
                placeholders = {
                    '[OUTPUT_FORMAT]': 'plain_text',
                    '[ORIGINAL_PROMPT_LENGTH]': str(prompt_length),
                    '[RESPONSE_PROMPT_LENGTH]': str(int(prompt_length * 0.9)),
                    '[INPUT_PROMPT]': initial_prompt,
                    '[ADDITIONAL_CONSTRAINTS]': '',
                    '[ADDITIONAL_PROCESS_STEPS]': '',
                    '[ADDITIONAL_GUIDELINES]': '',
                    '[ADDITIONAL_REQUIREMENTS]': '',
                }
                for ph, val in placeholders.items():
                    file_content = file_content.replace(ph, val)
                return file_content
            except Exception as e:
                logger.error(f"Error loading template content from {template_file_path}: {e}")
                return None

        def _extract_template_parts(self, text):
            system_tag = '<system_prompt value="'
            start = text.find(system_tag)
            if start == -1:
                return "", ""
            start += len(system_tag)
            end = text.find("\"/>", start)
            sys_prompt = text[start:end]
            i_start = text.find("<instructions>")
            i_end = text.find("</instructions>")
            if i_start == -1 or i_end == -1:
                return sys_prompt, ""
            i_start += len("<instructions>")
            instructions = text[i_start:i_end]
            return sys_prompt, instructions

        def _build_messages(self, system_prompt, instructions_content, user_prompt):
            return [
                {"role": "system", "content": str(system_prompt)},
                {"role": "system", "content": str(instructions_content)},
                {"role": "user", "content": str(user_prompt)},
            ]

        def execute_prompt_refinement_chain_from_file(
            self,
            template_file_path,
            initial_prompt,
            agent,
            refinement_count=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            initial_prompt = str(initial_prompt)
            content = self.load_template_from_file(template_file_path, initial_prompt)
            if not content:
                return None
            if display_instructions:
                logger.info('=' * 60)
                logger.info(content)
                logger.info('=' * 60)
            sys_prompt, instr = self._extract_template_parts(content)
            current = str(initial_prompt)
            results = []
            for _ in range(refinement_count):
                msgs = self._build_messages(sys_prompt, instr, current)
                refined = agent.generate_response(msgs, model_name=model_name, temperature=temperature, max_tokens=max_tokens)
                if refined:
                    refined_str = str(refined)
                    results.append(refined_str)
                    current = refined_str
            return results

        def _execute_single_template_refinement(self, template_name, initial_prompt, agent, refinement_count, **kwargs):
            initial_prompt = str(initial_prompt)
            path = self.get_template_path(template_name)
            if not path:
                logger.error(f"No template file found with name: {template_name}")
                return None
            return self.execute_prompt_refinement_chain_from_file(
                path, initial_prompt, agent, refinement_count, **kwargs
            )

        def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, agent, refinement_levels, **kwargs):
            if not all(isinstance(t, str) for t in template_name_list):
                logger.error("All items in template_name_list must be strings.")
                return None

            if isinstance(refinement_levels, int):
                counts = [refinement_levels] * len(template_name_list)
            elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):
                counts = refinement_levels
            else:
                logger.error("refinement_levels must be int or a list matching the length of template_name_list.")
                return None

            all_results = []
            current_prompt = str(initial_prompt)
            for name, cnt in zip(template_name_list, counts):
                chain_result = self._execute_single_template_refinement(name, current_prompt, agent, cnt, **kwargs)
                if chain_result:
                    final_str = str(chain_result[-1])
                    current_prompt = final_str
                    all_results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})
            return all_results

        def execute_prompt_refinement_by_name(
            self,
            template_name_or_list: Union[str, List[str]],
            initial_prompt: str,
            agent,
            refinement_levels: Union[int, List[int]] = 1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            initial_prompt = str(initial_prompt)
            if isinstance(template_name_or_list, str):
                return self._execute_single_template_refinement(
                    template_name_or_list,
                    initial_prompt,
                    agent,
                    refinement_levels,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            elif isinstance(template_name_or_list, list):
                if not all(isinstance(x, str) for x in template_name_or_list):
                    logger.error("All items in template_name_or_list must be strings.")
                    return None
                return self._execute_multiple_template_refinement(
                    template_name_or_list,
                    initial_prompt,
                    agent,
                    refinement_levels,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            else:
                logger.error("template_name_or_list must be str or list[str].")
                return None

        def execute_prompt_evaluation_by_name(
            self,
            template_name,
            prompt_to_evaluate,
            agent,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            prompt_to_evaluate = str(prompt_to_evaluate)
            path = self.get_template_path(template_name)
            if not path:
                logger.error(f"No template file found with name: {template_name}")
                return None
            content = self.load_template_from_file(path, prompt_to_evaluate)
            if not content:
                return None
            if display_instructions:
                logger.info('=' * 60)
                logger.info(content)
                logger.info('=' * 60)
            sys_prompt, instr = self._extract_template_parts(content)
            msgs = self._build_messages(sys_prompt, instr, prompt_to_evaluate)
            return agent.generate_response(msgs, model_name=model_name, temperature=temperature, max_tokens=max_tokens)

        # -----------------------------------------------------
        # RECIPE SYSTEM FOR LAYERING
        # -----------------------------------------------------
        def execute_recipe(self, recipe: List[Dict], agent, initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:
            """
            Enhanced recipe executor with minimal type-checking; always uses strings.
            Steps can define:
              - chain: str or list[str]
              - repeats: int
              - gather: bool
              - aggregator_chain: optional chain
            Returns a dictionary with final output and a history log.
            """
            current_input = str(initial_prompt)
            refinement_history = []
            gathered_outputs = []

            for step_index, step in enumerate(recipe, start=1):
                chain = step.get("chain")
                repeats = step.get("repeats", 1)
                gather = step.get("gather", False)
                aggregator_chain = step.get("aggregator_chain")

                # Basic validation
                if not chain:
                    logger.error(f"Recipe step {step_index} missing 'chain' key.")
                    continue

                step_gathered = []
                for rep in range(repeats):
                    # Each iteration reuses current_input unless aggregator updates it
                    refinement_data = self.execute_prompt_refinement_by_name(
                        template_name_or_list=chain,
                        initial_prompt=current_input,
                        agent=agent,
                        refinement_levels=1
                    )
                    if refinement_data:
                        # For logging
                        refinement_history.append({
                            "step": step_index,
                            "repeat": rep + 1,
                            "chain": chain,
                            "result": refinement_data
                        })
                        final_str = str(refinement_data[-1])
                        step_gathered.append(final_str)
                        if not gather:
                            current_input = final_str

                if gather and step_gathered:
                    gathered_outputs.extend(step_gathered)
                    if aggregator_chain:
                        aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])
                        aggregator_data = self.execute_prompt_refinement_by_name(
                            template_name_or_list=aggregator_chain,
                            initial_prompt=aggregator_prompt,
                            agent=agent,
                            refinement_levels=1
                        )
                        if aggregator_data:
                            # For logging
                            refinement_history.append({
                                "step": step_index,
                                "aggregator_chain": aggregator_chain,
                                "aggregator_input": aggregator_prompt,
                                "aggregator_result": aggregator_data
                            })
                            aggregator_str = str(aggregator_data[-1])
                            current_input = aggregator_str
                        else:
                            current_input = step_gathered[-1]
                    else:
                        # No aggregator, so last item in step_gathered
                        current_input = step_gathered[-1]

            return {
                "final_output": current_input,
                "refinement_history": refinement_history,
                "gathered_outputs": gathered_outputs
            }


    class Execution:
        """
        Main execution class.
        """
        def __init__(self, provider=None):
            self.config = Config()
            self.agent = LLMInteractions(provider=provider)
            self.template_manager = TemplateFileManager()
            self.output_handler = OutputHandler()

        def execute_prompt_evaluation_by_name(
            self,
            template_name,
            prompt_to_evaluate,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            return self.template_manager.execute_prompt_evaluation_by_name(
                template_name=template_name,
                prompt_to_evaluate=str(prompt_to_evaluate),
                agent=self.agent,
                display_instructions=display_instructions,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )

        def log_usage_demo(self):
            self.template_manager.refresh_template_cache(force_reload=True)
            self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])

            placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")
            logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")

            metadata = self.template_manager.get_template_metadata("IntensityEnhancer")
            logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")

            close_matches = self.template_manager.find_similar_templates("PromptOpt")
            logger.info(f"Close matches for 'PromptOpt': {close_matches}")

            all_temps = self.template_manager.list_templates()
            logger.info(f"Found a total of {len(all_temps)} templates.")
            logger.info("Template keys: " + ", ".join(all_temps.keys()))

        def run(self):
            self.template_manager.refresh_template_cache(force_reload=True)
            initial_prompt = "the current version of the code seems unneccessarily complex and difficult to read. without the use of excessive comments, how would you propose we organize the code in a much more simplified manner without loosing the core functionality? as an example, we could Refine the existing code by organizing it into logical sections while maintaining all functionality. Minimize comments and unnecessary wording for improved clarity and readability."

            # Example recipe usage
            recipe_steps = [
                {
                    "chain": ["PromptOptimizerExpert", "MultiResponseSelector"],
                    "repeats": 3,
                    "gather": True,
                    "aggregator_chain": ["PromptOptimizerExpert"]
                },
                {
                    "chain": "RunwayPromptBuilder",
                    "repeats": 1
                },
                {
                    "chain": "MultiResponseSelector",
                    "repeats": 1
                }
            ]

            recipe_result = self.template_manager.execute_recipe(
                recipe=recipe_steps,
                agent=self.agent,
                initial_prompt=initial_prompt
            )

            print(f'recipe_result: {recipe_result}')
            logger.info(f"Final recipe result: {recipe_result['final_output']}")
            logger.info(f"Refinement history: {recipe_result['refinement_history']}")
            logger.info(f"Gathered outputs: {recipe_result['gathered_outputs']}")


    if __name__ == "__main__":
        provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
        execution = Execution(provider=provider_to_use)
        execution.run()

    ```



Notice the effortless efficiency in use:
- No manual effort needed for new agent usage, you just need to write the name.
- Dynamic templates morph fluently with placeholders, ensuring unparalleled adaptability

Please express in a single sentence exactly what it is and what makes this particular approach for an llm framework uniquely suited for the task.
