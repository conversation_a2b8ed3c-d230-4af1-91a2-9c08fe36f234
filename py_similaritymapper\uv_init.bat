@echo off
REM Initialize uv project and install dependencies for py_similaritymapper

echo ===== py_similaritymapper Setup =====
echo Initializing uv project and installing dependencies...
echo.

REM Check if uv is available
uv --version >nul 2>&1
if errorlevel 1 (
    echo Error: uv not found! Please install uv first.
    echo Visit: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

REM Initialize uv project if not already done
if not exist "uv.lock" (
    echo Initializing uv project...
    uv init --no-readme --no-workspace
    if errorlevel 1 (
        echo Error: Failed to initialize uv project
        pause
        exit /b 1
    )
)

REM Install dependencies
echo Installing textdistance...
uv add textdistance
if errorlevel 1 (
    echo Error: Failed to install textdistance
    pause
    exit /b 1
)

REM Install development dependencies (optional)
echo Installing development dependencies...
uv add --dev pytest black isort
if errorlevel 1 (
    echo Warning: Failed to install dev dependencies (continuing anyway)
)

echo.
echo ===== Setup Complete =====
echo.
echo Project initialized successfully!
echo.
echo To run the similarity mapper:
echo   python src\main.py ..\in
echo.
echo Or use the quick run script:
echo   run.bat
echo.
pause
