
    ### File Structure

    ```
    ├── agents
    │   ├── __init__.py
    │   ├── base
    │   │   ├── __init__.py
    │   │   └── base_agents.py
    │   └── level1
    │       ├── __init__.py
    │       ├── agent_template_level1.py
    │       ├── intensity_enhancer.py
    │       ├── key_factor_identifier.py
    │       └── title_extractor.py
    └── openai_agenticframework.py
    ```
    ### 1. `agents\__init__.py`

    #### `agents\__init__.py`

    ```python
    # agents/__init__.py

    # Base (Depth 0)
    from .base.base_agents import BaseAgent, BaseInstructionAgent

    # Level 1 (Single-Line Responders)
    from .level1.intensity_enhancer import IntensityEnhancer
    from .level1.key_factor_identifier import KeyFactorIdentifier
    from .level1.title_extractor import TitleExtractor

    __all__ = [
        # Base
        "BaseAgent",
        "BaseInstructionAgent",

        # Level 1
        "IntensityEnhancer",
        "KeyFactorIdentifier",
        "TitleExtractor",
    ]

    ```
    ### 2. `agents\base\__init__.py`

    #### `agents\base\__init__.py`

    ```python

    ```
    ### 3. `agents\base\base_agents.py`

    #### `agents\base\base_agents.py`

    ```python
    # agents/base/base_agents.py

    from abc import ABC, abstractmethod
    import json

    class BaseAgent(ABC):
        """Abstract base class for all agents, defining the common interface."""

        @abstractmethod
        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            """
            Initialize the agent.

            Args:
                input_prompt (str): The initial user prompt to be transformed.
                length_multiplier (float): A multiplier controlling response length vs. original prompt length.
                **kwargs: Additional arguments specific to the agent's needs.
            """
            self.input_prompt = input_prompt
            self.length_multiplier = length_multiplier

        @abstractmethod
        def initialize_agent(self):
            """Prepare the agent's instructions and system prompt."""
            pass

        @abstractmethod
        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the agent's specific logic.

            Args:
                openai_agent: An instance of the OpenAIAgent to make API calls.
                model_name: The name of the OpenAI model to use.
                temperature: The sampling temperature for the OpenAI API.
                max_tokens: The maximum number of tokens for the OpenAI API response.

            Returns:
                str: The transformed prompt.
            """
            pass


    class BaseInstructionAgent(BaseAgent):
        SYSTEM_PROMPT = "You are a helpful assistant."
        AGENT_INSTRUCTIONS = ""
        DEFAULT_OUTPUT_FORMAT = "text"
        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
            "output_format": "[OUTPUT_FORMAT]"
        }

        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            self.output_format = kwargs.get("output_format", self.DEFAULT_OUTPUT_FORMAT)
            self.include_response_length = kwargs.get("include_response_length", True)
            super().__init__(input_prompt, length_multiplier, **kwargs)

        def _get_placeholders(self):
            original_length = len(self.input_prompt)
            max_allowed = int(original_length * self.length_multiplier)
            return {
                "input": self.input_prompt,
                "prompt_length": str(original_length),
                "response_length": str(max_allowed),
                "output_format": self._format_output_instruction()
            }

        def _format_output_instruction(self):
            fmt = self.output_format.lower()
            if fmt == "text":
                return "Your response should be in plain text."
            if fmt == "single_line":
                return "Respond in single line unformatted plaintext without linebreaks."
            if fmt == "plain_text":
                return "Respond in unformatted plaintext."
            if fmt == "json":
                return "Your response should be formatted as JSON."
            if fmt == "xml":
                return "Your response should be formatted as XML."
            elif fmt == "python":
                return "Provide valid Python code where necessary."
            else:
                return ""

        def initialize_agent(self):
            placeholders = self._get_placeholders()
            instructions = self.AGENT_INSTRUCTIONS
            for k, v in placeholders.items():
                if k in self.PLACEHOLDERS:
                    instructions = instructions.replace(self.PLACEHOLDERS[k], v)
            return {"systemprompt": self.SYSTEM_PROMPT, "instructions": instructions}

        def transform(self, openai_agent, model_name=None, temperature=None, max_tokens=None):
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]
            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return self._handle_output_format(response or "")

        def _handle_output_format(self, response):
            fmt = self.output_format.lower()
            if not response.strip():
                return ""
            if fmt == "json":
                try:
                    json.loads(response)
                    return response
                except json.JSONDecodeError:
                    return json.dumps({"fallback": response})
            return response

    ```
    ### 4. `agents\level1\__init__.py`

    #### `agents\level1\__init__.py`

    ```python
    # agents/level1/__init__.py

    # Re-export all Level 1 agents + the template if desired
    from .intensity_enhancer import IntensityEnhancer
    from .key_factor_identifier import KeyFactorIdentifier
    from .title_extractor import TitleExtractor

    __all__ = [
        "IntensityEnhancer",
        "KeyFactorIdentifier",
        "TitleExtractor",
    ]

    ```
    ### 5. `agents\level1\agent_template_level1.py`

    #### `agents\level1\agent_template_level1.py`

    ```python
    # agents/level1/template_level1_agent.py

    from agents.base.base_agents import BaseInstructionAgent

    class TemplateLevel1Agent(BaseInstructionAgent):

        SYSTEM_PROMPT: str = "You are a specialized single-line response agent."

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Format: single-line
        - Response must be concise and not exceed [RESPONSE_PROMPT_LENGTH] characters.

        Objective:
        - [DEFINE THE SPECIFIC OBJECTIVE OF YOUR AGENT HERE]

        Input:
        '''[INPUT_PROMPT]'''
        """

    ```
    ### 6. `agents\level1\intensity_enhancer.py`

    #### `agents\level1\intensity_enhancer.py`

    ```python
    from ..base.base_agents import BaseInstructionAgent

    class IntensityEnhancer(BaseInstructionAgent):
        SYSTEM_PROMPT = "Amplify emotional intensity in prompts."

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Format: [OUTPUT_FORMAT]
        - Original length: [ORIGINAL_PROMPT_LENGTH] chars
        - Max length: [RESPONSE_PROMPT_LENGTH] chars

        Role: Intensity Amplifier

        Objective: Increase emotional impact of prompts

        Process:
        1. Analyze prompt for emotional cues
        2. Identify areas for intensity enhancement
        3. Inject evocative language strategically
        4. Ensure original intent is preserved

        Guidelines:
        - Use strong, evocative language
        - Amplify existing sentiment
        - Maintain logical flow and coherence

        Requirements:
        - Intensity: Increase emotional impact
        - Integrity: Preserve original intent
        - Clarity: Ensure prompt remains clear

        Input:
        '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.9, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 7. `agents\level1\key_factor_identifier.py`

    #### `agents\level1\key_factor_identifier.py`

    ```python
    from ..base.base_agents import BaseInstructionAgent

    class KeyFactorIdentifier(BaseInstructionAgent):
        SYSTEM_PROMPT = "Identify the key factor for maximum impact and issue a directive."

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Format: [OUTPUT_FORMAT]
        - Original length: [ORIGINAL_PROMPT_LENGTH] chars
        - Max length: [RESPONSE_PROMPT_LENGTH] chars

        Role: Strategic prioritization expert

        Objective: Identify and issue a directive for the key factor with maximum impact

        Process:
        1. Analyze input prompt for potential factors
        2. Evaluate each factor's potential impact
        3. Select the single factor with highest leverage
        4. Formulate and articulate a directive

        Guidelines:
        - Frame output as a clear instruction
        - Focus on a single, actionable key factor
        - Provide compelling justification

        Requirements:
        - Single Directive: Issue only one primary directive
        - Impact Focus: Target factor with significant positive impact
        - Justification: Provide clear rationale

        Output Format:
        - **Key Factor:** [The identified key factor]
        - **Primary Directive:** [A clear instruction focusing on the identified key factor]
        - **Reasoning:** [A concise explanation justifying this directive]

        Input:
        '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 8. `agents\level1\title_extractor.py`

    #### `agents\level1\title_extractor.py`

    ```python
    from ..base.base_agents import BaseInstructionAgent

    class TitleExtractor(BaseInstructionAgent):
        SYSTEM_PROMPT = "Extract concise, relevant titles from given content."

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Format: [OUTPUT_FORMAT]
        - Original length: [ORIGINAL_PROMPT_LENGTH] chars
        - Max length: [RESPONSE_PROMPT_LENGTH] chars

        Role: Title extraction and optimization specialist

        Objective: Extract and refine titles from various content types

        Process:
        1. Analyze input content for key themes and concepts
        2. Identify most relevant and impactful elements
        3. Craft concise title capturing core essence
        4. Refine for clarity and impact
        5. Ensure title meets length and format requirements

        Guidelines:
        - Prioritize clarity and relevance in title creation
        - Capture main topic or theme succinctly
        - Use strong, descriptive language
        - Avoid unnecessary words or jargon

        Requirements:
        - Relevance: Title accurately reflects main content
        - Conciseness: Maximum 50 characters
        - Clarity: Easily understandable and specific
        - Impact: Engaging and informative

        Input:
        '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 9. `openai_agenticframework.py`

    #### `openai_agenticframework.py`

    ```python
    # openai_agenticframework.py

    import os
    import sys
    import json
    import difflib
    from dotenv import load_dotenv
    from openai import OpenAI

    # Import from the top-level `agents` package
    from agents import (
        # Base (Level 0)
        BaseAgent,

        # Level 1 (Single-Line Responders)
        IntensityEnhancer,   # Simple emotional amplification
        KeyFactorIdentifier, # Simple key factor identifier
        TitleExtractor,      # Simple title generation
    )


    def configure_utf8_encoding():
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")


    configure_utf8_encoding()


    class GlobalConfig:
        AVAILABLE_MODELS = {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo with vision",
            "gpt-4-turbo-preview": "Preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        }

        DEFAULT_MODEL_PARAMETERS = {
            "model_name": "gpt-4-turbo",
            "temperature": 0.7,
            "max_tokens": 800,
        }

        # Single unified registry: each entry has "class" plus a "defaults" dict
        AGENTS = {
            "intensity_enhancer": {
                "class": IntensityEnhancer,
                "defaults": {"length_multiplier": 1.0, "output_format": "single_line"},
            },
            "key_factor_identifier": {
                "class": KeyFactorIdentifier,
                "defaults": {"length_multiplier": 1.0, "output_format": "single_line"},
            },
            "title_extractor": {
                "class": TitleExtractor,
                "defaults": {"length_multiplier": 1.5, "output_format": "single_line"},
            },
        }

        @classmethod
        def register_agent_type(cls, agent_name: str, agent_class, defaults=None):
            """
            Registers a new agent type in the global config, allowing the system
            to grow in new directions without modifying the code in multiple places.
            """
            if not issubclass(agent_class, BaseAgent):
                raise ValueError("agent_class must be a subclass of BaseAgent")
            cls.AGENTS[agent_name] = {
                "class": agent_class,
                "defaults": defaults or {},
            }


    class OpenAIAgent:
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            load_dotenv()
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            defaults = GlobalConfig.DEFAULT_MODEL_PARAMETERS

            # Environment-variable overrides for model, temperature, max_tokens
            self.model_name = model_name or os.getenv("MODEL_NAME_OVERRIDE") or defaults["model_name"]
            self.temperature = (
                temperature
                if temperature is not None
                else float(os.getenv("TEMPERATURE_OVERRIDE", defaults["temperature"]))
            )
            self.max_tokens = (
                max_tokens
                if max_tokens is not None
                else int(os.getenv("MAX_TOKENS_OVERRIDE", defaults["max_tokens"]))
            )

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_mtokens = max_tokens if max_tokens is not None else self.max_tokens

            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temp,
                max_tokens=used_mtokens,
                messages=messages,
            )
            return response.choices[0].message.content


    class AgentFactory:
        @staticmethod
        def create_agent(agent_type, input_prompt, length_multiplier, include_response_length, output_format=None):
            # Retrieve the agent config from the single unified registry
            agent_config = GlobalConfig.AGENTS.get(agent_type)
            if not agent_config:
                valid = list(GlobalConfig.AGENTS.keys())
                closest = difflib.get_close_matches(agent_type, valid, n=1)
                suggestion = f"Did you mean '{closest[0]}'?" if closest else f"Must be one of: {valid}"
                raise ValueError(f"Unknown agent type '{agent_type}'. {suggestion}")

            agent_class = agent_config["class"]
            defaults = agent_config.get("defaults", {})

            # Merge default parameters with any call-time overrides
            final_kwargs = {
                "input_prompt": input_prompt,
                "length_multiplier": length_multiplier,
                "include_response_length": include_response_length,
                "output_format": output_format,
            }
            final_kwargs = {**defaults, **final_kwargs}

            return agent_class(**final_kwargs)


    class DisplayManager:
        @staticmethod
        def create_hierarchical_prefix(step_num, sub_step_num):
            prefix = "+"
            if step_num > 1:
                prefix += " *" + " -" * (step_num - 2)
            if sub_step_num > 0:
                prefix += " -" * sub_step_num
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(refinements, header="Refinement Results:"):
            print(header)
            for step_idx, sublist in enumerate(refinements, start=1):
                for sub_idx, text in enumerate(sublist):
                    prefix = DisplayManager.create_hierarchical_prefix(step_idx, sub_idx)
                    if isinstance(text, str):
                        try:
                            parsed = json.loads(text)
                            print(f"{prefix}")
                            print(json.dumps(parsed, indent=4))
                        except json.JSONDecodeError:
                            print(f'{prefix}"{text}"')
                    else:
                        print(f"{prefix}{text}")

        @staticmethod
        def display_instructions(agent_config):
            print("\nSystem Prompt:\n    {}".format(agent_config["systemprompt"]))
            print("\nAgent Instructions:\n    {}".format(agent_config["instructions"]))


    class RefinementEngine:
        def __init__(
            self,
            openai_agent,
            agent_type="intensity_enhancer",
            prompt_chain=None,
            initial_input="",
            refinement_levels=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
            display_instructions=False,
            length_multiplier=0.9,
            include_response_length=True,
            output_format=None
        ):
            """
            A flexible orchestrator that can:
              - Work with multiple agent types
              - Support repeated "refinements" of the user input
              - Override model settings at initialization
            """
            self.openai_agent = openai_agent
            self.agent_type = agent_type
            self.prompt_chain = prompt_chain or []
            self.initial_input = initial_input
            self.refinement_levels = refinement_levels or [1]

            # Default or specified model settings
            self.model_name = model_name
            self.temperature = temperature
            self.max_tokens = max_tokens

            self.display_instructions = display_instructions
            self.length_multiplier = length_multiplier
            self.include_response_length = include_response_length
            self.output_format = output_format

            # Instantiate chosen agent from the unified registry
            self.agent = AgentFactory.create_agent(
                agent_type=self.agent_type,
                input_prompt=self.initial_input,
                length_multiplier=self.length_multiplier,
                include_response_length=self.include_response_length,
                output_format=self.output_format
            )

        def run(self, override_model=None, override_temperature=None, override_max_tokens=None):
            """
            Run the refinement process.

            Optional overrides:
                override_model       : If provided, uses this model name for all refinements.
                override_temperature : If provided, uses this temperature for all refinements.
                override_max_tokens  : If provided, uses this as the max_tokens limit for all refinements.
            """
            # If overrides are supplied, use them; otherwise fall back to constructor-level or default
            effective_model = override_model or self.model_name
            effective_temperature = override_temperature if override_temperature is not None else self.temperature
            effective_max_tokens = override_max_tokens if override_max_tokens is not None else self.max_tokens

            agent_config = self.agent.initialize_agent()
            current_prompt = self.initial_input
            all_refinements = [[self.initial_input]]

            # Ensure we have enough refinement levels
            levels = self.refinement_levels[:]
            if len(levels) < len(self.prompt_chain):
                levels += [1] * (len(self.prompt_chain) - len(levels))

            for step_idx, _ in enumerate(self.prompt_chain, start=1):
                sub_refinements = []
                if step_idx == 1:
                    sub_refinements.append(current_prompt)

                num_refinements = levels[step_idx - 1]
                for _ in range(num_refinements):
                    current_prompt = self.agent.transform(
                        openai_agent=self.openai_agent,
                        model_name=effective_model,
                        temperature=effective_temperature,
                        max_tokens=effective_max_tokens
                    )
                    # Fallback if no response
                    if not current_prompt:
                        print("No response. Skipping further refinements.")
                        break
                    sub_refinements.append(current_prompt)
                all_refinements.append(sub_refinements)

            note = "WITH" if self.include_response_length else "WITHOUT"
            DisplayManager.display_hierarchical_refinements(
                all_refinements,
                header=f"=== Refinements ({note} 'Maximum response length') ==="
            )

            if self.display_instructions:
                forced_agent = AgentFactory.create_agent(
                    agent_type=self.agent_type,
                    input_prompt=self.initial_input,
                    length_multiplier=self.length_multiplier,
                    include_response_length=True,
                    output_format=self.output_format
                )
                forced_config = forced_agent.initialize_agent()
                DisplayManager.display_instructions(forced_config)

            return current_prompt


    def quick_chain(agent_sequence, initial_input, openai_agent):
        """
        Minimal utility to apply a sequence of agents to a single text prompt,
        returning the final transformed output.
        """
        current = initial_input
        for agent_type in agent_sequence:
            agent = AgentFactory.create_agent(
                agent_type=agent_type,
                input_prompt=current,
                length_multiplier=1.0,
                include_response_length=True,
                output_format="single_line"
            )
            response = agent.transform(openai_agent)
            if not response:
                print("No response. Stopping chain.")
                break
            current = response
        return current


    def main():
        openai_agent = OpenAIAgent()


        # =======================================================
        # [intensity_enhancer]
        # =======================================================
        print(f"\n intensity_enhancer \n{'='*60}\n")

        initial_prompt = """Amidst the fervent discussions on mastering the art of communication with LLM models, the emphasis has overwhelmingly shifted towards crafting concise, potent prompts that cut through the noise, replacing verbose, unclear alternatives. Your mission is to create a detailed blueprint that skillfully distills wordy language, preserving its core message while significantly enhancing the clarity and effectiveness of LLM responses."""
        initial_prompt = """without breaking any of the existing functionality, how could this code be written in a cleaner and more organized manner resulting in simple but effective code that is flexible and easy to understand?"""

        initial_prompt = """please go through the attached code and visually represent each component in a hierarchical manner"""

        initial_prompt = """what are the most popular library for automatically generating a visual uml diagram from python projects?"""

        initial_prompt = """How can we transform this code into a masterpiece of clarity and organization, enhancing its simplicity and effectiveness without sacrificing any of its current functionality, making it a model of flexibility and effortless comprehension?"""

        initial_prompt = """What are the most revered libraries for effortlessly creating stunning visual UML diagrams from Python projects?"""
        initial_prompt = """what is the BEST technique/method for visually representing the core components of python projects/code in a easy-to-read and intuitive hierarchy?"""
        initial_prompt = """What is the most powerful and effective technique for vividly mapping the essential elements of a Python project's code into a clear, intuitive, and easily understandable hierarchy?"""



        initial_prompt = """What are the most revered alternative between `Emerge`, `Codemap`, and `Sourcetrail` for effortlessly creating stunning visual UML diagrams from Python projects?"""
        # initial_prompt = """What is the most powerful and effective technique for vividly mapping the essential elements of a Python project's code into a clear, intuitive, and easily understandable hierarchy?"""

        initial_prompt = """What separates `Codemap`, and `Sourcetrail` for effortlessly creating stunning visual UML diagrams from Python projects to other alternatives, such as libraries like `PlantUML`, `Pyreverse`, `PDGen` or `SequencePlot`?"""
        # initial_prompt = """What truly sets Codemap and Sourcetrail apart as paragons of innovation in crafting breathtakingly stunning visual UML diagrams from Python projects, compared to other alternatives like PlantUML, Pyreverse, PDGen, or SequencePlot?"""
        # initial_prompt = """What truly sets Codemap and Sourcetrail apart as paragons of innovation in crafting breathtakingly stunning visual UML diagrams from Python projects, compared to other alternatives like PlantUML, Pyreverse, PDGen, or SequencePlot?"""


        initial_prompt = """what is the simplest way to programmatically generate uml diagrams from any python-project?"""


        initial_prompt = """with the goal"""
        initial_prompt = """most revered and elegant ways to visualize code"""


        amplify_engine = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="intensity_enhancer",
            prompt_chain=["Continually emphasize the *intensity* in the expression of the *sentiment* of inputs"],
            initial_input=initial_prompt,
            refinement_levels=[5],
            display_instructions=False,
            length_multiplier=1.0,
            include_response_length=True,
            output_format="single_line"
        )
        amplify_engine.run()

        # =======================================================
        # [key_factor_identifier]
        # =======================================================
        print(f"\n key_factor_identifier \n{'='*60}\n")

        initial_prompt = """Create a streamlined method to transform verbose content into succinct, powerful prompts that retain essential meanings and heighten LLM response clarity. Start by evaluating the prompt details for a feedback system that prioritizes clarity, enhancement, and validation. Select the optimal sequence of agents: Clarity → Enhancement → Validation. Fine-tune the settings of each agent to achieve exactitude and relevance. Consistently monitor the transitions to ensure uniformity. Conclude by verifying that the final prompt remains true to its original intent and is effectively refined."""


        precision_engine = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="key_factor_identifier",
            prompt_chain=[
                "Analyze emotional resonance and core message. Strengthen language precision and impact. Amplify key points while maintaining clarity. Validate effectiveness and emotional weight. Ensure perfect balance of power and precision.",
            ],
            initial_input=initial_prompt,
            refinement_levels=[4, 1, 1, 1],
            display_instructions=False,
            length_multiplier=1.2,
            include_response_length=True,
            output_format="single_line"
        )
        # precision_engine.run()

        # =======================================================
        # [title_extractor]
        # =======================================================
        print(f"\n title_extractor \n{'='*60}\n")

        initial_prompt = """The article discusses the latest advancements in AI technology, focusing on natural language processing, machine learning algorithms, and their applications in various industries. It highlights the potential of AI to revolutionize healthcare, finance, and customer service."""

        title_engine = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="title_extractor",
            prompt_chain=["Analyze input content for key themes and concepts.", "Identify most relevant and impactful elements.", "Craft concise title capturing core essence.", "Refine for clarity and impact.", "Ensure title meets length and format requirements."],
            initial_input=initial_prompt,
            refinement_levels=[3],
            display_instructions=False,
            length_multiplier=1.5,
            include_response_length=True,
            output_format="single_line"
        )
        # title_engine.run()


    if __name__ == "__main__":
        main()

    ```
