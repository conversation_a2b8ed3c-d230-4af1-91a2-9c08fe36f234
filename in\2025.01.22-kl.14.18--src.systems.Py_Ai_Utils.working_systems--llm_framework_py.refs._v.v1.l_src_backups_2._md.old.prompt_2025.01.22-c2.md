given the provided projectstructure, what would be the best way to separate and isolate separate components within clean class structures, e.g. the functionality that allows for running templates only by specifying a name (without the need to hardcode/specify path) could be structured into a separate class. this new class could include methods for listing the available agents etc.

Here's the projectstructure:

    ### File Structure

    ```
    ├── level1
    │   ├── amplifiers
    │   │   ├── EmphasisEnhancer.xml
    │   │   └── IntensityEnhancer.xml
    │   ├── builders
    │   │   └── RunwayPromptBuilder.xml
    │   ├── characters
    │   │   └── ArtSnobCritic.xml
    │   ├── clarifiers
    │   │   ├── ClarityEnhancer.xml
    │   │   └── PromptEnhancer.xml
    │   ├── generators
    │   │   ├── CritiqueGenerator.xml
    │   │   ├── ExampleGenerator.xml
    │   │   └── MotivationalMuse.xml
    │   ├── identifiers
    │   │   ├── KeyFactorIdentifier.xml
    │   │   └── KeyFactorMaximizer.xml
    │   ├── meta
    │   │   ├── InstructionsCombiner.xml
    │   │   └── InstructionsGenerator.xml
    │   ├── optimizers
    │   │   ├── KeyFactorOptimizer.xml
    │   │   └── StrategicValueOptimizer.xml
    │   ├── reducers
    │   │   ├── ComplexityReducer.xml
    │   │   ├── IntensityReducer.xml
    │   │   └── TitleExtractor.xml
    │   ├── transformers
    │   │   ├── AbstractContextualTransformer.xml
    │   │   ├── AdaptiveEditor.xml
    │   │   └── GrammarCorrector.xml
    │   └── translators
    │       └── EnglishToNorwegianTranslator.xml
    └── main.py
    ```
    ### 1. `main.py`

    #### `main.py`

    ```python
    import os
    import sys
    from pathlib import Path
    import glob

    from dotenv import load_dotenv
    from openai import OpenAI



    # Ensure UTF-8 encoding for standard output and error streams
    def configure_utf8_encoding():
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()

    AVAILABLE_MODELS = {
        "gpt-3.5-turbo"          : "Base GPT-3.5 Turbo model",
        "gpt-3.5-turbo-1106"     : "Enhanced GPT-3.5 Turbo",
        "gpt-4"                  : "Latest GPT-4 stable release",
        "gpt-4-0125-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-0613"             : "June 2023 GPT-4 snapshot",
        "gpt-4-1106-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-turbo"            : "Latest GPT-4 Turbo release",
        "gpt-4-turbo-2024-04-09" : "Current GPT-4 Turbo with vision",
        "gpt-4-turbo-preview"    : "Latest preview GPT-4 Turbo",
        "gpt-4o"                 : "Base GPT-4o model",
        "gpt-4o-mini"            : "Lightweight GPT-4o variant",
    }

    DEFAULT_MODEL_PARAMETERS = {
        "model_name"  : "gpt-4-turbo",
        # "model_name"  : "gpt-3.5-turbo",
        "temperature" : 0.7,
        "max_tokens"  : 800,
    }

    class OpenAIAgent:
        """
        A client for interacting with the OpenAI API.
        """
        def __init__(
            self,
            api_key=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            # Initializes the OpenAI agent
            load_dotenv()
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            self.model_name = model_name or DEFAULT_MODEL_PARAMETERS["model_name"]
            self.temperature = temperature or DEFAULT_MODEL_PARAMETERS["temperature"]
            self.max_tokens = max_tokens or DEFAULT_MODEL_PARAMETERS["max_tokens"]

        def generate_response(
            self,
            messages,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            # Generates a response from the OpenAI API based on the provided messages
            used_model = model_name or self.model_name
            used_temperature = temperature if temperature is not None else self.temperature
            used_max_tokens = max_tokens if max_tokens is not None else self.max_tokens
            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temperature,
                max_tokens=used_max_tokens,
                messages=messages,
            )
            return response.choices[0].message.content

    def create_hierarchical_prefix(step_number, sub_step_number):
        """ Creates a hierarchical prefix string for output formatting. """
        prefix = "+"
        if step_number > 1:
            prefix += " *" + " -" * (step_number - 2)
        if sub_step_number > 0:
            prefix += " -" * sub_step_number
        return prefix + " "

    def load_template_from_xml_string(xml_file_path, initial_prompt):
        """Loads the template from an XML file as a string and replaces placeholders."""

        with open(xml_file_path, 'r') as f:
            xml_content = f.read()

        placeholders = {
            '[OUTPUT_FORMAT]': 'plain_text',  # Assuming default, can be made dynamic if needed
            '[ORIGINAL_PROMPT_LENGTH]': str(len(initial_prompt)),
            '[RESPONSE_PROMPT_LENGTH]': str(int(len(initial_prompt) * 0.9)),
            '[INPUT_PROMPT]': initial_prompt,
            '[ADDITIONAL_CONSTRAINTS]': '',  # Example of handling optional placeholders
            '[ADDITIONAL_PROCESS_STEPS]': '',
            '[ADDITIONAL_GUIDELINES]': '',
            '[ADDITIONAL_REQUIREMENTS]': '',
        }

        for placeholder, value in placeholders.items():
            xml_content = xml_content.replace(placeholder, value)

        return xml_content

    def execute_prompt_refinement_chain_from_xml(
        xml_file_path,
        initial_prompt,
        agent,
        display_instructions=False,
        num_iterations=1,
        model_name=None,
        temperature=None,
        max_tokens=None,
    ):
        """Executes prompt refinement iteratively using instructions from an XML file."""
        template_string = load_template_from_xml_string(xml_file_path, initial_prompt)

        if display_instructions:
            print('='*60)
            print(template_string)
            print('='*60)

        # Extract relevant parts from the template string (crude method)
        system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
        system_prompt_end = template_string.find("\"/>", system_prompt_start)
        system_prompt = template_string[system_prompt_start:system_prompt_end]

        instructions_start = template_string.find("<instructions>") + len("<instructions>")
        instructions_end = template_string.find("</instructions>", instructions_start)
        instructions_content = template_string[instructions_start:instructions_end]

        current_prompt = initial_prompt
        all_refinements = []

        for i in range(num_iterations):
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": instructions_content},
                {"role": "user", "content": current_prompt},
            ]

            refined_prompt = agent.generate_response(messages, model_name, temperature, max_tokens)
            all_refinements.append(refined_prompt)
            current_prompt = refined_prompt  # Update for the next iteration

        return all_refinements

    # NEW FUNCTION: Searches for XML file by name and executes refinement
    def execute_prompt_refinement_by_name(
        xml_name,
        initial_prompt,
        agent,
        display_instructions=False,
        num_iterations=1,
        model_name=None,
        temperature=None,
        max_tokens=None,
    ):
        """
        Searches for an XML file by name (without extension) within the project structure.
        If found, executes prompt refinement; otherwise, does nothing.
        """
        # Construct the search pattern
        search_pattern = os.path.join(os.getcwd(), "**", f"{xml_name}.xml")

        # Use glob to find the file
        matching_files = glob.glob(search_pattern, recursive=True)

        if matching_files:
            xml_file_path = matching_files[0]  # Use the first match if multiple are found

            print(f"Found XML file: {xml_file_path}") # Added a print to show path.
            return execute_prompt_refinement_chain_from_xml(
                xml_file_path,
                initial_prompt,
                agent,
                display_instructions=display_instructions,
                num_iterations=num_iterations,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        else:
            print(f"No XML file found with name: {xml_name}")
            return None # Return None when no file is found

    def display_hierarchical_refinements(all_refinements, header="Refinement Steps:", ):
        """
        Prints the hierarchical refinements to the console.
        """
        print(header)
        if all_refinements:
            for i, refinement in enumerate(all_refinements):
                print(f'{create_hierarchical_prefix(1, i)}"{refinement}"')

    if __name__ == "__main__":

        initial_prompt = """Immersed in the art of digital creation, I delve into the depths of crafting a new entity, where every step pulsates with significance, breathing life into its very core. Initially, I mold its essence within the code sanctuaries of `agents/level1/prompt_chain_inquirer.py`; I then intricately lace this nascent being into our system's heart through `from agents import PromptChainInquirer` in `openai_agenticframework.py`; I honor its existence by embedding it in the `AGENTS` array of our `GlobalConfig`, setting it at the vanguard, ready for action; and with a final flourish, I unleash its capabilities by integrating a command. Driven by a relentless quest for efficiency, I seek a streamlined pathway that reduces the complexity of birthing each new digital intellect. How can we ingeniously simplify this essential process?"""

        agent = OpenAIAgent()
        num_refinement_steps = 1

        # Example usage of the new function:
        refinements = execute_prompt_refinement_by_name(
            "IntensityEnhancer",
            initial_prompt,
            agent,
            display_instructions=False,
            num_iterations=num_refinement_steps,
        )
        display_hierarchical_refinements(refinements, header="Refined Prompts from XML:")

        # Example with a file that does not exist:
        refinements = execute_prompt_refinement_by_name(
            "NonExistentFile",
            initial_prompt,
            agent,
            display_instructions=False,
            num_iterations=num_refinement_steps,
        )
        display_hierarchical_refinements(refinements, header="Refined Prompts from XML:")


    ```
