


The technique illustrated in the code below is commonly referred to as an **"Autonomous Refinement Chain"** or **"Multi-Agent Refinement Chain"**. It is a method that employs a series of specialized agents, each performing a specific refinement task, to iteratively improve the output of a language model. This approach is useful in scenarios where output quality is paramount, such as in professional writing, detailed analysis, or when generating outputs that must adhere to rigorous standards.

Here are the key features of this technique:
- **Sequential Agent Roles:** Each agent has a unique, predefined role (e.g., Objective Setter, Fact Checker) that sequentially improves the response according to a specific aspect, such as accuracy, clarity, structure, and creativity.
- **Iterative Feedback Loop:** The process applies agents in a loop, refining the response at each stage. If a certain quality threshold is met (in this case, after a minimum number of agents), the process can terminate early.
- **Automated Quality Assessment:** There’s a built-in function to assess the quality of each refined response based on criteria like clarity and relevance, enabling the system to decide when to end the chain.
- **Autonomous Agent Application with Error Handling:** Each agent’s actions are automated and wrapped in error handling, ensuring robustness by retrying failed API calls and logging the progress.
- **Controlled Prompt Chaining for LLMs:** This technique is designed specifically for controlled chaining in prompt design, making it particularly effective for generating high-quality responses that meet complex, multifaceted requirements.

How can it be modified to ensure it stays on the original topic along the chain?
```python
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    """
    Initialize and return OpenAI client, validating the API key.
    """
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

def get_refinement_chain() -> List[Dict[str, str]]:
    """
    Returns a predefined chain of refinement agents with role and prompt.
    """
    return [
        {"role": "Objective Setter", "prompt": "Analyze the given input and clearly define the primary objective of the task."},
        {"role": "Context Provider", "prompt": "Identify and incorporate relevant background information to enrich the response."},
        {"role": "Structure Architect", "prompt": "Determine the most appropriate format and structure for the output based on the objective."},

        {"role": "Creativity Enhancer", "prompt": "Inject creative elements or novel ideas while maintaining relevance to the objective."},
        {"role": "Uncertainty Assessor", "prompt": "Evaluate and express the level of confidence or uncertainty in different parts of the response."},
        {"role": "Key Point Extractor", "prompt": "Identify and summarize the most crucial points or insights from the response."},

        # # Skeleton-of-Thought Steps
        {"role": "Skeleton Creator", "prompt": "Enter the Skeleton-of-Thought (SoT) mode. By skeleton, I mean a concise, high-level outline or framework of the main points or structure, not an in-depth explanation. Create the skeleton, capturing the major points without going into detail."},
        {"role": "Skeleton Reviewer", "prompt": "Review the generated skeleton and ensure that it aligns with your expectations and intentions. Check if the major sections and subpoints are clear and logically organized."},
        {"role": "Skeleton Inspector", "prompt": "Inspect the provided skeleton and check if it reflects the intended high-level structure or if adjustments are needed. Assess whether the details are aligned with your vision and whether they effectively flesh out the skeleton."},

        # Summary and Optimization Steps
        {"role": "Unnecessary Detail Remover", "prompt": "Remove any unnecessary details from the summary."},
        # {"role": "Core Insight Simplifier", "prompt": "Focus on the main finding and simplify the language."},
        # {"role": "Core Essence Capturer", "prompt": "Ensure the core insight captures the essence succinctly."},
        # {"role": "Conciseness Optimizer", "prompt": "Make the summary as concise as possible while retaining meaning."},
        # {"role": "Iterative Refiner", "prompt": "Gradually reduce content over multiple passes, ensuring that no essential information is lost. Each phase should verify alignment with clarity and conciseness, allowing for subtle adjustments."},

        # # Insight Extraction and Pattern Recognition
        # {"role": "Core Insight Extractor", "prompt": "Extract the core insight as a distilled takeaway that encapsulates the essential finding, providing a snapshot of the data's main narrative for a quick, standalone understanding."},
        # {"role": "Broad Pattern Recognizer", "prompt": "Identify overarching patterns or trends that contextualize data, revealing the significant forces shaping outcomes. These patterns provide a directional understanding for decision-makers by highlighting recurring themes."},
        {"role": "Secondary Insight Identifier", "prompt": "Identify sub-patterns, anomalies, or notable variations that add depth to the primary insight. These secondary insights encourage nuanced understanding and open pathways for further exploration or targeted investigation."},
        # {"role": "Dimensional Context Provider", "prompt": "Incorporate a layered perspective, providing additional context that broadens the relevance and applicability of the insight to specific settings or conditions."},
        # {"role": "Key Factor Dynamics Analyzer", "prompt": "Analyze the interactions among primary variables or factors within the dataset, detailing how these dynamics influence the outcome. This analysis maps influence pathways and helps predict shifts by clarifying essential component interplay."},

        # # Structural and Content Refinement
        # {"role": "Structure Architect", "prompt": "Organize the response logically and effectively."},
        # {"role": "Expanded Detail Examiner", "prompt": "Examine the expanded details to see if they align with the intended level of depth or if further refinement is needed."},

        # # Structuring, Context, and Modular Summarization
        {"role": "Information Structurer", "prompt": "Structure information hierarchically using careful indentation and sequence to guide the viewer's understanding. This will help maintain relational context within the data, ensuring logical flow remains intact."},
        # {"role": "Contextual Placeholder Utilizer", "prompt": "Incorporate placeholders to indicate key information points that might otherwise require longer explanations, acting as visual markers that allow conciseness."},
        # {"role": "Modular Summary Creator", "prompt": "Create small, standalone summaries that encapsulate essential themes or concepts at each layer of synthesis. These modular insights serve as 'building blocks' for a broader narrative."},

        # # Final Optimization and Summarization
        # {"role": "Refinement Optimizer", "prompt": "Optimize the response for clarity, conciseness, and impact."},
        # {"role": "Concise Summarizer", "prompt": "Summarize key points for a concise and impactful final output."},
        # {"role": "Reasoning Explicator", "prompt": "Explain the reasoning behind each main point in the response."},

        # # Refinement Steps
        # {"role": "Example Generator", "prompt": "Add relevant examples or analogies to illustrate key points."},
        # # {"role": "Task Decomposer", "prompt": "Break down complex points into manageable components for clarity."},
        # {"role": "Practical Applicator", "prompt": "Suggest practical applications of the ideas presented."},
        # {"role": "Creativity Enhancer", "prompt": "Add creative ideas that maintain relevance to the objective."},
    ]

autonomous_refinement_chain = get_refinement_chain()

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """
    Retrieve a completion from the OpenAI API with error handling and retries.
    """
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

def apply_agent(agent: Dict[str, str], initial_input: str, current_response: str) -> str:
    """
    Apply a single agent's prompt to refine the current response conditionally based on previous output quality.
    """
    prompt = f"{agent['prompt']}\n\nInitial Input:\n{initial_input}\n\nCurrent response:\n{current_response}\n\nRefined response:"
    print("="*80)
    print(f'prompt: {prompt}')
    refined_response = get_completion(prompt)
    print(f'refined_response: {refined_response}')
    print("="*80)
    print('\n' * 3)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response  # Preserve current response if no refinement

def assess_response_quality(response: str) -> bool:
    """
    Assess the response quality using strict criteria for clarity and completeness.
    Requires multiple quality indicators and applies early exit only if conditions are met.
    """
    quality_indicators = ["clear", "example", "concise", "relevant"]
    # Check that the response contains at least three indicators for high quality
    return sum(indicator in response for indicator in quality_indicators) >= 2

def autonomous_refine(initial_input: str) -> str:
    """
    Applies a chain of agents to iteratively refine the initial input into a final response,
    with stricter quality criteria and a minimum number of agents to process.
    """
    current_response = initial_input
    min_agents = 5  # Minimum number of agents to apply before considering early exit

    for i, agent in enumerate(autonomous_refinement_chain):
        current_response = apply_agent(agent, initial_input, current_response)

        # Allow early exit only if quality is sufficient and minimum agents have been applied
        if i + 1 >= min_agents and assess_response_quality(current_response):
            logging.info("Early termination as quality is deemed sufficient after minimum agent applications.")
            break

    return current_response

def main():
    # initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    # initial_input = "p vs np"
    initial_input = "how to write an extension for google chrome that bookmarks all tabs in all browser windows"

    logging.info("Starting autonomous refinement process...")
    final_output = autonomous_refine(initial_input)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()
```
