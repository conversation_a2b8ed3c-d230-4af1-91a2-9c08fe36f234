The technique illustrated in the code below is commonly referred to as an "Autonomous Refinement Chain" or "Multi-Agent Refinement Chain". It is a method that employs a series of specialized agents, each performing a specific refinement task, to iteratively improve the output of a language model. This approach is useful in scenarios where output quality is paramount, such as in professional writing, detailed analysis, or when generating outputs that must adhere to rigorous standards.

Here are the key features of this technique:
- Sequential Agent Roles: Each agent has a unique, predefined role (e.g., Objective Setter, Fact Checker) that sequentially improves the response according to a specific aspect, such as accuracy, clarity, structure, and creativity.
- Iterative Feedback Loop: The process applies agents in a loop, refining the response at each stage. If a certain quality threshold is met (in this case, after a minimum number of agents), the process can terminate early.
- Automated Quality Assessment: There’s a built-in function to assess the quality of each refined response based on criteria like clarity and relevance, enabling the system to decide when to end the chain.
- Autonomous Agent Application with Error Handling: Each agent’s actions are automated and wrapped in error handling, ensuring robustness by retrying failed API calls and logging the progress.
- Controlled Prompt Chaining for LLMs: This technique is designed specifically for controlled chaining in prompt design, making it particularly effective for generating high-quality responses that meet complex, multifaceted requirements.

To maximize the effectiveness of LLM prompt chains, a hybrid testing approach that combines automated and manual methods is crucial. This comprehensive strategy allows for a thorough evaluation of prompt chain performance and functionality. Tailoring testing strategies such as performance, regression, and integration testing to the unique aspects of prompt chains is essential to ensuring their effectiveness in generating high-quality responses. By implementing this hybrid approach, prompt chains can be optimized to their full potential, leading to more successful outcomes in natural language processing tasks.

How can the scropt below be modified to ensure it stays on the original topic along the chain?



```
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# Define the Blueprint creation based on initial input and mode
def create_guiding_blueprint(initial_input: str, mode: Optional[str] = None) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users."
    }
    mode = mode or "General"
    mode_description = mode_descriptions.get(mode, "balanced, high-quality response.")

    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Improve clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n")

# Define grouped agent chains based on mode and categorize prompt for dynamic selection
def get_refinement_chain(mode: str) -> Dict[str, List[Dict[str, str]]]:
    chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint with topic, objective, and structure based on the input."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": f"Rephrase the data as a comprehensive question, focusing on '{mode}'."},
            {"role": "Objective Clarifier", "prompt": f"Define the primary objective based on the Blueprint, aligned with '{mode}'."},
            {"role": "Topic Alignment", "prompt": "Review and realign with the original topic and Blueprint if needed."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": f"Add relevant background for enriched response accuracy, with '{mode}' as focus."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint."},
            {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint and topic before further development."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback."},
            {"role": "Topic Alignment", "prompt": "Ensure content remains aligned with the original topic and Blueprint."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability."},
            {"role": "Topic Consistency Tracker", "prompt": "Ensure content aligns with the original question and topic."}
        ],
        "Final Optimization": [
            {"role": "Performance Optimizer", "prompt": "Optimize for efficiency without sacrificing clarity."},
            {"role": "Documentation Curator", "prompt": "Provide clear documentation focusing on usability."},
            {"role": "Quality Assessor", "prompt": "Perform a final quality check, allowing early exit if quality is sufficient."},
            {"role": "Final Topic Consistency Checker", "prompt": "Ensure the response aligns fully with the original topic and Blueprint."}
        ]
    }
    return chain

# Function to categorize the prompt
def categorize_prompt(prompt: str) -> List[str]:
    categories = []
    if any(keyword in prompt.lower() for keyword in ["clarity", "reformulation"]):
        categories.append("Prompt Reformulation")
    if any(keyword in prompt.lower() for keyword in ["background", "context", "content development"]):
        categories.append("Content Development")
    if any(keyword in prompt.lower() for keyword in ["quality assurance", "standards", "error handling"]):
        categories.append("Quality Assurance")
    if any(keyword in prompt.lower() for keyword in ["user experience", "readability"]):
        categories.append("User Experience and Readability")
    if any(keyword in prompt.lower() for keyword in ["optimization", "performance"]):
        categories.append("Final Optimization")
    return categories if categories else ["Blueprint Creation"]

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """
    Retrieve a completion from the OpenAI API with error handling and retries.
    """
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

# Apply agent and re-anchor if necessary
def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, iteration: int) -> str:
    re_anchor_message = f"{blueprint}\n\n" if iteration % 3 == 0 else ""
    prompt = f"{agent['prompt']}\n\nTopic Context from Blueprint:\n{blueprint}\n\n{re_anchor_message}Current Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response

# Process the agent chain with dynamically selected categories
def apply_agent_chain(blueprint: str, initial_input: str, mode: str) -> str:
    current_response = initial_input
    refinement_chain = get_refinement_chain(mode)
    selected_categories = categorize_prompt(initial_input)

    logging.info(f"Selected categories based on prompt: {selected_categories}")

    for category in selected_categories:
        logging.info(f"Starting group: {category}")
        agents = refinement_chain.get(category, [])
        for i, agent in enumerate(agents):
            current_response = apply_agent(agent, blueprint, current_response, i)
            if category == "Final Optimization" and i + 1 >= 5 and assess_response_quality(current_response):
                logging.info("Early exit after quality criteria met.")
                return current_response
    return current_response

def main():
    # initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    initial_input = "Lets say you wanted to add the possibility for the user to choose an overarching 'theme' or context for the response, almost as if the user could specify '*mode*': 'Prompt Generation'? Is that a viable approach, or are there simpler or more effective ways to achieve this kind of specificity?"
    mode = "Prompt Generation"  # Can also be dynamically set based on input type
    blueprint = create_guiding_blueprint(initial_input, mode)

    logging.info(f"Starting process in '{mode}' mode...")
    final_output = apply_agent_chain(blueprint, initial_input, mode)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```

