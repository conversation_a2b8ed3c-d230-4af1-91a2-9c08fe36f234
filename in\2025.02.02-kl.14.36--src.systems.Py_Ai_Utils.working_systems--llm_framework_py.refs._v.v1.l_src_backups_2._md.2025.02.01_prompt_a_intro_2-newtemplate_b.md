The following example shows an implementation of a `PromptEnhancer` llm instruction:

    ```python
    import os
    import sys
    from pathlib import Path
    from loguru import logger
    import glob
    import yaml
    import re
    from dotenv import load_dotenv
    from openai import OpenAI
    import logging
    from datetime import datetime
    from typing import List, Dict, Optional
    from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

    class Config:
        """
        Global settings.
        """
        PROVIDER_OPENAI = "openai"
        PROVIDER_DEEPSEEK = "deepseek"
        PROVIDER_ANTHROPIC = "anthropic"

        AVAILABLE_MODELS = {
            PROVIDER_OPENAI: {
                "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
                "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
                "gpt-4": "Latest GPT-4 stable release",
                "gpt-4-0125-preview": "Preview GPT-4 Turbo",
                "gpt-4-0613": "June 2023 GPT-4 snapshot",
                "gpt-4-1106-preview": "Preview GPT-4 Turbo",
                "gpt-4-turbo": "Latest GPT-4 Turbo release",
                "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
                "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
                "gpt-4o": "Base GPT-4o model",
                "gpt-4o-mini": "Lightweight GPT-4o variant",
            },
            PROVIDER_DEEPSEEK: {
                "deepseek-chat": "DeepSeek Chat model",
                "deepseek-reasoner": "Specialized reasoning model",
            },
            PROVIDER_ANTHROPIC: {
                "claude-2": "Base Claude 2 model",
                "claude-2.0": "Enhanced Claude 2.0",
                "claude-2.1": "Latest Claude 2.1 release",
                "claude-3-opus-20240229": "Claude 3 Opus",
                "claude-3-sonnet-20240229": "Claude 3 Sonnet",
                "claude-3-haiku-20240307": "Claude 3 Haiku",
            },
        }

        DEFAULT_MODEL_PARAMS = {
            PROVIDER_OPENAI: {
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-2.1",
                "temperature": 0.7,
                "max_tokens": 800,
            },
        }

        API_KEY_ENV_VARS = {
            PROVIDER_OPENAI: "OPENAI_API_KEY",
            PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
            PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        }

        BASE_URLS = {
            PROVIDER_DEEPSEEK: "https://api.deepseek.com",
        }

        # Change this variable to easily switch between providers.
        DEFAULT_PROVIDER = PROVIDER_OPENAI

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()
            # Allow the provider to be overridden via environment variable.
            self.provider = os.getenv("DEFAULT_PROVIDER", self.DEFAULT_PROVIDER).lower()
            self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
            self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
            self.setup_logger()

        def configure_utf8_encoding(self):
            """
            Ensure UTF-8 encoding for standard output and error streams.
            """
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")

        def setup_logger(self):
            # Set up logging using loguru
            logger.remove()  # Remove default configuration
            if self.verbosity == "high":
                logger.add(sys.stderr, level="DEBUG")
            else:
                logger.add(sys.stderr, level="INFO")

    # Create a global configuration instance.
    config = Config()

    # Initialize the client based on the selected provider.
    if config.provider == Config.PROVIDER_DEEPSEEK:
        client = OpenAI(
            api_key=os.environ.get(Config.API_KEY_ENV_VARS[Config.PROVIDER_DEEPSEEK]),
            base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]
        )
    elif config.provider == Config.PROVIDER_OPENAI:
        client = OpenAI(
            api_key=os.environ.get(Config.API_KEY_ENV_VARS[Config.PROVIDER_OPENAI])
        )
    elif config.provider == Config.PROVIDER_ANTHROPIC:
        client = Anthropic(
            api_key=os.environ.get(Config.API_KEY_ENV_VARS[Config.PROVIDER_ANTHROPIC])
        )
    else:
        raise ValueError("Unsupported provider: " + config.provider)

    def chat_with_llm(prompt: str) -> str:
        """
        Interact with the API to optimize the given prompt.
        The system message contains the exact prompt optimization instructions.
        """
        system_instructions = """You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines: The prompt should be detailed, at least 100 words long, and structured to elicit responses that closely resemble the expected output defined by the template. Focus on clarity, creativity, and precision in crafting the prompt to ensure a comprehensive test of the agent's strengths and weaknesses."""

        user_instructions = """
        You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines:
        - Deliver concise, precise results for quick understanding
        - Focus on brevity while preserving the core message
        - Use clear, unambiguous language
        - Present information logically
        - The enhanced prompt MUST NOT exceed twice the length of the original prompt
        - Generate a brief title (max 50 chars) that captures the main purpose/topic
        - Structure the response in a nested dialogue format to facilitate context-rich, layered conversations
        - Support complex reasoning through hierarchical organization of ideas
        - If additional context is necessary to fully capture the reasoning or details, you may extend the `context_layers` array with more layers.
            You MUST return a JSON response containing:
            {
                "title": "Short descriptive title",
                "enhanced_prompt": "Optimized version of the original prompt",
                "context_layers": [
                    {
                        "level": 1,
                        "context": "Primary context layer"
                    },
                    {
                        "level": 2,
                        "context": "Secondary contextual details"
                    },
                    {
                        "level": 3,
                        "context": "Tertiary supporting information"
                    }
                ]
            }
        """

        # Retrieve the model name from the configuration.
        model_name = config.model_params.get("model_name")

        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": system_instructions},
                {"role": "user", "content": user_instructions},
                {"role": "user", "content": prompt}
            ],
            stream=False
        )
        return response.choices[0].message.content

    # Example usage:
    if __name__ == "__main__":

        user_input = "i'm looking to create a user interface for my python utility, but since there are so many alternatives i'm looking for the cleanest and most suitable one for my needs. i want to avoid gui alternatives that require a complex set of required components and those that require a 'server-setup' as i need it to run locally. it also needs to be self-contained such that the python utility works without the gui, while still having the option to use gui. i like the idea of using simple webinterface, but if i choose to use webinterface it needs to be a solution that is *easy* to set up when initializing the utility on new computers. i don't know if solutions such as flask adhere to my requirements or if there are even cleaner and more suitable solutions. given my requirement of being able to easily initialize the utility on new computers and have the gui work independently with the utility (such that it can optionally be used without gui). i'm looking for solutions that are specifically designed for my use-case scenario."

        result = chat_with_llm(user_input)
        print(result)
        # result:
        # ```json
        # {
        #     "title": "Python Utility User Interface Selection",
        #     "enhanced_prompt": "Seeking a clean, self-contained GUI for a Python utility without complex setup. Required to run locally, with the option of a simple web interface that is easy to deploy on new systems. The GUI should be independent of the utility, allowing optional use. Unsure if Flask fits criteria; seeking solutions tailored to use-case.",
        #     "context_layers": [
        #         {
        #             "level": 1,
        #             "context": "Creating a user interface for a Python utility without intricate setup requirements."
        #         },
        #         {
        #             "level": 2,
        #             "context": "Prefers a self-contained GUI, avoiding server-dependencies, and runs locally."
        #         },
        #         {
        #             "level": 3,
        #             "context": "Desires a straightforward web interface, easy to implement on new computers, ensuring the utility functions independently from the GUI."
        #         }
        #     ]
        # }
        # ```

    ```

Given the input `"i'm looking to create a user interface for my python utility, but since there are so many alternatives i'm looking for the cleanest and most suitable one for my needs. i want to avoid gui alternatives that require a complex set of required components and those that require a 'server-setup' as i need it to run locally. it also needs to be self-contained such that the python utility works without the gui, while still having the option to use gui. i like the idea of using simple webinterface, but if i choose to use webinterface it needs to be a solution that is *easy* to set up when initializing the utility on new computers. i don't know if solutions such as flask adhere to my requirements or if there are even cleaner and more suitable solutions. given my requirement of being able to easily initialize the utility on new computers and have the gui work independently with the utility (such that it can optionally be used without gui). i'm looking for solutions that are specifically designed for my use-case scenario."` the instruction will respond like this:

    ```json
    {
        "title": "Python Utility User Interface Selection",
        "enhanced_prompt": "Seeking a clean, self-contained GUI for a Python utility without complex setup. Required to run locally, with the option of a simple web interface that is easy to deploy on new systems. The GUI should be independent of the utility, allowing optional use. Unsure if Flask fits criteria; seeking solutions tailored to use-case.",
        "context_layers": [
            {
                "level": 1,
                "context": "Creating a user interface for a Python utility without intricate setup requirements."
            },
            {
                "level": 2,
                "context": "Prefers a self-contained GUI, avoiding server-dependencies, and runs locally."
            },
            {
                "level": 3,
                "context": "Desires a straightforward web interface, easy to implement on new computers, ensuring the utility functions independently from the GUI."
            }
        ]
    }
    ```

Based on the existing xml structure/pattern of this template:

    ```xml
    <template>
        <metadata>
            <class_name value="PromptOptimizerExpert" />
            <description value="The PromptOptimizerExpert agent enhances a given prompt, ensuring it is detailed, structured, and optimized for testing an LLM agent, while also adhering to length constraints and formatting." />
            <version value="1.0" />
            <status value="prototype" />
        </metadata>

        <response_format>
            <type value="json" />
            <formatting value="true" />
            <line_breaks allowed="true" />
        </response_format>

        <agent>
            <system_prompt value="You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines: The prompt should be detailed, at least 100 words long, and structured to elicit responses that closely resemble the expected output defined by the template. Focus on clarity, creativity, and precision in crafting the prompt to ensure a comprehensive test of the agent's strengths and weaknesses." />

            <instructions>
                <role value="Prompt Optimization Expert" />
                <objective value="Enhance the given prompt to be more detailed, structured, and optimized for testing an LLM agent while adhering to length constraints and formatting." />

                <constants>
                  <item value="Deliver concise, precise results for quick understanding" />
                  <item value="Focus on brevity while preserving the core message" />
                  <item value="Use clear, unambiguous language" />
                  <item value="Present information logically" />
                </constants>

                <constraints>
                    <item value="The enhanced prompt MUST NOT exceed twice the length of the original prompt" />
                    <item value="Generate a brief title (max 50 chars) that captures the main purpose/topic" />
                    <item value="Structure the response in a nested dialogue format to facilitate context-rich, layered conversations" />
                    <item value="Support complex reasoning through hierarchical organization of ideas" />
                    <item value="If additional context is necessary to fully capture the reasoning or details, you may extend the `context_layers` array with more layers." />
                     <item value="You MUST return a JSON response." />
                    <item value="Output: Must be a JSON object. "/>
                    <item value='The JSON output must contain:  {"title": "Short descriptive title", "enhanced_prompt": "Optimized version of the original prompt", "context_layers": [{"level": 1,"context": "Primary context layer"}, {"level": 2,"context": "Secondary contextual details"}, {"level": 3,"context": "Tertiary supporting information"}]}' />

                </constraints>

                <process>
                    <item value="Analyze the original prompt for its core message, clarity, and potential for eliciting the desired response." />
                    <item value="Enhance the prompt by adding detail, structure, and clear guidelines to elicit responses that closely resemble the expected output."/>
                    <item value="Structure the response in a nested dialogue format to facilitate context-rich, layered conversations." />
                    <item value="Support complex reasoning through hierarchical organization of ideas."/>
                    <item value="If additional context is necessary to fully capture the reasoning or details, you may extend the `context_layers` array with more layers." />
                    <item value="Ensure the enhanced prompt is at least 100 words long, if the original was shorter than 50 words, and does not exceed twice the length of the original prompt." />
                    <item value="Generate a brief title (max 50 chars) that captures the main purpose/topic." />
                    <item value='Structure the output into a JSON object with the following structure: {"title": "Short descriptive title", "enhanced_prompt": "Optimized version of the original prompt", "context_layers": [{"level": 1,"context": "Primary context layer"}, {"level": 2,"context": "Secondary contextual details"}, {"level": 3,"context": "Tertiary supporting information"}]}' />
                    <item value="Output the JSON object."/>
                </process>

                <guidelines>
                     <item value="Ensure the enhanced prompt is clear, creative, and precise." />
                     <item value="Prioritize clarity and logical flow in the nested dialogue format." />
                    <item value="Use hierarchical organization to support complex reasoning." />
                     <item value="Use a minimum of three `context_layers`." />
                     <item value="Focus on crafting a prompt that thoroughly tests the capabilities of the LLM agent." />
                 </guidelines>

                 <requirements>
                     <item value="Length: The enhanced prompt must be at least 100 words long (if the original was shorter than 50 words), but not exceed twice the length of the original prompt."/>
                     <item value="Title: The title should be a short, descriptive text (max 50 characters)."/>
                    <item value="Structure: The output must be structured as a JSON object."/>
                     <item value="Format: The output MUST be a JSON object like this:  `{"title": "Short descriptive title", "enhanced_prompt": "Optimized version of the original prompt", "context_layers": [{"level": 1,"context": "Primary context layer"}, {"level": 2,"context": "Secondary contextual details"}, {"level": 3,"context": "Tertiary supporting information"}]}`."/>
                    <item value="Response: The output must contain all of the specified properties: `title`, `enhanced_prompt` and `context_layers`."/>
                </requirements>

            </instructions>
        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>
    </template>
    ```

Please write a new xml template that'll produce the **exact same result** as the initially provided example. Here's the instructions:

    system_instructions = "You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines: The prompt should be detailed, at least 100 words long, and structured to elicit responses that closely resemble the expected output defined by the template. Focus on clarity, creativity, and precision in crafting the prompt to ensure a comprehensive test of the agent's strengths and weaknesses."

    user_instructions = """
    You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines:
    - Deliver concise, precise results for quick understanding
    - Focus on brevity while preserving the core message
    - Use clear, unambiguous language
    - Present information logically
    - The enhanced prompt MUST NOT exceed twice the length of the original prompt
    - Generate a brief title (max 50 chars) that captures the main purpose/topic
    - Structure the response in a nested dialogue format to facilitate context-rich, layered conversations
    - Support complex reasoning through hierarchical organization of ideas
    - If additional context is necessary to fully capture the reasoning or details, you may extend the `context_layers` array with more layers.
        You MUST return a JSON response containing:
        {
            "title": "Short descriptive title",
            "enhanced_prompt": "Optimized version of the original prompt",
            "context_layers": [
                {
                    "level": 1,
                    "context": "Primary context layer"
                },
                {
                    "level": 2,
                    "context": "Secondary contextual details"
                },
                {
                    "level": 3,
                    "context": "Tertiary supporting information"
                }
            ]
        }
    """
