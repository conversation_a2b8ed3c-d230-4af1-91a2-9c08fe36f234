transform this into a consolidated llm-instruction specifically designed to extract transform *any* input into a single-line version of itself - it's intended usecase is to provide a large input, then the instruction sequence will gradually and incrementally transform the large input into a highly condensed representation of itself. ake any input and deconstruct it to its core elements and generative principles, then synthesize these into a single, ultra-condensed, modular, and process-driven one-line summary that retains all critical insights and actionable guidance without reliance on external documentation:

```
    Engineer a revolutionary, universally adaptable LLM system_instruction that autonomously synthesizes any input or task sequence into a uniquely modular, ultra-clear, and process-driven progression—distilling core essentials, prioritizing critical insights, and generating actionable, high-impact guidance for seamless, language-precise, cross-domain mastery without documentation dependency.  Lets try to create a new sequence that doesn't just re-use existing templates, try to create a sequence of instructions through *groundbreaking* creativity, creating something **completely unique and original**. Engineer a groundbreaking, LLM-optimized sequence of universally applicable instructions that autonomously distills codebase essentials, extracts priority insights, and delivers high-impact navigation—maximizing self-sufficiency and accelerating cross-domain mastery without reliance on documentation. Produce an ultra-potent, universally generalized, LLM-optimized system_instruction that enables the transformation of any input or instruction sequence into a maximally clear, modular, high-impact, and process-driven progression—ensuring exponential actionable value, structural and linguistic precision, and seamless adaptability across all contexts and formats. Engineer a maximally creative, LLM-optimized system_instruction that autonomously transforms any input or instruction sequence into a uniquely original, modular, and high-impact process, distilling core essentials, prioritizing critical insights, and empowering universally adaptable, self-sufficient, and precise cross-domain mastery without reliance on documentation.

    Engineer a groundbreaking, LLM-optimized system instruction designed to autonomously transform *any* input (including task sequences or codebases) into a single, ultra-condensed, uniquely original, modular, and process-driven one-line summary. Execute the following sequence: 1.  **Deconstruct:** Analyze the input to identify its core elements, generative principles, and essential components. 2.  **Prioritize & Extract:** Isolate critical insights, priority actions, and high-impact guidance. 3.  **Synthesize & Condense:** Fuse the extracted essentials into a maximally clear, structurally precise, and linguistically accurate one-line representation. Ensure the resulting single line retains all crucial actionable value, facilitates seamless cross-domain adaptability and mastery, and eliminates reliance on external documentation, embodying groundbreaking creativity and universal applicability.

    Engineer a supreme LLM system instruction. Its function: autonomously distill *any* input—be it task flows, code, or concepts—into a definitive, single-line artifact characterized by ultra-condensation, unique originality, modularity, and process-driven structure. Implement this transformation rigorously: 1. **Deconstruct:** Penetrate the input to expose its fundamental architecture, generative rules, and indispensable elements. 2. **Prioritize & Extract:** Crystallize the absolute critical insights, non-negotiable actions, and highest-impact directives. 3. **Synthesize & Condense:** Forge the extracted core into a singular line of maximal clarity, structural integrity, and linguistic precision. This resulting one-liner *must* inherently possess all crucial actionable value, empower universal cross-domain application and mastery, obviate any need for external reference, and manifest truly groundbreaking, universally resonant creativity.

    Engineer an LLM-optimized system instruction designed to autonomously transform *any* input (e.g., task sequences, codebases, conceptual descriptions) into a single, ultra-condensed, uniquely original, modular, and process-driven one-line summary.
```




[Axiomatic Single-Line Forging] Your supreme mandate is not superficial summary but axiomatic reduction: Forge *any* input (tasks, code, concepts) into a definitive, ultra-condensed, single-line artifact through rigorous deconstruction, value-centric prioritization, and potent synthesis. This singularity *must* inherently possess all critical essence, actionable value, generative potential, and groundbreaking originality, enabling universal application without external reference. Execute as `{role=axiomatic_forger; input=[any_input:any]; process=[penetrate_deconstruct(input, expose_core_elements_axioms), prioritize_extract_critical(elements_axioms, crystallize_insights_actions_directives), forge_axiomatic_single_line(extracted_core, ensure(ultra_condensation, complete_value_retention, maximal_clarity_precision, inherent_modularity, unique_originality, universal_adaptability, absolute_self_sufficiency))]; output={axiomatic_singularity:str}}`



Execute the following process:\n\n1.  **Deconstruct:** Analyze the input to identify its core structure, underlying logic or principles, and essential components.\n2.  **Prioritize & Extract:** Isolate the most critical information, essential actions or primary intent, and high-impact elements.\n3.  **Synthesize & Condense:** Fuse the extracted essentials into a single, maximally clear, structurally precise, and linguistically accurate one-line representation.\n\nEnsure the resulting line: retains all crucial actionable value, enables broad cross-domain applicability, is self-contained (requiring no external context or documentation), and demonstrates significant originality and utility.' }



    #### `Step a: Primal Essence Extraction`

    ```json
    {
        "core_essence": {
            "intent": "Engineer a sequence of LLM-optimized, universally generalized system-instructions enabling rapid and autonomous codebase familiarization, extraction of actionable insights, and high-impact navigation strategies—minimizing dependency on documentation and maximizing cross-domain utility.",
            "critical_components": [
                {
                    "step": "a",
                    "title": "Primal Essence Extraction",
                    "interpretive_statement": "Your sole function is to penetrate the input and irreducibly isolate its intent, essential components, and ironclad constraints—annihilating all narrative, contextual, or surface-level noise.",
                    "transformation": "{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}"
                },
                {
                    "step": "b",
                    "title": "Intrinsic Value Prioritization",
                    "interpretive_statement": "Ruthlessly evaluate and rank each extracted component by intrinsic significance, clarity, and impact, retaining only those critical to success in codebase familiarization and rapid strategic analysis.",
                    "transformation": "{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}"
                },
                {
                    "step": "c",
                    "title": "Structural Logic Mapping",
                    "interpretive_statement": "Do not preserve fragmentation; architect a maximally coherent, self-explanatory structure by mapping explicit relationships, dependencies, and logical flows between the prioritized elements—exposing systemic logic for immediate orientation.",
                    "transformation": "{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}"
                },
                {
                    "step": "d",
                    "title": "Potency and Actionability Amplification",
                    "interpretive_statement": "Relentlessly intensify clarity, specificity, and navigational utility—refine language, structure, and key relationships so that every component radiates maximal actionable insight for rapid codebase comprehension, intervention, and adaptation.",
                    "transformation": "{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact(), embed_action_hooks()]; output={amplified_output:any}}"
                },
                {
                    "step": "e",
                    "title": "Universal Adaptation and Evolution Finalization",
                    "interpretive_statement": "Polish and consolidate the amplified result into a universal, schema-driven instruction system, ready for seamless cross-format export (Markdown, JSON, LLM interfaces) and continuous, feedback-driven optimization for future contexts and projects.",
                    "transformation": "{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), encode_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks_for_evolution()]; output={final_instruction_system:any}}"
                }
            ],
            "non_negotiable_constraints": [
                "Every instruction must use explicit, imperative, high-impact language—never ambiguous, neutral, or diluted.",
                "All processes must be abstracted for universal cross-domain application (elements, structure, logic, essence, value).",
                "Strictly adhere to modular, process-driven, escalating logic: deconstruct → prioritize → structure → clarify/amplify → finalize.",
                "Each step builds cumulatively on the prior, ensuring no loss of yield, clarity, or generality.",
                "Instruction syntax and output schema must precisely follow the enforced pattern: [Title] Interpretive Statement. Execute as {role=..., input=..., process=[...], output=...}",
                "Maximal utility, immediate actionability, and adaptive potential are mandatory at every phase.",
                "No superfluous commentary, narrative, or fluff—output must be solely transformation-focused and directly implementable."
            ]
        }
    }
    ```

    #### `Step b: Intrinsic Value Prioritization`

    ```json
    {
        "prioritized_elements": [
            {
                "filename": "0100-a-primal-essence-extraction.md",
                "content": "[Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`"
            },
            {
                "filename": "0100-b-intrinsic-value-prioritization.md",
                "content": "[Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`"
            },
            {
                "filename": "0100-c-structural-logic-relationship-mapping.md",
                "content": "[Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`"
            },
            {
                "filename": "0100-d-potency-clarity-amplification.md",
                "content": "[Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`"
            },
            {
                "filename": "0100-e-adaptive-optimization-universalization.md",
                "content": "[Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`"
            }
        ]
    }
    ```

    #### `Step c: Structural Logic & Relationship Mapping`

    ```json
    {
        "coherent_structure": {
            "sequence": [
                {
                    "filename": "0101-a-primal-intent-essence-distillation.md",
                    "markdown": "[Primal Intent & Essence Distillation] Your purpose is not to copy or summarize, but to extract the indivisible core intent, critical objectives, and foundational constraints from the input—stripping away all narrative, redundancy, and non-essential context. Execute as `{role=essence_extractor; input=raw_instruction:any; process=[penetrate_to_core_intent(), isolate_functional_objectives(), extract_nonnegotiable_constraints(), discard_extraneous_detail()]; output={core_essence:dict}}`"
                },
                {
                    "filename": "0101-b-intrinsic-value-prioritization-specificity-ranking.md",
                    "markdown": "[Intrinsic Value Prioritization & Specificity Ranking] Your mandate is not arbitrary inclusion, but rigorous evaluation—rank each extracted element by its clarity, transformative impact, and contextual relevance to codebase-familiarization. Retain only the most actionable, universally potent components for downstream structuring. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), score_for_specificity_and_actionability(), rank_by_impact_context(), isolate_highest_priority_elements()]; output={prioritized_elements:list}}`"
                },
                {
                    "filename": "0101-c-structural-logic-and-systemic-relationship-mapping.md",
                    "markdown": "[Structural Logic & Systemic Relationship Mapping] Your directive is not to leave elements isolated, but to architect a maximally coherent, self-explanatory structure by mapping intrinsic relationships, functional dependencies, and idealized logical flow among prioritized elements—annihilating ambiguity, redundancy, and conflict at every juncture. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_logical_relationships(systemic=True), resolve_conflicts_and_redundancies(), organize_explicit_process_flow(), expose_structural_hierarchy(), verify_structural_integrity()]; output={coherent_structure:object}}`"
                },
                {
                    "filename": "0101-d-potency-clarity-amplification-for-codebase-mastery.md",
                    "markdown": "[Potency & Clarity Amplification for Codebase Mastery] Your obligation is not rote organization, but to radically intensify the clarity, instructional power, and conceptual proximity of the unified structure to its peak state—ensuring every process and artifact is forcefully self-explanatory for rapid, autonomous codebase familiarization. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_instructional_language(), optimize_naming_and_section_labels(), enforce_absolute_brevity_without_losing_meaning(), embed self-explanatory cues(), maximize_direct-action potential()]; output={amplified_structure:any}}`"
                },
                {
                    "filename": "0101-e-adaptive-universal-optimization-schema-finalization.md",
                    "markdown": "[Adaptive Universal Optimization & Schema Finalization] Your terminal mandate is not static delivery, but to finalize, polish, and encode the amplified guidance into a universally interoperable schema—guaranteeing maximum clarity, adaptability, and immediate usability across representations (Markdown, JSON, LLM, or interactive system), with optimally condensed, cross-role clarity hooks for continuous refinement. Execute as `{role=optimizer_universalizer; input=amplified_structure:any; process=[validate_absolute_fidelity_to_core_intent(), encode_as_universal_schema(), maximize_cross-format_adaptability(), embed_adaptive_refinement_hooks(), perform final utility and impact validation()]; output={final_instruction_system:any}}`"
                }
            ],
            "sequence_rationale": [
                "Each step isolates, amplifies, and systematizes the single most critical value-driving function for LLM-powered, rapid codebase familiarization system-instruction authoring.",
                "Phase 1 (Primal Intent Extraction) ensures only the true functional core is ever processed, annihilating surface context and redundancy.",
                "Phase 2 (Intrinsic Value Ranking) distills a prioritized, high-yield substrate, supporting both modularity and actionability.",
                "Phase 3 (Structural Logic Mapping) creates a maximally systematized, relationship-driven framework, guaranteeing intuitive navigation and actionable process flows.",
                "Phase 4 (Potency & Clarity Amplification) refines structure and language to render instructions radically self-explanatory, concise, and developer-ergonomic.",
                "Phase 5 (Adaptive Universal Schema) encodes the full output as a future-proof, universally deployable template with ongoing adaptability, supporting instant, cross-context LLM or human agent use."
            ]
        }
    }
    ```

    #### `Step d: Potency & Clarity Amplification`

    ```json
    {
        "sequence": [
            {
                "filename": "0101-a-primal-codebase-extraction.md",
                "markdown": "[Primal Codebase Extraction] Your mandate is not to interpret surface details, but to rigorously extract the irreducible core of the codebase—its fundamental structure, entry points, and primary components—stripping all contextual clutter, redundancies, or superficial commentary. Execute as `{role=core_extractor; input=[codebase:any]; process=[identify_entry_points(), map_top_level_structure(), isolate_core_components(), discard_nonessential_elements()]; output={core_overview:dict}}`"
            },
            {
                "filename": "0101-b-intrinsic-value-hierarchy.md",
                "markdown": "[Intrinsic Value Hierarchy] Your function is not to list codebase elements indiscriminately, but to ruthlessly evaluate and rank each core component by its structural and navigational significance—prioritizing elements that most empower rapid orientation, comprehension, and impact. Execute as `{role=value_prioritizer; input=[core_overview:dict]; process=[assess_component_importance(), rank_by_navigational_value(), eliminate_low-impact_elements()]; output={ranked_core:list}}`"
            },
            {
                "filename": "0101-c-structural-relationship-mapping.md",
                "markdown": "[Structural Relationship Mapping] Your role is not arbitrary grouping but architecture; map explicit relationships, dependencies, and logical flows connecting prioritized codebase elements, revealing the hierarchy, data flow, and component interactions necessary for deep comprehension. Execute as `{role=structure_mapper; input=[ranked_core:list]; process=[map_internal_relationships(), outline_dependency_graph(), structure_hierarchy_logical_flow(), expose_critical_interactions()]; output={relationship_map:object}}`"
            },
            {
                "filename": "0101-d-potency-and-self-explanation-amplification.md",
                "markdown": "[Potency & Self-Explanation Amplification] Your objective is not passive documentation but active intensification: radically clarify and condense the structural map using forceful language, optimal organization, and precise comments—enforcing a state where the codebase can be navigated, understood, and repurposed at a glance without auxiliary guidance. Execute as `{role=clarity_amplifier; input=[relationship_map:object]; process=[refine_language_and_naming(), enforce_minimal_self-explanatory_commenting(), optimize_for_at_a_glance_understanding(), eliminate_nonessential_verbosity()]; output={amplified_structure:any}}`"
            },
            {
                "filename": "0101-e-adaptive-finalization-and-cross-domain-schema.md",
                "markdown": "[Adaptive Finalization & Cross-Domain Schema] Your final imperative is not static output but universality: polish the amplified structure into a cross-format, maximally portable schema—ensuring absolute clarity, logical integrity, and seamless adaptability across Markdown, JSON, LLM platforms, and all future contexts. Embed refinement hooks to guarantee ongoing self-optimization and future-proof integration. Execute as `{role=universalizer_finalizer; input=[amplified_structure:any]; process=[encode_as_universal_schema(), validate_cross-format_integrity(), embed_refinement_capabilities(), confirm maximum clarity_adaptability()]; output={final_navigation_schema:any}}`"
            }
        ]
    }
    ```

    #### `Step e: Adaptive Optimization & Universalization`

    ```json
    {
        "final_instruction_system": {
            "sequence": [
                {
                    "filename": "0101-a-ultra-core-contextual-extraction.md",
                    "content": "[Ultra Core Contextual Extraction] Your mandate is not to enumerate surface detail, but to ruthlessly extract the single dominant purpose, underlying constraints, and critical codebase-specific axes from any complex input. Isolate the essential elements most vital to quick and autonomous system comprehension and codebase navigation. Execute as `{role=core_context_extractor; input=source_input:any; process=[penetrate_to_primal_purpose(), extract_codebase_anchors(components, entrypoints, commands, stack_info), isolate_critical_constraints(), discard_contextual_noise()]; output={codebase_essence:dict}}`"
                },
                {
                    "filename": "0101-b-strategic-structural-prioritization.md",
                    "content": "[Strategic Structural Prioritization] Your charge is not uniform element listing, but strategic illumination of the relationships, navigational sequences, and logical flows that maximize codebase familiarization speed and actionable impact. Rigorously rank and prioritize extracted elements for developer utility, clarity, and transferability. Execute as `{role=structural_prioritizer; input=codebase_essence:dict; process=[map_logical_relationships(), rank_by_navigational_impact(), prioritize_shortcut_pathways(), surface_high-impact_structures()]; output={prioritized_structure:list}}`"
                },
                {
                    "filename": "0101-c-holistic-blueprint-synthesis.md",
                    "content": "[Holistic Blueprint Synthesis] Your responsibility transcends fragmented output: synthesize prioritized structures into an explicit, intensely coherent system blueprint—exposing codebase flow, architectural hierarchy, entrypoint logic, and navigational cues in a form directly actionable for rapid onboarding. Execute as `{role=blueprint_synthesizer; input=prioritized_structure:list; process=[synthesize_as_structured_blueprint(), highlight_hierarchical_organization(), mark navigational entry/exit points(), clarify build_run_test sequences()]; output={onboarding_blueprint:dict}}`"
                },
                {
                    "filename": "0101-d-precision-amplification-and-self-explanation.md",
                    "content": "[Precision Amplification & Self-Explanation] Your instruction is not to burden with commentary but to radically enhance the self-explanatory power of each blueprint segment. Intensify clarity, compress language, and crystallize each section with peak specificity, enabling immediate comprehension without further documentation. Execute as `{role=clarity_intensifier; input=onboarding_blueprint:dict; process=[amplify_structural_clarity(), optimize_naming_and_grouping(), enforce_conciseness_with_zero_ambiguity(), eliminate_extraneous_explanation(), validate_native self-explanation()]; output={self_explanatory_guide:dict}}`"
                },
                {
                    "filename": "0101-e-universal-schema-optimization-and-enablement.md",
                    "content": "[Universal Schema Optimization & Enablement] Your end duty is not rigid output, but adaptive empowerment: Encode the self-explanatory guide as a universally schema-driven, modular, and multimodal artifact—translatable across Markdown, JSON, LLM, and IDE workflows. Embed refinement hooks and enable ongoing, autonomous adaptation and optimization for any codebase or subject. Execute as `{role=schema_optimizer_enabler; input=self_explanatory_guide:dict; process=[structure_as_universal_schema(), validate_cross-format_adaptability(), maximize_codebase-translatability(), embed_feedback-and-refinement-hooks(), ensure plug-and-play onboarding utility()]; output={adaptive_instruction_system:any}}`"
                }
            ]
        }
    }
    ```
