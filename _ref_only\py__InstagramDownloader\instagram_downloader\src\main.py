import argparse
import os
import re
import pyperclip
import sys
from pathlib import Path

import instaloader
from loguru import logger
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeElapsedColumn

console = Console()

DOWNLOAD_TEMPLATES = {
    1: {'name': 'Video + Images: Full Quality', 'download_videos': True, 'download_images': True, 'compress_json': False},
    2: {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True},
    3: {'name': 'Images Only: Full Quality', 'download_videos': False, 'download_images': True, 'compress_json': True},
    4: {'name': 'Video + Images: Compressed', 'download_videos': True, 'download_images': True, 'compress_json': True},
}

def is_valid_instagram_url(url):
    if not url or not isinstance(url, str):
        return False
    pattern = r'(https?://)?(www\.)?instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)/?(\?.*)?'
    return re.match(pattern, url.strip()) is not None

def extract_shortcode(url):
    match = re.search(r'instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)', url)
    return match.group(2) if match else None

def get_clipboard_url():
    try:
        clipboard_content = pyperclip.paste()
        if clipboard_content and is_valid_instagram_url(clipboard_content):
            console.print(f"[bold green]✅ Instagram URL found in clipboard: {clipboard_content.strip()}[/bold green]")
            return clipboard_content.strip()
    except Exception as e:
        console.print(f"[yellow]Warning: Could not access clipboard: {e}[/yellow]")
    return ""

def parse_arguments():
    parser = argparse.ArgumentParser(description="Download content from Instagram")
    parser.add_argument("-i", "--input_urls", nargs="+", help="Instagram URL(s)")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory")
    parser.add_argument("--prompt", action="store_true", help="Interactive mode")
    return parser.parse_args()

def select_download_type():
    console.print("\n[bold cyan]Select download type:[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Option", style="dim", width=12)
    table.add_column("Description")
    
    for key, value in DOWNLOAD_TEMPLATES.items():
        table.add_row(str(key), value['name'])
    console.print(table)
    
    while True:
        try:
            choice = int(Prompt.ask("Enter your choice", default="2"))
            if choice in DOWNLOAD_TEMPLATES:
                return choice
            console.print("[bold red]Invalid choice. Please try again.[/bold red]")
        except ValueError:
            console.print("[bold red]Please enter a valid number.[/bold red]")

def get_user_inputs(args):
    default_url = get_clipboard_url()
    
    if args.prompt or not args.input_urls:
        urls_input = Prompt.ask("Enter Instagram URL(s) (space-separated)", default=default_url)
        args.input_urls = urls_input.split()
    
    if not args.input_urls:
        console.print("[bold red]Error: No URLs provided. Exiting...[/bold red]")
        sys.exit(1)
    
    if args.prompt or not args.output_path:
        args.output_path = Prompt.ask("Output directory", default=args.output_path or os.getcwd())
    
    download_type = select_download_type()
    return args.input_urls, args.output_path, download_type

def setup_logger():
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
    logger.add("instagram_downloader.log", level="DEBUG", format="{time} - {level} - {message}")

def sanitize_filename(text, max_length=100):
    """Sanitize text for use as filename"""
    if not text:
        return "untitled"

    # Remove or replace invalid characters
    import string
    valid_chars = "-_.() %s%s" % (string.ascii_letters, string.digits)
    sanitized = ''.join(c for c in text if c in valid_chars)

    # Replace multiple spaces with single space and strip
    sanitized = ' '.join(sanitized.split())

    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length].rsplit(' ', 1)[0]

    return sanitized or "untitled"

def get_post_title(post):
    """Extract a meaningful title from the post"""
    caption = post.caption
    if caption:
        # Take first line or first sentence, whichever is shorter
        first_line = caption.split('\n')[0].strip()
        first_sentence = caption.split('.')[0].strip()

        # Use the shorter one, but prefer first sentence if it's reasonable length
        if len(first_sentence) <= 80 and first_sentence:
            title = first_sentence
        elif first_line:
            title = first_line
        else:
            title = caption[:80]

        # Remove hashtags and mentions for cleaner filename
        import re
        title = re.sub(r'[#@]\w+', '', title).strip()
        title = re.sub(r'\s+', ' ', title)  # Clean up multiple spaces

        return sanitize_filename(title)

    # Fallback to post type and username
    return f"{post.typename}_by_{post.owner_username}"

def download_post(url, output_dir, options, progress, task_id):
    try:
        progress.update(task_id, description=f"[cyan]Processing {url}")

        shortcode = extract_shortcode(url)
        if not shortcode:
            raise ValueError("Invalid Instagram URL")

        progress.update(task_id, description=f"[cyan]Fetching data for {shortcode}")

        # First, get the post to extract title
        temp_loader = instaloader.Instaloader()
        post = instaloader.Post.from_shortcode(temp_loader.context, shortcode)

        # Generate filename with date and title
        title = get_post_title(post)
        post_date = post.date_utc.strftime("%Y.%m.%d")
        filename_pattern = f"{post_date}-{post.owner_username}-{title}"

        logger.info(f"Using filename pattern: {filename_pattern}")

        # Create the actual loader with the custom filename
        loader = instaloader.Instaloader(
            download_videos=options.get('download_videos', True),
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=not options.get('compress_json', True),
            compress_json=options.get('compress_json', True),
            dirname_pattern=str(output_dir),
            filename_pattern=filename_pattern
        )

        progress.update(task_id, description=f"[cyan]Downloading from @{post.owner_username}")

        # Download with better error handling
        try:
            loader.download_post(post, target=str(output_dir))
        except Exception as download_error:
            # If custom filename fails, try with default pattern
            logger.warning(f"Custom filename failed, trying default: {download_error}")
            default_loader = instaloader.Instaloader(
                download_videos=options.get('download_videos', True),
                download_video_thumbnails=False,
                download_geotags=False,
                download_comments=False,
                save_metadata=not options.get('compress_json', True),
                compress_json=options.get('compress_json', True),
                dirname_pattern=str(output_dir),
                filename_pattern="{date_utc:%Y-%m-%d_%H-%M-%S}_{shortcode}"
            )
            default_loader.download_post(post, target=str(output_dir))
            logger.info("Download successful with default filename pattern")

        progress.update(task_id, completed=100, total=100)
        logger.info(f"Download successful: {url}")

        return {
            "url": url,
            "status": "success",
            "output_dir": str(output_dir),
            "post_type": post.typename,
            "username": post.owner_username,
            "title": title
        }

    except Exception as e:
        logger.exception(f"Download failed for {url}: {str(e)}")
        progress.update(task_id, description=f"[red]Failed: {url}")
        return {"url": url, "status": "error", "error": str(e)}

def download_posts(urls, output_dir, options):
    results = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        overall_task = progress.add_task("[cyan]Overall progress", total=len(urls))
        
        for url in urls:
            task_id = progress.add_task(f"[cyan]Processing {url}", total=100)
            result = download_post(url, output_dir, options, progress, task_id)
            results.append(result)
            progress.update(overall_task, advance=1)
            
            if result["status"] == "success":
                username = result.get('username', 'unknown')
                post_type = result.get('post_type', 'post')
                progress.update(task_id, description=f"[green]✓ Downloaded @{username} ({post_type})", completed=100)
            else:
                progress.update(task_id, description=f"[red]✗ Failed {result['url']}", completed=100)
    
    return results

def show_alternatives(failed_url):
    console.print("\n" + "="*60)
    console.print("[bold yellow]🚫 INSTAGRAM BLOCKED THE REQUEST[/bold yellow]")
    console.print("="*60)
    
    console.print("\n[bold red]Why this happened:[/bold red]")
    console.print("• Instagram blocks automated downloading tools")
    console.print("• 403 Forbidden errors affect all Instagram scrapers")
    
    console.print("\n[bold green]🔧 ALTERNATIVE SOLUTIONS:[/bold green]")
    console.print("\n[bold cyan]Online Services (Recommended):[/bold cyan]")
    console.print("   • SaveFrom.net")
    console.print("   • DownloadGram.com") 
    console.print("   • InstaDownloader.co")
    
    console.print(f"\n[bold magenta]Your URL:[/bold magenta] {failed_url}")
    console.print("[dim]Copy and paste into one of the services above[/dim]")
    console.print("="*60)

def print_results(results):
    console.print("\n" + "="*50)
    console.print("[bold green]📋 DOWNLOAD SUMMARY[/bold green]")
    console.print("="*50)
    
    successful = [r for r in results if r["status"] == "success"]
    failed = [r for r in results if r["status"] == "error"]
    
    if successful:
        console.print(f"\n[bold green]✓ Successfully downloaded {len(successful)} post(s):[/bold green]")
        for item in successful:
            console.print(f"  • {item['url']}")
            console.print(f"    → @{item.get('username', 'unknown')} ({item.get('post_type', 'post')})")
            if item.get('title'):
                console.print(f"    → Title: {item['title']}")
            console.print(f"    → Saved to: {item['output_dir']}")
    
    if failed:
        console.print(f"\n[bold red]✗ Failed to download {len(failed)} post(s):[/bold red]")
        for item in failed:
            console.print(f"  • {item['url']}")
            console.print(f"    → Error: {item['error']}")
        
        # Check for Instagram blocking
        instagram_blocked = any("403" in str(item.get('error', '')) or 
                              "Forbidden" in str(item.get('error', '')) or
                              "metadata failed" in str(item.get('error', ''))
                              for item in failed)
        
        if instagram_blocked:
            show_alternatives(failed[0]['url'])
    
    console.print(f"\n[bold cyan]📊 Total processed: {len(results)} post(s)[/bold cyan]")
    console.print("="*50)

def close_logger():
    """
    Remove all logger sinks, ensuring that any open file handles (e.g. for the log file)
    are properly closed.
    """
    logger.remove()

def cleanup_logs():
    log_file = "instagram_downloader.log"
    # Close the logger to release the file handle before cleaning up
    close_logger()
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            console.print("[bold green]Log file cleaned up[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error cleaning log: {str(e)}[/bold red]")

def wait_for_user_exit():
    """Wait for user to press any key before exiting"""
    console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
    try:
        input()
    except KeyboardInterrupt:
        pass

def main():
    args = parse_arguments()
    urls, output_path, download_type = get_user_inputs(args)
    
    output_dir = Path(output_path)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    setup_logger()
    download_options = DOWNLOAD_TEMPLATES[download_type].copy()
    
    try:
        results = download_posts(urls, output_dir, download_options)
        print_results(results)
        
        if all(r["status"] == "success" for r in results):
            cleanup_logs()
            
    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Finished processing downloads.[/bold green]")
        wait_for_user_exit()

if __name__ == "__main__":
    main()
