#!/usr/bin/env python3
"""
py_similaritymapper - Text File Similarity Detection Tool

Efficiently detects duplicate and near-duplicate text files using:
1. Fast MD5 hashing for exact duplicates
2. textdistance algorithms for fuzzy similarity
3. Line-based set operations for performance
4. Structured JSON output with detailed statistics

Designed to handle hundreds of files with >80% similarity efficiently.
"""

import hashlib
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, field

try:
    import textdistance
except ImportError:
    print("Installing textdistance...")
    import subprocess
    subprocess.run(["uv", "add", "textdistance"], check=True)
    import textdistance


@dataclass
class FileInfo:
    """Information about a file for similarity comparison."""
    path: Path
    size: int
    hash_md5: str
    lines: List[str] = field(default_factory=list)
    line_set: Set[str] = field(default_factory=set)
    
    def __post_init__(self):
        """Calculate line set for efficient comparison."""
        if self.lines:
            self.line_set = set(line.strip() for line in self.lines if line.strip())


@dataclass
class SimilarityResult:
    """Result of similarity comparison between two files."""
    file1: Path
    file2: Path
    similarity_percentage: float
    method: str
    identical_lines: int
    total_unique_lines: int
    file1_only_lines: int
    file2_only_lines: int


class SimilarityMapper:
    """Main class for detecting file similarities."""
    
    def __init__(self, input_dir: Path, output_dir: Path = None):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else Path("out")
        self.output_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            "total_files": 0,
            "exact_duplicates": 0,
            "similar_pairs": 0,
            "processing_time": 0,
            "start_time": datetime.now().isoformat()
        }
        
        # Results storage
        self.exact_duplicates: Dict[str, List[Path]] = {}
        self.similar_pairs: List[SimilarityResult] = []
        self.file_info: Dict[Path, FileInfo] = {}
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file content."""
        hasher = hashlib.md5()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception as e:
            print(f"Error hashing {file_path}: {e}")
            return ""
    
    def read_file_lines(self, file_path: Path) -> List[str]:
        """Read file lines with encoding detection."""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.readlines()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                return []
        
        print(f"Could not decode {file_path} with any encoding")
        return []
    
    def scan_files(self) -> None:
        """Scan input directory and collect file information."""
        print(f"Scanning files in {self.input_dir}...")
        
        text_extensions = {'.md', '.txt', '.py', '.json', '.yaml', '.yml'}
        
        for file_path in self.input_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in text_extensions:
                try:
                    lines = self.read_file_lines(file_path)
                    file_info = FileInfo(
                        path=file_path,
                        size=file_path.stat().st_size,
                        hash_md5=self.calculate_file_hash(file_path),
                        lines=lines
                    )
                    self.file_info[file_path] = file_info
                    self.stats["total_files"] += 1
                    
                    if self.stats["total_files"] % 50 == 0:
                        print(f"Processed {self.stats['total_files']} files...")
                        
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
        
        print(f"Scanned {self.stats['total_files']} files")
    
    def find_exact_duplicates(self) -> None:
        """Find files with identical content using MD5 hashes."""
        print("Finding exact duplicates...")
        
        hash_groups: Dict[str, List[Path]] = {}
        
        for file_info in self.file_info.values():
            if file_info.hash_md5:
                if file_info.hash_md5 not in hash_groups:
                    hash_groups[file_info.hash_md5] = []
                hash_groups[file_info.hash_md5].append(file_info.path)
        
        # Keep only groups with multiple files
        self.exact_duplicates = {
            hash_val: paths for hash_val, paths in hash_groups.items() 
            if len(paths) > 1
        }
        
        duplicate_count = sum(len(paths) - 1 for paths in self.exact_duplicates.values())
        self.stats["exact_duplicates"] = duplicate_count
        print(f"Found {len(self.exact_duplicates)} groups of exact duplicates ({duplicate_count} duplicate files)")
    
    def calculate_line_similarity(self, file1: FileInfo, file2: FileInfo) -> SimilarityResult:
        """Calculate similarity between two files using line-based comparison."""
        set1, set2 = file1.line_set, file2.line_set
        
        if not set1 and not set2:
            return SimilarityResult(
                file1=file1.path, file2=file2.path,
                similarity_percentage=100.0, method="empty_files",
                identical_lines=0, total_unique_lines=0,
                file1_only_lines=0, file2_only_lines=0
            )
        
        intersection = set1 & set2
        union = set1 | set2
        
        # Jaccard similarity (intersection over union)
        similarity = (len(intersection) / len(union)) * 100 if union else 0
        
        return SimilarityResult(
            file1=file1.path, file2=file2.path,
            similarity_percentage=similarity, method="jaccard_lines",
            identical_lines=len(intersection),
            total_unique_lines=len(union),
            file1_only_lines=len(set1 - set2),
            file2_only_lines=len(set2 - set1)
        )
    
    def calculate_textdistance_similarity(self, file1: FileInfo, file2: FileInfo) -> SimilarityResult:
        """Calculate similarity using textdistance library."""
        content1 = '\n'.join(file1.lines)
        content2 = '\n'.join(file2.lines)
        
        # Use Ratcliff-Obershelp algorithm (good for text)
        similarity = textdistance.ratcliff_obershelp.similarity(content1, content2) * 100
        
        # Also calculate line-based stats for consistency
        set1, set2 = file1.line_set, file2.line_set
        intersection = set1 & set2
        union = set1 | set2
        
        return SimilarityResult(
            file1=file1.path, file2=file2.path,
            similarity_percentage=similarity, method="ratcliff_obershelp",
            identical_lines=len(intersection),
            total_unique_lines=len(union),
            file1_only_lines=len(set1 - set2),
            file2_only_lines=len(set2 - set1)
        )
    
    def find_similar_files(self, threshold: float = 80.0, method: str = "jaccard") -> None:
        """Find files with similarity above threshold."""
        print(f"Finding similar files (threshold: {threshold}%, method: {method})...")
        
        # Get files that aren't exact duplicates
        non_duplicate_files = []
        duplicate_paths = set()
        for paths in self.exact_duplicates.values():
            duplicate_paths.update(paths)
        
        for path, file_info in self.file_info.items():
            if path not in duplicate_paths:
                non_duplicate_files.append(file_info)
        
        total_comparisons = len(non_duplicate_files) * (len(non_duplicate_files) - 1) // 2
        print(f"Comparing {len(non_duplicate_files)} files ({total_comparisons:,} comparisons)...")
        
        comparisons_done = 0
        for i, file1 in enumerate(non_duplicate_files):
            for file2 in non_duplicate_files[i+1:]:
                comparisons_done += 1
                
                if comparisons_done % 1000 == 0:
                    progress = (comparisons_done / total_comparisons) * 100
                    print(f"Progress: {progress:.1f}% ({comparisons_done:,}/{total_comparisons:,})")
                
                # Choose similarity method
                if method == "jaccard":
                    result = self.calculate_line_similarity(file1, file2)
                elif method == "textdistance":
                    result = self.calculate_textdistance_similarity(file1, file2)
                else:
                    raise ValueError(f"Unknown method: {method}")
                
                if result.similarity_percentage >= threshold:
                    self.similar_pairs.append(result)
        
        self.stats["similar_pairs"] = len(self.similar_pairs)
        print(f"Found {len(self.similar_pairs)} similar file pairs")
    
    def generate_report(self) -> Dict:
        """Generate comprehensive similarity report."""
        # Sort similar pairs by similarity (highest first)
        sorted_pairs = sorted(self.similar_pairs, 
                            key=lambda x: x.similarity_percentage, reverse=True)
        
        report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "input_directory": str(self.input_dir),
                "processing_time_seconds": self.stats["processing_time"]
            },
            "statistics": self.stats,
            "exact_duplicates": {
                hash_val: [str(p) for p in paths] 
                for hash_val, paths in self.exact_duplicates.items()
            },
            "similar_pairs": [
                {
                    "file1": str(result.file1.relative_to(self.input_dir)),
                    "file2": str(result.file2.relative_to(self.input_dir)),
                    "similarity_percentage": round(result.similarity_percentage, 2),
                    "method": result.method,
                    "identical_lines": result.identical_lines,
                    "total_unique_lines": result.total_unique_lines,
                    "file1_only_lines": result.file1_only_lines,
                    "file2_only_lines": result.file2_only_lines
                }
                for result in sorted_pairs
            ]
        }
        
        return report
    
    def save_report(self, report: Dict, filename: str = None) -> Path:
        """Save report to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"similarity_report_{timestamp}.json"
        
        output_path = self.output_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"Report saved to: {output_path}")
        return output_path
    
    def run_analysis(self, similarity_threshold: float = 80.0, method: str = "jaccard") -> Path:
        """Run complete similarity analysis."""
        start_time = time.time()
        
        print("=== Text File Similarity Analysis ===")
        print(f"Input directory: {self.input_dir}")
        print(f"Output directory: {self.output_dir}")
        print(f"Similarity threshold: {similarity_threshold}%")
        print(f"Method: {method}")
        print()
        
        # Step 1: Scan files
        self.scan_files()
        
        # Step 2: Find exact duplicates
        self.find_exact_duplicates()
        
        # Step 3: Find similar files
        self.find_similar_files(threshold=similarity_threshold, method=method)
        
        # Step 4: Generate and save report
        end_time = time.time()
        self.stats["processing_time"] = round(end_time - start_time, 2)
        
        report = self.generate_report()
        output_path = self.save_report(report)
        
        print("\n=== Analysis Complete ===")
        print(f"Total files processed: {self.stats['total_files']}")
        print(f"Exact duplicates: {self.stats['exact_duplicates']}")
        print(f"Similar pairs found: {self.stats['similar_pairs']}")
        print(f"Processing time: {self.stats['processing_time']} seconds")
        
        return output_path


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Text File Similarity Detection Tool")
    parser.add_argument("input_dir", help="Input directory containing text files")
    parser.add_argument("-o", "--output", help="Output directory (default: ./out)")
    parser.add_argument("-t", "--threshold", type=float, default=80.0,
                       help="Similarity threshold percentage (default: 80.0)")
    parser.add_argument("-m", "--method", choices=["jaccard", "textdistance"], 
                       default="jaccard", help="Similarity calculation method")
    
    args = parser.parse_args()
    
    mapper = SimilarityMapper(
        input_dir=args.input_dir,
        output_dir=args.output
    )
    
    mapper.run_analysis(
        similarity_threshold=args.threshold,
        method=args.method
    )


if __name__ == "__main__":
    main()
