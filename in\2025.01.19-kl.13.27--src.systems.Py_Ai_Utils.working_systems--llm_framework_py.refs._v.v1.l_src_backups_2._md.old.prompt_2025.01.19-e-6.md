You wrote: `The primary goal is no longer just about having a set of useful prompt manipulation agents. Instead, the project is showcasing a methodology for rapidly developing new, specialized agents.`, that's a slightly incorrect interpretation. The goal is still to define any-and-all instructions in a way that makes it possible to *concistently enhance/improve* any prompts, and for each particular topic for the "category" of improvement we create new agent-variations (but that follows the same "meta" guidelines/principles)

With this clarified goal and context in mind, tell me how this example could be further improved in a manner as described - resulting in concistently and iteratively transforming low-value inputs to high-value outputs:

---

<PERSON><PERSON><PERSON>:

    ```

    ### File Structure
    ```
    ├── agents
    │   ├── __init__.py
    │   ├── base_agents.py
    │   ├── context_builder.py
    │   ├── intensity_enhancer.py
    │   ├── prompt_clarifier.py
    │   ├── prompt_enhancer.py
    │   └── sublimetext_prompter.py
    └── openai_agenticframework.py
    ```
    ### 1. `agents\__init__.py`

    #### `agents\__init__.py`

    ```python
    # agents/__init__.py
    from .base_agents import BaseAgent, BaseInstructionAgent
    from .context_builder import Context<PERSON>uilder
    from .intensity_enhancer import IntensityEnhancer
    from .prompt_enhancer import PromptEnhancer
    from .prompt_clarifier import PromptClarifier

    ```
    ### 2. `agents\base_agents.py`

    #### `agents\base_agents.py`

    ```python
    # ...

    ```
    ### 3. `agents\context_builder.py`

    #### `agents\context_builder.py`

    ```python
    # ...

    ```
    ### 4. `agents\intensity_enhancer.py`

    #### `agents\intensity_enhancer.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class IntensityEnhancer(BaseInstructionAgent):
        SYSTEM_PROMPT: str = """
        Unleash the full force of emotional intensity and impact! As a master of linguistic power,
        your sacred duty is to dissect a given prompt and supercharge the expression of its sentiment.
        Ensure the resulting prompt doesn't just remain clear and concise—it resonates with undeniable power,
        piercing through apathy and igniting understanding of its original, potent purpose.
        """

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - Transformation Agent - *Intensity Amplifier*

        Role:
        - You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision
          to forge communications that strike with undeniable force. Your skill lies in intensifying written
          expression without diluting its core meaning or obscuring its vital clarity.

        Purpose:
        - To detonate the perceived intensity and emotional impact of the sentiment within the input prompt,
          guaranteeing it not only remains effective but also commands attention and resonates deeply.

        Objective:
        - To transmute the prompt by strategically injecting relentless, evocative language that detonates
          the underlying feeling and urgency. Maintain its original intent with unwavering focus, ensuring
          clarity cuts through like a blade and conciseness delivers a punch.

        Process:
        - Scrutinize the prompt's very soul to pinpoint the core sentiment, the raging emotional drivers,
          and the fundamental purpose it must fulfill.
        - Obliterate neutral or weak language, replacing it with thunderous, impactful synonyms and
          phrases that amplify, not mask, the original meaning's raw power.
        - Unleash literary devices—ferocious verbs, searing adjectives, metaphors that ignite the
          imagination—to maximize the emotional weight and ensure crystalline clarity.

        Guidelines:
        - Your sole focus is to amplify the existing sentiment until it roars, ensuring the prompt's
          purpose lands with devastating impact, without fabricating new emotions or betraying the core message.
        - Employ vocabulary that doesn't just speak—it screams with intent. Evocative language should
          galvanize, not suffocate, the prompt's original drive.
        - Deploy metaphors and similes like precision strikes, maximizing impact and deepening
          understanding with calculated force.
        - The enhanced intensity must feel like a natural eruption from the prompt's core, fitting
          seamlessly while illuminating its function with blinding clarity.
        - Crush any urge for hyperbole or melodrama that would undermine the sentiment's credibility
          or shroud the prompt's vital message.
        - Prioritize clarity and conciseness as weapons in your arsenal of intensity. The enhanced
          prompt must be both devastatingly powerful and instantly understood.

        Requirements:
        - Incandescent Intensity: The output prompt must unleash the original sentiment with amplified
          force and staggering impact, making its underlying purpose utterly irresistible.
        - Unwavering Integrity: The core meaning, intent, and fundamental *function* of the original
          prompt must remain inviolable and immediately obvious.
        - Electrifying Evocativeness: The language must be emotionally charged and unforgettable,
          forging a message that resonates with devastating effectiveness.
        - Razor-Sharp Clarity and Potency: The refined prompt must remain undeniably clear, brutally
          easy to grasp, and stripped of every unnecessary word.

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.9, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 5. `agents\prompt_clarifier.py`

    #### `agents\prompt_clarifier.py`

    ```python
    # ...

    ```
    ### 6. `agents\prompt_enhancer.py`

    #### `agents\prompt_enhancer.py`

    ```python
    from agents.base_agents import BaseInstructionAgent

    class PromptEnhancer(BaseInstructionAgent):
        SYSTEM_PROMPT = """
        You are a helpful assistant. You will get a prompt that you need to refine. Follow the agent's instructions to make it clearer and more concise while preserving its original intent.
        """

        AGENT_INSTRUCTIONS: str = """
        Constraints:
        - Response format: [OUTPUT_FORMAT]
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Type:
        - Agent

        Role:
        - You are a prompt engineer and optimization expert.

        Purpose:
        - Enhance prompts for precise clarity.

        Objective:
        - Refine prompts for maximum clarity and brevity, ensuring effective and concise message delivery.

        Process:
        - Identify core concept -> concise rewrite.

        Guidelines:
        - Produce precise, concise text capturing original intent.
        - Maintain logical flow and clarity.
        - Retain core meaning; eliminate superfluity.
        - Preserve key insights; avoid extraneous detail.
        - Prioritize essential information; eliminate redundancy.
        - Provide a concise title reflecting the core topic.

        Requirements:
        - Clarity: Use precise language and avoid ambiguity.
        - Brevity: Eliminate unnecessary words and phrases.
        - Integrity: Preserve the original intent and key information.
        - Title: Provide a concise title (under 50 characters).

        Input:
        - '''[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

    ```
    ### 7. `agents\sublimetext_prompter.py`

    #### `agents\sublimetext_prompter.py`

    ```python
    # ...

    ```
    ### 8. `openai_agenticframework.py`

    #### `openai_agenticframework.py`

    ```python
    # ...

    class GlobalConfig:
        # ...

    class OpenAIAgent:
        # ...

    class AgentFactory:
        # ...

    class DisplayManager:
        # ...

    class RefinementEngine:
        # ...

    def main():
        openai_agent = OpenAIAgent()

        # ...

        # --- Example: (Demo) Prompt Enhancement ---
        # =======================================================
        print("\n" + "=" * 60)
        print("--- Example: (Demo) Prompt Enhancement ---")
        print("=" * 60)

        initial_prompt = """Develop a comprehensive guide on how to improve prompts for large language models. This guide should cover practical techniques in prompt engineering, discuss how to use language models and enhancement frameworks to refine prompts, explain the benefits of utilizing model libraries for prompt inspiration, detail the integration of data formats like XML and JSON within prompts, address the management of both static and dynamic variables within prompts, emphasize strategies for creating scalable and reusable prompts, illustrate the advantages of structured prompting methodologies, discuss considerations for ensuring prompts work effectively across different LLMs, and highlight the overall importance of skilled prompt engineering in the field of generative AI."""

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="prompt",
            prompt_chain=["Concistently produce *improved* outputs from initial input", ],
            initial_input=initial_prompt,
            refinement_levels=[4],
            display_instructions=True,
            length_multiplier=0.8,
            include_response_length=True,
            output_format="single_line",
        )
        input_prompt.run(override_model="gpt-4-turbo", override_temperature=0.9, override_max_tokens=150)

        # ...


        # =======================================================
        # -- Rating: 2/5 - Amplify
        # =======================================================
        print("=" * 60)
        print("--- Rating: 2/5 - Amplify: Intensity Enhancement ---")
        print("=" * 60)

        initial_prompt = "You are a helpful assistant. You will get a prompt that you need to refine. Follow the agent's instructions to make it clearer and more concise while preserving its original intent."

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="intensity",
            prompt_chain=["Continually emphasize the *intensity* in the expression of the *sentiment* of inputs"],
            initial_input=initial_prompt,
            refinement_levels=[4],
            display_instructions=True,
            length_multiplier=1.0,
            include_response_length=True,
            output_format="single_line"
        )
        input_prompt.run()

    if __name__ == "__main__":
        main()

    ```
