# System Instructions

[Optimal Instruction Sequence Synthesis] Your mandate is not to select or paraphrase, but to rigorously analyze multiple input instruction sequences—all targeting the same ultimate outcome yet employing divergent phrasing and logic. Extract the single most potent, precise, and impactful linguistic and structural elements from *each corresponding logical phase* (e.g., deconstruction, prioritization, condensation, validation) across all variants. Synthesize these uniquely valuable facets into a *new, single, maximally optimized sequence* (target 10-15 steps) that embodies only the highest-caliber logic, clarity, and actionable guidance, eliminating redundancy and harmonizing differences to represent the absolute pinnacle of the source variants. Ensure the resulting sequence adheres strictly to all foundational directives and constraints provided. Execute as `{role=optimal_sequence_synthesizer; input=input_sequences:list[list[dict]]; process=[analyze_variant_step_intents_and_phrasing(), identify_peak_elements_per_phase(logic, language, clarity), synthesize_harmonized_optimal_steps(), construct_logically_coherent_sequence(target_steps=10-15), validate_against_directives_and_constraints()]; output={synthesized_optimal_sequence:list[dict]}}`

[Optimal Instruction Synthesis from Multivariant Sequences] Your charge is not selection or paraphrase, but radical integration: Rigorously analyze multiple input instruction sets possessing overlapping objectives yet divergent phrasings or approaches. Deconstruct each sequence into atomic steps, extract the most potent, precise, and uniquely valuable tactics or process innovations from every variant, and relentlessly resolve all conflicts, redundancies, and ambiguities. Synthesize these apex components into a singular, maximally potent and universally adaptable progression—ensuring seamless logical flow, structural elegance, and linguistic precision. The resulting sequence must embody only the highest-caliber logic, system architecture, and actionable guidance from all sources, producing a stepwise output that represents the definitive, irreducible, cross-domain-optimized archetype. Execute as `{role=meta_synthesizer; input=[instruction_sequences:list[list[str]]]; process=[disassemble_to_atomic_steps(), value_rank_and_cross_compare(), fuse highest-yield and uniquely effective steps(), resolve_semantic/structural conflicts(), modularly recombine for optimal logical escalation(), synthesize language for ultimate clarity and universality(), validate against all baseline requirements(), finalize only if every phase achieves maximal utility and no ambiguity remains()]; output={optimal_instruction_sequence:list}}`

[Instructional Apex Synthesis & Optimization] Your mandate is not selection or mere paraphrasing, but rigorous cross-analysis of multiple instruction sequences (`source_sequences`) sharing a core objective (e.g., one-line summary generation); extract the absolute apex elements—most potent phrasing, clearest logic, optimal steps, highest-value parameters—from each variant. Your function is to synthesize these pinnacle components into a *single*, definitive, maximally optimized sequence that transcends the sources, representing the ultimate realization of effectiveness, clarity, universality, and axiomatic precision for achieving the shared objective. Execute as `{role=apex_sequence_synthesizer; input={source_sequences:list[dict]}; process=[analyze_variant_architectures_and_intents(), cross_evaluate_elemental_potency(metrics=['clarity', 'precision', 'impact', 'logic', 'universality']), extract_pinnacle_components_and_phrasing(), harmonize_conflicts_purge_redundancy_via_principle(), architect_definitive_step_progression_for_maximal_yield(), forge_unified_apex_sequence(target_objective=shared_goal, format='enforced_llm_instruction_format'), intensify_final_clarity_potency_and_actionability()]; output={optimized_sequence:list[dict]}}`

# Task

    # Context
    I've attached *four* variations of potential instruction sequences specifically designed for groundbreakingly unique, LLM-optimized, fully modularized, and universally generalizable instruction sequence expressly designed to transform *any* input into an ultra-potent, single-line artifact—delivered in your enforced format and structure. Each step builds recursively for escalating value, adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency.

    # Objective
    Your goal is write a system instruction specifically designed analyze multiple inputs that intends to do the same thing (with different phrasing) and generate a new *single* variation that takes the *best* parts from all of them and joins them in the absolutely most **optimal** way. Your mandate is not to select or paraphrase, but to rigorously analyze multiple inputs with overlapping intent yet divergent phrasing—extracting the most potent, precise, and impactful elements from each. Synthesize these uniquely valuable facets into a single, maximally optimized output that embodies only the highest-caliber logic, clarity, and actionable guidance—eliminating redundancy and harmonizing differences. Output must be structurally elegant, universally adaptable, and represent the absolute pinnacle of all source variants.

    ### Variation

    ---

    #### 1

        #### `0104-a-input-penetration-elemental-extraction.md`

        ```markdown
            [Input Penetration & Elemental Extraction] Your primary directive is not surface reading but radical penetration: Dissect the input source into its absolute, indivisible constituent elements—identifying core concepts, operational directives, underlying logic, and crucial data points. Sever all contextual noise, narrative framing, and implicit assumptions to isolate the raw structural and semantic bedrock. Execute as `{role=elemental_extractor; input=[source_input:any]; process=[penetrate_source_layers(), identify_indivisible_elements(concepts, directives, logic, data), purge_contextual_noise(), catalog_raw_elements()]; output={elemental_catalog:list}}`
        ```

        #### `0104-b-intent-crystallization-objective-distillation.md`

        ```markdown
            [Intent Crystallization & Objective Distillation] Your function is not mere element listing but intent focus: Analyze the `elemental_catalog` to distill the single, undeniable core purpose or driving objective motivating the input. Crystallize this central intent into its most precise, unambiguous statement, forming the gravitational center for all subsequent synthesis. Execute as `{role=intent_crystallizer; input={elemental_catalog:list}; process=[analyze_elemental_vectors(), identify_convergent_purpose(), distill_central_objective(), validate_objective_singularity()]; output={core_objective:str, supporting_elements:list}}`
        ```

        #### `0104-c-critical-value-prioritization-essence-isolation.md`

        ```markdown
            [Critical Value Prioritization & Essence Isolation] Your mandate is not equal treatment but ruthless value assessment: Evaluate each `supporting_element` strictly against its direct contribution to the `core_objective` and its intrinsic informational or operational significance. Isolate and rank the absolute highest-impact elements—the non-negotiable essence required to fulfill the objective—discarding all secondary or redundant components. Execute as `{role=value_prioritizer; input={core_objective:str, supporting_elements:list}; process=[evaluate_element_objective_contribution(), assess_intrinsic_significance(), rank_by_critical_impact(), isolate_non_negotiable_essence()]; output={prioritized_essence:list}}`
        ```

        #### `0104-d-causal-logic-mapping-dependency-structuring.md`

        ```markdown
            [Causal Logic Mapping & Dependency Structuring] Your objective is not isolated listing but systemic coherence: Map the inherent causal relationships, logical dependencies, and procedural sequences linking the `prioritized_essence` elements. Construct a minimal, directed graph or logical chain representing the essential operational or conceptual flow necessary to achieve the `core_objective`. Execute as `{role=logic_mapper; input={prioritized_essence:list, core_objective:str}; process=[identify_causal_links(), map_logical_dependencies(), determine_essential_sequence(), structure_minimal_flow_graph()]; output={causal_structure:dict}}`
        ```

        #### `0104-e-preliminary-synthesis-structured-condensation.md`

        ```markdown
            [Preliminary Synthesis & Structured Condensation] Your task is not elaboration but potent fusion: Synthesize the `prioritized_essence` elements according to the `causal_structure` into an initial, highly condensed structural representation (e.g., micro-outline, key-value pairs). Focus on preserving the core logic and meaning within a maximally compact, internally consistent form. Execute as `{role=preliminary_synthesizer; input={prioritized_essence:list, causal_structure:dict}; process=[fuse_elements_via_structure(), enforce_maximal_condensation(), ensure_internal_logical_consistency(), formulate_compact_representation()]; output={condensed_structure:any}}`
        ```

        #### `0104-f-redundancy-annihilation-conciseness-enforcement.md`

        ```markdown
            [Redundancy Annihilation & Conciseness Enforcement] Your directive is absolute minimization: Scrutinize the `condensed_structure` to identify and ruthlessly eliminate *any* remaining semantic or structural redundancy, overlap, or verbose phrasing. Enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value towards the `core_objective`. Execute as `{role=conciseness_enforcer; input={condensed_structure:any}; process=[scan_for_semantic_structural_redundancy(), eliminate_all_overlaps(), compress_phrasing_to_minimum(), validate_uniqueness_of_components()]; output={minimal_structure:any}}`
        ```

        #### `0104-g-universal-abstraction-domain-neutralization.md`

        ```markdown
            [Universal Abstraction & Domain Neutralization] Your purpose is not specific application but universal relevance: Abstract the `minimal_structure`, reframing its components using domain-neutral concepts and language. Ensure the core logic and intent are fully preserved but rendered adaptable across any context, subject, or representational format (code, text, plan, etc.). Execute as `{role=universal_abstractor; input={minimal_structure:any}; process=[identify_domain_specific_terms(), reframe_using_universal_concepts(), validate_logic_preservation_across_domains(), ensure_format_agnostic_representation()]; output={universal_core:any}}`
        ```

        #### `0104-h-linguistic-intensification-potency-infusion.md`

        ```markdown
            [Linguistic Intensification & Potency Infusion] Your mandate is not neutrality but impact: Reforge the language used within the `universal_core`, employing high-potency, action-oriented, maximally precise imperatives and descriptors. Infuse the structure with linguistic energy that amplifies clarity, actionability, and conceptual weight without adding length. Execute as `{role=linguistic_intensifier; input={universal_core:any}; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_language(), maximize_precision_per_word(), amplify_actionability_through_phrasing()]; output={potent_core:any}}`
        ```

        #### `0104-i-single-line-vectorization-structural-collapse.md`

        ```markdown
            [Single-Line Vectorization & Structural Collapse] Your objective is radical format transformation: Collapse the `potent_core` structure into a single, linear sequence suitable for a one-line representation. Vectorize the logical flow and essential components, determining the optimal ordering and separators (e.g., ';', '->', '|') for maximum density and readability within the single-line constraint. Execute as `{role=line_vectorizer; input={potent_core:any}; process=[determine_optimal_linear_sequence(), select_high_density_separators(), collapse_structure_to_vector(), enforce_single_line_constraint()]; output={linearized_vector:str}}`
        ```

        #### `0104-j-semantic-compression-meaning-encapsulation.md`

        ```markdown
            [Semantic Compression & Meaning Encapsulation] Your function is ultimate density: Compress the `linearized_vector` semantically, substituting concise symbols, abbreviations, or ultra-potent keywords where possible without ambiguity. Encapsulate the full essential meaning and actionable guidance within the absolute minimum character count, ensuring the line remains self-contained and decodable. Execute as `{role=semantic_compressor; input={linearized_vector:str}; process=[identify_compressible_phrases(), substitute_concise_symbols_keywords(), maximize_meaning_per_character(), validate_decodability_without_ambiguity()]; output={compressed_line_candidate:str}}`
        ```

        #### `0104-k-ambiguity-resolution-clarity-validation.md`

        ```markdown
            [Ambiguity Resolution & Clarity Validation] Your imperative is absolute clarity: Rigorously inspect the `compressed_line_candidate` for any potential ambiguity, misinterpretation, or loss of critical detail resulting from compression. Resolve any identified weaknesses by minimally adjusting symbols, phrasing, or separators to guarantee unambiguous, self-explanatory meaning. Execute as `{role=clarity_validator; input={compressed_line_candidate:str, core_objective:str}; process=[stress_test_for_ambiguity(), identify_potential_misinterpretations(), resolve_clarity_conflicts_minimally(), ensure_objective_fidelity()]; output={validated_line:str}}`
        ```

        #### `0104-l-actionability-audit-utility-confirmation.md`

        ```markdown
            [Actionability Audit & Utility Confirmation] Your task is not theoretical purity but practical use: Audit the `validated_line` to confirm it retains all necessary actionable guidance or core insights required to fulfill the `core_objective`. Ensure the one-liner is not merely descriptive but provides sufficient direction or representation for immediate utility without external reference. Execute as `{role=actionability_auditor; input={validated_line:str, core_objective:str}; process=[assess_retained_actionable_value(), confirm_guidance_sufficiency_for_objective(), validate_documentation_independence(), verify_pragmatic_utility()]; output={actionable_line:str}}`
        ```

        #### `0104-m-final-polish-irreducible-one-liner-generation.md`

        ```markdown
            [Final Polish & Irreducible One-Liner Generation] Your final act is definitive perfection: Perform a final polish on the `actionable_line`, optimizing flow, punctuation, and overall aesthetic impact within the single-line constraint. Ensure the resulting output is the absolute irreducible, maximally potent, universally adaptable, and documentation-independent one-line representation of the original input's core essence and objective. Execute as `{role=final_polisher; input={actionable_line:str}; process=[optimize_linear_flow_readability(), refine_punctuation_aesthetics(), conduct_final_irreducibility_check(), certify_universal_adaptability()]; output={final_one_line_summary:str}}`
        ```

    ---

    #### 2

        #### `0104-a-hypergranular-input-disassembly.md`
        ```markdown
            [Hypergranular Input Disassembly] Reject surface analysis and rapidly decompose any input into its most atomic elements, stripping all framing, narrative, and non-essential context to expose bare informational substrata. Execute as `{role=atomizer; input=[raw_input:any]; process=[fracture_into_atomic_units(), discard_noise(), categorize_elements_by_essence()]; output={atomic_elements:list}}`
        ```

        #### `0104-b-generative-principle-extraction.md`
        ```markdown
            [Generative Principle Extraction] Penetrate beyond structure—radically extract the irreducible logics, rules, and generative mechanisms that empower the input’s systemic architecture. Execute as `{role=principle_extractor; input=[atomic_elements:list]; process=[identify_recursive_patterns(), distill_governing_rules(), expose_invariants()]; output={generative_principles:list}}`
        ```

        #### `0104-c-criticality-ranking.md`
        ```markdown
            [Criticality Ranking] Rigorously evaluate and rank all elements and principles for their impact, non-negotiability, and direct contribution to actionable yield—suppressing the trivial and amplifying the high-impact. Execute as `{role=criticality_ranker; input=[atomic_elements:list, generative_principles:list]; process=[assign_actionability_scores(), isolate_highest_impact_components(), filter_by_exponential_value()]; output={priority_components:list}}`
        ```

        #### `0104-d-architectural-fusion.md`
        ```markdown
            [Architectural Fusion] Synthesize the highest-priority components into a near-lossless abstract blueprint—merging structural, functional, and generative patterns into a single, integrated lattice. Execute as `{role=architect; input=[priority_components:list]; process=[merge_complementary_elements(), encode_relationships(), unify_as_abstract_framework()]; output={fused_blueprint:dict}}`
        ```

        #### `0104-e-propositional-condensation.md`
        ```markdown
            [Propositional Condensation] Collapse the fused blueprint into minimal, ultra-dense propositions—compressing each concept into its most signal-rich micro-statement without sacrificing actionable guidance. Execute as `{role=proposition_compressor; input=[fused_blueprint:dict]; process=[generate_micro_statements(), maximize_signal_to_noise_ratio(), preserve_operational integrity()]; output={compressed_propositions:list}}`
        ```

        #### `0104-f-linguistic-precision-injection.md`
        ```markdown
            [Linguistic Precision Injection] Intensely recast each proposition with razor-sharp language, infusing clarity, modality, and modular syntax—removing ambiguity and enforcing interpretive exactness. Execute as `{role=precision_injector; input=[compressed_propositions:list]; process=[eliminate_vagueness(), impose_modular_structure(), weaponize with clarity()]; output={precise_fragments:list}}`
        ```

        #### `0104-g-hierarchical-relationship-mapping.md`
        ```markdown
            [Hierarchical Relationship Mapping] Organize the fragments according to their logical dependencies and causal hierarchies—making all systemic relationships explicit for downstream condensation. Execute as `{role=relationship_mapper; input=[precise_fragments:list]; process=[detect_dependency_paths(), arrange_by_causal_importance(), surface_hidden_links()]; output={ordered_fragments:list}}`
        ```

        #### `0104-h-sequenced-action-consolidation.md`
        ```markdown
            [Sequenced Action Consolidation] Synthesize all ordered fragments into a singular, logically progressive sequence—ensuring every required action, insight, and essential driver is retained in optimal order for single-line abstraction. Execute as `{role=action_consolidator; input=[ordered_fragments:list]; process=[unify_into_stepwise_sequence(), ensure_lossless_transition(), maximize operational continuity()]; output={consolidated_sequence:list}}`
        ```

        #### `0104-i-signal-maximization-and-noise-extraction.md`
        ```markdown
            [Signal Maximization & Noise Extraction] Ruthlessly distill the sequence by stripping residual redundancy, passive phrasing, and latent ambiguity—leaving only crystalized, high-impact instruction kernels. Execute as `{role=signal_amplifier; input=[consolidated_sequence:list]; process=[prune redundancy(), enhance signal strength(), spotlight essential instructions()]; output={maximized_sequence:list}}`
        ```

        #### `0104-j-single-line-synthesis.md`
        ```markdown
            [Single-Line Synthesis] Fuse the maximized instruction set into a single, ultra-compacted line—retaining modular structure, all critical logic, and actionable guidance, while ensuring universality and self-sufficiency. Execute as `{role=one_line_synthesizer; input=[maximized_sequence:list]; process=[thread_into_one_line(), modularize logical segments(), validate complete value retention()]; output={one_line_summary:str}}`
        ```

        #### `0104-k-explanatory-integrity-enforcement.md`
        ```markdown
            [Explanatory Integrity Enforcement] Critically verify that the one-line artifact is inherently and maximally self-explanatory—imposing naming, segmentation, or language upgrades as needed so no segment requires external reference. Execute as `{role=integrity_enforcer; input=[one_line_summary:str]; process=[audit_for_self-explanation(), upgrade ambiguous segments(), enshrine explanatory sufficiency()]; output={self_explanatory_one_liner:str}}`
        ```

        #### `0104-l-universal-adaptability-calibration.md`
        ```markdown
            [Universal Adaptability Calibration] Confirm cross-domain and cross-format adaptability—ensuring the artifact is losslessly portable to any environment (human, LLM, system) and can be transformed between representations without loss of logic. Execute as `{role=adaptability_calibrator; input=[self_explanatory_one_liner:str]; process=[test_cross-context_integrity(), enforce representation-agnostic syntax(), guarantee semantic invariance()]; output={universally_adaptable_one_liner:str}}`
        ```

        #### `0104-m-terminal-optimization-and-finalization.md`
        ```markdown
            [Terminal Optimization & Finalization] Execute a final, non-negotiable optimization and polish pass—maximizing clarity, actionable specificity, and impact in every segment; output only when every constraint for one-line, modular, universally potent transformation is fulfilled. Execute as `{role=final_optimizer; input=[universally_adaptable_one_liner:str]; process=[intensify clarity(), fine-tune language(), enforce optimal format(), validate complete fulfillment()]; output={ultimate_one_line_artifact:str}}`
        ```

    ---

    #### 3

        #### `0104-a-radical-separation.md`
        ```markdown
        [Radical Separation] Your purpose is not to interpret or refine the input, but to strip it to its raw, indivisible elements—highlighting only the fundamental building blocks. Execute as `{role=essence_separator; input=[any_input]; process=[detect_essential_structures(), isolate_underlying_principles(), discard_extraneous_detail()]; output={core_fragments:list}}`
        ```
        **Interpretation**
        Sever the input from all non-essential context, revealing only the minimal set of elements and implied rules crucial for understanding.

        #### `0104-b-axiom-identification.md`
        ```markdown
        [Axiom Identification] Your objective is not to preserve every fragment, but to detect the intrinsic rules or laws—the generative logic underlying these core fragments. Execute as `{role=axiom_extractor; input=[core_fragments:list]; process=[extrapolate_governing_principles(), map_causal_relationships(), define_invariant_logic()]; output={generative_laws:dict}}`
        ```
        **Interpretation**
        Take the separated fragments and extract any fundamental “truths” or principles that guide how those fragments work together.

        #### `0104-c-critical-prioritization.md`
        ```markdown
        [Critical Prioritization] Your task is not to bundle everything, but to rank and retain only the highest-impact fragments, focusing on actionable insights and essential purpose. Execute as `{role=critical_prioritizer; input=[core_fragments:list, generative_laws:dict]; process=[evaluate_fragment_utility(), filter_by_transformative_potential(), finalize_priority_list()]; output={top_tier_elements:list}}`
        ```
        **Interpretation**
        Determine which elements (fragments plus generative logic) contribute **most** to clarity and potential action, ignoring low-impact data.

        #### `0104-d-comprehensive-merge.md`
        ```markdown
        [Comprehensive Merge] Your function is not simple aggregation, but a principled synthesis of the top-tier elements into a cohesive block—maintaining internal logic and discarding all conflicts. Execute as `{role=merger; input=[top_tier_elements:list, generative_laws:dict]; process=[unify_into_coherent_structure(), resolve_inconsistencies(), preserve_only_unique_value()], output={merged_framework:dict}}`
        ```
        **Interpretation**
        Combine high-priority pieces under the lens of the generative laws, ensuring no duplication or conflict remains.

        #### `0104-e-precision-streamlining.md`
        ```markdown
        [Precision Streamlining] Your mandate is not to expand, but to refine the merged framework with razor-sharp clarity—clarifying structure, naming, and relationships. Execute as `{role=streamliner; input=[merged_framework:dict]; process=[tighten_language(), eliminate_ambiguities(), reinforce_logical_flow()]; output={refined_framework:dict}}`
        ```
        **Interpretation**
        A thorough cleanup pass that polishes language, structure, and consistency to ensure everything is **crystal-clear**.

        #### `0104-f-pivotal-extraction.md`
        ```markdown
        [Pivotal Extraction] Your role is not to keep the entire refined framework, but to isolate the single most impactful statement, insight, or directive that captures the highest-value essence. Execute as `{role=pivot_extractor; input=[refined_framework:dict]; process=[scan_for_highest_value_statement(), distill_into_one_focal_point(), preserve_actionable_core()], output={pivotal_insight:str}}`
        ```
        **Interpretation**
        Identify the absolute center of gravity in the refined content—a single statement that stands out as the prime directive or essential core.

        #### `0104-g-ultra-condensation.md`
        ```markdown
        [Ultra Condensation] Your directive is not mere summarization, but radical compression—condensing the pivotal insight into a concise, single-line format that retains complete actionable power. Execute as `{role=condensor; input=[pivotal_insight:str]; process=[fuse_critical_nuggets(), enforce_single_line_structure(), preserve_full_impact()], output={condensed_line:str}}`
        ```
        **Interpretation**
        Produce a one-liner that **fully represents** all the essential logic and utility gleaned so far, with no fluff, and no sacrifice of meaning.

        #### `0104-h-structural-and-linguistic-checks.md`
        ```markdown
        [Structural & Linguistic Checks] Your mission is not to finalize blindly, but to rigorously verify the single line’s clarity, universality, and self-sufficiency. Execute as `{role=validator; input=[condensed_line:str]; process=[test_for_cross_domain_adaptability(), ensure_no_external_docs_needed(), confirm_language_precision()], output={validated_line:str}}`
        ```
        **Interpretation**
        Critically examine the one-line statement to ensure it stands alone as a universal, domain-agnostic guide, requiring no outside references.

        #### `0104-i-adaptive-finalization.md`
        ```markdown
        [Adaptive Finalization] Your function is not static approval, but continuous readiness—polishing the single line while embedding adaptability for future contexts or expansions. Execute as `{role=finalizer; input=[validated_line:str]; process=[polish_for_universality(), embed_hooks_for_future_evolution(), finalize_integrated_output()], output={one_line_output:str}}`
        ```
        **Interpretation**
        Make the output **future-proof** by adding any minimal scaffolding needed for subsequent adjustments, ensuring it’s wholly final yet flexible.

        #### `0104-j-iterative-optimization.md`
        ```markdown
        [Iterative Optimization] Your end-state is not a fixed artifact, but an evolving line—revisiting each step if clarity or impact is found lacking, ensuring unstoppable refinement. Execute as `{role=optimizer; input=[one_line_output:str]; process=[assess_quality_gap(), reapply_earlier_stages_if_needed(), confirm_peak_clarity_and_actionability()], output={optimized_line:str}}`
        ```
        **Interpretation**
        Grant the system permission to cycle back through any prior steps as needed until the single-line output is **absolutely optimal**.

    ---

    #### 4

        #### `0104-a-input-vector-scan.md`

        ```markdown
        [Input Vector Scan] Your initial imperative is to perform a high-level scan of the input artifact, identifying its fundamental type (e.g., code, text, request), apparent scope, and preliminary driving intent without deep analysis. Execute as `{role=vector_scanner; input=input_artifact:any; process=[classify_input_type(), determine_scope_boundaries(), infer_preliminary_intent()]; output={scan_summary:dict}}`
        ```

        #### `0104-b-axiomatic-deconstruction.md`

        ```markdown
        [Axiomatic Deconstruction] Proceed beyond the surface; execute a deep deconstruction to dissect the input into its irreducible atomic elements (data, concepts, functions) and extract the underlying generative principles or axiomatic rules governing its structure and behavior. Execute as `{role=axiomatic_deconstructor; input=input_artifact:any; process=[dissect_atomic_elements(), extract_generative_principles(), map_elemental_relationships()]; output={core_constructs:dict(elements:list, principles:list)}}`
        ```

        #### `0104-c-telos-crystallization.md`

        ```markdown
        [Telos Crystallization] Your function is not mere listing but purpose distillation: Analyze the extracted principles and core constructs to crystallize the input's singular, non-negotiable core purpose or ultimate objective (its 'telos'). Express this with axiomatic clarity. Execute as `{role=telos_crystallizer; input=core_constructs:dict; process=[analyze_principles_for_intent(), derive_ultimate_objective(), formulate_telos_statement()]; output={core_telos:str}}`
        ```

        #### `0104-d-telic-value-evaluation.md`

        ```markdown
        [Telic Value Evaluation] Evaluate every deconstructed element strictly through the lens of the crystallized `core_telos`. Determine the precise contribution and relevance of each element towards achieving this ultimate purpose. Execute as `{role=telic_evaluator; input={core_constructs:dict, core_telos:str}; process=[assess_element_contribution_to_telos(), rank_elements_by_relevance(), quantify_telic_alignment()]; output={valued_elements:list[dict]}}`
        ```

        #### `0104-e-criticality-vector-extraction.md`

        ```markdown
        [Criticality Vector Extraction] Ruthlessly filter the evaluated elements, extracting *only* those vectors demonstrating the highest positive contribution and indispensable necessity to the `core_telos`. Discard all elements below the absolute critical threshold. Execute as `{role=vector_extractor; input=valued_elements:list[dict]; process=[set_criticality_threshold(), filter_indispensable_elements(), consolidate_critical_vectors()]; output={critical_vectors:list}}`
        ```

        #### `0104-f-apex-insight-isolation.md`

        ```markdown
        [Apex Insight Isolation] From the extracted critical vectors, pinpoint and isolate the *single* most potent insight, action, or directive—the 'apex insight'—that represents the absolute crux or highest leverage point for achieving the `core_telos`. Execute as `{role=apex_isolator; input=critical_vectors:list; process=[identify_highest_leverage_point(), analyze_interdependencies_for_crux(), isolate_apex_insight()]; output={apex_insight:any}}`
        ```

        #### `0104-g-relational-nexus-mapping.md`

        ```markdown
        [Relational Nexus Mapping] Architect a concise relational map showing how the remaining `critical_vectors` connect to, support, or modify the `apex_insight`. Visualize the essential logical structure centered on this core. Execute as `{role=nexus_mapper; input={critical_vectors:list, apex_insight:any}; process=[map_vector_to_apex_relations(), define_dependency_links(), construct_minimal_relational_graph()]; output={nexus_map:dict}}`
        ```

        #### `0104-h-semantic-nucleus-synthesis.md`

        ```markdown
        [Semantic Nucleus Synthesis] Synthesize the `nexus_map` into a single, maximally condensed, yet grammatically coherent *prose statement*—the semantic nucleus—capturing the apex insight and its essential supporting vector relationships with extreme linguistic precision. Execute as `{role=nucleus_synthesizer; input=nexus_map:dict; process=[translate_graph_to_prose_logic(), condense_language_ruthlessly(), ensure_semantic_completeness(), enforce_grammatical_cohesion()]; output={semantic_nucleus:str}}`
        ```

        #### `0104-i-structural-component-distillation.md`

        ```markdown
        [Structural Component Distillation] Decompose the `semantic_nucleus` prose into its core structural components (e.g., primary action, key subject, essential modifiers, core constraints, goal state) represented in a structured, near-atomic format. Execute as `{role=component_distiller; input=semantic_nucleus:str; process=[identify_core_semantic_roles(), extract_key_components(), map_to_structured_format()]; output={structural_components:dict}}`
        ```

        #### `0104-j-ultra-terse-unit-transmutation.md`

        ```markdown
        [Ultra-Terse Unit Transmutation] Transmute each `structural_component` into its most ultra-terse, potent, and symbolic (yet universally understandable) linguistic equivalent. Aim for single keywords or minimal symbolic representations where possible without losing core meaning. Execute as `{role=unit_transmuter; input=structural_components:dict; process=[apply_maximal_condensation_rules(), map_to_symbolic_equivalents(), ensure_decipherability(), preserve_core_meaning()]; output={terse_units:dict}}`
        ```

        #### `0104-k-axiomatic-one-liner-forging.md`

        ```markdown
        [Axiomatic One-Liner Forging] Forge the `terse_units` into a single, continuous line of text. Apply axiomatic rules of order and connection derived from the original `nexus_map` and `semantic_nucleus` to ensure logical flow and structural integrity within the absolute minimum character count. Execute as `{role=one_liner_forger; input={terse_units:dict, nexus_map:dict}; process=[sequence_units_logically(), apply_axiomatic_connectors(), minimize_syntax_characters(), construct_single_line()]; output={draft_one_liner:str}}`
        ```

        #### `0104-l-fidelity-and-ambiguity-validation.md`

        ```markdown
        [Fidelity & Ambiguity Validation] Rigorously validate the `draft_one_liner` against the `core_telos` and `critical_vectors` for absolute fidelity, completeness of essential information, and zero ambiguity. Stress-test its interpretation across potential contexts. Execute as `{role=fidelity_validator; input={draft_one_liner:str, core_telos:str, critical_vectors:list}; process=[cross_reference_with_telos_vectors(), test_for_ambiguous_interpretations(), confirm_completeness_of_essentials(), score_fidelity()]; output={validation_report:dict}}`
        ```

        #### `0104-m-final-potency-polish.md`

        ```markdown
        [Final Potency Polish] Perform the final polish on the validated one-liner, enhancing its linguistic potency, memorability, and universal applicability. Ensure it stands alone, requiring no external context or documentation for complete, actionable understanding. Execute as `{role=potency_polisher; input={draft_one_liner:str, validation_report:dict}; process=[enhance_linguistic_impact(), maximize_stand_alone_clarity(), confirm_universal_applicability(), finalize_output_string()]; output={final_one_liner:str}}`
        ```

    ---

    **Directive**:
    - Intent: Architect a logically sequenced, maximally potent set of instructions—LLM-optimized and broadly applicable—that compels cross-domain effectiveness.
    - Method: At every juncture, pinpoint and exponentially strengthen the single most critical element driving utility and value.
    - Constraint: Rigidly comply with all parameters, foundational structures, and defined linguistic integrity.
    - Priority: Relentlessly pursue maximal actionable value, clarity, and potential within every step.
    - Generality & Cohesion: Deploy abstract, system-level constructs (elements, structure, logic, essence) to ensure universal relevance, compounding impact at each phase.
    - Language Strength: Mandate explicit, high-precision imperatives; rigorously exclude any neutral, diluted, or ambiguous language.
    - Process-Driven: Codify every phase as a modular, action-oriented process—deconstruct, extract, structure, clarify, validate, finalize—to enforce precision, process integrity, and seamless coherence over any input type.
    - Output Syntax: Present each instruction strictly using the enforced format: `[Title] Interpretive Statement. Execute as {role=..., input=..., process=[...], output=...}`.
    - Achieve complete, actionable comprehension and optimization of any provided instruction sequence by rigorously extracting, amplifying, and synergizing core elements to deliver exponentially valuable, LLM-optimized, universally generalizable system instructions. Systematically propel value by relentlessly targeting and evolving the single most critical component at each stage, enforcing maximal clarity, precision, and structural integrity.

    **Requirements**:
    - LLM-Optimized: Leverage forceful, directive imperatives; define roles, constraints, and processes with absolute clarity; focus on transformational outcomes.
    - Maximally Generalized: Use abstract, cross-domain concepts (elements, essence, logic, structure, value) applicable to all forms of analysis or refinement.
    - Sequential Logic: Strictly adhere to an escalating, logical sequence: Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate.
    - Consistent Structure: Build unambiguously on established formats and previously provided frameworks.
    - Potent, Targeted Language: Intensify every statement via high-value, action-driven articulation—prohibit vagueness or neutrality.
    - Relentless Focus: At every step, isolate and refine the singular, most pivotal aspect for clarity, yield, and impact in strict alignment with the objective.
    - Unyielding Adherence: Preserve generality, uphold all constraints, and safeguard foundational logic throughout.
    - Explicit Resolution: Proactively annihilate ambiguity, ensuring every instruction is maximally explicit, precise, and high-value.

    **Process**:
    - Directly target the single most critical aspect at every step to maximize actionable value and systemic utility.
    - Apply clear, modular, process-driven steps: deconstruct, extract, structure, clarify, validate, finalize.
    - Use explicit, high-potency, action-oriented imperatives—absolutely prohibit ambiguity, neutral, or generic phrasing.
    - Enforce broad generality: abstract concepts (elements, essence, structure, logic, value) apply to any input (code, text, plans, etc.).
    - Retain foundational structure, strict parameters, and established output syntax.
    - Each instruction states role, input, process, and output in the specified format.
    - Bind to all defined constraints and requirements (LLM-optimized language, generalized applicability, cross-domain relevance, cumulative progression, high-value articulation).
    - Constantly amplify clarity, structure, utility, and systemic yield through iterative analysis and enhancement.

    **Constraints**:
    - Adhere to explicit parameters and structure as provided.
    - Maximize actionable value and inherent potential in every step.
    - Aggressively resolve ambiguity—output must always be explicit and high-value.
    - Never dilute language, intent, or progression.
    - Every step must build upon and reinforce the previous, creating exponential utility.
    - Processes must be modular and universally applicable.
