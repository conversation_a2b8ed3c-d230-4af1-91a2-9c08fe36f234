
# Context:

Below is a single instructions (in a sequence) of **generalized instructions**. These generalizations will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (maximally enhanced LLM-optimized system_message) instructions**. Here's an example of how *one* such instruction can look (part of a sequence):

    #### `src\templates\lvl1\md\0005-d-enhance-persuasion.md`

    ```markdown
        [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`
    ```

    Part of sequence:

    ```
        ├── 0005-a-distill-core-essence.md
        ├── 0005-b-explore-implications.md
        ├── 0005-c-structure-argument.md
        ├── 0005-d-enhance-persuasion.md
        └── 0005-e-final-polish.md
    ```

Here's the relevant code from which the templates will be parsed through:

    ```python
    #!/usr/bin/env python3
    # templates_lvl1_md_catalog_generator.py

    '''
        # Philosophical Foundation
        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."
        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."
    '''

    #===================================================================
    # IMPORTS
    #===================================================================
    import os
    import re
    import json
    import glob
    import sys
    import datetime

    #===================================================================
    # BASE CONFIG
    #===================================================================
    class TemplateConfig:
        LEVEL = None
        FORMAT = None
        SOURCE_DIR = None

        # Sequence definition
        SEQUENCE = {
            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
            "id_group": 1,
            "step_group": 2,
            "name_group": 3,
            "order_function": lambda step: ord(step) - ord('a')
        }

        # Content extraction patterns
        PATTERNS = {}

        # Path helpers
        @classmethod
        def get_output_filename(cls):
            if not cls.FORMAT or not cls.LEVEL:
                raise NotImplementedError("FORMAT/LEVEL required.")
            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"

        @classmethod
        def get_full_output_path(cls, script_dir):
            return os.path.join(script_dir, cls.get_output_filename())

        @classmethod
        def get_full_source_path(cls, script_dir):
            if not cls.SOURCE_DIR:
                raise NotImplementedError("SOURCE_DIR required.")
            return os.path.join(script_dir, cls.SOURCE_DIR)

    #===================================================================
    # FORMAT: MARKDOWN (lvl1)
    #===================================================================
    class TemplateConfigMD(TemplateConfig):
        LEVEL = "lvl1"
        FORMAT = "md"
        SOURCE_DIR = "md"

        # Combined pattern for lvl1 markdown templates
        _LVL1_MD_PATTERN = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Match (but don't capture) whitespace AFTER title
            r"(.*?)"         # Group 2: Capture Interpretation text
            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )

        PATTERNS = {
            "title": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(1).strip() if m else ""
            },
            "interpretation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(2).strip() if m else ""
            },
            "transformation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(3).strip() if m else ""
            }
        }

    #===================================================================
    # HELPERS
    #===================================================================
    def _extract_field(content, pattern_cfg):
        try:
            match = pattern_cfg["pattern"].search(content)
            return pattern_cfg["extract"](match)
        except Exception:
            return pattern_cfg.get("default", "")

    def extract_metadata(content, template_id, config):
        content = content.strip()
        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}
        return {"raw": content, "parts": parts}

    #===================================================================
    # CATALOG GENERATION
    #===================================================================
    def generate_catalog(config, script_dir):
        # Find templates and extract metadata
        source_path = config.get_full_source_path(script_dir)
        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
        print(f"Source: {source_path} (*.{config.FORMAT})")
        print(f"Found {len(template_files)} template files.")

        templates = {}
        sequences = {}

        # Process each template file
        for file_path in template_files:
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]

            try:
                # Read content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract and store template metadata
                template_data = extract_metadata(content, template_id, config)
                templates[template_id] = template_data

                # Process sequence information from filename
                seq_match = config.SEQUENCE["pattern"].match(template_id)
                if seq_match:
                    seq_id = seq_match.group(config.SEQUENCE["id_group"])
                    step = seq_match.group(config.SEQUENCE["step_group"])
                    seq_order = config.SEQUENCE["order_function"](step)
                    sequences.setdefault(seq_id, []).append({
                        "template_id": template_id, "step": step, "order": seq_order
                    })
            except Exception as e:
                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)

        # Sort sequence steps
        for seq_id, steps in sequences.items():
            try:
                steps.sort(key=lambda step: step["order"])
            except Exception as e:
                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

        # Create catalog with metadata
        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
        return {
            "catalog_meta": {
                "level": config.LEVEL,
                "format": config.FORMAT,
                "generated_at": timestamp,
                "source_directory": config.SOURCE_DIR,
                "total_templates": len(templates),
                "total_sequences": len(sequences)
            },
            "templates": templates,
            "sequences": sequences
        }

    def save_catalog(catalog_data, config, script_dir):
        output_path = config.get_full_output_path(script_dir)
        print(f"Output: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(catalog_data, f, indent=2, ensure_ascii=False)
                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
                print("--- Catalog Generation Complete ---")
            return output_path
        except Exception as e:
            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
            return None

    #===================================================================
    # IMPORTABLE API FOR EXTERNAL SCRIPTS
    #===================================================================
    def get_default_catalog_path(script_dir=None):
        '''Get the path to the default catalog file.'''
        if script_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
        return TemplateConfigMD().get_full_output_path(script_dir)

    def load_catalog(catalog_path=None):
        '''Load a catalog from a JSON file.'''
        if catalog_path is None:
            catalog_path = get_default_catalog_path()

        if not os.path.exists(catalog_path):
            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")

    def get_template(catalog, template_id):
        '''Get a template by its ID.'''
        if "templates" not in catalog or template_id not in catalog["templates"]:
            return None
        return catalog["templates"][template_id]

    def get_sequence(catalog, sequence_id):
        '''Get all templates in a sequence, ordered by steps.'''
        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            return []

        sequence_steps = catalog["sequences"][sequence_id]
        return [(step["step"], get_template(catalog, step["template_id"]))
                for step in sequence_steps]

    def get_all_sequences(catalog):
        '''Get a list of all sequence IDs.'''
        if "sequences" not in catalog:
            return []
        return list(catalog["sequences"].keys())

    def get_system_instruction(template):
        '''Convert a template to a system instruction format.'''
        if not template or "parts" not in template:
            return None

        parts = template["parts"]
        instruction = f"# {parts.get('title', '')}\n\n"

        if "interpretation" in parts and parts["interpretation"]:
            instruction += f"{parts['interpretation']}\n\n"

        if "transformation" in parts and parts["transformation"]:
            instruction += parts["transformation"]

        return instruction

    def regenerate_catalog(force=False):
        '''Regenerate the catalog file.'''
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config = TemplateConfigMD()
        catalog_path = config.get_full_output_path(script_dir)

        if os.path.exists(catalog_path) and not force:
            # Check if catalog is older than any template file
            catalog_mtime = os.path.getmtime(catalog_path)
            templates_dir = config.get_full_source_path(script_dir)

            needs_update = False
            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                if os.path.getmtime(file_path) > catalog_mtime:
                    needs_update = True
                    break

            if not needs_update:
                print("Catalog is up to date")
                return load_catalog(catalog_path)

        # Generate and save new catalog
        catalog = generate_catalog(config, script_dir)
        save_catalog(catalog, config, script_dir)
        return catalog

    #===================================================================
    # SCRIPT EXECUTION
    #===================================================================
    if __name__ == "__main__":
        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

        # Check for command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
            # Simple API test
            catalog = regenerate_catalog()
            print("\nAvailable sequences:")
            for seq_id in get_all_sequences(catalog):
                sequence_steps = get_sequence(catalog, seq_id)
                if sequence_steps:
                    first_step_title = sequence_steps[0][1]["parts"]["title"]
                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

            sys.exit(0)

        # Regular execution
        try:
            config_to_use = TemplateConfigMD
            active_config = config_to_use()
            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        except NotImplementedError as e:
            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError as e:
            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"FATAL ERROR: {e}", file=sys.stderr)
            sys.exit(1)
    ```

Your goal is to yourself with the structure (based on code and examples provided below), then generalize it into llm-instruction to replace this document (containing a description of how the sequences should be formatted, each block is enclosed with triple backticks and a structure that includes the `title`, `a short interpretive statement`, and the `transformational instructions` - wrapped in curly braces: `{}`) - example:

    #### `0100-a-primal-essence-extraction.md`

    ```markdown
        [Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`
    ```

    ---

    #### `0100-b-intrinsic-value-prioritization.md`

    ```markdown
        [Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`
    ```

    ---

    #### `0100-c-structural-logic-relationship-mapping.md`

    ```markdown
        [Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`
    ```

    ---

    #### `0100-d-potency-clarity-amplification.md`

    ```markdown
        [Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`
    ```

    ---

    #### `0100-e-adaptive-optimization-universalization.md`

    ```markdown
        [Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`
    ```

Do this through consolidating based on this guide:

    ## Word of advice
    This is an example repo, I wouldn't do exactly like this in production. I am simply sharing one way this could be done, may not be the best, but it's a realistic way. I Recommend looking for other implementations as well to complement your research. The repo is also a bit outdated and many conventions have changed over time.

    ### Examples (please create a PR adding any other example you have)
    - https://github.com/bespoyasov/frontend-clean-architecture
    - https://betterprogramming.pub/how-to-implement-a-typescript-web-app-with-clean-architecture-27c7eb745ab4

    # React Clean Architecture
    Applying clean architecture to a react codebase brings lots of benefits, most of them you can find by simply googling what's clean architecture and what should we adopt architectural patterns.
    One advantage that strikes me is having business rules isolated from framework-specific things. This means that our core logic is not coupled to React, React Native, Express, etc...
    This gives you enough flexibility to, for example, move specific parts of the application to a backend, change libraries without too much pain, test once and reuse as many times as you want, share code between React and React Native applications, among others.
    This is a realistic approach, what I mean by that is: It's simple enough to be applicable and Robust enough to have it in a production environment.
    Although I have greatly simplified it, for educational purposes, I believe that this example is of great value to get you started with applying architectural patterns and adapting them to your own needs.

    ## Detailed explanation
    I've been pretty busy lately, so I'll write as much as possible.
    I'll write three blog posts explaining better what is Clean Architecture, why adopt it and how.
    Portuguese version of How to implement clean architecture for React codebases can be found [HERE](https://medium.com/@eduardomoroni/arquitetura-limpa-para-bases-de-c%C3%B3digo-react-df0f78d2b42e)

    ## Philosophy
    ![high-level-diagram](https://github.com/eduardomoroni/react-clean-architecture/blob/master/docs/images/high-level-diagram.jpg)

    The nomenclature may vary, but the concept behind this architectural pattern is: the domain dictates how tools should be organized and not the other way around.
    What I mean by that is that we should organize our codebase around the business rules and not around the frameworks we use to achieve business rules.
    The diagram above shows how the dependency rule works, the inner circles must not know about the outer circles. That is, there cannot be an import of a use case within an entity, or import of a framework within a use case.
    Another important rule is: entities and use cases should not rely on external libraries. The explanation is simple, the core of our application must be robust enough and malleable enough to meet the demands of the business without needing any external intervention.
    If by chance, an essential part of the application core MUST BE an external dependency. Dependency needs to be modeled following [dependency inversion principle](https://en.wikipedia.org/wiki/Dependency_inversion_principle).

    ## Communication flow
    ![communication-flow-diagram](https://github.com/eduardomoroni/react-clean-architecture/blob/master/docs/images/communication-flow.jpg)

    ### A brief explanation of each responsibility
    - **Entity**: Application independent business rules
    - **Interactor**: Application-specific business rules
    - **Adapter**: Glue code from/to *Interactors* and *Presenter*, most of the time implementing a framework-specific behaviour.
      e. g.: We have to connect *Interactor* with react container, to do so, we have to connect *Interactor* with redux (framework) and then connect redux to container components.
    - **Presenter**: Maps data from/to *Adapter* to/from *Components*.
    - **Components**: Simplest possible unit of presentation. Any mapping, conversion, MUST be done by the *Presenter*.

    ## Sample apps DEMO
    Talk is cheap, don't you think? That's why I'm sharing two sample apps to facilitate your digestion.
    A great advantage of following clean architecture is having all business logic self-contained and closer, in a readable way.
    Take a look at `core/entities/` and `core/useCases/` folders and see for yourself.
    ### Counter
    The counter app is a simple example of how to apply clean architecture to react world, it uses only synchronous actions and has no external dependencies.
    It contains 2 use case rules:
    - The count must not be negative.
    - The count must not be greater than 10.

    ![counter-gif](https://github.com/eduardomoroni/react-clean-architecture/blob/master/docs/images/counter.gif)

    ---

    ### Authentication
    An authentication app is a simple example, but not that simple, of how to apply clean architecture to a realistic scenario.
    It contains some shared business rules:
    - Users must have a valid email.
    - Users password must comprises only numbers and/or letters.
    - Users name must have a full name, and it must to be lowercased.
    - The App cannot sign up two users with the same email address.
    - The App must use an external dependency to persist user register.

    ![authentication-gif](https://github.com/eduardomoroni/react-clean-architecture/blob/master/docs/images/authentication.gif)

    ---

    ## Folder Structure
    This repository contains 2 examples of how to implement react following clean architecture, represented by the [diagram](#philosophy) above, and both follow the same folder structure:
    ```
    ./counter
    ├── core
    │   └── lib
    │       ├── adapters
    │       │   └── redux
    │       ├── entities
    │       ├── frameworks
    │       └── useCases
    ├── native
    │   └── src
    │       ├── components
    │       └── stylesheets
    └── web
        └── src
            ├── assets
            ├── components
            └── stylesheets
    ```
    *Note:* the `frameworks` folder comprises framework-specific setups to have it available to the adapters.

    ## Running the apps
    run `npm install` under the project you'd like to run, and then run `npm start`.

    ### Running on Windows

    There's an [issue](https://github.com/eduardomoroni/react-clean-architecture/issues/2) related to how yarn/npm symlink file dependencies on windows. Due to this issue, you should first go under the `core` module and run `npm install` and `npm run build`. This will make the `core` module ready to be installed on the other modules.

    ## References
    - [Clean Architecture: a craftsman's guide to software structure and design](https://goo.gl/2h3fsD)
    - [The clean architecture](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html)
    - [Agility and Architecture](https://www.youtube.com/watch?v=0oGpWmS0aYQ)
    - [Github](https://github.com/topics/clean-architecture?o=desc&s=stars)
    - [Presentational and Container Components](https://medium.com/@dan_abramov/smart-and-dumb-components-7ca2f9a7c7d0)

    ## Thanks
    - Microsoft: for providing a [typescript react native starter kit](https://github.com/Microsoft/TypeScript-React-Native-Starter).
    - [Will Monk](https://github.com/wmonk): for providing a [react typescript starter kit](https://github.com/wmonk/create-react-app-typescript) .

    ## Feedback
    If something looks odd, don't hesitate to reach me out or opening an issue.
