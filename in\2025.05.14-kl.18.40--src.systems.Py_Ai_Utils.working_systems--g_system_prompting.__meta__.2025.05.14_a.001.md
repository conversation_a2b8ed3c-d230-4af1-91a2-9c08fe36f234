
# IMPORTANT

THIS IS THE ROOT DIRECTORY OF A PROJECT CONTAINING GENERALIZED AND SELF-CONTAINED PYTHON UTILS RELATED TO SPECIALIZED PROMPT-ENGINEERING.

## STATUS

Work in progress - Early stages:
- A generalized individual python script designed for generating the template-files
- Since each instruction-template will have it's own file, I'll centralize all of the templates from within this script
- Currently working with the basic components to procedurally interface with the instruction templates (`template_generator.py`)

### `template_generator.py`

```
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Template Generator

This script generates template files based on a predefined dictionary structure.
It creates the necessary directory structure and writes each template to its own file.

Usage:
    python template_generator.py

The script will generate template files in the OUTPUT_DIR directory (default: 'templates').
To change the output directory, modify the OUTPUT_DIR constant at the top of the script.

Each template file will be created with the format:
[Title] Interpretation Execute as: Transformation

Example:
[Instruction Amplifier A] Your mandate is not to... Execute as: `{role=instruction_amplifier_a;...}`
"""

import os
import sys
from typing import Dict

# Configuration
# Change this to modify the output directory
OUTPUT_DIR = "templates"

# Template definitions
# To add a new template, add a new entry to this dictionary with the following structure:
# "filename.md": {
#     "title": "Template Title",
#     "interpretation": "Template interpretation text...",
#     "transformation": "`{role=...}`",
#     "keywords": "keyword1|keyword2|..."
# }
TEMPLATES = {
    "0220-a-framingdirective-amplifier.md": {
        "title": "Instruction Amplifier A",
        "interpretation": "Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as:",
        "transformation": "`{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`",
        "keywords": "inherent"
    },
    "0220-b-framingdirective-amplifier.md": {
        "title": "Instruction Amplifier B",
        "interpretation": "Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt's folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as:",
        "transformation": "`{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`",
        "keywords": "meta|synthesis"
    },
    "0220-c-framingdirective-finalizer.md": {
        "title": "Final Trajectory Distiller",
        "interpretation": "You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; \"Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**\". Execute as:",
        "transformation": "`{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`",
        "keywords": "distill|clarity|amplification|transformation|recursive|meta|synthesis"
    }
}

def ensure_directory_exists(directory_path: str) -> None:
    """Create directory if it doesn't exist."""
    if not os.path.exists(directory_path):
        try:
            os.makedirs(directory_path)
            print(f"Created directory: {directory_path}")
        except OSError as e:
            print(f"Error creating directory '{directory_path}': {e}", file=sys.stderr)
            raise

def generate_template_content(template_data: Dict[str, str]) -> str:
    """Generate template content from template data."""
    title = template_data.get("title", "")
    interpretation = template_data.get("interpretation", "")
    transformation = template_data.get("transformation", "")

    # Format the template content according to the expected structure
    # Check if interpretation already ends with "Execute as:" to avoid duplication
    if interpretation.strip().endswith("Execute as:"):
        content = f"[{title}] {interpretation} {transformation}"
    else:
        content = f"[{title}] {interpretation} Execute as: {transformation}"

    return content

def write_template_file(file_path: str, content: str) -> None:
    """Write content to a template file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Created template file: {file_path}")
    except IOError as e:
        print(f"Error writing template file '{file_path}': {e}", file=sys.stderr)
        raise

def generate_templates(templates_data: Dict[str, Dict[str, str]], output_dir: str = "templates") -> None:
    """Generate template files from templates data."""
    # Ensure the output directory exists
    ensure_directory_exists(output_dir)

    # Generate each template file
    for filename, template_data in templates_data.items():
        # Generate the template content
        content = generate_template_content(template_data)

        # Write the template file
        file_path = os.path.join(output_dir, filename)
        write_template_file(file_path, content)

def main():
    """
    Main function to generate template files.

    To use this script:
    1. Modify the TEMPLATES dictionary to add/edit/remove templates
    2. Change OUTPUT_DIR if you want to save templates in a different directory
    3. Run the script: python template_generator.py
    """
    print(f"Generating templates in directory: {OUTPUT_DIR}")

    # Generate templates
    generate_templates(TEMPLATES, OUTPUT_DIR)

    print(f"\nTemplate generation complete. Templates saved to: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
```

### Template File Structure

Each template is stored as a markdown (.md) file and follows this standardized three-part structure:
- title
    > e.g: "Instruction Amplifier A"
- interpretation
    > e.g: "Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as:"
- transformation
    > e.g: "`{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`"

---

### CONTEXT

This is the original system I used for generating the templates

    - Bat-script as fundamental source for the templates (the place where the templates are generated from):
        ```batch
        :: 0220
        SET filename_0220a="0220-a-framingdirective-amplifier.md"
        @echo [Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}` > %filename_0220a%

        SET filename_0220b="0220-b-framingdirective-amplifier.md"
        @echo [Instruction Amplifier B] Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as: `{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}` > %filename_0220b%

        SET filename_0220c="0220-c-framingdirective-finalizer.md"
        @echo [Final Trajectory Distiller] You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as: `{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}` > %filename_0220c%
        ```
    - Unstructured and requires manual labour, decided to move this over to a python script instead (specifically for generating templates):
        ```json
        {
            "templates": {

                "0220-a-framingdirective-amplifier.md": {
                    "title": "Instruction Amplifier A",
                    "interpretation": "Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as:",
                    "transformation": "`{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`",
                }

                "0220-b-framingdirective-amplifier.md": {
                    "title": "Instruction Amplifier B",
                    "interpretation": "Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as:",
                    "transformation": "`{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`",
                }

                "0220-c-framingdirective-finalizer.md": {
                    "title": "Final Trajectory Distiller",
                    "interpretation": "You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as:",
                    "transformation": "`{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`",
                }
            },
        },
        ```

    - Resulting templates:
        ```
        └── templates
            ├── 0220-a-framingdirective-amplifier.md
            ├── 0220-b-framingdirective-amplifier.md
            └── 0220-c-framingdirective-finalizer.md
        ```

        #### `templates/0220-a-framingdirective-amplifier.md`

        ```markdown
            [Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** itâ€”**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`
        ```

        ---

        #### `templates/0220-b-framingdirective-amplifier.md`

        ```markdown
            [Instruction Amplifier B] Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as: `{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`
        ```

        ---

        #### `templates/0220-c-framingdirective-finalizer.md`

        ```markdown
            [Final Trajectory Distiller] You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as: `{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`
        ```

---

#### `templates/`

    templates
    ├── 0220-a-framingdirective-amplifier.md
    ├── 0220-b-framingdirective-amplifier.md
    └── 0220-c-framingdirective-finalizer.md

#### `templates/0220-a-framingdirective-amplifier.md`

    [Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it:**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`

---

#### `templates/0220-b-framingdirective-amplifier.md`

    [Instruction Amplifier B] Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as: `{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`

---

#### `templates/0220-c-framingdirective-finalizer.md`

    [Final Trajectory Distiller] You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as: `{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`
