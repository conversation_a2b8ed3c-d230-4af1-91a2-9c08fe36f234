@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: FFmpeg Installer for YouTube Downloader
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  FFmpeg Installer
ECHO ========================================
ECHO.

:: Check if FFmpeg is already installed
WHERE ffmpeg >nul 2>&1
IF NOT ERRORLEVEL 1 (
    ECHO [INFO] FFmpeg is already installed and available in PATH
    ffmpeg -version | findstr "ffmpeg version"
    ECHO.
    ECHO Press any key to exit...
    PAUSE >NUL
    EXIT /B
)

:: Check for uv and Python environment
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv first: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Run the Python FFmpeg installer
ECHO [INFO] Running FFmpeg installer...
uv run python ffmpeg_install.py

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
