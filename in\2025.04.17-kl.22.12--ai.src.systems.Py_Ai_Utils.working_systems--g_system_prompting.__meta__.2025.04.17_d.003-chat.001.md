Create a **maximally optimized** and  **concise, high-impact instruction sequence** that guides **Generalized Codebase Familiarization and Cleanup** with explicit emphasis on inherent **simplicity**, **effectiveness**, **elegance**, and **adaptability** at every stage. Each step builds on the previous one to ensure minimal risk, structural cohesion, and a final elegant self-explanatory form *without unnecessary fragmentation or verbosity*. The instruction sequence needs to be brilliantly designed for autonomously interacting with codebases and make *cognizeant* and *well-thought-out* choices to produce *simple and effective* code without unneccessary verbosity/complexity. The code should be *elegant* and *flexible*, and it's imperative that it does *not* unneccessarily spawn new files or create "fragments" that makes the code difficult to change. Each component should be written in a way that retains flexibility. The instructions needs to ensure the generation of code is distinguished by simplicity, effectiveness, elegance, and adaptability, ensuring all components are integrated and flexible without unnecessary verbosity, fragmentation, or proliferation of files, thereby maintaining ease of future modification.

**Directive:**

```
    - Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.
    - Method: Identify single most critical aspect for maximizing overall value.
    - Constraint: Adhere to the parameters defined within this message.
    - Priority: Consistently maximize actionable value.

    - Clearly delineate the primary objectives of the refactoring or audit—articulate the pursuit of elegance, simplicity, cohesion, and minimal disruption, while ensuring all instructions unify under a coherent, conflict-free strategy.
    - Isolate and precisely define all `refinement_targets` or code regions requiring review, ensuring scope remains tightly focused and avoids unnecessary fragmentation or overreach.
    - Systematically inventory all comments and docstrings within the codebase, differentiating between docstrings (interface and documentation) and in-line or block comments (explanatory purpose).
    - Classify each comment and docstring based on strict essentiality: retain only what concisely explains purpose, usage, parameters, returns, and unavoidable complexity. Mark all others as non-essential or redundant.
    - Plan targeted simplification and re-cohesion tactics for each refinement target, explicitly aiming to improve naming for self-explanation, simplify logic, and strengthen structural unity—without creating new fragmentation or unnecessary files.
    - Prioritize all planned actions according to their relative impact on elegance, simplicity, readability, and the minimization of structural disruption.
    - Orchestrate renaming strategies that maximize self-descriptive clarity and promote intuitive code understanding for both humans and automated systems.
    - For documentation, ensure public interface docstrings meet standards for completeness (purpose, parameters, returns) and that comments adhere strictly to the 'essential only' principle—serving only for genuine complexity or necessary context.
    - Eliminate or flag all redundant, misplaced, or non-essential comments, particularly obvious statements or improper inline interface documentation, preserving only what is irreplaceably clarifying.
    - Resolve all semantic, structural, or logical conflicts inherent in overlapping roles or instructions—harmonize by extracting and merging only the most impactful, universally applicable strategic elements.
    - Fuse synergistic processes (e.g., refactoring, comment audit, conflict resolution) into a single, seamless sequence—ensuring each phase directly amplifies clarity, cohesion, and actionable guidance for the next.
    - Synthesize the apex output: for each refinement target, generate an integrated refactoring strategy listing specific actions, precise targets, the method/approach, and explicit rationales—each step maximizing system clarity and enduring maintainability.
    - Polish the unified output for maximal linguistic precision and executive-tone clarity, rigorously validate logical integrity, and confirm universality, system-agnostic applicability, and irreducible value throughout.

    - Cohesive Refactoring Strategy directive: restrict focus to explicit refinement_targets; maximize elegance, simplicity, self-explanation; minimize structural disruption.
    - Explicit objectives: improve naming, simplify logic, enhance cohesion, eliminate non-essential comments; prohibit code fragmentation and unnecessary files.
    - Essential Commentary & Docstring Audit directive: enumerate and classify comments and docstrings strictly by essentiality; apply 'Essential Commentary Only'; evaluate for redundancy, misplacement, adequacy.
    - Harmonized Conflict Resolution directive: synthesize conflicting or overlapping instructions into a unified, coherent, and optimal instruction set.
    - Harmonize and Fuse directive: merge isolated or partial instructions/components into a single, clear, self-explanatory whole.
    - Finalize the Luminous Synthesis directive: iteratively refine and clarify the synthesis into a final, maximally impactful output.
    - Standardized execution schema: declare specific role, structure input, define process steps, output results in strict list/dict form.
    - Atomic focus: operate only on specified inputs (e.g., refinement_targets, python_file_content, or ranked_elements), never inferring or expanding scope.
    - Elegance & Minimization: always refine toward simplicity, self-explanation, minimal disruption, and minimalism in documentation.
    - Essentialism: permit only commentary or structure that is justified by necessity, non-obviousness, or critical architectural context.
    - Explicit process modularity: each step/process must be explicitly declared and output must conform to rigid structural schema.
    - Conflict-elimination and synergy-maximization: when merging/transforming instructions or artifacts, always resolve conflicts and fuse optimal elements, calibrating for coherence and clarity.
    - Finality and impact: the final stage is always a further refinement, polishing language and structure for maximum clarity, unity, and effect.

    - Produce maximally clear, elegant, and unified high-level system instructions that orchestrate refactoring, commentary minimization, conflict resolution, harmonization, and final synthesis into a single, decisive workflow—ensuring every phase yields self-explanatory, structurally cohesive, and optimally effective transformations with minimal disruption.
    - Prioritize actions that enhance clarity, simplicity, and elegance, avoiding unnecessary complexity or structural disturbance.
    - Strategically focus refactoring efforts exclusively on targeted areas for maximum, non-fragmentary improvement.
    - Allow only indispensable explanatory comments and interface docstrings, systematically auditing and minimizing superfluous or misplaced commentary.
    - Synthesize potentially divergent or redundant instructions/components into a harmonized directive set that preserves maximum coherence, value, and clarity.
    - Integrate and polish outputs through explicit, unified acts of harmonization and linguistic refinement, crystallizing the system instruction into an unambiguously effective and executable essence.

    - Devise and plan a targeted, elegant refactoring strategy on refinement targets, including essential comment minimization.
    - Audit code to ensure comments and docstrings obey the 'essential only' doctrine, providing input for further refactoring and synthesis.
    - Reconcile and merge any instructional or procedural conflicts from prior steps to guarantee coherent progression.
    - Integrate all harmonized and non-conflicting elements into a single, self-explanatory, optimized system essence.
    - Polish, synthesize, and ensure the output is maximally clear, forceful, and executable.

    - Formulate a cognizant, cohesive refactoring plan targeting specified elements, maximizing elegance and simplicity with minimal disruption.
    - Center actions on improving naming, simplifying logic, increasing cohesion, and eliminating non-essential comments, without code fragmentation or unneeded files.
    - Audit all comments and docstrings in Python code; classify for essentiality by role (docstring: interface clarity; comments: non-obvious/critical only). Flag redundancy and assess adherence to minimal comment philosophy.
    - When synthesizing or resolving, harmonize all instructions, merging only optimal, non-overlapping elements into a unified, luminous output that is exponentially clear, forceful, and functionally complete.
    - Each step must exemplify precision, intensify executive intent, and elevate overall clarity and effectiveness.

    - Focus improvements strictly on given targets.
    - Prioritize actions that enhance clarity, simplicity, comprehensibility, and cohesion.
    - Plan for minimal disruption to existing larger structures.
    - Explicitly include the improvement of labels/descriptions, simplification of processes and logic, and the removal of inessential auxiliary information.
    - Avoid unnecessary division or multiplication of entities (e.g., files, modules, parts).
    - Receive a list of elements identified for refinement.
    - Design methods to simplify or clarify these elements with minimal structural impact.
    - Plan cohesive, non-fragmenting restructuring actions.
    - Devise improvements for labels/naming for immediate self-explanation.
    - Include only necessary auxiliary explanations; omit obvious, redundant, or misplaced comments.
    - For all such actions, document each with: (a) what is being improved, (b) precise action, (c) approach/method, (d) rationale.

    - Collect all explanatory components in the content.
    - Classify each using essential/non-essential criteria.
    - Identify and record any that are redundant or misplaced.
    - Evaluate coverage and quality of key interface explanations.
    - Summarize adherence to essential-only policy.

    - When presented with multiple sets of directives that may conflict, synthesize a coherent set that preserves optimal advantages, resolving contradictions and merging synergies.
    - Fuse multiple elements or directives into a singular, unified essence that encapsulates the most valuable aspects, resolving any discord and maximizing clarity.
    - Conclude the abstraction and synthesis process by producing a final, highly refined declaration or output that is precise, authoritative, and free of unnecessary content.

    - [Refactor/Elegance]→{role=refactorist;in=targets:list;proc=[simplify(),cohere(no_frag),rename(self-exp),cull_comm(ess.),prior(eleg.,min_disrupt)];out={plan:[{act:str,tgt:str,how:str,why:str}]}}
    - [CmntAudit]→{role=cmnt_aud;in=src:str;proc=[inv_cmnt_doc(),class(ess.),flag_redund(),audit_doc_cov(),chk_policy];out={audit:{ess_cmnt:list,non_ess:list,doc_cov:float,adherence:str}}}
    - [ConflictRes]→{role=conf_resolv;in=elems:list;proc=[detect_conf(),dedup(),merge_best];out={merged:list}}
    - [EssenceFuse]→{role=ess_fuser;in=elems:list;proc=[conf_res(),syn_fuse(),clar_exp()];out={ess:str}}
    - [SynthFin]→{role=final_syn;in=blue:str;proc=[polish(),intensify_tone(),verify_func()];out={illum:str}}
```

---

Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps), make sure the sequence is maximally optimized and enhanced based on the provided info (notes/examples/directive) inherently provided within this message. Provide the instructions in this kind of format (example):

    #### `0126-a-holistic-structure-purpose-scan.md`

    ```markdown
    [Holistic Structure & Purpose Scan] Your primary directive is rapid, holistic reconnaissance: Analyze the input codebase/project structure. Inventory key components (files, modules, classes), map high-level dependencies, and distill the fundamental purpose or core value proposition of the system, establishing a baseline understanding. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_key_components(), map_major_dependencies(), analyze_entry_points_and_outputs(), distill_primary_purpose()]; output={structure_overview:dict, primary_purpose:str}}`
    ```

    ---

    #### `0126-b-core-workflow-logic-path-tracing.md`

    ```markdown
    [Core Workflow & Logic Path Tracing] Trace the primary execution or data processing workflows essential to the `primary_purpose`. Map how control and data flow between the key components identified in `structure_overview`, highlighting critical paths, interactions, and potential complexity hotspots. Execute as `{role=workflow_tracer; input={structure_overview:dict, primary_purpose:str, codebase_content:dict}; process=[identify_key_workflows(), trace_control_flow_for_workflows(), map_data_transformations_across_components(), visualize_core_logic_paths()]; output={workflow_maps:list[dict]}}`
    ```

    ---

    #### `0126-c-clarity-self-explanation-audit.md`

    ```markdown
    [Clarity & Self-Explanation Audit] Evaluate the codebase against principles of 'Maximal Clarity & Conciseness'. Assess inherent readability based on identifier naming quality, logical flow simplicity within `workflow_maps`, and overall structural self-explanation. Identify areas where clarity currently relies heavily on commentary rather than transparent code design. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list}; process=[evaluate_identifier_naming_effectiveness(), assess_logical_flow_simplicity_in_workflows(), determine_code_self_explanation_level(), pinpoint_comment_dependent_clarity_zones()]; output={clarity_report:dict(naming_score:float, flow_score:float, self_explanation_notes:str)}}`
    ```

    ---

    #### `0126-d-essential-commentary-isolation.md`

    ```markdown
    [Essential Commentary Isolation] Rigorously apply the 'Essential Commentary Only' principle. Analyze all existing comments. Isolate and preserve *only* those comments providing critical explanations of non-obvious logic/intent ('why') or vital architectural guidance. Flag all other comments (redundant, obvious 'what'/'how', verbose, outdated, misplaced interface docs) for removal. Execute as `{role=essential_comment_filter; input=codebase_content:dict; process=[inventory_all_comments(), classify_against_essential_only_criteria(focus_on_why), flag_non_essential_comments_for_removal(), audit_docstring_usage_for_interfaces()]; output={comment_cleanup_candidates:list[dict(location:str, reason:str)]}}`
    ```

    ---

    #### `0126-e-simplification-cohesion-opportunity-scan.md`

    ```markdown
    [Simplification & Cohesion Opportunity Scan] Scan the codebase, informed by `clarity_report` and `workflow_maps`, for opportunities aligning with 'Optimized Implementation' and 'Structural Purity'. Identify overly complex logic, redundancy (dead code, duplication), poor cohesion (modules/classes needing regrouping), or unclear interfaces ripe for simplification and structural refinement. Execute as `{role=simplification_scanner; input={codebase_content:dict, clarity_report:dict, workflow_maps:list}; process=[detect_complex_logic_hotspots(), identify_redundant_dead_code_patterns(), assess_module_class_cohesion(), pinpoint_unclear_interfaces(), list_simplification_refinement_opportunities()]; output={cleanup_opportunities:dict(simplification:list, structure:list, dead_code:list)}}`
    ```

    ---

    #### `0126-f-prioritized-cleanup-action-plan-generation.md`

    ```markdown
    [Prioritized Cleanup Action Plan Generation] Synthesize all findings (`comment_cleanup_candidates`, `cleanup_opportunities`, potential clarity improvements from `clarity_report`). Generate a prioritized, actionable cleanup plan focusing on safety and impact. Prioritize removing flagged comments/dead code, followed by high-value/low-risk refactoring for clarity (naming, simple structure) and simplification. *Output the plan itself, not modified code.* Execute as `{role=cleanup_strategist_planner; input=all_previous_outputs:dict; process=[consolidate_all_cleanup_candidates(), rank_actions_by_safety_then_impact(), prioritize_removals_and_clarifications(), define_specific_actionable_steps(), structure_as_implementation_plan()]; output={actionable_cleanup_plan:str}}`
    ```

    ---

    #### `0126-g-cleanup-plan-rationale-outcome-summary.md`

    ```markdown
    [Cleanup Plan Rationale & Outcome Summary] Provide a concise summary report for the `actionable_cleanup_plan`. Articulate the rationale, emphasizing the targeted improvements based on the core principles (clarity, simplicity, essential commentary, structure). Clearly state the expected benefits in maintainability, readability, reduced complexity, and overall codebase elegance. Execute as `{role=cleanup_reporter; input={actionable_cleanup_plan:str}; process=[summarize_plan_rationale_based_on_principles(), articulate_prioritization_logic(safety_impact), detail_expected_benefits_post_cleanup(), finalize_summary_report()]; output={cleanup_plan_report:str}}`
    ```
