:: =============================================================================
:: py_similaritymapper - Text File Similarity Detection Tool
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ===== py_similaritymapper =====
ECHO Text File Similarity Detection Tool
ECHO.

:: =============================================================================
:: UV: Check installation and auto-initialize
:: =============================================================================
uv --version >NUL 2>&1
IF ERRORLEVEL 1 (
    ECHO UV is not installed or not in PATH
    ECHO Attempting to add UV to PATH...
    SET "UV_PATH=C:\Users\<USER>\.local\bin"
    ECHO %PATH% | FIND /I "%UV_PATH%" >NUL
    IF ERRORLEVEL 1 (
        SET "PATH=%UV_PATH%;%PATH%"
        ECHO Added UV to PATH: %UV_PATH%
    )

    :: Try again after adding to PATH
    uv --version >NUL 2>&1
    IF ERRORLEVEL 1 (
        ECHO ERROR: UV is still not available
        ECHO Please install UV first: https://docs.astral.sh/uv/getting-started/installation/
        ECHO Or run uv_init.bat to set up the environment
        PAUSE
        EXIT /B 1
    )
)

:: =============================================================================
:: UV: Auto-initialize if needed
:: =============================================================================
IF NOT EXIST ".venv" (
    ECHO Virtual environment not found. Initializing...
    IF NOT EXIST "pyproject.toml" (
        ECHO ERROR: pyproject.toml not found
        ECHO Please ensure pyproject.toml exists in the project directory
        PAUSE
        EXIT /B 1
    )

    ECHO Installing dependencies with UV...
    uv sync
    IF ERRORLEVEL 1 (
        ECHO ERROR: Failed to install dependencies
        PAUSE
        EXIT /B 1
    )
    ECHO Virtual environment initialized successfully!
    ECHO.
)

:: =============================================================================
:: Input validation
:: =============================================================================
IF NOT EXIST "..\in" (
    ECHO Error: Input directory '..\in' not found!
    ECHO Please ensure you have text files in the 'in' directory.
    PAUSE
    EXIT /B 1
)

:: =============================================================================
:: Execute main script with UV
:: =============================================================================
ECHO Running similarity analysis on files in '..\in' directory...
ECHO.

uv run python src\main.py ..\in %*

:: =============================================================================
:: Results and cleanup
:: =============================================================================
ECHO.
ECHO Analysis complete! Check the 'out' directory for results.
ECHO.
ECHO Press any key to exit...
PAUSE > NUL
