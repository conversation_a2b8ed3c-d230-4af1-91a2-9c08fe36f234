@echo off
REM py_similaritymapper - Text File Similarity Detection Tool
REM Quick run script for analyzing files in the '../in' directory

echo ===== py_similaritymapper =====
echo Text File Similarity Detection Tool
echo.

REM Check if input directory exists
if not exist "..\in" (
    echo Error: Input directory '..\in' not found!
    echo Please ensure you have text files in the 'in' directory.
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found! Please install Python.
    pause
    exit /b 1
)

REM Check if textdistance is installed, install if needed
python -c "import textdistance" >nul 2>&1
if errorlevel 1 (
    echo Installing textdistance...
    uv add textdistance
    if errorlevel 1 (
        echo Error: Failed to install textdistance
        pause
        exit /b 1
    )
)

echo Running similarity analysis on files in '..\in' directory...
echo.

REM Run the analysis with default settings
python src\main.py ..\in

echo.
echo Analysis complete! Check the 'out' directory for results.
pause
