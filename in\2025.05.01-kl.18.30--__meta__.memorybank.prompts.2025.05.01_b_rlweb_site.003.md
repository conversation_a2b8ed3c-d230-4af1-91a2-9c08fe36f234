Going back to the website itself; how could we refactor the projectstructure more *cohesively*? What's the single most *constructive* tip you could give in that regard (based on the initially provided project's filestructure)?

    ├── public
    │   ├── images
    │   │   ├── categorized
    │   │   │   ├── belegg
    │   │   │   │   ├── IMG_0035.webp
    │   │   │   │   ├── IMG_0085.webp
    │   │   │   │   ├── IMG_0121.webp
    │   │   │   │   ├── IMG_0129.webp
    │   │   │   │   ├── IMG_0208.webp
    │   │   │   │   ├── IMG_0451.webp
    │   │   │   │   ├── IMG_0453.webp
    │   │   │   │   ├── IMG_0715.webp
    │   │   │   │   ├── IMG_0717.webp
    │   │   │   │   ├── IMG_1935.webp
    │   │   │   │   ├── IMG_2941.webp
    │   │   │   │   ├── IMG_3001.webp
    │   │   │   │   ├── IMG_3021.webp
    │   │   │   │   ├── IMG_3023.webp
    │   │   │   │   ├── IMG_3033.webp
    │   │   │   │   ├── IMG_3034.webp
    │   │   │   │   ├── IMG_3035.webp
    │   │   │   │   ├── IMG_3036.webp
    │   │   │   │   ├── IMG_3037.webp
    │   │   │   │   ├── IMG_3084.webp
    │   │   │   │   ├── IMG_3133.webp
    │   │   │   │   ├── IMG_4080.webp
    │   │   │   │   ├── IMG_4305.webp
    │   │   │   │   ├── IMG_4547.webp
    │   │   │   │   ├── IMG_4586.webp
    │   │   │   │   ├── IMG_4644.webp
    │   │   │   │   ├── IMG_4996.webp
    │   │   │   │   ├── IMG_4997.webp
    │   │   │   │   ├── IMG_5278.webp
    │   │   │   │   ├── IMG_5279.webp
    │   │   │   │   └── IMG_5280.webp
    │   │   │   ├── ferdigplen
    │   │   │   │   ├── IMG_0071.webp
    │   │   │   │   └── IMG_1912.webp
    │   │   │   ├── hekk
    │   │   │   │   ├── IMG_0167.webp
    │   │   │   │   ├── IMG_1841.webp
    │   │   │   │   ├── IMG_2370.webp
    │   │   │   │   ├── IMG_2371.webp
    │   │   │   │   ├── IMG_3077.webp
    │   │   │   │   └── hekk_20.webp
    │   │   │   ├── kantstein
    │   │   │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
    │   │   │   │   ├── IMG_0066.webp
    │   │   │   │   ├── IMG_0364.webp
    │   │   │   │   ├── IMG_0369.webp
    │   │   │   │   ├── IMG_0427.webp
    │   │   │   │   ├── IMG_0429.webp
    │   │   │   │   ├── IMG_0445.webp
    │   │   │   │   ├── IMG_0716.webp
    │   │   │   │   ├── IMG_2955.webp
    │   │   │   │   ├── IMG_4683.webp
    │   │   │   │   └── IMG_4991.webp
    │   │   │   ├── platting
    │   │   │   │   ├── IMG_3251.webp
    │   │   │   │   └── IMG_4188.webp
    │   │   │   ├── stål
    │   │   │   │   ├── IMG_0068.webp
    │   │   │   │   ├── IMG_0069.webp
    │   │   │   │   ├── IMG_1916.webp
    │   │   │   │   ├── IMG_1917.webp
    │   │   │   │   ├── IMG_1918.webp
    │   │   │   │   ├── IMG_2441.webp
    │   │   │   │   ├── IMG_3599.webp
    │   │   │   │   ├── IMG_3602.webp
    │   │   │   │   ├── IMG_3829.webp
    │   │   │   │   ├── IMG_3832.webp
    │   │   │   │   ├── IMG_3844.webp
    │   │   │   │   ├── IMG_3845.webp
    │   │   │   │   ├── IMG_3847.webp
    │   │   │   │   ├── IMG_3848.webp
    │   │   │   │   ├── IMG_3966.webp
    │   │   │   │   ├── IMG_3969.webp
    │   │   │   │   ├── IMG_4030.webp
    │   │   │   │   ├── IMG_4083.webp
    │   │   │   │   ├── IMG_4086.webp
    │   │   │   │   ├── IMG_4536.webp
    │   │   │   │   └── IMG_5346.webp
    │   │   │   ├── støttemur
    │   │   │   │   ├── IMG_0144.webp
    │   │   │   │   ├── IMG_0318.webp
    │   │   │   │   ├── IMG_0324.webp
    │   │   │   │   ├── IMG_0325.webp
    │   │   │   │   ├── IMG_0452.webp
    │   │   │   │   ├── IMG_0932.webp
    │   │   │   │   ├── IMG_0985.webp
    │   │   │   │   ├── IMG_0986.webp
    │   │   │   │   ├── IMG_0987.webp
    │   │   │   │   ├── IMG_1132.webp
    │   │   │   │   ├── IMG_1134.webp
    │   │   │   │   ├── IMG_1140.webp
    │   │   │   │   ├── IMG_2032.webp
    │   │   │   │   ├── IMG_2083.webp
    │   │   │   │   ├── IMG_2274.webp
    │   │   │   │   ├── IMG_2522.webp
    │   │   │   │   ├── IMG_2523.webp
    │   │   │   │   ├── IMG_2855(1).webp
    │   │   │   │   ├── IMG_2855.webp
    │   │   │   │   ├── IMG_2859.webp
    │   │   │   │   ├── IMG_2861.webp
    │   │   │   │   ├── IMG_2891.webp
    │   │   │   │   ├── IMG_2920.webp
    │   │   │   │   ├── IMG_2921.webp
    │   │   │   │   ├── IMG_2951.webp
    │   │   │   │   ├── IMG_3007.webp
    │   │   │   │   ├── IMG_3151.webp
    │   │   │   │   ├── IMG_3269.webp
    │   │   │   │   ├── IMG_3271.webp
    │   │   │   │   ├── IMG_3369.webp
    │   │   │   │   ├── IMG_4090.webp
    │   │   │   │   ├── IMG_4150.webp
    │   │   │   │   ├── IMG_4151.webp
    │   │   │   │   ├── IMG_4153.webp
    │   │   │   │   └── IMG_4154.webp
    │   │   │   ├── trapp-repo
    │   │   │   │   ├── IMG_0295.webp
    │   │   │   │   ├── IMG_0401.webp
    │   │   │   │   ├── IMG_0448.webp
    │   │   │   │   ├── IMG_0449.webp
    │   │   │   │   ├── IMG_0450.webp
    │   │   │   │   ├── IMG_1081.webp
    │   │   │   │   ├── IMG_1735.webp
    │   │   │   │   ├── IMG_1782.webp
    │   │   │   │   ├── IMG_2095.webp
    │   │   │   │   ├── IMG_2097.webp
    │   │   │   │   ├── IMG_2807.webp
    │   │   │   │   ├── IMG_3086.webp
    │   │   │   │   ├── IMG_3132.webp
    │   │   │   │   ├── IMG_3838.webp
    │   │   │   │   ├── IMG_3939.webp
    │   │   │   │   ├── IMG_4111.webp
    │   │   │   │   ├── IMG_4516.webp
    │   │   │   │   ├── IMG_4551.webp
    │   │   │   │   ├── IMG_5317.webp
    │   │   │   │   └── image4.webp
    │   │   │   └── hero-prosjekter.HEIC
    │   │   ├── site
    │   │   │   ├── hero-corten-steel.webp
    │   │   │   ├── hero-granite.webp
    │   │   │   ├── hero-grass.webp
    │   │   │   ├── hero-grass2.webp
    │   │   │   ├── hero-illustrative.webp
    │   │   │   ├── hero-main.webp
    │   │   │   ├── hero-prosjekter.webp
    │   │   │   └── hero-ringerike.webp
    │   │   ├── team
    │   │   │   ├── firma.webp
    │   │   │   ├── jan.webp
    │   │   │   └── kim.webp
    │   │   └── metadata.json
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── vite.svg
    ├── src
    │   ├── components
    │   │   ├── layout
    │   │   │   ├── Footer.tsx
    │   │   │   ├── Header.tsx
    │   │   │   └── Meta.tsx
    │   │   ├── projects
    │   │   │   └── ProjectGallery.tsx
    │   │   ├── seo
    │   │   │   └── TestimonialsSchema.tsx
    │   │   ├── shared
    │   │   │   └── Elements
    │   │   │       ├── Form
    │   │   │       │   ├── Input.tsx
    │   │   │       │   ├── Select.tsx
    │   │   │       │   └── Textarea.tsx
    │   │   │       ├── Card.tsx
    │   │   │       ├── Icon.tsx
    │   │   │       ├── Image.tsx
    │   │   │       ├── Link.tsx
    │   │   │       └── Loading.tsx
    │   │   ├── testimonials
    │   │   │   └── index.tsx
    │   │   └── ui
    │   │       ├── Button.tsx
    │   │       ├── Container.tsx
    │   │       ├── Hero.tsx
    │   │       ├── Intersection.tsx
    │   │       ├── Logo.tsx
    │   │       ├── Notifications.tsx
    │   │       ├── SeasonalCTA.tsx
    │   │       ├── ServiceAreaList.tsx
    │   │       ├── Skeleton.tsx
    │   │       ├── Transition.tsx
    │   │       └── index.ts
    │   ├── content
    │   │   ├── services
    │   │   │   └── index.ts
    │   │   ├── team
    │   │   │   └── index.ts
    │   │   ├── testimonials
    │   │   │   └── index.ts
    │   │   └── index.ts
    │   ├── data
    │   │   ├── projects.ts
    │   │   ├── services.ts
    │   │   └── testimonials.ts
    │   ├── docs
    │   │   └── SEO_USAGE.md
    │   ├── features
    │   │   ├── home
    │   │   │   ├── FilteredServicesSection.tsx
    │   │   │   └── SeasonalProjectsCarousel.tsx
    │   │   ├── projects
    │   │   │   ├── ProjectCard.tsx
    │   │   │   ├── ProjectFilter.tsx
    │   │   │   ├── ProjectGrid.tsx
    │   │   │   └── ProjectsCarousel.tsx
    │   │   ├── services
    │   │   │   ├── ServiceCard.tsx
    │   │   │   ├── ServiceFeature.tsx
    │   │   │   ├── ServiceGrid.tsx
    │   │   │   └── data.ts
    │   │   ├── testimonials
    │   │   │   ├── AverageRating.tsx
    │   │   │   ├── Testimonial.tsx
    │   │   │   ├── TestimonialFilter.tsx
    │   │   │   ├── TestimonialSlider.tsx
    │   │   │   ├── TestimonialsSection.tsx
    │   │   │   └── data.ts
    │   │   └── testimonials.tsx
    │   ├── hooks
    │   │   └── useData.ts
    │   ├── lib
    │   │   ├── api
    │   │   │   └── index.ts
    │   │   ├── config
    │   │   │   ├── images.ts
    │   │   │   ├── index.ts
    │   │   │   ├── paths.ts
    │   │   │   └── site.ts
    │   │   ├── context
    │   │   │   └── AppContext.tsx
    │   │   ├── hooks
    │   │   │   ├── useAnalytics.ts
    │   │   │   ├── useEventListener.ts
    │   │   │   └── useMediaQuery.ts
    │   │   ├── types
    │   │   │   └── index.ts
    │   │   ├── utils
    │   │   │   ├── analytics.ts
    │   │   │   ├── dom.ts
    │   │   │   ├── formatting.ts
    │   │   │   ├── images.ts
    │   │   │   ├── index.ts
    │   │   │   ├── seasonal.ts
    │   │   │   ├── seo.ts
    │   │   │   └── validation.ts
    │   │   ├── config.ts
    │   │   ├── constants.ts
    │   │   ├── hooks.ts
    │   │   └── utils.ts
    │   ├── pages
    │   │   ├── about
    │   │   │   └── index.tsx
    │   │   ├── contact
    │   │   │   └── index.tsx
    │   │   ├── home
    │   │   │   └── index.tsx
    │   │   ├── services
    │   │   │   ├── detail.tsx
    │   │   │   └── index.tsx
    │   │   ├── testimonials
    │   │   │   └── index.tsx
    │   │   ├── ProjectDetail.tsx
    │   │   ├── Projects.tsx
    │   │   ├── ServiceDetail.tsx
    │   │   ├── Services.tsx
    │   │   └── TestimonialsPage.tsx
    │   ├── styles
    │   │   ├── animations.css
    │   │   ├── base.css
    │   │   └── utilities.css
    │   ├── types
    │   │   ├── content.ts
    │   │   └── index.ts
    │   ├── utils
    │   │   └── imageLoader.ts
    │   ├── App.tsx
    │   ├── index.css
    │   ├── index.html
    │   ├── main.tsx
    │   └── vite-env.d.ts
    ├── README.md
    ├── index.html
    ├── package.json
    ├── tailwind.config.js
    ├── tsconfig.json
    └── vite.config.ts
