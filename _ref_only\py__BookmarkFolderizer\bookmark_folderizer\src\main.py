#!/usr/bin/env python3
"""
py__BookmarkFolderizer (v2.5.1)
Single-file version that:
 - Uses data classes + a Config class
 - Uses YAML logging (Loguru)
 - Strictly sanitizes filenames/directories (whitelist approach)
 - Aggressively shortens each path segment
 - Preserves .json, .html, and (especially) .url extensions intact

In particular, for .url files, we never strip away the ".url" suffix,
no matter how long the path is.
"""

import sys
import os
import re
import json
import time
import logging
import socket
import hashlib
import platform
import argparse
import unicodedata
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Optional

import win32_setctime
from loguru import logger
from rich.console import Console
from rich.prompt import Confirm, Prompt
from rich.table import Table, box

__version__ = "2.5.1"
console = Console()


# =======================================================
# 1) Config
# =======================================================
class Config:
    """
    Global constants and default settings for the utility.
    Also sets up a YAML logger sink via Loguru.
    """
    DEFAULT_INPUT_FILE = "bookmarks.html"
    DEFAULT_OUTPUT_PATH = "out"
    DEFAULT_LOG_LEVEL = "DEBUG"  # Default log level

    DEFAULT_SANITIZATION_METHOD = "unicode"

    REPLACEMENT_MAPS = {
        "reversible": {
            "*": "[AS]",  # asterisk
            "\\": "[BS]", # backslash
            ":": "[CL]",  # colon
            ">": "[GT]",  # greaterthan
            "<": "[LT]",  # lessthan
            "%": "[PC]",  # percent
            "|": "[PL]",  # pipe
            "?": "[QS]",  # question
            '"': "[QT]",  # quote
            ";": "[SC]",  # semicolon
            "/": "[SL]",  # slash
            # Square brackets are preserved and not replaced
        },
        "spaces": {
            "*": " ",
            "\\": " ",
            ":": " ",
            ">": " ",
            "<": " ",
            "%": " ",
            "|": " ",
            "?": " ",
            '"': " ",
            ";": " ",
            "/": " ",
            # Square brackets are preserved and not replaced
        },
        "unicode": {
            "*": "﹡",
            "\\": "⧵",
            ":": "꞉",
            ">": "›",
            "<": "‹",
            "%": "％",
            "|": "⎸",
            "?": "？",
            '"': "＂",
            ";": "；",
            "/": "⁄",
            # Square brackets are preserved and not replaced
        },
    }

    # Shorten path segment (filename or folder)
    MAX_SEGMENT_LENGTH = 80

    # For known extensions, we never shorten them
    # For other unknown extensions, if > 20 chars, we will trim them
    MAX_EXTENSION_LENGTH = 20

    KNOWN_EXTENSIONS = {".json", ".html", ".url"}

    def __init__(self):
        self.enable_utf8_encoding()
        # Where logs go
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        # Initialize with default log level
        self.log_level = self.DEFAULT_LOG_LEVEL
        self.setup_logger()

    def enable_utf8_encoding(self):
        """
        Ensure UTF-8 encoding for standard output and error streams.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    def setup_logger(self):
        """
        YAML logging via Loguru: clears logs and configures a custom sink.
        """
        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
        log_filepath = os.path.join(self.log_dir, log_filename)
        # Clear the log file on each run
        open(log_filepath, "w").close()

        logger.remove()

        def yaml_logger_sink(log_message):
            log_record = log_message.record
            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
            formatted_level = f"!{log_record['level'].name}"
            logger_name = log_record["name"]
            formatted_function_name = f"*{log_record['function']}"
            line_number = log_record["line"]
            log_message_content = log_record["message"]

            if "\n" in log_message_content:
                formatted_message = "|\n" + "\n".join(
                    f"  {line}" for line in log_message_content.splitlines()
                )
            else:
                # If the message has a colon, we quote it; otherwise plain
                formatted_message = (
                    f"'{log_message_content}'"
                    if ":" in log_message_content
                    else log_message_content
                )

            log_lines = [
                f"- time: {formatted_timestamp}",
                f"  level: {formatted_level}",
                f"  name: {logger_name}",
                f"  funcName: {formatted_function_name}",
                f"  lineno: {line_number}",
                f"  message: {formatted_message}",
                ""
            ]

            with open(log_filepath, "a", encoding="utf-8") as log_file:
                log_file.write("\n".join(log_lines) + "\n")

        # Use the configured log level
        logger.add(yaml_logger_sink, level=self.log_level, enqueue=True, format="{message}")

    def set_log_level(self, level: str):
        """Set the logging level."""
        self.log_level = level.upper()
        self.setup_logger()


config = Config()


# =======================================================
# 2) Utility Functions
# =======================================================
import html
import unicodedata

DEFAULT_TIMESTAMP = 0  # Unix epoch start (1970-01-01)

def get_oldest_timestamp(*timestamps) -> int:
    """
    Returns the oldest non-zero timestamp from the provided timestamps.
    If no valid timestamps (>0) are found, returns DEFAULT_TIMESTAMP.
    
    When comparing timestamps, we use the following rules:
    1. Non-zero timestamps are always preferred over DEFAULT_TIMESTAMP
    2. When multiple non-zero timestamps exist, use the oldest one
    3. When timestamps are equal, prefer the first one provided
    """
    valid_timestamps = [(i, t) for i, t in enumerate(timestamps) if t and t > 0]
    if not valid_timestamps:
        return DEFAULT_TIMESTAMP
    
    # Sort by timestamp value (ascending) and original position (ascending)
    valid_timestamps.sort(key=lambda x: (x[1], x[0]))
    return valid_timestamps[0][1]

def make_long_path_safe(path: Path) -> Path:
    """
    Convert path to long-path-safe format on Windows using \\\\?\\ prefix.
    This bypasses the 260-character MAX_PATH limitation and allows paths up to ~32,767 characters.
    """
    if os.name == 'nt':  # Windows only
        path_str = str(path.resolve())
        # Check if path is approaching the 260-character limit (use 240 as buffer)
        if len(path_str) > 240:
            # Add \\?\ prefix if not already present
            if not path_str.startswith('\\\\?\\'):
                logger.debug(f"Converting to long path format: {path_str[:100]}...")
                return Path(f"\\\\?\\{path_str}")
    return path

def ensure_directory(path: Path) -> None:
    # Convert to long path format if needed on Windows
    safe_path = make_long_path_safe(path)
    safe_path.mkdir(parents=True, exist_ok=True)

def create_file_hash(text: str, length: int = 8) -> str:
    return hashlib.md5(text.encode('utf-8')).hexdigest()[:length]

def apply_replacement_map(text: str, method: str) -> str:
    replacements = config.REPLACEMENT_MAPS.get(method, {})
    result = []
    for ch in text:
        if ch in replacements:
            result.append(replacements[ch])
        else:
            result.append(ch)
    final = re.sub(r'\s+', ' ', ''.join(result))
    final = re.sub(r'-{2,}', '-', final)
    return final.strip()

def force_whitelist_ascii(filename: str) -> str:
    """
    1) Normalize Unicode => ASCII
    2) Whitelist [A-Za-z0-9._ ()-[]]
    3) Collapse repeated underscores
    4) Strip trailing underscores, spaces
    5) Preserve leading dots
    6) If empty => fallback
    """
    # Detect if the filename starts with a dot
    has_leading_dot = filename.startswith('.')
    
    normalized = unicodedata.normalize("NFKD", filename)
    ascii_approx = normalized.encode("ascii", "ignore").decode("ascii", "ignore")

    # Modified pattern to include square brackets
    safe_pattern = r'[^A-Za-z0-9 ._\-()[\]]+'
    out = re.sub(safe_pattern, '_', ascii_approx)
    out = re.sub(r'_{2,}', '_', out)
    # Only strip trailing characters, preserve leading dots
    out = out.rstrip("._ ")

    if not out and not has_leading_dot:
        out = "folder_" + create_file_hash(filename)
    elif not out and has_leading_dot:
        out = "." + create_file_hash(filename)
    elif has_leading_dot and not out.startswith('.'):
        out = '.' + out

    return out

def shorten_segment(text: str, maxlen: int) -> str:
    if len(text) > maxlen:
        short_hash = create_file_hash(text, 8)
        slice_len = maxlen - 1 - len(short_hash)
        if slice_len < 1:
            return short_hash
        text = text[:slice_len] + "-" + short_hash
    return text

def standardize_extension(ext: str) -> str:
    if not ext:
        return ""
    ext_l = ext.lower()
    if ext_l in config.KNOWN_EXTENSIONS:
        return ext_l
    if len(ext_l) > config.MAX_EXTENSION_LENGTH:
        ext_l = ext_l[:config.MAX_EXTENSION_LENGTH]
    return ext_l

def make_safe_filename(name: str, method: str = None, is_directory: bool = False) -> str:
    method = method or config.DEFAULT_SANITIZATION_METHOD
    decoded = html.unescape(name.strip())
    partial = apply_replacement_map(decoded, method)

    partial = force_whitelist_ascii(partial)

    if is_directory:
        safe_dir = shorten_segment(partial, config.MAX_SEGMENT_LENGTH)
        if not safe_dir:
            safe_dir = "folder_" + create_file_hash(name)
        logger.debug(f"Directory conversion steps for '{name}':")
        logger.debug(f"  - Decoded: '{decoded}'")
        logger.debug(f"  - After replacement map: '{partial}'")
        logger.debug(f"  - Final: '{safe_dir}'")
        return safe_dir
    else:
        root, ext = os.path.splitext(partial)
        ext = standardize_extension(ext)
        root = force_whitelist_ascii(root)
        root = shorten_segment(root, config.MAX_SEGMENT_LENGTH)
        final = root + ext
        logger.debug(f"Filename conversion steps for '{name}':")
        logger.debug(f"  - Decoded: '{decoded}'")
        logger.debug(f"  - After replacement map: '{partial}'")
        logger.debug(f"  - Root: '{root}'")
        logger.debug(f"  - Extension: '{ext}'")
        logger.debug(f"  - Final: '{final}'")
        return final

def make_safe_dirname(name: str) -> str:
    safe_name = make_safe_filename(name, is_directory=True)
    logger.debug(f"Directory name conversion: '{name}' -> '{safe_name}'")
    return safe_name

def restore_original_name(safe_name: str, method: str = None) -> str:
    method = method or config.DEFAULT_SANITIZATION_METHOD
    char_map = config.REPLACEMENT_MAPS.get(method, config.REPLACEMENT_MAPS["reversible"])
    original = safe_name
    for unsafe, safe in char_map.items():
        original = original.replace(safe, unsafe)
    return original

def safe_path_join(parent: Path, segment: str,
                   is_directory: bool = False,
                   preserve_url_extension: bool = False) -> Path:
    """
    Join parent path with segment, using long path support on Windows.
    If preserve_url_extension=True and segment ends with .url,
    we strip the .url extension, do our path checks, then reappend .url.
    This ensures .url never gets truncated away.
    """
    ext_to_restore = ""
    seg_lower = segment.lower()

    # If we want to preserve .url and the segment ends with .url
    if preserve_url_extension and seg_lower.endswith(".url"):
        ext_to_restore = ".url"
        # remove .url from the end
        base_segment = segment[:-4]  # remove .url
        # sanitize the base
        safe_base = make_safe_filename(base_segment, is_directory=is_directory)
        # create candidate path
        candidate = parent / (safe_base + ext_to_restore)

        # Use long path support if needed
        return make_long_path_safe(candidate)

    # else normal logic
    safe_seg = make_safe_filename(segment, is_directory=is_directory)
    candidate = parent / safe_seg

    # Use long path support if needed
    return make_long_path_safe(candidate)


# =======================================================
# 3) Data Classes
# =======================================================
@dataclass
class Bookmark:
    url: str
    title: str
    add_date: int
    last_modified: int
    icon: str = ""
    order: int = 0  # Track position in parent folder

@dataclass
class Folder:
    name: str
    add_date: int
    last_modified: int
    icon: str = ""
    order: int = 0  # Track position in parent folder
    subfolders: List["Folder"] = field(default_factory=list)
    bookmarks: List[Bookmark] = field(default_factory=list)

def count_folder_contents(folder: Folder) -> dict:
    """Recursively count bookmarks and folders in a folder structure."""
    bookmark_count = len(folder.bookmarks)
    folder_count = len(folder.subfolders)

    # Recursively count in subfolders
    for subfolder in folder.subfolders:
        sub_counts = count_folder_contents(subfolder)
        bookmark_count += sub_counts["bookmarks"]
        folder_count += sub_counts["folders"]

    return {"bookmarks": bookmark_count, "folders": folder_count}


# =======================================================
# 4) HTML Parsing
# =======================================================
def bookmarks_html_reader(html_path: Path, encoding="utf-8") -> str:
    safe_path = make_long_path_safe(html_path)
    with open(safe_path, "r", encoding=encoding) as f:
        return f.read()

def bookmarks_html_writer(html_data: str, html_path: Path,
                          encoding="utf-8", mode="w+"):
    safe_path = make_long_path_safe(html_path)
    with open(safe_path, mode, encoding=encoding) as f:
        f.write(html_data)

def extract_attr(segment: str, attr: str) -> Optional[int]:
    pattern = fr'{attr}\s*=\s*"([^"]+)"'
    match = re.search(pattern, segment, re.IGNORECASE)
    if match:
        try:
            return int(match.group(1))
        except ValueError:
            return None
    return None

def extract_attr_str(segment: str, attr: str) -> Optional[str]:
    pattern = fr'{attr}\s*=\s*"([^"]+)"'
    match = re.search(pattern, segment, re.IGNORECASE)
    if match:
        return match.group(1).strip()
    return None

def folder_from_html(html_data: str) -> Folder:
    root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)
    if root_name_match:
        root_name = root_name_match.group(1).strip()
    else:
        root_name = "Bookmarks"

    root_folder = Folder(
        name=make_safe_dirname(root_name),
        add_date=DEFAULT_TIMESTAMP,
        last_modified=DEFAULT_TIMESTAMP,
        order=0
    )

    def parse_folder(html: str, start_idx=0) -> (Folder, int):
        folder = Folder(
            name="",
            add_date=DEFAULT_TIMESTAMP,
            last_modified=DEFAULT_TIMESTAMP,
            order=0
        )
        idx = start_idx

        while True:
            dt_pos = html.find("<DT>", idx)
            dl_close = html.find("</DL>", idx)
            if dt_pos == -1 or (dl_close != -1 and dl_close < dt_pos):
                return folder, (dl_close + len("</DL>") if dl_close != -1 else len(html))

            idx = dt_pos + len("<DT>")
            if html[idx:idx+3].upper() == "<H3":
                h3_end = html.find("</H3>", idx)
                h3_tag_end = html.find(">", idx)
                if h3_end == -1 or h3_tag_end == -1:
                    continue
                h3_content = html[h3_tag_end+1:h3_end].strip()

                # Extract timestamps and order
                add_date = extract_attr(html[idx:h3_end], "ADD_DATE")
                last_mod = extract_attr(html[idx:h3_end], "LAST_MODIFIED")
                order = extract_attr(html[idx:h3_end], "order") or 0

                # If last_modified is missing but add_date exists, use add_date
                if not last_mod and add_date:
                    last_mod = add_date

                icon_val = extract_attr_str(html[idx:h3_end], "ICON") or ""

                subfolder = Folder(
                    name=make_safe_dirname(h3_content),
                    add_date=add_date or DEFAULT_TIMESTAMP,
                    last_modified=last_mod or DEFAULT_TIMESTAMP,
                    icon=icon_val,
                    order=int(order)
                )

                dl_start = html.find("<DL>", h3_end)
                if dl_start != -1:
                    sub_data, new_pos = parse_folder(html, dl_start + len("<DL>"))
                    subfolder.subfolders = sub_data.subfolders
                    subfolder.bookmarks = sub_data.bookmarks
                    idx = new_pos
                folder.subfolders.append(subfolder)

            elif html[idx:idx+2].upper() == "<A":
                a_end = html.find("</A>", idx)
                if a_end == -1:
                    continue
                a_tag_content = html[idx:a_end]
                link_end_tag = html.find(">", idx)
                link_title = html[link_end_tag+1:a_end].strip()
                url_val = extract_attr_str(a_tag_content, "HREF") or ""

                # Extract timestamps and order
                add_date = extract_attr(a_tag_content, "ADD_DATE")
                last_mod = extract_attr(a_tag_content, "LAST_MODIFIED")
                order = extract_attr(a_tag_content, "order") or 0

                # If last_modified is missing but add_date exists, use add_date
                if not last_mod and add_date:
                    last_mod = add_date

                icon_val = extract_attr_str(a_tag_content, "ICON") or ""

                bookmark = Bookmark(
                    url=url_val,
                    title=link_title,
                    add_date=add_date or DEFAULT_TIMESTAMP,
                    last_modified=last_mod or DEFAULT_TIMESTAMP,
                    icon=icon_val,
                    order=int(order)
                )
                folder.bookmarks.append(bookmark)
                idx = a_end + len("</A>")
            else:
                idx += 1

    dl_start = html_data.upper().find("<DL>")
    if dl_start == -1:
        return root_folder

    parsed_sub, _ = parse_folder(html_data, dl_start + len("<DL>"))
    root_folder.subfolders = parsed_sub.subfolders
    root_folder.bookmarks = parsed_sub.bookmarks
    return root_folder


# =======================================================
# 5) JSON Parsing
# =======================================================
def bookmarks_json_reader(json_path: Path, encoding="utf-8"):
    safe_path = make_long_path_safe(json_path)
    with open(safe_path, "r", encoding=encoding) as f:
        return json.load(f)

def bookmarks_json_writer(json_data, json_path: Path,
                          indent=4, encoding="utf-8", mode="w+"):
    safe_path = make_long_path_safe(json_path)
    with open(safe_path, mode, encoding=encoding) as f:
        json.dump(json_data, f, indent=indent, ensure_ascii=False)

def folder_to_dict(folder: Folder) -> dict:
    d = {"link": []}
    if folder.add_date:
        d["add_date"] = folder.add_date
    if folder.last_modified:
        d["last_modified"] = folder.last_modified
    if folder.icon:
        d["icon"] = folder.icon
    if folder.order:
        d["order"] = folder.order

    # Sort bookmarks by order before adding to dict
    for bm in sorted(folder.bookmarks, key=lambda x: x.order):
        d["link"].append({
            "url": bm.url,
            "title": bm.title,
            "add_date": bm.add_date,
            "last_modified": bm.last_modified,
            "icon": bm.icon,
            "order": bm.order
        })

    # Sort subfolders by order before adding to dict
    for sub in sorted(folder.subfolders, key=lambda x: x.order):
        sub_key = restore_original_name(sub.name)
        d[sub_key] = folder_to_dict(sub)

    return d

def folder_from_json(obj) -> Folder:
    folder = Folder(
        name="",
        add_date=DEFAULT_TIMESTAMP,
        last_modified=DEFAULT_TIMESTAMP,
        order=obj.get("order", 0)  # Get order from JSON
    )
    if not isinstance(obj, dict):
        return folder

    # Set folder timestamps from the object itself
    folder.add_date = obj.get("add_date", DEFAULT_TIMESTAMP)
    folder.last_modified = obj.get("last_modified", DEFAULT_TIMESTAMP)

    if "link" in obj and isinstance(obj["link"], list):
        for i, entry in enumerate(obj["link"]):
            if isinstance(entry, dict) and "url" in entry and "title" in entry:
                add_date = entry.get("add_date", DEFAULT_TIMESTAMP)
                last_mod = entry.get("last_modified", add_date if add_date != DEFAULT_TIMESTAMP else DEFAULT_TIMESTAMP)
                icon = entry.get("icon", "")
                order = entry.get("order", i)  # Use provided order or index as fallback
                bm = Bookmark(
                    url=entry["url"],
                    title=entry["title"],
                    add_date=add_date,
                    last_modified=last_mod,
                    icon=icon,
                    order=order
                )
                folder.bookmarks.append(bm)

    subfolder_order = 0
    for key, value in obj.items():
        if key == "link":
            continue
        if isinstance(value, dict):
            icon = value.get("icon", "")
            order = value.get("order", subfolder_order)  # Use provided order or counter
            subfolder_order += 1

            sub = folder_from_json(value)
            sub.name = make_safe_dirname(key)
            sub.icon = icon
            sub.order = order
            folder.subfolders.append(sub)

    return folder


# =======================================================
# 6) Directory Parsing (.url)
# =======================================================
def parse_url_file(filepath: Path, original_name: str) -> Optional[Bookmark]:
    metadata = {
        "title": original_name.replace(".url", ""),
        "url": "",
        "add_date": DEFAULT_TIMESTAMP,
        "last_modified": DEFAULT_TIMESTAMP,
        "icon": "",
        "order": 0,  # Default order
    }
    try:
        safe_path = make_long_path_safe(filepath)
        with open(safe_path, "r", encoding="utf-8") as file:
            for line in file:
                if line.startswith(";"):
                    parts = line[1:].split(":", 1)
                    if len(parts) == 2:
                        key, value = parts
                        key = key.strip().lower()
                        val = value.strip()
                        if key == "add_date":
                            try:
                                timestamp = int(val)
                                metadata["add_date"] = timestamp
                            except ValueError:
                                pass  # Keep default if invalid
                        elif key == "last_modified":
                            try:
                                timestamp = int(val)
                                metadata["last_modified"] = timestamp
                            except ValueError:
                                pass  # Keep default if invalid
                        elif key == "icon":
                            metadata["icon"] = val
                        elif key == "title":
                            metadata["title"] = val
                        elif key == "order":
                            try:
                                metadata["order"] = int(val)
                            except ValueError:
                                pass  # Keep default if invalid
                elif line.upper().startswith("URL="):
                    metadata["url"] = line[4:].strip()
    except Exception as e:
        logger.error(f"Error reading .url file {filepath}: {e}")
        return None

    # If last_modified is missing, use add_date
    if metadata["last_modified"] == DEFAULT_TIMESTAMP and metadata["add_date"] != DEFAULT_TIMESTAMP:
        metadata["last_modified"] = metadata["add_date"]

    return Bookmark(
        url=metadata["url"],
        title=metadata["title"],
        add_date=metadata["add_date"],
        last_modified=metadata["last_modified"],
        icon=metadata["icon"],
        order=metadata["order"]
    )

def bookmarks_urls_parser(directory: Path) -> Folder:
    # Try to read folder timestamps from a special file first
    folder_info_path = directory / ".folder_info"
    folder_timestamps = {
        "add_date": DEFAULT_TIMESTAMP,
        "last_modified": DEFAULT_TIMESTAMP,
        "order": 0  # Add order to folder info
    }

    if folder_info_path.exists():
        try:
            safe_path = make_long_path_safe(folder_info_path)
            with open(safe_path, "r", encoding="utf-8") as f:
                for line in f:
                    if line.startswith(";"):
                        parts = line[1:].split(":", 1)
                        if len(parts) == 2:
                            key, value = parts
                            key = key.strip().lower()
                            val = value.strip()
                            if key in ["add_date", "last_modified"]:
                                try:
                                    folder_timestamps[key] = int(val)
                                except ValueError:
                                    pass
                            elif key == "order":  # Parse order from folder info
                                try:
                                    folder_timestamps["order"] = int(val)
                                except ValueError:
                                    pass
        except Exception as e:
            logger.error(f"Error reading folder info: {e}")

    folder = Folder(
        name=make_safe_dirname(directory.name),
        add_date=folder_timestamps["add_date"],
        last_modified=folder_timestamps["last_modified"],
        order=folder_timestamps["order"]  # Set folder order
    )

    # First collect all items
    items = []
    for item in os.listdir(directory):
        if item == ".folder_info":
            continue
        item_path = directory / item
        original_name = restore_original_name(item)
        if item_path.is_dir():
            sub = bookmarks_urls_parser(item_path)
            sub.name = make_safe_dirname(original_name)
            items.append(("folder", sub))
        elif item_path.is_file() and item_path.suffix.lower() == ".url":
            bm = parse_url_file(item_path, original_name)
            if bm:
                items.append(("bookmark", bm))

    # Sort items by order and type (folders first, then bookmarks)
    items.sort(key=lambda x: (x[0] != "folder", getattr(x[1], "order", 0)))

    # Add items to folder in sorted order
    for item_type, item in items:
        if item_type == "folder":
            folder.subfolders.append(item)
        else:
            folder.bookmarks.append(item)

    return folder


# =======================================================
# 7) Export Utils
# =======================================================
def folder_to_html(folder: Folder, indent=0) -> str:
    tab = "    "
    lines = []
    personal_toolbar = ""
    if folder.name.lower() in ["bookmarks bar", "bookmarks_bar"]:
        personal_toolbar = ' PERSONAL_TOOLBAR_FOLDER="true"'

    icon_attr = f' ICON="{folder.icon}"' if folder.icon else ""

    lines.append(
        f'{tab*indent}<DT><H3 ADD_DATE="{folder.add_date}" LAST_MODIFIED="{folder.last_modified}"'
        f'{personal_toolbar}{icon_attr}>{folder.name}</H3>'
    )
    lines.append(f'{tab*indent}<DL><p>')

    # Sort items by order
    items = []
    for bm in folder.bookmarks:
        items.append(("bookmark", bm))
    for sub in folder.subfolders:
        items.append(("folder", sub))

    # Sort purely by order
    items.sort(key=lambda x: x[1].order)

    # Generate HTML for sorted items
    for item_type, item in items:
        if item_type == "bookmark":
            icon_attr = f' ICON="{item.icon}"' if item.icon else ""
            lines.append(
                f'{tab*(indent+1)}<DT><A HREF="{item.url}" ADD_DATE="{item.add_date}"'
                f'{icon_attr}>{item.title}</A>'
            )
        else:
            lines.append(folder_to_html(item, indent+1))

    lines.append(f'{tab*indent}</DL><p>')
    return "\n".join(lines)

def create_html_from_folder(root_folder: Folder) -> str:
    html_output = [
        "<!DOCTYPE NETSCAPE-Bookmark-file-1>",
        "<!-- This is an automatically generated file.",
        "     It will be read and overwritten.",
        "     DO NOT EDIT! -->",
        '<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">',
        "<TITLE>Bookmarks</TITLE>",
        "<H1>Bookmarks</H1>",
        "<DL><p>"
    ]

    # Sort all items by order
    items = []
    for bm in root_folder.bookmarks:
        items.append(("bookmark", bm))
    for sub in root_folder.subfolders:
        items.append(("folder", sub))

    # Sort purely by order
    items.sort(key=lambda x: x[1].order)

    # Generate HTML for sorted items
    for item_type, item in items:
        if item_type == "bookmark":
            icon_attr = f' ICON="{item.icon}"' if item.icon else ""
            html_output.append(
                f'<DT><A HREF="{item.url}" ADD_DATE="{item.add_date}"'
                f'{icon_attr}>{item.title}</A>'
            )
        else:
            html_output.append(folder_to_html(item, indent=1))

    html_output.append("</DL><p>")
    return "\n".join(html_output)

def count_total_items(folder: Folder) -> dict:
    """Count total bookmarks and folders in the folder structure."""
    counts = {"bookmarks": 0, "folders": 0}

    # Count bookmarks in current folder
    counts["bookmarks"] += len(folder.bookmarks)

    # Count subfolders and recursively count their contents
    for subfolder in folder.subfolders:
        counts["folders"] += 1
        sub_counts = count_total_items(subfolder)
        counts["bookmarks"] += sub_counts["bookmarks"]
        counts["folders"] += sub_counts["folders"]

    return counts

def collect_never_attempted_items(folder: Folder, intended_path: Path, parent_error: str, never_attempted: list):
    """Recursively collect all items that were never attempted due to parent directory failure."""
    # Add all bookmarks in this folder as never attempted
    for bm in folder.bookmarks:
        never_attempted.append({
            "type": "bookmark",
            "title": bm.title,
            "url": bm.url,
            "reason": "parent_directory_creation_failed",
            "failed_parent": str(intended_path),
            "parent_error": parent_error,
            "intended_path": str(intended_path / f"{make_safe_filename(bm.title, is_directory=False)}.url"),
            "add_date": bm.add_date,
            "last_modified": bm.last_modified,
            "icon": bm.icon,
            "order": bm.order
        })

    # Add all subfolders and their contents as never attempted
    for sub in folder.subfolders:
        never_attempted.append({
            "type": "folder",
            "name": sub.name,
            "reason": "parent_directory_creation_failed",
            "failed_parent": str(intended_path),
            "parent_error": parent_error,
            "intended_path": str(intended_path / sub.name),
            "add_date": sub.add_date,
            "last_modified": sub.last_modified,
            "order": sub.order
        })

        # Recursively collect items from subfolders
        collect_never_attempted_items(sub, intended_path / sub.name, parent_error, never_attempted)

def create_urls_from_folder(folder: Folder, directory: Path, failed_items=None, success_counts=None, never_attempted=None):
    """Create URL files from folder structure with comprehensive tracking."""
    if failed_items is None:
        failed_items = []
    if success_counts is None:
        success_counts = {"bookmarks": 0, "folders": 0}
    if never_attempted is None:
        never_attempted = []

    try:
        ensure_directory(directory)
        logger.debug(f"Creating URL files in directory: {directory}")
        success_counts["folders"] += 1
    except Exception as e:
        logger.error(f"Error creating directory {directory}: {e}")
        # Collect failed folder info
        failed_items.append({
            "type": "folder",
            "name": folder.name,
            "path": str(directory),
            "error": str(e),
            "add_date": folder.add_date,
            "last_modified": folder.last_modified,
            "order": folder.order
        })

        # Count all items that will never be attempted due to this folder failure
        collect_never_attempted_items(folder, directory, str(e), never_attempted)

        return failed_items, success_counts, never_attempted

    # Write folder timestamps to .folder_info file
    folder_info_path = directory / ".folder_info"
    try:
        safe_path = make_long_path_safe(folder_info_path)
        with open(safe_path, "w", encoding="utf-8") as f:
            f.write(f"; ADD_DATE: {folder.add_date}\n")
            f.write(f"; LAST_MODIFIED: {folder.last_modified}\n")
            f.write(f"; ORDER: {folder.order}\n")  # Write order to folder info
    except Exception as e:
        logger.error(f"Error writing folder info: {e}")

    for bm in sorted(folder.bookmarks, key=lambda x: x.order):  # Sort by order
        base_name = make_safe_filename(bm.title, is_directory=False)
        final_name = base_name + ".url"

        try:
            filepath = safe_path_join(directory, final_name,
                                    is_directory=False,
                                    preserve_url_extension=True)

            logger.debug(f"Creating bookmark file:")
            logger.debug(f"  - Original title: '{bm.title}'")
            logger.debug(f"  - URL: {bm.url}")
            logger.debug(f"  - Final path: {filepath}")

            safe_path = make_long_path_safe(filepath)
            with open(safe_path, "w", encoding="utf-8") as file:
                file.write(f"; TITLE: {bm.title}\n")
                file.write(f"; ADD_DATE: {bm.add_date}\n")
                file.write(f"; LAST_MODIFIED: {bm.last_modified}\n")
                file.write(f"; ICON: {bm.icon}\n")
                file.write(f"; ORDER: {bm.order}\n")  # Store order
                file.write("[InternetShortcut]\n")
                file.write(f"URL={bm.url}\n")

            set_file_times(filepath, bm.add_date, bm.last_modified)
            success_counts["bookmarks"] += 1

        except Exception as e:
            logger.error(f"Error processing bookmark '{bm.title}': {e}")
            # Collect failed bookmark info
            failed_items.append({
                "type": "bookmark",
                "title": bm.title,
                "url": bm.url,
                "path": str(directory / final_name) if 'final_name' in locals() else "unknown",
                "error": str(e),
                "add_date": bm.add_date,
                "last_modified": bm.last_modified,
                "icon": bm.icon,
                "order": bm.order,
                "folder_path": str(directory)
            })
            continue

    for sub in sorted(folder.subfolders, key=lambda x: x.order):  # Sort by order
        logger.debug(f"Processing subfolder: '{sub.name}'")
        try:
            sub_dir = safe_path_join(directory, sub.name,
                                    is_directory=True,
                                    preserve_url_extension=False)
            create_urls_from_folder(sub, sub_dir, failed_items, success_counts, never_attempted)
        except Exception as e:
            logger.error(f"Error processing subfolder '{sub.name}': {e}")
            # Collect failed subfolder info
            failed_items.append({
                "type": "subfolder",
                "name": sub.name,
                "path": str(directory / sub.name),
                "error": str(e),
                "add_date": sub.add_date,
                "last_modified": sub.last_modified,
                "order": sub.order,
                "parent_path": str(directory)
            })

            # Collect all items that will never be attempted due to this subfolder failure
            collect_never_attempted_items(sub, directory / sub.name, str(e), never_attempted)

    try:
        set_directory_times(directory, folder.add_date, folder.last_modified)
    except Exception as e:
        logger.error(f"Error setting directory times for {directory}: {e}")

    return failed_items, success_counts, never_attempted

def save_failed_items(failed_items: list, output_directory: Path, base_name: str, success_counts=None, total_counts=None, never_attempted=None):
    """Save comprehensive processing results to a JSON file."""
    if success_counts is None:
        success_counts = {"bookmarks": 0, "folders": 0}
    if total_counts is None:
        total_counts = {"bookmarks": 0, "folders": 0}
    if never_attempted is None:
        never_attempted = []

    # Create failed items filename
    failed_items_file = output_directory / f"{base_name}_failed_items.json"

    # Prepare summary data
    total_items = total_counts["bookmarks"] + total_counts["folders"]
    successful_items = success_counts["bookmarks"] + success_counts["folders"]

    # Count never attempted items by type
    never_attempted_by_type = {"bookmarks": 0, "folders": 0}
    for item in never_attempted:
        item_type = item.get("type", "unknown")
        if item_type in never_attempted_by_type:
            never_attempted_by_type[item_type] += 1

    never_attempted_total = never_attempted_by_type["bookmarks"] + never_attempted_by_type["folders"]

    summary = {
        "processing_timestamp": datetime.now().isoformat(),
        "total_items_in_source": total_items,
        "total_by_type": {
            "bookmarks": total_counts["bookmarks"],
            "folders": total_counts["folders"]
        },
        "total_successful_items": successful_items,
        "successful_by_type": {
            "bookmarks": success_counts["bookmarks"],
            "folders": success_counts["folders"]
        },
        "total_failed_items": len(failed_items),
        "failed_by_type": {},
        "failed_by_error": {},
        "total_never_attempted": never_attempted_total,
        "never_attempted_by_type": never_attempted_by_type,
        "failed_items": failed_items,
        "never_attempted_items": never_attempted
    }

    # Count by type
    for item in failed_items:
        item_type = item.get("type", "unknown")
        summary["failed_by_type"][item_type] = summary["failed_by_type"].get(item_type, 0) + 1

    # Count by error type
    for item in failed_items:
        error = item.get("error", "unknown")
        # Extract error type (e.g., "WinError 206" from "[WinError 206] The filename...")
        error_type = error.split("]")[0] + "]" if "[" in error and "]" in error else error.split(":")[0]
        summary["failed_by_error"][error_type] = summary["failed_by_error"].get(error_type, 0) + 1

    try:
        ensure_directory(output_directory)
        safe_path = make_long_path_safe(failed_items_file)
        with open(safe_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved processing results to: {failed_items_file}")
        console.print(f"\n📄 Processing results saved to: [cyan]{failed_items_file.name}[/cyan]")
        console.print(f"   Total items in source: [blue]{total_items}[/blue] ({total_counts['bookmarks']} bookmarks, {total_counts['folders']} folders)")
        console.print(f"   Successfully processed: [green]{successful_items}[/green] ({success_counts['bookmarks']} bookmarks, {success_counts['folders']} folders)")
        console.print(f"   Attempted but failed: [red]{len(failed_items)}[/red]")

        # Show failed summary by type
        for item_type, count in summary["failed_by_type"].items():
            console.print(f"     - {item_type}: {count}")

        console.print(f"   Never attempted: [yellow]{never_attempted_total}[/yellow] ({never_attempted_by_type['bookmarks']} bookmarks, {never_attempted_by_type['folders']} folders)")
        console.print(f"     - Due to parent directory creation failures")

        # Verification
        accounted_total = successful_items + len(failed_items) + never_attempted_total
        console.print(f"   [dim]Verification: {accounted_total}/{total_items} items accounted for[/dim]")

        # Show most common errors
        if summary["failed_by_error"]:
            most_common_error = max(summary["failed_by_error"].items(), key=lambda x: x[1])
            console.print(f"   Most common error: [yellow]{most_common_error[0]}[/yellow] ({most_common_error[1]} items)")

    except Exception as e:
        logger.error(f"Error saving failed items: {e}")
        console.print(f"✗ Error saving failed items: {e}", style="bold red")

def set_file_times(filepath: Path, add_date: int, last_modified: int):
    try:
        safe_path = make_long_path_safe(filepath)
        mod_time = time.mktime(time.localtime(last_modified))
        creation_time = time.mktime(time.localtime(add_date))
        os.utime(str(safe_path), (mod_time, mod_time))
        win32_setctime.setctime(str(safe_path), creation_time)
    except Exception as e:
        logger.error(f"Error setting timestamps for {filepath}: {e}")

def set_directory_times(dirpath: Path, add_date: int, last_modified: int):
    try:
        safe_path = make_long_path_safe(dirpath)
        mod_time = time.mktime(time.localtime(last_modified))
        os.utime(str(safe_path), (mod_time, mod_time))
    except Exception as e:
        logger.error(f"Failed to set timestamps for directory {dirpath}: {e}")


# =======================================================
# 8) Processors
# =======================================================
def process_html(input_path: Path, output_paths: dict):
    """Process HTML file with error collection."""
    failed_items = []
    processing_errors = []

    try:
        html_data = bookmarks_html_reader(input_path)
        root_folder = folder_from_html(html_data)
        out_dict = folder_to_dict(root_folder)
        bookmarks_json_writer(out_dict, output_paths['json'])

        # Count total items in the source
        total_counts = count_total_items(root_folder)

        # Collect failed items, success counts, and never attempted items during URL creation
        failed_items, success_counts, never_attempted = create_urls_from_folder(root_folder, output_paths['urls'])

        logger.info(f"Processed HTML file: {input_path}")

        # Save comprehensive processing results
        save_failed_items(failed_items, output_paths['urls'].parent, input_path.stem, success_counts, total_counts, never_attempted)

        if failed_items:
            logger.warning(f"Found {len(failed_items)} failed items")
        if never_attempted:
            logger.warning(f"Found {len(never_attempted)} never attempted items")

        return {
            "success": True,
            "failed_count": len(failed_items),
            "success_count": success_counts["bookmarks"] + success_counts["folders"],
            "success_counts": success_counts,
            "total_count": total_counts["bookmarks"] + total_counts["folders"],
            "total_counts": total_counts,
            "never_attempted_count": len(never_attempted),
            "never_attempted": never_attempted
        }

    except Exception as e:
        error_msg = f"Failed to process HTML: {e}"
        logger.error(error_msg)
        processing_errors.append({
            "type": "processing_error",
            "file": str(input_path),
            "error": str(e),
            "stage": "html_processing"
        })

        # Save processing errors
        if processing_errors:
            save_failed_items(processing_errors, output_paths['urls'].parent, f"{input_path.stem}_processing_errors")

        return {
            "success": False,
            "error": str(e),
            "failed_count": len(failed_items),
            "success_count": 0,
            "success_counts": {"bookmarks": 0, "folders": 0},
            "total_count": 0,
            "total_counts": {"bookmarks": 0, "folders": 0},
            "never_attempted_count": 0,
            "never_attempted": []
        }

def process_json(input_path: Path, output_paths: dict):
    """Process JSON file with error collection."""
    failed_items = []
    processing_errors = []

    try:
        obj = bookmarks_json_reader(input_path)
        root_folder = folder_from_json(obj)
        out_dict = folder_to_dict(root_folder)
        bookmarks_json_writer(out_dict, output_paths['json'])

        # Count total items in the source
        total_counts = count_total_items(root_folder)

        # Collect failed items, success counts, and never attempted items during URL creation
        failed_items, success_counts, never_attempted = create_urls_from_folder(root_folder, output_paths['urls'])

        logger.info(f"Processed JSON file: {input_path}")

        # Save comprehensive processing results
        save_failed_items(failed_items, output_paths['urls'].parent, input_path.stem, success_counts, total_counts, never_attempted)

        if failed_items:
            logger.warning(f"Found {len(failed_items)} failed items")
        if never_attempted:
            logger.warning(f"Found {len(never_attempted)} never attempted items")

        return {
            "success": True,
            "failed_count": len(failed_items),
            "success_count": success_counts["bookmarks"] + success_counts["folders"],
            "success_counts": success_counts,
            "total_count": total_counts["bookmarks"] + total_counts["folders"],
            "total_counts": total_counts,
            "never_attempted_count": len(never_attempted),
            "never_attempted": never_attempted
        }

    except Exception as e:
        error_msg = f"Failed to process JSON: {e}"
        logger.error(error_msg)
        processing_errors.append({
            "type": "processing_error",
            "file": str(input_path),
            "error": str(e),
            "stage": "json_processing"
        })

        # Save processing errors
        if processing_errors:
            save_failed_items(processing_errors, output_paths['urls'].parent, f"{input_path.stem}_processing_errors")

        return {
            "success": False,
            "error": str(e),
            "failed_count": len(failed_items),
            "success_count": 0,
            "success_counts": {"bookmarks": 0, "folders": 0},
            "total_count": 0,
            "total_counts": {"bookmarks": 0, "folders": 0},
            "never_attempted_count": 0,
            "never_attempted": []
        }

def process_urls(input_path: Path, output_paths: dict):
    """Process URLs directory with error collection."""
    failed_items = []
    processing_errors = []

    try:
        root_folder = bookmarks_urls_parser(input_path)
        html_data = create_html_from_folder(root_folder)
        bookmarks_html_writer(html_data, output_paths['html'])
        out_dict = folder_to_dict(root_folder)
        bookmarks_json_writer(out_dict, output_paths['json'])

        logger.info(f"Processed URLs directory: {input_path}")
        return {
            "success": True,
            "failed_count": 0,
            "success_count": 0,  # URLs processing doesn't create individual bookmarks
            "success_counts": {"bookmarks": 0, "folders": 0},
            "total_count": 0,
            "total_counts": {"bookmarks": 0, "folders": 0},
            "never_attempted_count": 0,
            "never_attempted": []
        }

    except Exception as e:
        error_msg = f"Failed to process URLs directory: {e}"
        logger.error(error_msg)
        processing_errors.append({
            "type": "processing_error",
            "file": str(input_path),
            "error": str(e),
            "stage": "urls_processing"
        })

        # Save processing errors
        if processing_errors:
            save_failed_items(processing_errors, output_paths['html'].parent, f"{input_path.stem}_processing_errors")

        return {
            "success": False,
            "error": str(e),
            "failed_count": 0,
            "success_count": 0,
            "success_counts": {"bookmarks": 0, "folders": 0},
            "total_count": 0,
            "total_counts": {"bookmarks": 0, "folders": 0},
            "never_attempted_count": 0,
            "never_attempted": []
        }

def process_inputs(input_path: Path, output_subfolder: Path, mode: str = None):
    ensure_directory(output_subfolder)

    # For .html / .json
    html_file = safe_path_join(output_subfolder,
                               f"{input_path.stem}.html",
                               is_directory=False)
    json_file = safe_path_join(output_subfolder,
                               f"{input_path.stem}.json",
                               is_directory=False)
    urls_dir = safe_path_join(output_subfolder,
                              f"{input_path.stem}.URLS",
                              is_directory=True)

    output_paths = {
        'html': html_file,
        'json': json_file,
        'urls': urls_dir,
    }

    try:
        # Use mode parameter if provided, otherwise auto-detect
        if mode:
            if mode == 'to_structure':
                if input_path.suffix.lower() == ".html":
                    result = process_html(input_path, output_paths)
                elif input_path.suffix.lower() == ".json":
                    result = process_json(input_path, output_paths)
                else:
                    raise ValueError(f"Cannot convert {input_path.suffix} to structure")
            elif mode == 'to_html':
                if input_path.is_dir():
                    result = process_urls(input_path, output_paths)
                else:
                    raise ValueError(f"Cannot convert file to HTML (need directory input)")
            elif mode == 'to_json':
                if input_path.is_dir():
                    # For to_json mode, we need to process URLs and then convert HTML to JSON
                    result = process_urls(input_path, output_paths)
                    if result["success"] and html_file.exists():
                        # Convert the generated HTML to JSON
                        json_result = process_html(html_file, output_paths)
                        # Merge results, prioritizing JSON result but keeping URL processing info
                        result.update(json_result)
                else:
                    raise ValueError(f"Cannot convert file to JSON (need directory input)")
            else:
                raise ValueError(f"Unknown mode: {mode}")
        else:
            # Auto-detect mode (legacy behavior)
            if input_path.suffix.lower() == ".html":
                result = process_html(input_path, output_paths)
            elif input_path.suffix.lower() == ".json":
                result = process_json(input_path, output_paths)
            elif input_path.is_dir():
                result = process_urls(input_path, output_paths)
            else:
                raise ValueError(f"Unsupported input type: {input_path.suffix}")

        if result["success"]:
            logger.info(f"Processing completed for {input_path}")
            logger.info(f"Output saved to: {output_subfolder}")

            success_count = result.get("success_count", 0)
            failed_count = result["failed_count"]
            total_count = result.get("total_count", 0)
            never_attempted_count = result.get("never_attempted_count", 0)

            if failed_count > 0 or never_attempted_count > 0:
                console.print(f"\n[yellow]⚠️  Processing completed: {success_count}/{total_count} successful, {failed_count} failed, {never_attempted_count} never attempted[/yellow]")
                console.print("   Check the failed_items.json file for manual processing")
            else:
                console.print(f"\n[green]✅ Processing completed successfully! {success_count}/{total_count} items processed[/green]")
        else:
            console.print(f"\n[red]❌ Processing failed: {result.get('error', 'Unknown error')}[/red]")

        return result

    except Exception as e:
        error_msg = f"Failed to process {input_path}: {e}"
        logger.error(error_msg)
        console.print(f"\n[red]❌ {error_msg}[/red]")
        return {
            "success": False,
            "error": str(e),
            "failed_count": 0,
            "success_count": 0,
            "success_counts": {"bookmarks": 0, "folders": 0},
            "total_count": 0,
            "total_counts": {"bookmarks": 0, "folders": 0},
            "never_attempted_count": 0,
            "never_attempted": []
        }


# =======================================================
# 9) CLI
# =======================================================
def parse_arguments():
    parser = argparse.ArgumentParser(description="Bookmarks Parser + Filesystem Generator")
    parser.add_argument('-i', '--input', type=str,
                        default=config.DEFAULT_INPUT_FILE,
                        help="Path to a bookmark file (HTML/JSON) or directory of URLs")
    parser.add_argument('-op', '--output_path', type=str,
                        default=config.DEFAULT_OUTPUT_PATH,
                        help="Directory for output")
    parser.add_argument('--prompt', action='store_true',
                        help="Prompt for missing arguments")
    parser.add_argument('--verbose', '-v', action='store_true',
                        help="Enable detailed debug logging")

    args = parser.parse_args()

    # Set logging level based on verbose flag
    if args.verbose:
        config.set_log_level("DEBUG")
    else:
        config.set_log_level(config.DEFAULT_LOG_LEVEL)

    if args.prompt:
        prompt_user_inputs(args)
    else:
        validate_non_prompt_inputs(args, parser)
    return args

def interactive_session():
    """Enhanced interactive CLI session with file selection menus."""
    working_directory = Path.cwd()

    while True:
        clear_console()
        display_header()

        console.print("\n===== Bookmark Folderizer =====", style="bold blue")
        console.print("What would you like to do?", style="cyan")
        console.print("1. Process single bookmark file")
        console.print("2. Process multiple files (batch)")
        console.print("3. Browse and select from available files")
        console.print("4. View processing history")
        console.print("5. Exit")

        choice = input("\n> ").strip()

        if choice == "1":
            single_file_processing(working_directory)
        elif choice == "2":
            batch_file_processing(working_directory)
        elif choice == "3":
            browse_and_select_files(working_directory)
        elif choice == "4":
            view_processing_history(working_directory)
        elif choice == "5":
            console.print("\n📁 Thanks for using Bookmark Folderizer! Goodbye!", style="bold green")
            break
        else:
            console.print("\n✗ Invalid choice. Please try again.", style="bold red")
            input("\nPress Enter to continue...")

def clear_console():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_header():
    """Display application header."""
    console.print("╔══════════════════════════════════════════════════════════════╗", style="blue")
    console.print("║                    BOOKMARK FOLDERIZER                      ║", style="bold blue")
    console.print("║              HTML/JSON ↔ Filesystem Converter               ║", style="blue")
    console.print("║          • HTML/JSON → .URLS Structure                      ║", style="dim blue")
    console.print("║          • .URLS Structure → HTML/JSON                      ║", style="dim blue")
    console.print("╚══════════════════════════════════════════════════════════════╝", style="blue")

def prompt_user_inputs(args):
    """Legacy prompt function - now redirects to interactive session."""
    interactive_session()
    sys.exit(0)

def single_file_processing(working_directory):
    """Handle single file processing with interactive prompts."""
    console.print("\n--- Single File Processing ---", style="bold blue")

    # Get input file with smart suggestions
    input_file = get_input_file_interactive(working_directory)
    if not input_file:
        return

    # Determine available conversion modes and let user choose
    mode = get_conversion_mode_interactive(input_file)
    if not mode:
        return

    # Get output directory
    default_output = working_directory / "out"
    output_path = input(f"Output directory [{default_output}]: ").strip() or str(default_output)

    # Process the file
    try:
        args_mock = type('Args', (), {
            'input': str(input_file),
            'output_path': output_path,
            'mode': mode
        })()

        console.print(f"\n📁 Processing: [bold cyan]{input_file.name}[/bold cyan]")
        console.print(f"📋 Mode: [bold yellow]{mode}[/bold yellow]")
        result = process_single_file(args_mock)

        if result and result.get("success"):
            success_count = result.get("success_count", 0)
            failed_count = result.get("failed_count", 0)
            total_count = result.get("total_count", 0)
            never_attempted_count = result.get("never_attempted_count", 0)

            if failed_count > 0 or never_attempted_count > 0:
                console.print(f"\n[yellow]⚠️  Processing completed: {success_count}/{total_count} successful, {failed_count} failed, {never_attempted_count} never attempted[/yellow]")
                console.print("   Check the failed_items.json file for details")
            else:
                console.print(f"\n[bold green]✓ Processing completed successfully! {success_count}/{total_count} items processed[/bold green]")
        else:
            console.print(f"\n[red]❌ Processing failed[/red]")

    except Exception as e:
        console.print(f"\n✗ Error processing file: {e}", style="bold red")
        logger.error(f"Error processing file: {e}")

    input("\nPress Enter to continue...")

def batch_file_processing(working_directory):
    """Handle batch processing of multiple files."""
    console.print("\n--- Batch File Processing ---", style="bold blue")

    # Find all bookmark files
    bookmark_files = find_bookmark_files(working_directory)

    if not bookmark_files:
        console.print("✗ No bookmark files found in current directory", style="bold red")
        input("\nPress Enter to continue...")
        return

    console.print(f"\nFound {len(bookmark_files)} bookmark files:", style="cyan")
    selected_files = []

    for i, file_path in enumerate(bookmark_files, 1):
        safe_file_path = make_long_path_safe(file_path)
        if safe_file_path.is_file():
            size = safe_file_path.stat().st_size
            if file_path.suffix.lower() == ".html":
                file_type = "HTML → Structure"
            elif file_path.suffix.lower() == ".json":
                file_type = "JSON → Structure"
            else:
                file_type = "Unknown"
            console.print(f"  {i:2d}. {file_path.name} ({file_type}, {size} bytes)")
        else:
            # Directory (likely .URLS)
            item_count = len(list(safe_file_path.iterdir()))
            console.print(f"  {i:2d}. {file_path.name}/ (Directory → HTML/JSON, {item_count} items)")

    console.print("\nSelect files to process:")
    console.print("  - Enter numbers separated by spaces (e.g., '1 3 5')")
    console.print("  - Enter 'all' to process all files")
    console.print("  - Enter 'none' to cancel")

    choice = input("\n> ").strip().lower()

    if choice == "none":
        return
    elif choice == "all":
        selected_files = bookmark_files
    else:
        try:
            indices = [int(x) for x in choice.split()]
            selected_files = [bookmark_files[i-1] for i in indices if 1 <= i <= len(bookmark_files)]
        except (ValueError, IndexError):
            console.print("✗ Invalid selection", style="bold red")
            input("\nPress Enter to continue...")
            return

    if not selected_files:
        console.print("✗ No files selected", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Get output directory
    default_output = working_directory / "out"
    output_path = input(f"Output directory [{default_output}]: ").strip() or str(default_output)

    # Process selected files
    console.print(f"\n📁 Processing {len(selected_files)} files...", style="bold green")

    total_failed = 0
    total_successful = 0
    total_never_attempted = 0
    successful_files = 0
    failed_files = []

    for i, file_path in enumerate(selected_files, 1):
        try:
            args_mock = type('Args', (), {
                'input': str(file_path),
                'output_path': output_path,
                'mode': determine_mode(file_path)
            })()

            console.print(f"[{i}/{len(selected_files)}] Processing: [cyan]{file_path.name}[/cyan]")
            result = process_single_file(args_mock)

            if result and result.get("success"):
                successful_files += 1
                success_count = result.get("success_count", 0)
                failed_count = result.get("failed_count", 0)
                total_count = result.get("total_count", 0)
                never_attempted_count = result.get("never_attempted_count", 0)
                total_successful += success_count
                total_failed += failed_count
                total_never_attempted += never_attempted_count

                if failed_count > 0 or never_attempted_count > 0:
                    console.print(f"   ⚠️  {success_count}/{total_count} successful, {failed_count} failed, {never_attempted_count} never attempted")
                else:
                    console.print(f"   ✅ Completed successfully ({success_count}/{total_count} items)")
            else:
                failed_files.append(file_path.name)
                console.print(f"   ❌ File processing failed")

        except Exception as e:
            failed_files.append(file_path.name)
            console.print(f"   ✗ Error processing {file_path.name}: {e}", style="bold red")
            logger.error(f"Error processing {file_path.name}: {e}")

    # Summary
    console.print(f"\n[bold blue]📊 Batch Processing Summary:[/bold blue]")
    console.print(f"   Files processed successfully: [green]{successful_files}[/green]")
    console.print(f"   Total successful items: [green]{total_successful}[/green]")
    if failed_files:
        console.print(f"   Files that failed completely: [red]{len(failed_files)}[/red]")
        console.print(f"   Failed files: {', '.join(failed_files)}")
    if total_failed > 0:
        console.print(f"   Total failed items: [red]{total_failed}[/red]")
    if total_never_attempted > 0:
        console.print(f"   Total never attempted items: [yellow]{total_never_attempted}[/yellow]")
    if total_failed > 0 or total_never_attempted > 0:
        console.print(f"   Check failed_items.json files for manual processing")

    if successful_files == len(selected_files) and total_failed == 0:
        console.print(f"\n[bold green]✓ All files processed successfully![/bold green]")
    elif successful_files > 0:
        console.print(f"\n[bold yellow]⚠️  Batch processing completed with some issues[/bold yellow]")
    else:
        console.print(f"\n[bold red]❌ Batch processing failed for all files[/bold red]")
    input("\nPress Enter to continue...")

def validate_non_prompt_inputs(args, parser):
    if not args.input or not args.output_path:
        parser.error("Arguments required: -i/--input, -op/--output_path")

    input_path = Path(args.input)
    args.mode = determine_mode(input_path)
    if not args.mode:
        console.print("[bold red]Error: Unsupported input type or path.[/bold red]")
        sys.exit(1)

def browse_and_select_files(working_directory):
    """Browse and select files from available bookmark files."""
    console.print("\n--- Browse and Select Files ---", style="bold blue")

    # Find bookmark files in common locations
    search_paths = [
        working_directory,
        working_directory / "in",
        working_directory / "input",
        working_directory / "bookmarks",
    ]

    all_files = []
    for search_path in search_paths:
        if search_path.exists():
            all_files.extend(find_bookmark_files(search_path))

    # Remove duplicates and sort
    unique_files = list(set(all_files))
    unique_files.sort(key=lambda x: (x.parent, x.name))

    if not unique_files:
        console.print("✗ No bookmark files found in search locations", style="bold red")
        console.print("Search locations:", style="dim")
        for path in search_paths:
            console.print(f"  - {path}", style="dim")
        input("\nPress Enter to continue...")
        return

    console.print(f"\nFound {len(unique_files)} bookmark files:", style="cyan")

    for i, file_path in enumerate(unique_files, 1):
        size = file_path.stat().st_size
        file_type = "HTML" if file_path.suffix.lower() == ".html" else "JSON" if file_path.suffix.lower() == ".json" else "DIR"
        rel_path = file_path.relative_to(working_directory) if file_path.is_relative_to(working_directory) else file_path
        console.print(f"  {i:2d}. {file_path.name} ({file_type}, {size} bytes) - {rel_path.parent}")

    choice = input(f"\nSelect file (1-{len(unique_files)}) or Enter to go back: ").strip()

    if choice.isdigit() and 1 <= int(choice) <= len(unique_files):
        selected_file = unique_files[int(choice) - 1]

        # Get output directory
        default_output = working_directory / "out"
        output_path = input(f"Output directory [{default_output}]: ").strip() or str(default_output)

        # Process the selected file
        try:
            args_mock = type('Args', (), {
                'input': str(selected_file),
                'output_path': output_path,
                'mode': determine_mode(selected_file)
            })()

            console.print(f"\n📁 Processing: [bold cyan]{selected_file.name}[/bold cyan]")
            result = process_single_file(args_mock)

            if result and result.get("success"):
                success_count = result.get("success_count", 0)
                failed_count = result.get("failed_count", 0)
                total_count = result.get("total_count", 0)
                never_attempted_count = result.get("never_attempted_count", 0)

                if failed_count > 0 or never_attempted_count > 0:
                    console.print(f"\n[yellow]⚠️  Processing completed: {success_count}/{total_count} successful, {failed_count} failed, {never_attempted_count} never attempted[/yellow]")
                    console.print("   Check the failed_items.json file for details")
                else:
                    console.print(f"\n[bold green]✓ Processing completed successfully! {success_count}/{total_count} items processed[/bold green]")
            else:
                console.print(f"\n[red]❌ Processing failed[/red]")

        except Exception as e:
            console.print(f"\n✗ Error processing file: {e}", style="bold red")
            logger.error(f"Error processing file: {e}")

        input("\nPress Enter to continue...")

def view_processing_history(working_directory):
    """View recent processing history."""
    console.print("\n--- Processing History ---", style="bold blue")

    # Look for output directories
    output_dirs = []
    search_paths = [working_directory / "out", working_directory / "output"]

    for search_path in search_paths:
        if search_path.exists():
            output_dirs.extend([d for d in search_path.iterdir() if d.is_dir()])

    if not output_dirs:
        console.print("✗ No processing history found", style="bold red")
        input("\nPress Enter to continue...")
        return

    # Sort by modification time (newest first)
    output_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    console.print(f"\nFound {len(output_dirs)} processed outputs:", style="cyan")

    for i, dir_path in enumerate(output_dirs[:10], 1):  # Show last 10
        modified = datetime.fromtimestamp(dir_path.stat().st_mtime).strftime("%Y-%m-%d %H:%M")
        file_count = len(list(dir_path.iterdir()))
        console.print(f"  {i:2d}. {dir_path.name} ({file_count} files, {modified})")

    if len(output_dirs) > 10:
        console.print(f"  ... and {len(output_dirs) - 10} more directories")

    choice = input(f"\nSelect directory to explore (1-{min(10, len(output_dirs))}) or Enter to go back: ").strip()

    if choice.isdigit() and 1 <= int(choice) <= min(10, len(output_dirs)):
        selected_dir = output_dirs[int(choice) - 1]
        explore_output_directory(selected_dir)

    input("\nPress Enter to continue...")

def find_bookmark_files(directory: Path) -> list[Path]:
    """Find all bookmark files (HTML, JSON) and URL directories in a directory."""
    bookmark_files = []

    if not directory.exists():
        return bookmark_files

    # Find HTML and JSON files
    for pattern in ["*.html", "*.json"]:
        bookmark_files.extend(directory.glob(pattern))

    # Find URL directories (directories ending with .URLS)
    for item in directory.iterdir():
        if item.is_dir() and item.name.endswith('.URLS'):
            bookmark_files.append(item)

    return sorted(bookmark_files, key=lambda x: x.name.lower())

def get_conversion_mode_interactive(input_path: Path) -> Optional[str]:
    """Get conversion mode with interactive selection."""
    console.print(f"\n--- Conversion Mode Selection ---", style="bold blue")
    console.print(f"Input: [cyan]{input_path.name}[/cyan]")

    available_modes = []

    if input_path.is_file():
        if input_path.suffix.lower() in ['.html', '.json']:
            available_modes.append(('to_structure', f'Convert {input_path.suffix.upper()} to folder structure (.URLS)'))
    elif input_path.is_dir():
        if input_path.name.endswith('.URLS'):
            available_modes.append(('to_html', 'Convert folder structure to HTML bookmarks'))
            available_modes.append(('to_json', 'Convert folder structure to JSON bookmarks'))

    if not available_modes:
        console.print(f"[red]❌ No conversion modes available for this input type[/red]")
        input("\nPress Enter to continue...")
        return None

    console.print("\nAvailable conversion modes:")
    for i, (mode, description) in enumerate(available_modes, 1):
        console.print(f"  {i}. {description}")

    if len(available_modes) == 1:
        console.print(f"\n[green]✓ Auto-selecting the only available mode: {available_modes[0][1]}[/green]")
        return available_modes[0][0]

    while True:
        try:
            choice = input(f"\nSelect conversion mode (1-{len(available_modes)}): ").strip()
            if not choice:
                return None

            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(available_modes):
                selected_mode, description = available_modes[choice_idx]
                console.print(f"[green]✓ Selected: {description}[/green]")
                return selected_mode
            else:
                console.print(f"[red]❌ Invalid choice. Please enter 1-{len(available_modes)}[/red]")
        except ValueError:
            console.print(f"[red]❌ Invalid input. Please enter a number 1-{len(available_modes)}[/red]")
        except KeyboardInterrupt:
            return None

def get_input_file_interactive(working_directory: Path) -> Optional[Path]:
    """Get input file with smart suggestions."""
    # Find available bookmark files
    bookmark_files = find_bookmark_files(working_directory)
    in_dir = working_directory / "in"
    if in_dir.exists():
        bookmark_files.extend(find_bookmark_files(in_dir))

    # Remove duplicates and sort
    unique_files = list(set(bookmark_files))
    unique_files.sort(key=lambda x: x.name.lower())

    if unique_files:
        console.print("\nAvailable bookmark files:", style="cyan")
        for i, file_path in enumerate(unique_files, 1):
            file_type = "HTML" if file_path.suffix.lower() == ".html" else "JSON" if file_path.suffix.lower() == ".json" else "DIR"
            console.print(f"  {i}. {file_path.name} ({file_type})")

        choice = input(f"\nSelect file (1-{len(unique_files)}) or enter custom path: ").strip()

        try:
            if choice.isdigit() and 1 <= int(choice) <= len(unique_files):
                return unique_files[int(choice) - 1]
            else:
                custom_path = Path(choice)
                if custom_path.exists():
                    return custom_path
                else:
                    console.print(f"✗ File not found: {choice}", style="bold red")
                    return None
        except (ValueError, IndexError):
            console.print("✗ Invalid selection", style="bold red")
            return None
    else:
        # No files found, ask for manual input
        file_path = input("Enter bookmark file path: ").strip()
        if file_path and Path(file_path).exists():
            return Path(file_path)
        else:
            console.print("✗ File not found", style="bold red")
            return None

def explore_output_directory(directory: Path):
    """Explore contents of an output directory."""
    console.print(f"\n--- Exploring: {directory.name} ---", style="bold blue")
    console.print(f"📁 Path: [dim]{directory}[/dim]")

    files = list(directory.iterdir())
    files.sort(key=lambda x: (x.is_file(), x.name.lower()))

    urls_directories = []

    for file_path in files:
        if file_path.is_file():
            safe_file_path = make_long_path_safe(file_path)
            size = safe_file_path.stat().st_size
            console.print(f"📄 {file_path.name} ({size} bytes)")
        else:
            safe_file_path = make_long_path_safe(file_path)
            item_count = len(list(safe_file_path.iterdir()))
            console.print(f"📁 {file_path.name}/ ({item_count} items)")

            # Track .URLS directories for conversion options
            if file_path.name.endswith('.URLS'):
                urls_directories.append(file_path)

    # Offer conversion options for .URLS directories
    if urls_directories:
        console.print(f"\n🔄 Conversion Options:", style="bold green")
        console.print("Convert .URLS structure to:")
        console.print("  h. HTML bookmarks")
        console.print("  j. JSON bookmarks")
        console.print("  Enter. Go back")

        choice = input("\n> ").strip().lower()

        if choice in ['h', 'j']:
            # Select which .URLS directory to convert (if multiple)
            if len(urls_directories) == 1:
                selected_urls_dir = urls_directories[0]
            else:
                console.print(f"\nSelect .URLS directory to convert:")
                for i, urls_dir in enumerate(urls_directories, 1):
                    console.print(f"  {i}. {urls_dir.name}")

                try:
                    dir_choice = input(f"\nSelect directory (1-{len(urls_directories)}): ").strip()
                    if dir_choice.isdigit() and 1 <= int(dir_choice) <= len(urls_directories):
                        selected_urls_dir = urls_directories[int(dir_choice) - 1]
                    else:
                        console.print("❌ Invalid selection", style="bold red")
                        input("\nPress Enter to continue...")
                        return
                except (ValueError, IndexError):
                    console.print("❌ Invalid selection", style="bold red")
                    input("\nPress Enter to continue...")
                    return

            # Perform the conversion
            mode = 'to_html' if choice == 'h' else 'to_json'
            output_format = 'HTML' if choice == 'h' else 'JSON'

            console.print(f"\n🔄 Converting {selected_urls_dir.name} to {output_format}...")

            try:
                # For conversion from processing history, put output directly in the same directory
                # Create output file paths directly in the current directory
                output_file_name = f"{selected_urls_dir.stem}.{'html' if choice == 'h' else 'json'}"
                output_file_path = directory / output_file_name

                # Create output paths dict for process_inputs
                output_paths = {
                    'html': directory / f"{selected_urls_dir.stem}.html",
                    'json': directory / f"{selected_urls_dir.stem}.json",
                    'urls': selected_urls_dir  # Use existing .URLS directory
                }

                # Call process_inputs directly to avoid creating a new subdirectory
                result = process_inputs(selected_urls_dir, directory, mode)

                if result and result.get("success"):
                    console.print(f"✅ Conversion to {output_format} completed successfully!", style="bold green")
                    console.print(f"📄 Output saved as: [cyan]{output_file_name}[/cyan]", style="green")
                    console.print(f"📁 Full path: [dim]{output_file_path}[/dim]", style="green")
                else:
                    console.print(f"❌ Conversion failed", style="bold red")

            except Exception as e:
                console.print(f"❌ Error during conversion: {e}", style="bold red")
                logger.error(f"Error during conversion: {e}")

            input("\nPress Enter to continue...")
        # If choice is Enter or anything else, just continue (go back)

def process_single_file(args):
    """Process a single file with the given arguments."""
    input_path = Path(args.input)
    output_path = Path(args.output_path)

    subfolder_segment = f"{input_path.stem}_{args.mode}"
    subfolder_segment = make_safe_dirname(subfolder_segment)
    output_subfolder = safe_path_join(output_path, subfolder_segment, is_directory=True)

    return process_inputs(input_path, output_subfolder, args.mode)

def determine_mode(input_path: Path) -> Optional[str]:
    if input_path.is_file():
        if input_path.suffix.lower() in ['.html', '.json']:
            return 'to_structure'
        return None
    elif input_path.is_dir():
        return 'to_html'
    return None

def clear_console():
    cmd = "cls" if platform.system() == "Windows" else "clear"
    os.system(cmd)

def display_summary(args):
    table = Table(title="Configuration Summary", show_header=True,
                  header_style="bold magenta", box=box.ASCII)
    table.add_column("Parameter", style="dim", width=30)
    table.add_column("Value", style="bold cyan")
    table.add_row("Input path", str(args.input))
    table.add_row("Output directory path", str(args.output_path))
    table.add_row("Mode", str(args.mode))
    console.print(table)

def get_confirmation():
    return Confirm.ask("Proceed with the above configuration?", default=True)


# =======================================================
# 10) Main
# =======================================================
def main():
    try:
        while True:
            clear_console()
            args = parse_arguments()

            input_path = Path(args.input)
            if not input_path.exists():
                console.print(f"\n[bold red]Error: Input path '{input_path}' does not exist.[/bold red]\n")
                logger.error(f"Input path '{input_path}' does not exist")
                continue

            output_path = Path(args.output_path)
            subfolder_segment = f"{input_path.stem}_{args.mode}"
            subfolder_segment = make_safe_dirname(subfolder_segment)
            output_subfolder = safe_path_join(output_path, subfolder_segment, is_directory=True)

            display_summary(args)
            if get_confirmation():
                try:
                    console.print(f"\nProcessing input: [bold cyan]{input_path.name}[/bold cyan]")
                    process_inputs(input_path, output_subfolder, args.mode)
                    console.print(f"\n[bold green]Bookmarks processed and saved to:[/bold green]")
                    console.print(f"[green]{output_subfolder}[/green]\n")
                    break
                except Exception as e:
                    logger.exception("Failed to process bookmarks")
                    console.print(f"\n[bold red]Error: {str(e)}[/bold red]\n")
                    if not get_confirmation():
                        break
            else:
                console.print("\n[bold yellow]Operation cancelled by user.[/bold yellow]\n")
                logger.info("Operation cancelled by user")
                break

    except KeyboardInterrupt:
        console.print("\n[bold yellow]Operation interrupted by user.[/bold yellow]\n")
        logger.info("Operation interrupted by user")
    except Exception as e:
        logger.exception("Unexpected error occurred")
        console.print(f"\n[bold red]Unexpected error: {str(e)}[/bold red]\n")


if __name__ == "__main__":
    # If run with no arguments, start interactive session
    if len(sys.argv) == 1:
        interactive_session()
    else:
        # If run with --prompt flag, also start interactive session
        if "--prompt" in sys.argv:
            interactive_session()
        else:
            # Otherwise run with command line arguments
            main()
