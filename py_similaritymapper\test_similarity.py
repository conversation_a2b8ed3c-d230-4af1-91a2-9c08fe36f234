#!/usr/bin/env python3
"""
Test script for py_similaritymapper

Creates sample test files and runs similarity analysis to verify functionality.
"""

import tempfile
import shutil
from pathlib import Path
from src.main import SimilarityMapper


def create_test_files():
    """Create sample test files with known similarity patterns."""
    
    # Create temporary directory for test files
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # Sample content for testing
    base_content = [
        "# AI Prompt Template",
        "You are an expert assistant.",
        "Please help with the following task:",
        "",
        "## Instructions",
        "1. Analyze the input carefully",
        "2. Provide a detailed response", 
        "3. Include examples where helpful",
        "",
        "## Output Format",
        "Respond in markdown format.",
        "",
        "## Additional Notes",
        "Be concise but thorough."
    ]
    
    # Create test files with different similarity levels
    
    # File 1: Original
    with open(test_dir / "prompt_original.md", "w") as f:
        f.write("\n".join(base_content))
    
    # File 2: 95% similar (one line changed)
    similar_95 = base_content.copy()
    similar_95[1] = "You are a helpful AI assistant."
    with open(test_dir / "prompt_95_similar.md", "w") as f:
        f.write("\n".join(similar_95))
    
    # File 3: 85% similar (two lines changed)
    similar_85 = base_content.copy()
    similar_85[1] = "You are a helpful AI assistant."
    similar_85[6] = "2. Provide a comprehensive response"
    with open(test_dir / "prompt_85_similar.md", "w") as f:
        f.write("\n".join(similar_85))
    
    # File 4: 70% similar (more changes)
    similar_70 = base_content.copy()
    similar_70[1] = "You are a helpful AI assistant."
    similar_70[6] = "2. Provide a comprehensive response"
    similar_70[7] = "3. Include detailed examples"
    similar_70.append("4. Verify your answer")
    with open(test_dir / "prompt_70_similar.md", "w") as f:
        f.write("\n".join(similar_70))
    
    # File 5: Exact duplicate
    with open(test_dir / "prompt_duplicate.md", "w") as f:
        f.write("\n".join(base_content))
    
    # File 6: Completely different
    different_content = [
        "# Different Document",
        "This is completely different content.",
        "It has no similarity to the other files.",
        "Used for testing the similarity detection.",
        "",
        "## Random Section",
        "Some random text here.",
        "More random content.",
        "End of different file."
    ]
    with open(test_dir / "different_file.md", "w") as f:
        f.write("\n".join(different_content))
    
    print(f"Created test files in: {test_dir}")
    return test_dir


def run_test():
    """Run similarity analysis on test files."""
    
    print("=== py_similaritymapper Test ===")
    print()
    
    # Create test files
    test_dir = create_test_files()
    
    try:
        # Create output directory
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # Run similarity analysis
        mapper = SimilarityMapper(input_dir=test_dir, output_dir=output_dir)
        
        print("Running similarity analysis...")
        report_path = mapper.run_analysis(similarity_threshold=75.0, method="jaccard")
        
        print(f"\nTest completed successfully!")
        print(f"Report saved to: {report_path}")
        
        # Display some results
        print("\n=== Test Results Summary ===")
        print(f"Total files processed: {mapper.stats['total_files']}")
        print(f"Exact duplicates found: {mapper.stats['exact_duplicates']}")
        print(f"Similar pairs found: {mapper.stats['similar_pairs']}")
        
        if mapper.similar_pairs:
            print("\nSimilar file pairs:")
            for result in sorted(mapper.similar_pairs, 
                               key=lambda x: x.similarity_percentage, reverse=True):
                file1_name = result.file1.name
                file2_name = result.file2.name
                similarity = result.similarity_percentage
                print(f"  {file1_name} <-> {file2_name}: {similarity:.1f}%")
        
        print(f"\nCheck {report_path} for detailed results.")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return False
    
    finally:
        # Clean up test files
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"\nCleaned up test files from: {test_dir}")
    
    return True


if __name__ == "__main__":
    success = run_test()
    if success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Tests failed!")
        exit(1)
