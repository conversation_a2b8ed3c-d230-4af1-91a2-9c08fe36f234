
Here's the version of the code i'm running:

    ```python
    import os
    import sys
    from pathlib import Path
    import glob
    import re
    from dotenv import load_dotenv
    from openai import OpenAI
    import logging
    from datetime import datetime
    from typing import List, Dict, Optional
    from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

    class Config:
        """
        Configuration class to manage global settings.
        """
        PROVIDER_OPENAI = "openai"
        PROVIDER_DEEPSEEK = "deepseek"
        PROVIDER_ANTHROPIC = "anthropic"

        AVAILABLE_MODELS = {
            PROVIDER_OPENAI: {
                "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
                "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
                "gpt-4": "Latest GPT-4 stable release",
                "gpt-4-0125-preview": "Preview GPT-4 Turbo",
                "gpt-4-0613": "June 2023 GPT-4 snapshot",
                "gpt-4-1106-preview": "Preview GPT-4 Turbo",
                "gpt-4-turbo": "Latest GPT-4 Turbo release",
                "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
                "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
                "gpt-4o": "Base GPT-4o model",
                "gpt-4o-mini": "Lightweight GPT-4o variant",
            },
            PROVIDER_DEEPSEEK: {
                "deepseek-chat": "DeepSeek Chat model",
                "deepseek-reasoner": "Specialized reasoning model",
            },
            PROVIDER_ANTHROPIC: {
                "claude-2": "Base Claude 2 model",
                "claude-2.0": "Enhanced Claude 2.0",
                "claude-2.1": "Latest Claude 2.1 release",
                "claude-3-opus-20240229": "Claude 3 Opus",
                "claude-3-sonnet-20240229": "Claude 3 Sonnet",
                "claude-3-haiku-20240307": "Claude 3 Haiku",
            },
        }

        DEFAULT_MODEL_PARAMS = {
            PROVIDER_OPENAI: {
                "model_name": "gpt-4-turbo-preview",
                "model_name": "gpt-3.5-turbo",
                "model_name": "gpt-4-turbo",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek-reasoner",
                "model_name": "deepseek-coder",
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-2.1",
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.7,
                "max_tokens": 800,
            },
        }

        API_KEY_ENV_VARS = {
            PROVIDER_OPENAI: "OPENAI_API_KEY",
            PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
            PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        }

        BASE_URLS = {
            PROVIDER_DEEPSEEK: "https://api.deepseek.com",
        }

        DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()
            self.provider = os.getenv("LLM_PROVIDER", self.DEFAULT_PROVIDER).lower()
            if self.provider not in self.AVAILABLE_MODELS:
                print(f"Warning: Invalid LLM_PROVIDER '{self.provider}'. Defaulting to '{self.DEFAULT_PROVIDER}'.")
                self.provider = self.DEFAULT_PROVIDER
            self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]

        def configure_utf8_encoding(self):
            """
            Ensure UTF-8 encoding for standard output and error streams.
            """
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")


    class LLMInteractions:
        """
        Handles interactions with OpenAI, DeepSeek, and Anthropic APIs in a generalized way.
        """
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):

            self.provider = provider or Config().provider
            config = Config()
            default_model_params = config.DEFAULT_MODEL_PARAMS.get(self.provider, config.DEFAULT_MODEL_PARAMS[Config.DEFAULT_PROVIDER])

            self.model_name = model_name or default_model_params["model_name"]
            self.temperature = temperature if temperature is not None else default_model_params["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else default_model_params["max_tokens"]

            api_key_env_var = Config.API_KEY_ENV_VARS.get(self.provider)
            api_key_to_use = api_key or os.getenv(api_key_env_var)

            if self.provider == Config.PROVIDER_OPENAI:
                self.client = OpenAI(api_key=api_key_to_use)
            elif self.provider == Config.PROVIDER_DEEPSEEK:
                self.client = OpenAI(api_key=api_key_to_use, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK])
            elif self.provider == Config.PROVIDER_ANTHROPIC:
                self.client = Anthropic(api_key=api_key_to_use)
            else:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")


        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            """
            Generates a response from the chosen LLM API (OpenAI, DeepSeek, or Anthropic).
            Corrected Anthropic implementation.
            """
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_tokens = max_tokens if max_tokens is not None else self.max_tokens

            if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:
                r = self.client.chat.completions.create(
                    model=used_model,
                    temperature=used_temp,
                    max_tokens=used_tokens,
                    messages=messages
                )
                return r.choices[0].message.content
            elif self.provider == Config.PROVIDER_ANTHROPIC:
                user_messages = []
                system_prompt_content = ""
                for message in messages:
                    if message['role'] == 'system':
                        system_prompt_content += message['content'] + "\n" # Combine system instructions if multiple
                    elif message['role'] == 'user':
                        user_messages.append({"role": "user", "content": message['content']})

                r = self.client.messages.create(
                    model=used_model,
                    max_tokens=used_tokens,
                    temperature=used_temp,
                    system=system_prompt_content.strip(), # Pass combined system prompt, strip whitespace
                    messages=user_messages # Pass list of user messages
                )
                return r.content[0].text
            else:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")


    class OutputHandler:
        """
        Manages the output and display of information.
        """
        @staticmethod
        def create_hierarchical_prefix(step_number, sub_step_number):
            """
            Creates a hierarchical prefix string for output formatting.
            """
            prefix = "+"
            if step_number > 1:
                prefix += " *" + " -" * (step_number - 2)
            if sub_step_number > 0:
                prefix += " -" * sub_step_number
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(all_refinements, header="Refinement Steps:"):
            """
            Prints the hierarchical refinements to the console.
            """
            print('\n')
            print(f"Here's the input prompt that was used:\n```{header}```")

            print('\n')
            print("Here's the result when executed with your version:")
            print('```')
            for refinement in all_refinements:
                agent_name = refinement['agent_name']
                for step_number, response in enumerate(refinement["outputs"], 1):
                    # replace quotes from response for enabling selection-macros to select all text between quotes
                    response = response.replace('\"', "\'")
                    # replace newlines from response for ensuring condensed result
                    response = response.replace('\n', ".")
                    print_prefix = f"{refinement['agent_name']}: {step_number}/{refinement['iterations']}"
                    print(f'{OutputHandler.create_hierarchical_prefix(1, step_number)}{print_prefix}: "{response}"')

            print('```')
            print('\n')

        @staticmethod
        def display_evaluation_result(xml_name, prompt_to_evaluate, evaluation_result):
            """
            Prints the evaluation result to the console.
            """
            print("\n--- Evaluation Result ---")
            print(f"Evaluator: {xml_name}")
            print(f"Prompt Evaluated: '{prompt_to_evaluate}'")
            print(f"Evaluation Result: {evaluation_result}")
            print("\n")



    class TemplateManager:
        """
        Manages XML templates, including searching, listing, and executing.
        """
        def __init__(self):
            self.template_dir = os.getcwd()
            self.template_cache = self._load_template_info()

        def _extract_value_from_xml(self, file_path, pattern):
            """
            Extracts a value from XML content using regex.
            """
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                match = re.search(pattern, content)
                return match.group(1) if match else None
            except Exception:
                return None

        def _load_template_info(self):
            """
            Loads all template info from XML files into the class.
            """
            search_pattern = os.path.join(self.template_dir, "**", "*.xml")
            template_files = glob.glob(search_pattern, recursive=True)

            template_info = {}
            for file_path in template_files:
                filename = os.path.basename(file_path)
                name_without_extension = os.path.splitext(filename)[0]

                version = self._extract_value_from_xml(file_path, r'<version value="([^"]*)"')
                status = self._extract_value_from_xml(file_path, r'<status value="([^"]*)"')

                template_info[name_without_extension] = {
                    'path': file_path,
                    'version': version,
                    'status': status,
                }
            return template_info

        def list_templates(self, exclude_versions=None, exclude_statuses=None,
                                exclude_none_versions=False, exclude_none_statuses=False):
            """
            Lists available templates with options to exclude by version, status, or None values.
            Returns a dictionary with template names as keys and their info as values.
            """
            filtered_templates = {}
            for name, info in self.template_cache.items():
                if (not exclude_versions or info['version'] not in exclude_versions) and \
                   (not exclude_statuses or info['status'] not in exclude_statuses) and \
                   (not exclude_none_versions or info['version'] is not None) and \
                   (not exclude_none_statuses or info['status'] is not None):
                    filtered_templates[name] = info
            return filtered_templates

        def get_template_path(self, xml_name):
            """
            Get the template path by name.
            """
            info = self.template_cache.get(xml_name)
            return info['path'] if info else None

        def load_template_from_xml_string(self, xml_file_path, initial_prompt=""):
            """
            Loads the template from an XML file as a string and replaces placeholders.
            """
            with open(xml_file_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            placeholders = {
                '[OUTPUT_FORMAT]': 'plain_text',
                '[ORIGINAL_PROMPT_LENGTH]': str(len(initial_prompt)),
                '[RESPONSE_PROMPT_LENGTH]': str(int(len(initial_prompt) * 0.9)),
                '[INPUT_PROMPT]': initial_prompt,
                '[ADDITIONAL_CONSTRAINTS]': '',
                '[ADDITIONAL_PROCESS_STEPS]': '',
                '[ADDITIONAL_GUIDELINES]': '',
                '[ADDITIONAL_REQUIREMENTS]': '',
            }

            for placeholder, value in placeholders.items():
                xml_content = xml_content.replace(placeholder, value)

            return xml_content

        def execute_prompt_refinement_chain_from_xml(
            self,
            xml_file_path,
            initial_prompt,
            agent,
            refinement_count=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            """
            Executes prompt refinement iteratively using instructions from an XML file.
            """
            template_string = self.load_template_from_xml_string(xml_file_path, initial_prompt)

            if display_instructions:
                print('=' * 60)
                print(template_string)
                print('=' * 60)

            # Extract system prompt
            system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
            system_prompt_end = template_string.find("\"/>", system_prompt_start)
            system_prompt = template_string[system_prompt_start:system_prompt_end]

            # Extract instructions
            instructions_start = template_string.find("<instructions>") + len("<instructions>")
            instructions_end = template_string.find("</instructions>", instructions_start)
            instructions_content = template_string[instructions_start:instructions_end]

            current_prompt = initial_prompt
            all_refinements = []

            for _ in range(refinement_count):
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "system", "content": instructions_content},
                    {"role": "user", "content": current_prompt},
                ]

                refined_prompt = agent.generate_response(
                    messages,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                all_refinements.append(refined_prompt)
                current_prompt = refined_prompt

            return all_refinements

        def execute_prompt_refinement_by_name(
            self,
            xml_name_or_list,
            initial_prompt,
            agent,
            refinement_levels=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            """
            Executes prompt refinement by XML template name (or names).
            :param xml_name_or_list: String or List[str] of template names.
            :param refinement_levels: None (use num_iterations) or List[int] (same length as xml_name_or_list).
            """
            # --- 1) Single agent (string) scenario ---
            if isinstance(xml_name_or_list, str):
                agent_name = xml_name_or_list
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    print(f"No XML file found with name: {agent_name}")
                    return None

                # If refinement_levels is int => do that many refinements
                if isinstance(refinement_levels, int):
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=refinement_levels,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                # If refinement_levels is a list => must have length 1
                elif isinstance(refinement_levels, list):
                    if len(refinement_levels) != 1:
                        raise ValueError(
                            "When using a single agent, refinement_levels list must have exactly 1 item."
                        )
                    single_count = refinement_levels[0]
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=single_count,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                else:
                    raise TypeError("refinement_levels must be an int or a list[int].")

            # --- 2) Multiple agents (list) scenario ---
            elif isinstance(xml_name_or_list, list):
                return self._execute_prompt_chain(
                    xml_name_list=xml_name_or_list,
                    initial_prompt=initial_prompt,
                    agent=agent,
                    refinement_levels=refinement_levels,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            else:
                print("Invalid 'xml_name_or_list' type. Must be str or list[str].")
                return None

        def _execute_prompt_chain(
            self,
            xml_name_list,
            initial_prompt,
            agent,
            refinement_levels,
            display_instructions,
            model_name,
            temperature,
            max_tokens,
        ):
            """
            Executes a sequence of agents in order.
            """
            current_prompt = initial_prompt
            all_agent_results = []

            # Interpret refinement_levels
            #  A) integer => each agent uses that many
            if isinstance(refinement_levels, int):
                iteration_counts = [refinement_levels] * len(xml_name_list)
            #  B) list => must match agent list length
            elif isinstance(refinement_levels, list):
                if len(refinement_levels) != len(xml_name_list):
                    raise ValueError("If refinement_levels is a list, it must match the length of xml_name_list.")
                iteration_counts = refinement_levels
            else:
                raise TypeError("refinement_levels must be an int or a list[int].")

            for agent_name, refinement_count in zip(xml_name_list, iteration_counts):
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    print(f"No XML file found with name: {agent_name}")
                    continue

                refinements_for_this_agent = self.execute_prompt_refinement_chain_from_xml(
                    xml_file_path=xml_file_path,
                    initial_prompt=current_prompt,
                    agent=agent,
                    refinement_count=refinement_count,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                if refinements_for_this_agent:
                    current_prompt = refinements_for_this_agent[-1]

                all_agent_results.append({
                    "agent_name": agent_name,
                    "iterations": refinement_count,
                    "outputs": refinements_for_this_agent
                })

            return all_agent_results

        def execute_prompt_evaluation_by_name(
            self,
            xml_name,
            prompt_to_evaluate,
            agent,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            """
            Executes a prompt evaluation using instructions from an XML file.
            """
            xml_file_path = self.get_template_path(xml_name)
            if not xml_file_path:
                print(f"No XML file found with name: {xml_name}")
                return None

            template_string = self.load_template_from_xml_string(xml_file_path, prompt_to_evaluate)

            if display_instructions:
                print('=' * 60)
                print(template_string)
                print('=' * 60)

            # Extract system prompt
            system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
            system_prompt_end = template_string.find("\"/>", system_prompt_start)
            system_prompt = template_string[system_prompt_start:system_prompt_end]

            # Extract instructions
            instructions_start = template_string.find("<instructions>") + len("<instructions>")
            instructions_end = template_string.find("</instructions>", instructions_start)
            instructions_content = template_string[instructions_start:instructions_end]

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": instructions_content},
                {"role": "user", "content": prompt_to_evaluate},
            ]

            evaluation_result = agent.generate_response(
                messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return evaluation_result


    class Execution:
        """
        Main execution class to run the prompt refinement process.
        """
        def __init__(self, provider=None):
            self.config = Config()
            self.agent = LLMInteractions(provider=provider)
            self.template_manager = TemplateManager()
            self.output_handler = OutputHandler()

        def execute_prompt_evaluation_by_name(
            self,
            xml_name,
            prompt_to_evaluate,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            """
            Executes prompt evaluation by XML template name. (Wrapper for TemplateManager)
            """
            return self.template_manager.execute_prompt_evaluation_by_name(
                xml_name=xml_name,
                prompt_to_evaluate=prompt_to_evaluate,
                agent=self.agent,
                display_instructions=display_instructions,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )


        def run(self):
            """
            Examples:

            # --- Example 1: Evaluate initial prompt clarity ---
            # =======================================================
            clarity_evaluation = self.execute_prompt_evaluation_by_name(
                xml_name="ClarityEvaluator",
                prompt_to_evaluate=initial_prompt,
            )
            self.output_handler.display_evaluation_result(
                xml_name="ClarityEvaluator",
                prompt_to_evaluate=initial_prompt,
                evaluation_result=clarity_evaluation
            )


            # --- Example 2: Evaluate prompt after refinement ---
            # =======================================================
            refined_prompt_outputs = self.template_manager.execute_prompt_refinement_by_name(
                xml_name_or_list=["EmphasisEnhancer"], # Using EmphasisEnhancer for refinement
                initial_prompt=initial_prompt,
                agent=self.agent,
                refinement_levels=1,
            )
            refined_prompt = refined_prompt_outputs[0]["outputs"][-1] # Get the final refined prompt

            effectiveness_evaluation = self.execute_prompt_evaluation_by_name(
                xml_name="EffectivenessEvaluator", evaluators/
                prompt_to_evaluate=refined_prompt,
            )
            self.output_handler.display_evaluation_result(
                xml_name="EffectivenessEvaluator",
                prompt_to_evaluate=refined_prompt,
                evaluation_result=effectiveness_evaluation
            )


            # --- Example 3: Refinement Chain and then Evaluation ---
            # =======================================================
            agent_sequence_for_refinement = [
                "IntensityEnhancer",
                "SingleLineFormatterForced",
                "PromptEnhancer",
            ]
            refined_prompts_chain = self.template_manager.execute_prompt_refinement_by_name(
                xml_name_or_list=agent_sequence_for_refinement,
                initial_prompt=initial_prompt,
                agent=self.agent,
                refinement_levels=1,
            )
            final_refined_prompt = refined_prompts_chain[-1]["outputs"][-1]
            self.output_handler.display_hierarchical_refinements(refined_prompts_chain, header=f'Refinement chain for "{initial_prompt}"' )


            bias_evaluation = self.execute_prompt_evaluation_by_name(
                xml_name="BiasEvaluator",
                prompt_to_evaluate=final_refined_prompt,
            )
            self.output_handler.display_evaluation_result(
                xml_name="BiasEvaluator",
                prompt_to_evaluate=final_refined_prompt,
                evaluation_result=bias_evaluation
            )
            """

            # todo: add ability to quickly save good prompt+sequence-combinations
            # todo: implement evaluators properly

            # Summarize a template.xml in a single sentence
            sequence_template_sumarizexmltemplate = [
                "XMLTemplateDecoderSingleSentence",
            ]

            # Template/chain for maths/abstraction transformer
            sequence_template_abstractiontransformer = [
                # -- AbstractionTransformer --
                "MathematicalVisualizer",
                "MathematicalDetailEnhancer",
                "AnimationRefiner",
                "VisualClarityOptimizer",
                "SceneContextualizer",
                "StyleAndMoodInfuser",
                "SemanticSimplifier",
                "VisualMetaphorGenerator",
                "RunwayPromptBuilder",
            ]
            # Template/chain for maths/abstraction transformer
            sequence_template_musicalstructureanalyzer = [
                # -- MusicVisualize --
                "MusicalStructureAnalyzer",
                "HarmonicDetailer",
                "RhythmicAnimator",
                "VisualHarmonyOptimizer",
                "MusicalMetaphorGenerator",
            ]

            # Template/chain for
            sequence_template_cognitivemapper = [
                # -- CognitiveMapper --
                "KnowledgeExtractor",
                "SemanticLinker",
                "HierarchicalOrganizer",
                "VisualPathwaysGenerator",
                "KnowledgeContextualizer",
                "CognitiveStyleInfuser",
            ]

            # Template/chain for
            sequence_template_realityruleextractor = [
                # -- RealityFabricator --
                "RealityRuleExtractor",
                "DimensionGenerator",
                "PropertyManipulator",
                "DynamicSimulator",
                "PerceptionShaper",
                "RealityMetaphorGenerator",
                "RealityOutputGenerator",
            ]

            # Template/chain for
            sequence_template_universalharmony = [
                # -- UniversalHarmony --
                "UniversalRuleExtractor",
                "ScaleAndStructureGenerator",
            ]

            # Template/chain for sublime text
            sequence_template_sublimetext = [
                "SublimeTextContextTransformer",
                "SublimeTextInstructionGenerator",
                "SublimeTextInstructionEnhancer",
            ]

            # Template to condense detailed info into a singleline
            sequence_template_promptimprover = [
                "IntensityEnhancer",
                "SingleLineFormatterForced",
                "PromptEnhancer",
                "IntensityEnhancer",
                "SingleLineFormatterForced",
            ]
            # Template to extract meaning
            sequence_template_extractmeaning = [
                "IntensityEnhancer",
                "InterpretMeaning",
                "ExtractMeaning",
                "SingleLineFormatterForced",
            ]

            # Template transform text into visually compelling prompts for RunwayML
            sequence_template_runwaygenerator = [
                "ImpactfulRestatement",
                "EmphasisEnhancer",
                "PoteticPerfection",
                "ImpactRefinement",
                "SpectacleInfusionAgent_a",
                "ImpactRefinement",
                "RunwayPromptBuilder",
                "RunwayPromptBuilder_e",
            ]
            initial_prompt = "A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky."
            initial_prompt = """visual depictions of infinity"""
            initial_prompt = """dimensions=3D, properties=organic_interconnected_flow, scale=universal, attributes=deep_purple_black_with_gold_and_teal_accents, cosmic_abstract_organic_flow, immersive_ethereal_wonder, harmonious_balance, abstract_richness, visual_emotion, aesthetic_harmony, color_shift=0.5, morphing_rate=0.2, pulsing_speed=1.0, transparency=0.7, reflectivity=0.4, movement_speed=0.3, scaling_factor=1.5, movement=path:organic_flow,speed:0.3,direction:random,acceleration:0.1,colorShiftRate:0.5,sizeChangeFrequency:0.2,morph:0.2,pulsationPeriod:1.0,transparencyLevel:0.7,gridDynamics:responsive,duration:continuous,delay:0,frequency:1.0, mood=calm_ethereal_awe, emotional_tone=serene_wonder, clarity=abstract_richness, complexity=cosmic_organic_flow, mystery=immersive_ethereal_wonder, aesthetic_response=harmonious_balance, intellectual_response=deep_contemplation, metaphors=galactic_veins_pulsing_with_starlight,a_cosmic_tapestry_of_interwoven_auroras,shimmering_abyss_of_infinite_depths,golden_teal_whispers_dancing_in_void,ethereal_breath_of_universal_flow,celestial_rhythms_in_fluid_harmony,a_dreamlike_sea_of_shifting_shadows,cosmic_heartbeat_echoing_through_space."""


            # Using this sequence interactively when testing
            # =======================================================
            agent_sequence = [
                # "NorwegianToEnglishTranslator",
                # "RunwayPromptBuilder",
                # "ImpactfulRestatement",
                # "EmphasisEnhancer",
                # "PoteticPerfection",
                # "ClarityEnhancer",
                # "SubtlePoeticRefiner",
                # "PoeticRefiner",
                # "GoalSetter",
                # "SubtlePoeticRefiner",
                # "AdjustedToPerfection",
                # "PromptEnhancer",
                # "SublimeTextInquiryGenerator",
                # "GoalSetter",
                # "StripFormatting",
                # "SingleLineFormatterForced",
                # "SingleLineFormatterForced_a",
                # "ClarityEnhancer",
                # "RedefineFromMetaPerspective",
                # "XMLTemplateDecoderSingleSentence",
                # "PerformanceEnhancer",
                # "PhilosophicalMusings",
                # "PhilosophicalMusings",

                # "ImpactRefinement",
                # "ArtSnobCritic",
                # "RunwayPromptBuilder",
                # "SpectacleInfusionAgent_a",
                # "ColoringAgent",
                # "ImpactRefinement",
                # "IdeaGenerator",
                # "SpectacleInfusionAgent_a",
                # "RunwayPromptBuilder",
                # "InstructionsGenerator",
                # "SpectacleInfusionAgent_a",
                # "IntensityEnhancer",
                # "SpectacleInfusionAgent_a",
                # "XMLTemplateDecoderSingleSentence",
                # "ValuableConversationSummarizer",

                # "RephraseAsQuestion",


                # "UnderwaterDayNightSceneGenerator",
                # "SynthesizerAgent",

                # "SceneDescriptor_a",

                # -- MusicVisualize --
                "MusicalStructureAnalyzer",
                "HarmonicDetailer",
                # "RhythmicAnimator",
                # "VisualHarmonyOptimizer",
                "MusicalMetaphorGenerator",

                # -- CognitiveMapper --
                "KnowledgeExtractor",
                "SemanticLinker",
                "HierarchicalOrganizer",
                "VisualPathwaysGenerator",
                "KnowledgeContextualizer",
                "CognitiveStyleInfuser",

                # -- UniversalHarmony --
                "UniversalRuleExtractor",
                "ScaleAndStructureGenerator",

                # -- RealityFabricator --
                "RealityRuleExtractor",
                "DimensionGenerator",
                "PropertyManipulator",
                "DynamicSimulator",
                "PerceptionShaper",
                "RealityMetaphorGenerator",
                "RealityOutputGenerator",



                # # -- runway --
                # # "SceneDescriptor_a",
                # "SceneDescriptor",
                # # "CinematicSceneDescriptor",
                # "VisualStoryteller",
                "VisualPromptGenerator",
                # "EmotionalResonanceInfuser",
                # # # "SpectacleInfusionAgent",
                # "ScenicCompositionExpert",
                # # "SynthesizerAgent",
                # "CameraMovementInfuser",
                "BrilliantCameraShotTransformer",
                # "ColoringAgent",
                "RunwayPromptBuilder",

                # # -- maths --
                # "MathematicalVisualizer",
                # "MathematicalDetailEnhancer",
                # "AnimationRefiner",
                # "VisualClarityOptimizer",
                # "SceneContextualizer",
                # "StyleAndMoodInfuser",
                # "SemanticSimplifier",
                # "VisualMetaphorGenerator",
                # "RunwayPromptBuilder",


                # # "PrehistoricSceneGenerator",
                # "RunwayPromptBuilder",

                # "IntensityEnhancer",
                # "ImpactRefinement",
                # "IntensityEnhancer",
                # "ImpactRefinement",
                # "IntensityEnhancer",


                # "InstructionInterpreter",
                # "MinimalistCodePlanner",
                # "CriticalAspectIdentifier",
            ]

            refinements = self.template_manager.execute_prompt_refinement_by_name(
                # xml_name_or_list=agent_sequence,
                # xml_name_or_list=sequence_template_promptimprover,
                # xml_name_or_list=sequence_template_extractmeaning,
                # xml_name_or_list=sequence_template_runwaygenerator,
                # xml_name_or_list=sequence_template_sublimetext,
                # xml_name_or_list=sequence_template_sumarizexmltemplate,
                # xml_name_or_list=sequence_template_abstractiontransformer,
                xml_name_or_list=sequence_template_cognitivemapper,
                # xml_name_or_list=sequence_template_realityruleextractor,
                # xml_name_or_list=sequence_template_universalharmony,
                initial_prompt=initial_prompt,
                agent=self.agent,
                refinement_levels=1,
                display_instructions=True,
            )
            self.output_handler.display_hierarchical_refinements(refinements, header=f'"{initial_prompt}"' )


    if __name__ == "__main__":
        provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
        execution = Execution(provider=provider_to_use)
        execution.run()

    ```



In certain cases the script is halted and throws an error like this:

    Traceback (most recent call last):
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 950, in <module>
        execution.run()
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 928, in run
        refinements = self.template_manager.execute_prompt_refinement_by_name(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 418, in execute_prompt_refinement_by_name
        return self._execute_prompt_chain(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 467, in _execute_prompt_chain
        refinements_for_this_agent = self.execute_prompt_refinement_chain_from_xml(
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 349, in execute_prompt_refinement_chain_from_xml
        refined_prompt = agent.generate_response(
                         ^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\src\ai\templates\agents\agent_template_level1.py", line 140, in generate_response
        r = self.client.chat.completions.create(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_utils\_utils.py", line 274, in wrapper
        return func(*args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\resources\chat\completions.py", line 815, in create
        return self._post(
               ^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_base_client.py", line 1277, in post
        return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_base_client.py", line 954, in request
        return self._request(
               ^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_base_client.py", line 1060, in _request
        return self._process_response(
               ^^^^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_base_client.py", line 1159, in _process_response
        return api_response.parse()
               ^^^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_response.py", line 317, in parse
        parsed = self._parse(to=to)
                 ^^^^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\openai\_response.py", line 259, in _parse
        data = response.json()
               ^^^^^^^^^^^^^^^
      File ".\llm_framework_py\venv\Lib\site-packages\httpx\_models.py", line 766, in json
        return jsonlib.loads(self.content, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File "C:\Program Files\Python311\Lib\json\__init__.py", line 346, in loads
        return _default_decoder.decode(s)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
      File "C:\Program Files\Python311\Lib\json\decoder.py", line 337, in decode
        obj, end = self.raw_decode(s, idx=_w(s, 0).end())
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      File "C:\Program Files\Python311\Lib\json\decoder.py", line 355, in raw_decode
        raise JSONDecodeError("Expecting value", s, err.value) from None
    json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

Which is problematic because if i'm e.g. running a chain of agents, all progress is lost.



Your goal is to familiarize yourself with the provided framework and list your best alternatives (the most precice way possible); each line should express in a single sentence exactly what it is and what makes it particular relevant with our context (i.e. ones that are uniquely suited)

