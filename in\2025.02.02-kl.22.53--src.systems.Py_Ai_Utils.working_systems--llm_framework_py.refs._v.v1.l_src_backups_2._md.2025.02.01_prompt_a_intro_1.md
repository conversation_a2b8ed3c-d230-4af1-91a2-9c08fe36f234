
Here's the code for the framework (in which the templates belong to):
- The provided Python framework enhances prompt refinement for various LLM providers like OpenAI, DeepSeek, and Anthropic by dynamically adapting to new agents, utilizing placeholders in templates, and configuring model parameters with ease.
- The framework supports easy incorporation of new agents by specifying their names and automates interactions with LLM providers, showcasing a high level of automation and adaptability.
- Efficiency is ensured through the framework's design, which allows for the seamless adaptation of templates with placeholders and easy configuration of model parameters like model name, temperature, and max tokens for different providers.
- The framework's flexibility and efficiency are highlighted by its ability to streamline prompt refinements, enhance usability across various LLM tasks, and uniquely cater to the diverse needs of the LLM domain.
- By leveraging dynamic templates that adapt fluently with placeholders, the framework stands out for its effortless efficiency in prompt refinement, making it uniquely suited for a wide range of LLM tasks.
- The framework's unique approach lies in its seamless adaptation to new agents, efficient placeholder usage in templates, and effortless configuration of model parameters, setting it apart as a versatile and adaptable tool for prompt refinement in the LLM domain


    ```python
    import os
    import sys
    from pathlib import Path
    from loguru import logger
    import glob
    import yaml
    import re
    from dotenv import load_dotenv
    from openai import OpenAI
    import logging
    from datetime import datetime
    from typing import List, Dict, Optional
    from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

    class Config:
        '''
        Global settings.
        '''
        PROVIDER_OPENAI = "openai"
        PROVIDER_DEEPSEEK = "deepseek"
        PROVIDER_ANTHROPIC = "anthropic"

        AVAILABLE_MODELS = {
            PROVIDER_OPENAI: {
                "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
                "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
                "gpt-4": "Latest GPT-4 stable release",
                "gpt-4-0125-preview": "Preview GPT-4 Turbo",
                "gpt-4-0613": "June 2023 GPT-4 snapshot",
                "gpt-4-1106-preview": "Preview GPT-4 Turbo",
                "gpt-4-turbo": "Latest GPT-4 Turbo release",
                "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
                "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
                "gpt-4o": "Base GPT-4o model",
                "gpt-4o-mini": "Lightweight GPT-4o variant",
            },
            PROVIDER_DEEPSEEK: {
                "deepseek-chat": "DeepSeek Chat model",
                "deepseek-reasoner": "Specialized reasoning model",
            },
            PROVIDER_ANTHROPIC: {
                "claude-2": "Base Claude 2 model",
                "claude-2.0": "Enhanced Claude 2.0",
                "claude-2.1": "Latest Claude 2.1 release",
                "claude-3-opus-20240229": "Claude 3 Opus",
                "claude-3-sonnet-20240229": "Claude 3 Sonnet",
                "claude-3-haiku-20240307": "Claude 3 Haiku",
            },
        }

        DEFAULT_MODEL_PARAMS = {
            PROVIDER_OPENAI: {
                "model_name": "gpt-4-turbo-preview",
                "model_name": "gpt-4-turbo",
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek-reasoner",
                "model_name": "deepseek-coder",
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-2.1",
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.7,
                "max_tokens": 800,
            },
        }

        API_KEY_ENV_VARS = {
            PROVIDER_OPENAI: "OPENAI_API_KEY",
            PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
            PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        }

        BASE_URLS = {
            PROVIDER_DEEPSEEK: "https://api.deepseek.com",
        }

        DEFAULT_PROVIDER = PROVIDER_OPENAI

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()
            self.provider = self.DEFAULT_PROVIDER.lower()
            self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
            self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
            self.setup_logger()

        def configure_utf8_encoding(self):
            '''
            Ensure UTF-8 encoding for standard output and error streams.
            '''
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")

        def setup_logger(self):
            '''
            YAML logging via Loguru: clears logs, sets global context, and configures sinks
            '''
            log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
            log_filepath = os.path.join(self.log_dir, log_filename)
            open(log_filepath, "w").close()
            logger.remove()
            logger.configure(extra={"provider": self.provider, "model": self.model_params.get("model_name")})

            def yaml_sink(log_message):
                log_record = log_message.record
                formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
                formatted_level = f"!{log_record['level'].name}"
                logger_name = log_record["name"]
                formatted_function_name = f"*{log_record['function']}"
                line_number = log_record["line"]
                extra_provider = log_record["extra"].get("provider")
                extra_model = log_record["extra"].get("model")
                log_message_content = log_record["message"]
                formatted_message = (
                    "|\n" + "\n".join(f"  {log_line}" for log_line in log_message_content.splitlines())
                    if "\n" in log_message_content
                    else (f"'{log_message_content}'" if ":" in log_message_content else log_message_content)
                )
                log_lines = [
                    f"- time: {formatted_timestamp}",
                    f"  level: {formatted_level}",
                    f"  name: {logger_name}",
                    f"  funcName: {formatted_function_name}",
                    f"  lineno: {line_number}",
                ]
                if extra_provider is not None:
                    log_lines.append(f"  provider: {extra_provider}")
                if extra_model is not None:
                    log_lines.append(f"  model: {extra_model}")
                log_lines.append(f"  message: {formatted_message}")
                log_lines.append("")
                with open(log_filepath, "a", encoding="utf-8") as log_file:
                    log_file.write("\n".join(log_lines) + "\n")

            logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")

    class LLMInteractions:
        '''
        Handles LLM interactions.
        '''
        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):
            self.provider = provider or Config().provider
            config = Config()
            default_model_params = config.DEFAULT_MODEL_PARAMS.get(self.provider, config.DEFAULT_MODEL_PARAMS[Config.DEFAULT_PROVIDER])
            self.model_name = model_name or default_model_params["model_name"]
            self.temperature = temperature if temperature is not None else default_model_params["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else default_model_params["max_tokens"]

            api_key_env_var = Config.API_KEY_ENV_VARS.get(self.provider)
            api_key_to_use = api_key or os.getenv(api_key_env_var)

            # Map providers to client constructors to reduce repetitive conditionals.
            client_builders = {
                Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),
                Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),
                Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key)
            }
            try:
                self.client = client_builders[self.provider](api_key_to_use)
            except KeyError:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")

        def _log_api_response(self, response):
            '''
            Logs the API response and extracts prompt_tokens if available.
            '''
            extra = {"prompt_tokens": getattr(getattr(response, 'usage', None), 'prompt_tokens', "N/A")}
            logger.bind(**extra).debug(response)

        def _log_api_error(self, exception, model_name, messages):
            logger.error(f"Error during API call: {exception}")
            logger.error(f"Exception type: {type(exception).__name__}")
            logger.error(f"Detailed exception: {exception}")
            logger.error(f"Input messages: {messages}")

        def _execute_api_call(self, call_fn, model_name, messages):
            '''
            Executes an API call with uniform error handling and logging.
            '''
            try:
                response = call_fn()
                self._log_api_response(response)
                return response
            except Exception as e:
                self._log_api_error(e, model_name, messages)
                return None

        def _handle_openai_response(self, messages, model_name, temperature, max_tokens):
            '''
            using individual method for flexibility (e.g. for models not accepting system prompts)
            '''
            response = self._execute_api_call(
                lambda: self.client.chat.completions.create(
                    model=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages
                ),
                model_name, messages
            )
            if response is not None:
                return response.choices[0].message.content
            return None

        def _handle_anthropic_response(self, messages, model_name, temperature, max_tokens):
            '''
            using individual method for flexibility (e.g. for models not accepting system prompts)
            '''
            user_messages = []
            system_prompt_content = ""
            for message in messages:
                if message['role'] == 'system':
                    system_prompt_content += message['content'] + "\n"
                elif message['role'] == 'user':
                    user_messages.append({"role": "user", "content": message['content']})
            response = self._execute_api_call(
                lambda: self.client.messages.create(
                    model=model_name,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    system=system_prompt_content.strip(),
                    messages=user_messages
                ),
                model_name, messages
            )
            if response is not None:
                return response.content[0].text
            return None

        def _handle_deepseek_response(self, messages, model_name, temperature, max_tokens):
            '''
            using individual method for flexibility (e.g. for models not accepting system prompts)
            '''
            system_prompt_content = ""
            instructions_content = ""
            user_prompt = ""
            for message in messages:
                if message['role'] == 'system':
                    if not system_prompt_content:
                        system_prompt_content = message['content']
                    else:
                        instructions_content += message['content'] + "\n"
                elif message['role'] == 'user':
                    user_prompt = message['content']
            combined_prompt = f"{system_prompt_content}\n{instructions_content}\n{user_prompt}"
            response = self._execute_api_call(
                lambda: self.client.chat.completions.create(
                    model=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=[{"role": "user", "content": combined_prompt}]
                ),
                model_name, messages
            )
            if response is not None:
                return response.choices[0].message.content
            return None

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_tokens = max_tokens if max_tokens is not None else self.max_tokens

            if self.provider == Config.PROVIDER_OPENAI:
                return self._handle_openai_response(messages, used_model, used_temp, used_tokens)
            elif self.provider == Config.PROVIDER_ANTHROPIC:
                return self._handle_anthropic_response(messages, used_model, used_temp, used_tokens)
            elif self.provider == Config.PROVIDER_DEEPSEEK:
                return self._handle_deepseek_response(messages, used_model, used_temp, used_tokens)
            else:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")


    class OutputHandler:
        '''
        Manages the output and display of information.
        '''
        @staticmethod
        def create_hierarchical_prefix(step_number, sub_step_number):
            '''
            Creates a hierarchical prefix string for output formatting.
            '''
            prefix = "+"
            if step_number > 1:
                prefix += " *" + " -" * (step_number - 2)
            if sub_step_number > 0:
                prefix += " -" * sub_step_number
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(
            all_refinements,
            header="Refinement Steps:",
            replace_quotes=True,
            replace_newlines="."
        ):
            '''
            Prints the hierarchical refinements to the console.
            - A subjective representation of the chain of events that has occurred.
            '''
            print('\n')
            print(f"Here's the input prompt that was used:\n```{header}```")

            print('\n')
            print("Here's the result when executed with your version:")
            print('```')
            for refinement in all_refinements:
                agent_name = refinement['agent_name']
                for step_number, response in enumerate(refinement["outputs"], 1):
                    if response is not None:
                        # Optionally replace quotes
                        if replace_quotes:
                            response = response.replace('\"', "\'")
                        # Optionally replace newlines
                        if replace_newlines:
                            response = response.replace('\n', replace_newlines)
                        print_prefix = f"{refinement['agent_name']}: {step_number}/{refinement['iterations']}"
                        print(f'{OutputHandler.create_hierarchical_prefix(1, step_number)}{print_prefix}: "{response}"')
                    else:
                         print_prefix = f"{refinement['agent_name']}: {step_number}/{refinement['iterations']}"
                         print(f'{OutputHandler.create_hierarchical_prefix(1, step_number)}{print_prefix}: "--- API Error - No Response ---"') # Indicate API error

            print('```')
            print('\n')

        @staticmethod
        def display_evaluation_result(xml_name, prompt_to_evaluate, evaluation_result):
            '''
            Prints the evaluation result to the console.
            '''
            print("\n--- Evaluation Result ---")
            print(f"Evaluator: {xml_name}")
            print(f"Prompt Evaluated: '{prompt_to_evaluate}'")
            print(f"Evaluation Result: {evaluation_result}")
            print("\n")

    class TemplateManager:
        '''
        Manages XML templates, including searching, listing, and executing.
        '''
        def __init__(self):
            self.template_dir = os.getcwd()
            self.template_cache = self._load_template_info()

        def _extract_value_from_xml(self, file_path, pattern):
            '''
            Extracts a value from XML content using regex.
            '''
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                match = re.search(pattern, content)
                return match.group(1) if match else None
            except Exception as e:
                logger.error(f"Error extracting XML value from {file_path}: {e}")
                return None

        def _load_template_info(self):
            '''
            Loads all template info from XML files into the class.
            '''
            search_pattern = os.path.join(self.template_dir, "**", "*.xml")
            template_files = glob.glob(search_pattern, recursive=True)

            template_info = {}
            for file_path in template_files:
                filename = os.path.basename(file_path)
                name_without_extension = os.path.splitext(filename)[0]

                version = self._extract_value_from_xml(file_path, r'<version value="([^"]*)"')
                status = self._extract_value_from_xml(file_path, r'<status value="([^"]*)"')

                template_info[name_without_extension] = {
                    'path': file_path,
                    'version': version,
                    'status': status,
                }
            return template_info

        def list_templates(self, exclude_versions=None, exclude_statuses=None,
                                exclude_none_versions=False, exclude_none_statuses=False):
            '''
            Lists available templates with options to exclude by version, status, or None values.
            Returns a dictionary with template names as keys and their info as values.
            '''
            filtered_templates = {}
            for name, info in self.template_cache.items():
                if (not exclude_versions or info['version'] not in exclude_versions) and \
                   (not exclude_statuses or info['status'] not in exclude_statuses) and \
                   (not exclude_none_versions or info['version'] is not None) and \
                   (not exclude_none_statuses or info['status'] is not None):
                    filtered_templates[name] = info
            return filtered_templates

        def get_template_path(self, xml_name):
            '''
            Get the template path by name.
            '''
            info = self.template_cache.get(xml_name)
            return info['path'] if info else None

        def load_template_from_xml_string(self, xml_file_path, initial_prompt=""):
            '''
            Loads the template from an XML file as a string and replaces placeholders.
            '''
            try:
                with open(xml_file_path, 'r', encoding='utf-8') as f:
                    xml_content = f.read()

                # Ensure initial_prompt is not None before getting its length
                prompt_length = len(initial_prompt) if initial_prompt else 0

                placeholders = {
                    '[OUTPUT_FORMAT]': 'plain_text',
                    '[ORIGINAL_PROMPT_LENGTH]': str(prompt_length),
                    '[RESPONSE_PROMPT_LENGTH]': str(int(prompt_length * 0.9)),
                    '[INPUT_PROMPT]': initial_prompt if initial_prompt else '',
                    '[ADDITIONAL_CONSTRAINTS]': '',
                    '[ADDITIONAL_PROCESS_STEPS]': '',
                    '[ADDITIONAL_GUIDELINES]': '',
                    '[ADDITIONAL_REQUIREMENTS]': '',
                }

                for placeholder, value in placeholders.items():
                    xml_content = xml_content.replace(placeholder, value)

                return xml_content
            except Exception as e:
                logger.error(f"Error loading template from {xml_file_path}: {e}")
                return None

        def _extract_template_parts(self, template_string: str):
            '''
            Extracts <system_prompt value="..."/> and <instructions>...</instructions>
            '''

            # Extract <system_prompt value="..."/>
            system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
            system_prompt_end = template_string.find("\"/>", system_prompt_start)
            system_prompt = template_string[system_prompt_start:system_prompt_end]

            # Extract <instructions>...</instructions>
            instructions_start = template_string.find("<instructions>") + len("<instructions>")
            instructions_end = template_string.find("</instructions>", instructions_start)
            instructions_content = template_string[instructions_start:instructions_end]

            return system_prompt, instructions_content

        def _build_messages(self, system_prompt: str, instructions_content: str, user_prompt: str):
            '''
            Creates a standard messages list for the LLM agent:
             - system prompt
             - system instructions
             - user prompt
            '''
            return [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": instructions_content},
                {"role": "user", "content": user_prompt},
            ]

        def execute_prompt_refinement_chain_from_xml(
            self,
            xml_file_path,
            initial_prompt,
            agent,
            refinement_count=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            '''
            Executes prompt refinement iteratively using instructions from an XML file.
            '''
            template_string = self.load_template_from_xml_string(xml_file_path, initial_prompt)
            if not template_string:
                return None

            if display_instructions:
                logger.info('=' * 60)
                logger.info(template_string)
                logger.info('=' * 60)

            # Extract system prompt and instructions
            system_prompt, instructions_content = self._extract_template_parts(template_string)

            current_prompt = initial_prompt
            all_refinements = []

            for _ in range(refinement_count):
                messages = self._build_messages(system_prompt, instructions_content, current_prompt)

                refined_prompt = agent.generate_response(
                    messages,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                if refined_prompt:
                   all_refinements.append(refined_prompt)
                   current_prompt = refined_prompt

            return all_refinements

        def execute_prompt_refinement_by_name(
            self,
            xml_name_or_list,
            initial_prompt,
            agent,
            refinement_levels=1,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            '''
            Executes prompt refinement by XML template name (or names).
            :param xml_name_or_list: String or List[str] of template names.
            :param refinement_levels: None (use num_iterations) or List[int] (same length as xml_name_or_list).
            '''
            # --- 1) Single agent (string) scenario ---
            if isinstance(xml_name_or_list, str):
                agent_name = xml_name_or_list
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    logger.error(f"No XML file found with name: {agent_name}")
                    return None

                # If refinement_levels is int => do that many refinements
                if isinstance(refinement_levels, int):
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=refinement_levels,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                # If refinement_levels is a list => must have length 1
                elif isinstance(refinement_levels, list):
                    if len(refinement_levels) != 1:
                        raise ValueError(
                            "When using a single agent, refinement_levels list must have exactly 1 item."
                        )
                    single_count = refinement_levels[0]
                    return self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=initial_prompt,
                        agent=agent,
                        refinement_count=single_count,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                else:
                    raise TypeError("refinement_levels must be an int or a list[int].")

            # --- 2) Multiple agents (list) scenario ---
            elif isinstance(xml_name_or_list, list):
                return self._execute_prompt_chain(
                    xml_name_list=xml_name_or_list,
                    initial_prompt=initial_prompt,
                    agent=agent,
                    refinement_levels=refinement_levels,
                    display_instructions=display_instructions,
                    model_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            else:
                logger.error("Invalid 'xml_name_or_list' type. Must be str or list[str].")
                return None

        def _execute_prompt_chain(
            self,
            xml_name_list,
            initial_prompt,
            agent,
            refinement_levels,
            display_instructions,
            model_name,
            temperature,
            max_tokens,
        ):
            '''
            Executes a sequence of agents in order.
            '''
            current_prompt = initial_prompt
            all_agent_results = []

            # Interpret refinement_levels
            #  A) integer => each agent uses that many
            if isinstance(refinement_levels, int):
                iteration_counts = [refinement_levels] * len(xml_name_list)
            #  B) list => must match agent list length
            elif isinstance(refinement_levels, list):
                if len(refinement_levels) != len(xml_name_list):
                    raise ValueError("If refinement_levels is a list, it must match the length of xml_name_list.")
                iteration_counts = refinement_levels
            else:
                raise TypeError("refinement_levels must be an int or a list[int].")

            for agent_name, refinement_count in zip(xml_name_list, iteration_counts):
                xml_file_path = self.get_template_path(agent_name)
                if not xml_file_path:
                    logger.error(f"No XML file found with name: {agent_name}")
                    continue

                try:
                    refinements_for_this_agent = self.execute_prompt_refinement_chain_from_xml(
                        xml_file_path=xml_file_path,
                        initial_prompt=current_prompt,
                        agent=agent,
                        refinement_count=refinement_count,
                        display_instructions=display_instructions,
                        model_name=model_name,
                        temperature=temperature,
                        max_tokens=max_tokens,
                    )

                    if refinements_for_this_agent:
                        current_prompt = refinements_for_this_agent[-1]

                    all_agent_results.append({
                        "agent_name": agent_name,
                        "iterations": refinement_count,
                        "outputs": refinements_for_this_agent
                    })
                except Exception as e:
                     logger.error(f"Skipping agent '{agent_name}' Error:': {e}")
                     continue

            return all_agent_results

        def execute_prompt_evaluation_by_name(
            self,
            xml_name,
            prompt_to_evaluate,
            agent,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            '''
            Executes a prompt evaluation using instructions from an XML file.
            '''
            xml_file_path = self.get_template_path(xml_name)
            if not xml_file_path:
                 logger.error(f"No XML file found with name: {xml_name}")
                 return None

            template_string = self.load_template_from_xml_string(xml_file_path, prompt_to_evaluate)
            if not template_string:
                return None

            if display_instructions:
                logger.info('=' * 60)
                logger.info(template_string)
                logger.info('=' * 60)

            # Extract system prompt
            system_prompt_start = template_string.find("<system_prompt value=\"") + len("<system_prompt value=\"")
            system_prompt_end = template_string.find("\"/>", system_prompt_start)
            system_prompt = template_string[system_prompt_start:system_prompt_end]

            # Extract instructions
            instructions_start = template_string.find("<instructions>") + len("<instructions>")
            instructions_end = template_string.find("</instructions>", instructions_start)
            instructions_content = template_string[instructions_start:instructions_end]

            messages = self._build_messages(system_prompt, instructions_content, current_prompt)

            evaluation_result = agent.generate_response(
                messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return evaluation_result

    class Execution:
        '''
        Main execution class to run the prompt refinement process.
        '''
        def __init__(self, provider=None):
            self.config = Config()
            self.agent = LLMInteractions(provider=provider)
            self.template_manager = TemplateManager()
            self.output_handler = OutputHandler()

        def execute_prompt_evaluation_by_name(
            self,
            xml_name,
            prompt_to_evaluate,
            display_instructions=False,
            model_name=None,
            temperature=None,
            max_tokens=None
        ):
            '''
            Executes prompt evaluation by XML template name. (Wrapper for TemplateManager)
            '''
            return self.template_manager.execute_prompt_evaluation_by_name(
                xml_name=xml_name,
                prompt_to_evaluate=prompt_to_evaluate,
                agent=self.agent,
                display_instructions=display_instructions,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )

        def run(self):

            # =======================================================
            # LEVEL1:
            # - LEVEL 1 TEMPLATES ARE DEFINED BY EXPECTING A SINGLE-LINE INPUT-PROMPT
            # =======================================================

            # Summarize a template.xml in a single sentence
            sequence_template_sumarizexmltemplate = [
                "XMLTemplateDecoderSingleSentence",
            ]

            # Template/chain for maths/abstraction transformer
            sequence_template_abstractiontransformer = [
                # -- AbstractionTransformer --
                "MathematicalVisualizer",
                "MathematicalDetailEnhancer",
                "AnimationRefiner",
                "VisualClarityOptimizer",
                "SceneContextualizer",
                "StyleAndMoodInfuser",
                "SemanticSimplifier",
                "VisualMetaphorGenerator",
                "RunwayPromptBuilder",
            ]
            # Template/chain for maths/abstraction transformer
            sequence_template_musicalstructureanalyzer = [
                # -- MusicVisualize --
                "MusicalStructureAnalyzer",
                "HarmonicDetailer",
                "RhythmicAnimator",
                "VisualHarmonyOptimizer",
                "MusicalMetaphorGenerator",
            ]

            # Template/chain for
            sequence_template_cognitivemapper = [
                # -- CognitiveMapper --
                "KnowledgeExtractor",
                "SemanticLinker",
                "HierarchicalOrganizer",
                "VisualPathwaysGenerator",
                "KnowledgeContextualizer",
                "CognitiveStyleInfuser",
            ]

            # Template/chain for
            sequence_template_realityruleextractor = [
                # -- RealityFabricator --
                "RealityRuleExtractor",
                "DimensionGenerator",
                "PropertyManipulator",
                "DynamicSimulator",
                "PerceptionShaper",
                "RealityMetaphorGenerator",
                "RealityOutputGenerator",
            ]

            # Template/chain for
            sequence_template_universalharmony = [
                # -- UniversalHarmony --
                "UniversalRuleExtractor",
                "ScaleAndStructureGenerator",
            ]

            # Template/chain for sublime text
            sequence_template_sublimetext = [
                "SublimeTextContextTransformer",
                "SublimeTextInstructionGenerator",
                "SublimeTextInstructionEnhancer",
            ]

            # Template to condense detailed info into a singleline
            sequence_template_promptimprover = [
                "IntensityEnhancer",
                "SingleLineFormatterForced",
                "PromptEnhancer",
                "IntensityEnhancer",
                "SingleLineFormatterForced",
            ]
            # Template to extract meaning
            sequence_template_extractmeaning = [
                "IntensityEnhancer",
                "InterpretMeaning",
                "ExtractMeaning",
                "SingleLineFormatterForced",
            ]

            # Template transform text into visually compelling prompts for RunwayML
            sequence_template_runwaygenerator = [
                "EmotionallyDrivenSceneBuilder",
                "ImpactRefinement",
                "ImpactfulRestatement",
                "EmphasisEnhancer",
                # "PoteticPerfection",
                # "ImpactRefinement",
                "SpectacleInfusionAgent_a",
                "ImpactRefinement",
                "RunwayPromptBuilder_d",
                "RunwayPromptBuilder_e",
            ]

            # Using this sequence interactively when testing
            # =======================================================
            initial_prompt = '''You are a helpful assistant. You will get a prompt that you need to refine. Follow the agent's instructions to make it clearer and more concise while preserving its original intent.'''

            agent_sequence = [
                "IntensityEnhancer",
                "SingleLineFormatterForced",
                "PromptEnhancer",
                "IntensityEnhancer",
                "SingleLineFormatterForced",
            ]

            refinements = self.template_manager.execute_prompt_refinement_by_name(
                xml_name_or_list=agent_sequence,
                # xml_name_or_list=sequence_template_abstractiontransformer,
                # xml_name_or_list=sequence_template_extractmeaning,
                # xml_name_or_list=sequence_template_promptimprover,
                # xml_name_or_list=sequence_template_realityruleextractor,
                # xml_name_or_list=sequence_template_runwaygenerator,
                # xml_name_or_list=sequence_template_sublimetext,
                # xml_name_or_list=sequence_template_sumarizexmltemplate,
                # xml_name_or_list=sequence_template_universalharmony,
                # xml_name_or_list=sequence_template_cognitivemapper,
                # -- keep --
                initial_prompt=initial_prompt,
                agent=self.agent,
                refinement_levels=2,
                display_instructions=False,
                model_name=self.agent.model_name,
                temperature=self.agent.temperature,
                max_tokens=self.agent.max_tokens,
            )
            print(f'refinements: {refinements}')
            self.output_handler.display_hierarchical_refinements(refinements, header=f'"{initial_prompt}"' )

    if __name__ == "__main__":
        provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
        execution = Execution(provider=provider_to_use)
        execution.run()

    ```

Notice the effortless efficiency in use:
- No manual effort needed for new agent usage, you just need to write the name.
- Dynamic templates morph fluently with placeholders, ensuring unparalleled adaptability

Please express in a single sentence exactly what it is and what makes this particular approach for an llm framework uniquely suited for the task.
