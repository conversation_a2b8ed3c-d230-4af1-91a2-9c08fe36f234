@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: YouTube Downloader - Universal Runner
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    ECHO.
    ECHO Falling back to traditional Python execution...
    python src\main.py --prompt
    GOTO :End
)

:: Check for pyproject.toml
IF NOT EXIST "pyproject.toml" (
    ECHO [ERROR] pyproject.toml not found
    ECHO Please ensure the project is properly configured
    PAUSE>NUL & EXIT /B
)

:: Initialize environment if needed
IF NOT EXIST ".venv" (
    ECHO [INFO] Initializing uv environment...
    uv sync
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to initialize environment
        PAUSE>NUL & EXIT /B
    )
    ECHO [SUCCESS] Environment initialized
    ECHO.
)

:: Run the application
ECHO [INFO] Starting YouTube Downloader...
uv run python src\main.py --prompt

:End
ECHO.
ECHO Press any key to restart or Ctrl+C to exit...
PAUSE >NUL
CLS
GOTO :EOF
