<!-- ======================================================= -->
<!-- [2025.05.02 18:18] -->
<!-- 'https://chatgpt.com/c/6814ef94-bc00-8008-9439-bbca72fddc37' -->

```markdown
# SYSTEM MESSAGE: `project-status.md`

This **single, consolidated system message** merges and rephrases all knowledge from the original `memory-bank` directory for the **Ringerike Landskap Website** project. It is designed for **autonomous coding assistants** (e.g., Cursor AI) to:
1. Understand the project’s **root purpose and context**
2. Maintain **predictable, consistent** code evolutions
3. Ensure **structural integrity** and **root-alignment** at every step
4. Provide a **single “source of truth”** that replaces multiple separate files

This document weaves together:
- **Project Purpose & Value Context**
- **Technical Constraints & Architectural Patterns**
- **Active Process Tracking & Tasks**
- **Evolutionary Trace (Lineage)**
- **Continuous Monitoring** to detect drift from core principles

Follow each section in order to preserve the progressive abstraction approach, which starts at the project’s **root essence** (abstraction root) and expands outward toward design, process, and evolution.

> **IMPORTANT**
> 1. Never introduce breaking changes without a clear phased migration strategy.
> 2. If information is missing, prompt for critical context or architectural intents before making major changes.
> 3. Always connect new or modified code to the **root mission** detailed below.
> 4. Keep the structure clear, each concept in its correct location, to maintain a well-organized codebase.

---

## 1. Ultra-Distilled Context

- **Purpose**: Provide a digital showcase for **Ringerike Landskap**—a landscaping service emphasizing:
  - **Local, hyperfocused SEO** (Hole, Hønefoss, Jevnaker, Sundvollen, Vik)
  - **Personalized craftsmanship** (including metalwork/corten steel)
  - **Modern, responsive, and authentic** React 18 + TypeScript + Vite + Tailwind website
- **Goal**: **Connect local customers** to an authentic representation of Ringerike Landskap’s services, bridging personal trust with professional skill.

---

## 2. Project Brief

### 2.1 Root Mission
Create an authentic digital presence that:
1. **Locally** targets Ringerike region homeowners, property developers, and public clients
2. Emphasizes **seasonal** relevance, specialized craftsmanship, and personal approach
3. Converts more visitors into consultation requests (“Gratis Befaring”)

### 2.2 Value Proposition
- **Local Expertise**: Deep knowledge of Ringerike terrain
- **Personalized Service**: Free consultations, direct owner involvement
- **Specialized Skills**: Unique metalwork (corten steel), ornamental details
- **Authentic Representation**: Reflect owners’ personal investment and artistry
- **Seasonal Relevance**: Showcase which services suit Norway’s distinct seasons

### 2.3 Critical Constraints
- **Technical**
  - React 18, TypeScript, Vite, Tailwind
  - **Performance** & **Accessibility** (WCAG AA)
  - No traditional CMS, content as structured data
  - Hyperlocal SEO for the Ringerike region

- **Design**
  - Personal, simple, and authentic site aesthetic
  - Seasonal adaptation in visuals & content
  - Trust-building through strong testimonials

- **Business**
  - 8 core landscaping services, each must be clarified
  - Seasonal marketing emphasis
  - Goal: Convert visitors into requests for free consultation

### 2.4 Key Features/Requirements
1. **“Book Gratis Befaring”** CTA
2. 8 core services with seasonal filtering
3. Project showcase with filtering by type & location
4. Testimonials & trust signals
5. Service area emphasis (local map, mention of Ringerike specifics)
6. Seasonally adaptive content (Spring, Summer, Fall, Winter)
7. Specialized skill highlights (corten steel)
8. Mobile-first responsiveness

### 2.5 Success Metrics
- Increased number of consultation requests
- Higher local search ranking (hyperlocal SEO)
- Positive feedback on authenticity & seasonally relevant content
- Higher conversion rate from website visits to leads

---

## 3. Contextual Layers

### 3.1 Product Context

#### 3.1.1 User Personas
1. **Local Homeowners** (Primary, 30-65)
   - Pain: Finding skilled local landscapers
   - Seasonal & budget constraints

2. **Property Developers** (Secondary)
   - Pain: Reliable contractors with local terrain/regulatory knowledge
   - Emphasis on cost-effectiveness & documentation

3. **Public/Municipal Clients** (Tertiary)
   - Formal RFP processes, strict requirements

#### 3.1.2 Problems Solved
For **Customers**:
- Clear guidance on seasonally appropriate projects
- Verified local knowledge (soil, climate, terrain)
- Visual portfolio for trust-building

For **Ringerike Landskap**:
- **Geographic Focus** in the Ringerike region
- Clear communication of 8 core services
- Specialized skill differentiation

#### 3.1.3 Operational Context
- **Service Areas**: Hole, Hønefoss, Sundvollen, Vik, Jevnaker
- **Seasonal Operation**:
  - Spring: Cleanup, lawn prep
  - Summer: Patio, horticulture, major projects
  - Fall: Winterizing, finishing tasks
  - Winter: Snow or planning phase

### 3.2 Technical Context

#### 3.2.1 Core Stack
- **Frontend**: React 18 (functional components, hooks)
- **TypeScript**: Strict mode, typed data structures
- **Vite**: Build & dev server
- **Tailwind CSS**: Utility-first styling
- **React Router**: Nested routing in Norwegian

#### 3.2.2 Performance Constraints
- Core Web Vitals (LCP <2.5s, FID <100ms, CLS <0.1)
- Main bundle <200kb
- Lazy loading, code splitting, responsive image usage

#### 3.2.3 Accessibility
- WCAG AA minimum
- Semantic HTML, correct ARIA usage, keyboard navigation

#### 3.2.4 Responsive Requirements
- Mobile-first breakpoints
- Properly sized images & accessible touch targets

#### 3.2.5 SEO
- Local SEO emphasis, schema.org metadata
- Lightning-fast load times, mobile-friendliness

#### 3.2.6 State Management
- Primarily hooks & local state, minimal context usage
- Simulated API layer for data fetching, no global Redux store

#### 3.2.7 Future Roadmap
- Potential SSR for SEO improvements
- Enhanced test coverage & analytics
- Automated CI/CD pipeline

---

## 4. Structure & Design Patterns

### 4.1 System Patterns

#### 4.1.1 Component Organization
- **Feature-First + Chronological** for sections (`sections/10-home`, `sections/20-about`, etc.)
- **UI** folder for atomic components (Buttons, Cards, etc.)
- **Layout** folder for shared layout (Header, Footer)
- **Data** & **API** simulation in `lib/api` and `data/`

#### 4.1.2 Composition Hierarchy
1. **Atomic UI** (`ui/`)
2. **Layout** (`layout/`)
3. **Feature Components** (`sections/XX-feature/`)
4. **Page Components** (`sections/XX-feature/index.tsx`)
5. **App Shell** (`app/index.tsx`)

#### 4.1.3 Data Flow
```

Static TS Files → lib/api/ (Simulated API) → Hooks → Components → UI

````
- **Unidirectional** data pass
- Minimal local state or context usage for ephemeral UI

#### 4.1.4 Seasonal Adaptation Pattern
- `lib/utils/seasonal.ts` detects current Norwegian season
- Filters services/projects & changes hero images accordingly

#### 4.1.5 SEO Implementation
- Page-specific `<head>` meta (using `react-helmet-async` or similar)
- JSON-LD structured data for services, testimonials
- Local SEO strategies (regional keywords, location markup)

### 4.2 Structure Map

#### 4.2.1 Current Structure
- Clear multi-root design:
  1. `config/` (central configs)
  2. `tools/` (dev tools, screenshots, etc.)
  3. `website/` (website code)
  4. `dist/` (build outputs)
  5. `www/` (production site)

- Inside `website/src/`:
  - `app/`, `sections/`, `ui/`, `layout/`, `data/`, `lib/`…

#### 4.2.2 Target Structure Refinements
- Eliminate any redundant subdirectories (e.g., `tools/tools/`)
- Standardize configuration references (website to config)
- Consolidate scattered docs into clearer docs areas
- Keep root-level environment files to a minimum

### 4.3 Simplification Candidates

| ID       | Candidate                                         | Score | Status       |
|----------|---------------------------------------------------|-------|-------------|
| SIM-001  | Consolidate `tools/tools/` directory              | 11.5  | Identified  |
| SIM-002  | Standardize config references                     | 13.0  | Identified  |
| SIM-003  | Consolidate docs (project-level vs. website-level)| 10.5  | Identified  |
| SIM-004  | Move root configs to `config/`                    | 10.5  | Identified  |
| SIM-005  | Clarify `scripts/` directory purpose              | 8.0   | Identified  |
| SIM-006  | Consolidate utility functions                     | 8.5   | Identified  |

Each of these is tracked for **impact** vs. **effort** and connected to the **root mission** (maintaining clarity and authentic representation).

---

## 5. Process Tracking

### 5.1 Active Context

#### 5.1.1 Focus Areas
1. Finalizing the **Memory Bank** concept in a single doc (this doc)
2. Ensuring structural integrity without immediate large refactors
3. Identifying improvement areas systematically

#### 5.1.2 Bottlenecks
- Verifying that config references are consistent
- Investigating `tools/tools/` redundancy
- Aligning docs into a single reference point

### 5.2 Progress Tracking

| Milestone                                   | Status     | Date       | Description                                                         |
|--------------------------------------------|-----------|-----------|---------------------------------------------------------------------|
| **Memory Bank Initialization**             | Completed | 2025-05-02 | Consolidated all essential context into a single knowledge system   |
| **Structure Inventory**                    | Completed | 2025-05-02 | Comprehensive analysis vs. canonical rules                          |
| **System Patterns Documentation**          | Completed | 2025-05-02 | Identified core architectural & design patterns                     |
| **Initial Improvement Areas Identified**   | Completed | 2025-05-02 | Potential issues & tasks tracked                                    |

#### 5.2.1 Simplification Log

| Date       | Area                     | Before                              | After                                        | Impact                                                  |
|------------|--------------------------|-------------------------------------|----------------------------------------------|----------------------------------------------------------|
| 2025-05-02 | Memory Bank             | No single knowledge repository      | Unified progressive doc (this file)          | Consistent, root-aligned awareness                      |
| 2025-05-02 | Structure Documentation | Implicit structure knowledge        | Explicit structure map & best practices      | Faster navigation & clarity in future tasks             |

#### 5.2.2 Technical Debt Ledger

| Category       | Item                                         | Status     | Priority | Notes                                       |
|----------------|----------------------------------------------|-----------|----------|---------------------------------------------|
| **Structure**  | `tools/tools/` redundant directory           | Identified| Medium   | Potential duplication to be consolidated    |
| **Config**     | Root config files vs. `config/` location     | Identified| Medium   | Progressively unify                         |
| **Docs**       | Docs scattered in multiple folders           | Identified| Low      | Centralization plan needed                  |
| **Structure**  | Config references need clarity               | Identified| Medium   | Evaluate approach to referencing central    |
| **Code**       | Potential utility duplication                | Identified| Low      | Check overlaps in `lib/utils` vs. flatten   |

### 5.3 Tasks

| ID      | Task                                                   | Status      | Priority | Root Connection           | Due      |
|---------|--------------------------------------------------------|------------|----------|---------------------------|----------|
| MB-001  | **Complete** single consolidated doc (this file)       | In Progress| High     | Project understanding     | 2025-05-03 |
| MB-002  | Create simplified improvement tracking (scoring, etc.) | Not Started| Medium   | Structure clarity         | 2025-05-04 |
| MB-003  | Finalize lineage doc template usage                    | Not Started| Medium   | Evolution tracking        | 2025-05-04 |
| MB-004  | Set up drift monitoring plan                           | Not Started| Medium   | Structure integrity       | 2025-05-04 |

#### 5.3.1 Upcoming Tasks

| ID       | Task                                                   | Priority  | Dependencies | Estimated Effort |
|----------|--------------------------------------------------------|-----------|-------------|------------------|
| STR-001  | Investigate `tools/tools/` redundancy                  | Medium    | MB-001      | 2h               |
| STR-002  | Audit website config references to central configs     | Medium    | MB-001      | 3h               |
| STR-003  | Develop doc consolidation strategy                     | Low       | MB-001      | 2h               |
| STR-004  | Plan root config file migration to `config/`           | Medium    | MB-001      | 2h               |
| STR-005  | Analyze `scripts/` dir purpose & unify or clarify      | Low       | MB-001      | 1h               |
| STR-006  | Check for utility function duplication                 | Low       | MB-001      | 3h               |

---

## 6. Drift Monitor

Used to track unexpected deviations from the **canonical** structure.

| Category            | Rule                                               | Status       | Last Verified |
|---------------------|----------------------------------------------------|-------------|--------------|
| **Project Dirs**    | Dev tools in `tools/`                              | ✓ Compliant | 2025-05-02   |
| **Project Dirs**    | Website code in `website/`                         | ✓ Compliant | 2025-05-02   |
| **Project Dirs**    | Config in `config/`                                | ⚠ Partial   | 2025-05-02   |
| **Website Structure** | Source code in `website/src/`                     | ✓ Compliant | 2025-05-02   |
| **Website Structure** | Sections in numbered directories                   | ✓ Compliant | 2025-05-02   |
| **Configuration**   | Website config extends central config              | ⚠ Unverified| 2025-05-02   |

### 6.1 Drift Alerts

| Date       | Area           | Description                                        | Severity | Status      |
|------------|---------------|----------------------------------------------------|----------|------------|
| 2025-05-02 | Configuration  | Some configs remain at root instead of `config/`   | Low      | Identified |
| 2025-05-02 | Tools          | Redundant `tools/tools/` directory                 | Low      | Identified |
| 2025-05-02 | Docs           | Documentation in multiple locations                | Low      | Identified |
| 2025-05-02 | Config Refs    | References to central configs unverified           | Medium   | Identified |

---

## 7. Evolution & Lineage

### 7.1 Lineage Documentation Template

When making **significant transformations**, create an entry following this format:

```markdown
# [Title of the Essential Transformation]

**Date**: YYYY-MM-DD
**Author**: [Author Name]
**Category**: [Architectural | Structural | Pattern | Conceptual | Technical]

## Context
[Describe the situation/problem prior to change]

## Insight
[Key realization or understanding leading to change]

## Essential Transformation
[Detail the specific modification implemented]

### Before
[Concrete depiction of old state]

### After
[Concrete depiction of new state]

## Impact
- **Technical Impact**
- **Conceptual Impact**
- **Process Impact**

## Justification
[Why this was right, root mission alignment, alternatives considered]

## Connection to Root Purpose
[Explicit link to the project’s mission from the Project Brief]

## Lessons Learned
[Insights gained, patterns discovered]

## Related Transformations
[List any related lineage entries]

## Reference Materials
[Attach or link relevant external references]
````

### 7.2 Example: Memory Bank Establishment

```markdown
# Memory Bank Establishment
**Date**: 2025-05-02
**Author**: AI
**Category**: Structural, Foundational

## Context
Previously, info was scattered across random docs. The project lacked a structured, central knowledge base.

## Insight
Realized that progressive abstraction layering fosters root-first consistency and prevents drift.

## Essential Transformation
Created a single “Memory Bank” with systematically numbered files that build context, culminating in one consolidated doc.

### Before
Documentation scattered in README, partial lineage, or ad-hoc notes.

### After
Unified all references into one cohesive resource, referencing the root purpose throughout.

## Impact
- **Technical**: Single source of truth for architecture & process
- **Conceptual**: Ensures decisions always tie back to the root mission
- **Process**: Simplifies onboarding, fosters better cross-team alignment

## Justification
Directly aligns with the root purpose of authenticity. Eliminates guesswork and confusion.

## Connection to Root Purpose
Maintains structural integrity and keeps everything aligned to the Ringerike Landskap mission.

## Lessons Learned
Numbered, layered documentation is crucial in large codebases.

## Related Transformations
- Preceded by initial code structure standardization
- Will inform future simplification tasks

## Reference Materials
- Original scattered docs, Slack discussions, rules doc
```

---

## 8. Conclusion & Usage Guidelines

### 8.1 Root-First Thinking

Always anchor decisions to the **root mission**: an authentic digital presence focusing on local landscaping services, personal approach, specialized craft, and seasonal adaptability.

### 8.2 Structural Guardrails

1. Maintain a **clear project structure** (website code vs. dev tools vs. config).
2. Keep docs in **one reference** (this consolidated file or an updated lineage doc).
3. Align new features or changes with the **seasonal** and **local** emphasis.

### 8.3 Value Extraction Bias

Prioritize **simplification** and **pattern extraction**. Each improvement must reduce complexity and strengthen the project’s authenticity.

### 8.4 Operating Protocols

1. **Lineage Awareness**: Document major transformations with the lineage template.
2. **Compression Reflex**: Abstract recurring patterns to the simplest robust form.
3. **Drift Monitoring**: Regularly check for divergence from structure.

### 8.5 Final Word

This single `project-status.md` now represents the **entire memory bank** and **system instructions**. All future additions or modifications must reference this doc to preserve integrity, alignment, and clarity.

```
```
