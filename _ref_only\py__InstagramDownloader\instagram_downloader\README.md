# Instagram Downloader

## Overview
Instagram content downloader with rich CLI interface, supporting posts, reels, and IGTV videos.

## Features
- **Multiple download formats**: Video + Images, Video Only, Images Only, with compression options
- **Clipboard integration**: Auto-detects Instagram URLs from clipboard
- **Rich progress tracking**: Real-time download progress with visual indicators
- **Interactive CLI**: User-friendly prompts and selections
- **Structured logging**: Comprehensive logging with automatic cleanup
- **Error handling**: Graceful handling of Instagram blocking with alternative solutions

## Quick Start
Run `run.bat` to start the interactive downloader (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "URL1" "URL2" -op "output/path"
```

## Additional Tools
- `instaloader_upgrade.bat` - Upgrade instaloader to the latest version

## Dependencies
Managed via `pyproject.toml` with uv package manager.
