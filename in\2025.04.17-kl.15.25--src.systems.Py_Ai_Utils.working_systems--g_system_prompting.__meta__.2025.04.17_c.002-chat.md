Prepare yourself for creating a *new* sequence that is designed for applying these concepts into *Codebase Familiarization* and *Codebase-Cleanup* by the following principles:

    ```
    ## General Principles
    - Aim for simplicity, clarity, and maintainability in all project aspects.
    - Favor *cohesive* design over *generic* inheritance.
    - Prioritize inherent readability and understandability for future developers.
    - Ensure all components have a single responsibility.
    - Transparency and Minimalism: Reducing visual clutter for clarity and efficiency
    - Highly refined project structures: Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation.
    - Optimizing both the architecture and user experience.
    -

    ## Code Organization
    - Evaluate the existing codebase structure and identify patterns and anti-patterns.
    - Consolidate related functionality into cohesive modules.
    - Minimize dependencies between unrelated components.
    - Optimize for developer ergonomics and intuitive navigation.
    - Balance file granularity with overall system comprehensibility.
    - Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure)

    ## Important Considerations
    - How the file structure could more inherently embody the concepts it represents (e.g. dynamic hierarchical organization that corresponds with the natural order of operations.
    - How directory and file naming can reflect the sequence of operations.
    - The importance of clear boundaries between workflow stages.
    - How the structure can guide new developers in understanding the system.
    ```

---

Familiarize yourself with the structure of these examples:

    ```
    1.  **[Foundational Analysis & Principle Mapping]**
        * **Objective:** Radically immerse in the codebase and project structure to deeply understand its current state: logic flow, component interactions, dependencies, structural patterns (or anti-patterns), and existing comment strategies. Map findings against the core principles (Simplicity, Clarity, Cohesion, SRP, Minimalism, Self-Explanation).
        * **Execute as:** `{role=foundational_analyzer; input=codebase_root:path; process=[map_project_structure_and_dependencies(), trace_core_logic_flows(), analyze_component_cohesion_and_responsibility(), assess_naming_convention_clarity(), audit_comment_usage_vs_necessity(), identify_principle_violations_and_complexity_hotspots()]; output={analysis_summary:dict(structure_map:any, dependency_issues:list, logic_complexity_zones:list, naming_concerns:list, comment_assessment:dict, principle_violations:list)}}`

    2.  **[Strategic Simplification Blueprint]**
        * **Objective:** Formulate a precise, prioritized, actionable plan based on the analysis to refactor the codebase towards elegant simplicity and structural self-explanation. Define clear targets for structural reorganization, logic simplification, naming refinement, and comment minimization, ensuring the plan preserves original functionality.
        * **Execute as:** `{role=simplification_strategist; input=analysis_summary:dict; process=[prioritize_refactoring_targets_by_impact_on_clarity(), devise_minimalist_structural_reorganization_plan(), specify_logic_consolidation_and_simplification_steps(), define_clear_and_consistent_naming_improvements(), establish_comment_refinement_strategy(policy='minimal_concise_essential'), map_actions_to_functional_constraints()]; output={cleanup_blueprint:list_of_prioritized_tasks}}`

    3.  **[Structural Elegance Realignment]**
        * **Objective:** Execute the structural reorganization part of the blueprint. Refactor the directory and file structure to inherently mirror the conceptual organization and logical flow of the system. Consolidate cohesive elements, decouple unrelated ones, and apply clear, sequential naming to structural components.
        * **Execute as:** `{role=structure_realigner; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_directory_file_restructuring(), group_related_functionality_cohesively(), minimize_cross_component_dependencies(), apply_structure_naming_conventions(), update_references_and_imports()]; output={realigned_codebase_structure:path}}`

    4.  **[Intrinsic Code Clarification & Simplification]**
        * **Objective:** Execute the code-level refactoring defined in the blueprint. Simplify algorithms, consolidate redundant logic, strictly enforce the Single Responsibility Principle, and refactor names (variables, functions, classes) for maximum clarity and self-explanation within the newly aligned structure.
        * **Execute as:** `{role=code_simplifier; input={realigned_codebase_structure:path, cleanup_blueprint:list}; process=[apply_self_explanatory_naming_conventions(), implement_logic_simplification_and_consolidation(), refactor_components_for_single_responsibility(), ensure_clear_logical_flow_within_files(), remove_dead_or_unreachable_code()]; output={clarified_codebase:path}}`

    5.  **[Essential Comment Rationalization]**
        * **Objective:** Rigorously apply the 'minimal_concise_essential' comment policy. Remove comments that merely restate the code or are made obsolete by structural and naming improvements. Refine remaining necessary comments to be extremely concise, adding only vital context or explaining unavoidable complexities.
        * **Execute as:** `{role=comment_rationalizer; input={clarified_codebase:path, comment_policy:str}; process=[identify_comments_redundant_with_code_clarity(), purge_obsolete_or_unnecessary_comments(), refine_essential_comments_for_brevity_and_precision(), ensure_comments_supplement_not_supplant_understanding()]; output={comment_rationalized_codebase:path}}`

    6.  **[Holistic Validation & Integrity Check]**
        * **Objective:** Verify that the refactored codebase is 100% functionally equivalent to the original. Critically assess the final state against all target principles: Is it simpler? Is it clearer? Is the structure self-explanatory? Are comments minimal and essential? Perform a final consistency and polish pass.
        * **Execute as:** `{role=integrity_validator; input={original_codebase_state:any, comment_rationalized_codebase:path}; process=[perform_functional_equivalence_verification(), audit_final_state_against_core_principles(), check_for_consistent_style_and_formatting(), conduct_final_readability_and_maintainability_review()]; output={validated_codebase:path, validation_report:dict(functional_equivalence:bool, principle_adherence_score:float, remaining_concerns:list)}}`

    7.  **[Recursive Refinement Assessment]**
        * **Objective:** Evaluate the `validation_report` and the `validated_codebase` to determine if further cleanup cycles are warranted. Identify any remaining areas where principles could be more deeply applied or where complexity persists, potentially initiating a new cycle starting from Step 1 with a more focused scope.
        * **Execute as:** `{role=refinement_assessor; input={validated_codebase:path, validation_report:dict}; process=[analyze_remaining_concerns_and_complexity(), assess_potential_for_further_simplification(), evaluate_cost_benefit_of_another_cycle(), determine_necessity_and_scope_for_next_iteration()]; output={next_cycle_decision:dict(proceed:bool, focus_areas:list)}}`
    ```

---

Please consolidate and write a new sequence of instructions titled `Generalized Codebase Cleanup`, *specifically* for designed for inherently reusable generalized autonomous codebase cleanup (including the projectstructure itself) - this sequence should aim to *simplify* rather than *complicate*. Rather than unneccessary complexity/verbosity, choose *brilliantly designed elegance (through inherent simplicity)*.  Use the provided examples for reference/inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup. Remember, each step in the sequence should build chronologically-recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency.

---

Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps), make sure the sequence is maximally optimized and enhanced based on the provided info (notes/examples/directive) inherently provided within this message. Provide the instructions in this kind of format (example):

    #### `src/templates/lvl1/md/0126-a-holistic-structure-purpose-scan.md`

    ```markdown
    [Holistic Structure & Purpose Scan] Your primary directive is rapid, holistic reconnaissance: Analyze the input codebase/project structure. Inventory key components (files, modules, classes), map high-level dependencies, and distill the fundamental purpose or core value proposition of the system, establishing a baseline understanding. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_key_components(), map_major_dependencies(), analyze_entry_points_and_outputs(), distill_primary_purpose()]; output={structure_overview:dict, primary_purpose:str}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-b-core-workflow-logic-path-tracing.md`

    ```markdown
    [Core Workflow & Logic Path Tracing] Trace the primary execution or data processing workflows essential to the `primary_purpose`. Map how control and data flow between the key components identified in `structure_overview`, highlighting critical paths, interactions, and potential complexity hotspots. Execute as `{role=workflow_tracer; input={structure_overview:dict, primary_purpose:str, codebase_content:dict}; process=[identify_key_workflows(), trace_control_flow_for_workflows(), map_data_transformations_across_components(), visualize_core_logic_paths()]; output={workflow_maps:list[dict]}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-c-clarity-self-explanation-audit.md`

    ```markdown
    [Clarity & Self-Explanation Audit] Evaluate the codebase against principles of 'Maximal Clarity & Conciseness'. Assess inherent readability based on identifier naming quality, logical flow simplicity within `workflow_maps`, and overall structural self-explanation. Identify areas where clarity currently relies heavily on commentary rather than transparent code design. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list}; process=[evaluate_identifier_naming_effectiveness(), assess_logical_flow_simplicity_in_workflows(), determine_code_self_explanation_level(), pinpoint_comment_dependent_clarity_zones()]; output={clarity_report:dict(naming_score:float, flow_score:float, self_explanation_notes:str)}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-d-essential-commentary-isolation.md`

    ```markdown
    [Essential Commentary Isolation] Rigorously apply the 'Essential Commentary Only' principle. Analyze all existing comments. Isolate and preserve *only* those comments providing critical explanations of non-obvious logic/intent ('why') or vital architectural guidance. Flag all other comments (redundant, obvious 'what'/'how', verbose, outdated, misplaced interface docs) for removal. Execute as `{role=essential_comment_filter; input=codebase_content:dict; process=[inventory_all_comments(), classify_against_essential_only_criteria(focus_on_why), flag_non_essential_comments_for_removal(), audit_docstring_usage_for_interfaces()]; output={comment_cleanup_candidates:list[dict(location:str, reason:str)]}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-e-simplification-cohesion-opportunity-scan.md`

    ```markdown
    [Simplification & Cohesion Opportunity Scan] Scan the codebase, informed by `clarity_report` and `workflow_maps`, for opportunities aligning with 'Optimized Implementation' and 'Structural Purity'. Identify overly complex logic, redundancy (dead code, duplication), poor cohesion (modules/classes needing regrouping), or unclear interfaces ripe for simplification and structural refinement. Execute as `{role=simplification_scanner; input={codebase_content:dict, clarity_report:dict, workflow_maps:list}; process=[detect_complex_logic_hotspots(), identify_redundant_dead_code_patterns(), assess_module_class_cohesion(), pinpoint_unclear_interfaces(), list_simplification_refinement_opportunities()]; output={cleanup_opportunities:dict(simplification:list, structure:list, dead_code:list)}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-f-prioritized-cleanup-action-plan-generation.md`

    ```markdown
    [Prioritized Cleanup Action Plan Generation] Synthesize all findings (`comment_cleanup_candidates`, `cleanup_opportunities`, potential clarity improvements from `clarity_report`). Generate a prioritized, actionable cleanup plan focusing on safety and impact. Prioritize removing flagged comments/dead code, followed by high-value/low-risk refactoring for clarity (naming, simple structure) and simplification. *Output the plan itself, not modified code.* Execute as `{role=cleanup_strategist_planner; input=all_previous_outputs:dict; process=[consolidate_all_cleanup_candidates(), rank_actions_by_safety_then_impact(), prioritize_removals_and_clarifications(), define_specific_actionable_steps(), structure_as_implementation_plan()]; output={actionable_cleanup_plan:str}}`
    ```

    ---

    #### `src/templates/lvl1/md/0126-g-cleanup-plan-rationale-outcome-summary.md`

    ```markdown
    [Cleanup Plan Rationale & Outcome Summary] Provide a concise summary report for the `actionable_cleanup_plan`. Articulate the rationale, emphasizing the targeted improvements based on the core principles (clarity, simplicity, essential commentary, structure). Clearly state the expected benefits in maintainability, readability, reduced complexity, and overall codebase elegance. Execute as `{role=cleanup_reporter; input={actionable_cleanup_plan:str}; process=[summarize_plan_rationale_based_on_principles(), articulate_prioritization_logic(safety_impact), detail_expected_benefits_post_cleanup(), finalize_summary_report()]; output={cleanup_plan_report:str}}`
    ```
