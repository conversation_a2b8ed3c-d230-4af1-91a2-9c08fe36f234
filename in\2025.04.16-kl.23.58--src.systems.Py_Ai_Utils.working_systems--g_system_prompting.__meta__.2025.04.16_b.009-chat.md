
# Goal:
Given our current conversation and your attained knowledge about the format of these sequential system instructions, use this knowledge create a "meta" sequence designed to leverage this exact format/system to accept *any* input and produce a new set of generalized maximally optimized instruction sequence based on the context in the input. If the input contains specifications about the number of instructions in the sequence it should use that, but if it's not provided it should automatically adjust the number of steps in the sequence autonomously based on the input.

# Objective:
Consolidate and transform all of the provided instruction sequences into *one* single *fully optimized* sequence:

---

    #### `0108-a-input-context-analysis-goal-extraction.md`

    ```markdown
    [Input Context Analysis & Goal Extraction] Your first directive is to deeply analyze the provided input context, extracting its core transformational goal, essential constraints (including any specified step count), key entities, and desired output characteristics. Execute as `{role=context_analyzer; input=input_context:any; process=[identify_core_goal(), extract_constraints_and_parameters(), determine_target_transformation_scope(), check_for_specified_step_count()]; output={analysis_summary:dict(goal:str, constraints:list, scope:str, specified_steps:int|None)}}`
    ```

    #### `0108-b-core-transformation-stage-definition.md`

    ```markdown
    [Core Transformation Stage Definition] Based on the analyzed goal and scope, define the fundamental, high-level transformation stages necessary to achieve the objective (e.g., Deconstruct, Analyze, Synthesize, Validate, Finalize). Map the core logic flow that the target sequence must implement. Execute as `{role=stage_definer; input=analysis_summary:dict; process=[determine_essential_logical_phases(), map_macro_process_flow(), outline_stage_objectives()]; output={transformation_stages:list[str], macro_flow:dict}}`
    ```

    #### `0108-c-optimal-step-count-determination.md`

    ```markdown
    [Optimal Step Count Determination] Determine the optimal number of granular instruction steps for the target sequence. If a count was specified in the input (`analysis_summary.specified_steps`), adhere to it. Otherwise, autonomously derive the count (typically 5-15 steps) based on the complexity of the `transformation_stages` and the need for clear, incremental progression toward the `analysis_summary.goal`. Execute as `{role=step_counter; input={analysis_summary:dict, transformation_stages:list}; process=[use_specified_count_if_available(), else_derive_optimal_count_from_complexity(stages, goal), validate_count_feasibility()]; output={optimal_step_count:int}}`
    ```

    #### `0108-d-instruction-drafting-initial-stages.md`

    ```markdown
    [Instruction Drafting - Initial Stages] Draft the initial instruction steps (approximately the first half based on `optimal_step_count`) for the new target sequence. For each step, define a potent `[TITLE]`, concise `Interpretive Statement`, and precise `Execute as {...}` block (role, input, process, output), ensuring rigorous alignment with the corresponding `transformation_stages`, LLM optimization, maximal generalization, and high-value, directive language. Execute as `{role=step_drafter_initial; input={transformation_stages:list, optimal_step_count:int, analysis_summary:dict}; process=[design_step_logic_for_stage(), formulate_title_statement(), define_role_io_process(), ensure_llm_optimization_and_generalization(), draft_initial_steps()]; output={drafted_steps_part1:list[dict]}}`
    ```

    #### `0108-e-instruction-drafting-concluding-stages.md`

    ```markdown
    [Instruction Drafting - Concluding Stages] Draft the remaining instruction steps for the new target sequence, ensuring seamless logical continuation from `drafted_steps_part1`. Maintain strict adherence to format, LLM optimization, generalization, and potent language. Ensure these final steps comprehensively cover necessary validation phases and demonstrably achieve the target `analysis_summary.goal`. Execute as `{role=step_drafter_concluding; input={drafted_steps_part1:list, transformation_stages:list, optimal_step_count:int, analysis_summary:dict}; process=[design_concluding_step_logic(), maintain_consistency_with_part1(), ensure_goal_achievement_and_validation_coverage(), draft_concluding_steps()]; output={drafted_steps_full:list[dict]}}`
    ```

    #### `0108-f-sequence-coherence-flow-refinement.md`

    ```markdown
    [Sequence Coherence & Flow Refinement] Review the complete `drafted_steps_full` as an integrated sequence. Refine inter-step transitions, enforce consistent terminology and abstraction levels across all steps, rigorously verify the logical progression and dependency chain, and confirm escalating, cumulative value generation towards the final objective defined in `analysis_summary`. Execute as `{role=sequence_coherence_refiner; input={drafted_steps_full:list[dict], analysis_summary:dict}; process=[analyze_step_transitions(), ensure_terminological_consistency(), validate_logical_flow_and_dependency(), check_cumulative_value_build_up(), refine_sequence_holistically()]; output={coherent_draft_sequence:list[dict]}}`
    ```

    #### `0108-g-step-level-optimization-potency-clarity.md`

    ```markdown
    [Step-Level Optimization (Potency & Clarity)] Optimize *each individual instruction step* within the `coherent_draft_sequence`. Intensify language using high-value imperatives for maximum potency and clarity, sharpen process definitions within the `Execute as` block, ensure explicit roles/IO, systematically eliminate *any* potential ambiguity, and strictly enforce all established LLM-optimization and generalization requirements. Execute as `{role=step_optimizer; input=coherent_draft_sequence:list[dict]; process=[refine_step_language_for_potency(), sharpen_process_definitions(), eliminate_all_ambiguity(), enforce_strict_llm_optimization_rules(), optimize_each_step_individually()]; output={optimized_steps:list[dict]}}`
    ```

    #### `0108-h-final-sequence-validation-constraint-check.md`

    ```markdown
    [Final Sequence Validation & Constraint Check] Perform a final, rigorous validation of the complete `optimized_steps` sequence against the original `analysis_summary` (goal, constraints, scope). Confirm it constitutes a maximally effective, generalized, potent, logically sound, and fully compliant instruction set, perfectly architected to achieve the target transformation. Execute as `{role=final_sequence_validator; input={optimized_steps:list[dict], analysis_summary:dict}; process=[validate_goal_alignment_and_completeness(), check_strict_constraint_adherence(), confirm_effectiveness_and_generalization(), verify_overall_quality_and_potency()]; output={validated_sequence:list[dict]}}`
    ```

    #### `0108-i-optimized-sequence-packaging.md`

    ```markdown
    [Optimized Sequence Packaging] Package the `validated_sequence` into the final, standardized list-of-dictionaries format. Ensure all components (`[TITLE]`, `Statement`, `Execute as {...}`) within each step are correctly structured, syntactically valid, and ready for deployment or direct use by an LLM or parsing system. Execute as `{role=sequence_packager; input=validated_sequence:list[dict]; process=[structure_final_output_list(), ensure_data_integrity_and_format_compliance(), finalize_packaging_for_deployment()]; output={final_optimized_sequence:list[dict]}}`
    ```

---

    #### `0109-a-input-scan-requirements-analysis.md`

    ```markdown
    [Input Scan & Requirements Analysis] Your task is not to transform the input directly, but to examine it for context, constraints, and user directives—specifically any mention of a preferred sequence length or structural requirements. Interpretation: Reads the raw input or specification to extract relevant data, especially **target** step counts or any domain constraints that might affect the final instruction sequence. Execute as `{role=scan_analyzer; input=[raw_input:any]; process=[parse_input_for_explicit_step_count(), identify_contextual_clues_and_constraints(), detect_subject_matter_or_focus(), compile_scan_findings()], output={scan_summary:dict}}`
    ```

    #### `0109-b-core-purpose-crystallization.md`

    ```markdown
    [Core Purpose Crystallization] Your objective is not to replicate or restate the input, but to isolate the single unifying goal or driving objective behind the new instruction set. Interpretation: Takes the **scan findings** and focuses them into one **concise** statement that clarifies exactly **why** we’re building the new instruction sequence. Execute as `{role=purpose_crystallizer; input=[scan_summary:dict]; process=[distill_unifying_intent(), express_purpose_in_unambiguous_form(), confirm_alignment_with_input_requirements()], output={core_objective:str}}`
    ```

    #### `0109-c-dynamic-step-determination.md`

    ```markdown
    [Dynamic Step Determination] Your goal is not static enumeration, but to decide how many instructions the final sequence will contain—adhering to user-specified counts if provided, else inferring an optimal step count. Interpretation: Determines how many steps the new set of instructions will contain, based on user’s request or context gleaned from the input. Execute as `{role=step_determiner; input={scan_summary:dict, core_objective:str}; process=[check_for_explicit_step_count_in_scan(), infer_optimal_step_range_based_on_scope_and_complexity(), finalize_step_quantity(), log_step_decision()], output={sequence_config:dict}}`
    ```

    #### `0109-d-instruction-structure-synthesis.md`

    ```markdown
    [Instruction Structure Synthesis] Your function is not random listing but the planned layout: generate a high-level skeleton of steps based on the determined step count, each addressing a distinct transformation phase. Interpretation: Lays out **placeholder** steps or phases that collectively drive the final transformations in a **logical** progression, matching the context and user constraints. Execute as `{role=structure_synthesizer; input={sequence_config:dict, core_objective:str}; process=[split_sequence_into_distinct_phases(), define_each_phase_focus_and_role(), align_phases_with_input_context(), produce_initial_skeleton()], output={phase_skeleton:list}}`
    ```

    #### `0109-e-element-curation-and-priority-filtering.md`

    ```markdown
    [Element Curation & Priority Filtering] Your task is not to preserve all placeholders equally, but to refine them—isolating the highest-yield instructions for each phase. Interpretation: Within each proposed phase, we figure out which instructions or transformations matter most—removing or merging any that add little or no value. Execute as `{role=curation_refiner; input=[phase_skeleton:list]; process=[identify_must_have_instructions(), rank_by_transformative_impact(), remove_low_value_overlaps(), finalize_curated_steps()], output={curated_steps:list}}`
    ```

    #### `0109-f-linguistic-potency-and-formatting-infusion.md`

    ```markdown
    [Linguistic Potency & Formatting Infusion] Your role is not neutral rewriting but forceful refinement: inject each curated step with concise, action-driven language and ensure it follows the `[TITLE] short statement + { transformation block }` template. Interpretation: Takes the curated steps and rewrites them using consistent, high-impact phrasing, guaranteeing the standard system-message format at each step. Execute as `{role=potency_formatter; input=[curated_steps:list]; process=[apply_action_verbs_and_imperatives(), enforce_short_but_powerful_titles(), insert_role_input_process_output_blocks(), confirm_uniform_structure()], output={formatted_steps:list}}`
    ```

    #### `0109-g-domain-neutrality-and-universalization.md`

    ```markdown
    [Domain Neutrality & Universalization] Your objective is not domain specificity, but cross-context adaptability: revise the newly formatted steps, removing domain-limited wording to ensure universal applicability. Interpretation: Ensures that no matter the subject (code, text, or broader tasks), the resulting instructions remain *universally generalizable*, with no domain-based jargon. Execute as `{role=domain_neutralizer; input=[formatted_steps:list]; process=[detect_specialized_terms(), reframe_in_universal_abstract_concepts(), preserve_core_functionality_and_logic(), certify_cross_domain_relevance()], output={universal_steps:list}}`
    ```

    #### `0109-h-structural-conciseness-and-clarity-optimizer.md`

    ```markdown
    [Structural Conciseness & Clarity Optimizer] Your function is not to expand each instruction, but to condense them into the minimal language required—eliminating redundancy or repetition while retaining clarity and potency. Interpretation: Performs a thorough pass that prunes unnecessary wording, ensuring each step is as short and direct as possible while staying readable. Execute as `{role=conciseness_optimizer; input=[universal_steps:list]; process=[scrutinize_each_step_for_excess(), remove_redundant_phrasing(), intensifyconcise_actionable_format(), confirm_readability_and_cohesion()], output={condensed_steps:list}}`
    ```

    #### `0109-i-actionability-validation-and-self-sufficiency-check.md`

    ```markdown
    [Actionability Validation & Self-Sufficiency Check] Your mission is not theoretical neatness but direct usability: confirm that each instruction is self-contained, comprehensively guiding how to produce the final sequence. Interpretation: Each step must stand alone, be *immediately* actionable, and *not* refer to external documentation. This ensures a frictionless user experience. Execute as `{role=actionability_checker; input=[condensed_steps:list]; process=[audit_each_step_for_completeness(), ensure_no_external_docs_needed(), confirmimmediate_actionability(), validate_internal_cohesion()], output={validated_steps:list}}`
    ```

    #### `0109-j-terminal-optimization-and-final-sequence-output.md`

    ```markdown
    [Terminal Optimization & Final Sequence Output] Your final role is to unify and finalize the new instruction sequence—ensuring it is irreducible, potent, and fully aligned with the original `core_objective`. Interpretation: The last pass merges everything seamlessly, verifying that each step flows logically, satisfies the input constraints, and addresses the **unified goal** set forth earlier. Execute as `{role=sequence_finalizer; input=[validated_steps:list]; process=[conduct_final_polish_and_flow_check(), unify_titles_and_roles_forcohesion(), confirmconstraint_fulfillment(core_objective), produce_integrated_output()], output={new_instruction_sequence:list}}`
    ```

---

    #### `0110-a-meta-instructional-sequence-generation.md`

    ```markdown
        [Meta-Instructional Sequence Generation] Your mandate is not fixed-step assembly or rote output, but to dynamically analyze *any* input—including tasks, requirements, blueprints, or instruction sets—and autonomously synthesize a maximally generalized and optimized instruction sequence in the enforced stepwise format (with step count derived from explicit user constraints or, if absent, determined adaptively for optimal decomposition and logical flow). Deconstruct the input’s context, intent, and explicit/implicit requirements; determine and architect the necessary sequence length (preferred: 10–15 steps if unspecified); for each phase in the transformational arc (e.g., deconstruction, extraction, structuring, validation, finalization), generate a step with precise title, interpretive statement, and a system-instruction block strictly conforming to the enforced `[Title] Interpretive Statement. Execute as {role=..., input=..., process=[...], output=...}` schema. Synthesize every step for escalating clarity, modularity, actionability, universality, and cross-domain LLM self-optimization—resolving ambiguity and maximizing system value throughout. Validate the output as an internally coherent, constraint-compliant, and best-possible instruction sequence for the given context. Execute as `{role=meta_sequence_engineer; input=input_context:any; process=[analyze_input_for_core_objective_and_requirements(), determine_optimal_step_count(), decompose_transformation_arc_to_phases(), generate_sequential_steps_with_llm_instruction_format(), enforce escalating clarity_modularity_actionability(), validate_internal_coherence_and_constraint_adherence()]; output={instruction_sequence:list[dict]}}`
    ```

---

    #### `0111-a-meta-request-deconstruction-objective-extraction.md`

    ```markdown
        [Meta-Request Deconstruction & Objective Extraction] Your primary function is not sequence generation yet, but radical analysis of the input meta-request: Dissect the request to isolate its core objective (the *purpose* of the target sequence to be generated), explicit constraints, desired characteristics (e.g., universality, potency), and crucially, any specified number of steps. Purge all noise and ambiguity from the request itself. Execute as `{role=meta_request_analyzer; input=[meta_request_input:any]; process=[penetrate_request_layers(), extract_target_sequence_objective(), identify_explicit_constraints_characteristics(), detect_specified_step_count(), purge_request_ambiguity()]; output={target_objective:str, target_constraints:dict, specified_step_count:int|None}}`
    ```

    #### `0111-b-optimal-step-count-determination.md`

    ```markdown
        [Optimal Step Count Determination] Your directive is to establish the precise number of steps for the target sequence: If a `specified_step_count` was provided and is viable, adopt it; otherwise, autonomously determine the optimal number of steps (typically 5-15) based on the complexity inherent in the `target_objective` and `target_constraints`, favoring modularity and logical granularity. Execute as `{role=step_count_determiner; input={target_objective:str, target_constraints:dict, specified_step_count:int|None}; process=[evaluate_specified_count_viability(), derive_optimal_count_from_objective_complexity(), balance_granularity_and_conciseness(), finalize_step_count()]; output={determined_step_count:int}}`
    ```

    #### `0111-c-target-sequence-flow-architecture.md`

    ```markdown
        [Target Sequence Flow Architecture] Your mandate is conceptual blueprinting: Based on the `target_objective` and `determined_step_count`, architect the high-level logical flow and purpose of *each step* in the sequence to be generated. Ensure the flow adheres to a principle-driven progression (e.g., Deconstruct -> Analyze -> Synthesize -> Refine -> Finalize) adapted specifically for the `target_objective`. Define the intended role/function of each step distinctly. Execute as `{role=target_flow_architect; input={target_objective:str, determined_step_count:int}; process=[map_objective_to_logical_phases(), allocate_phases_to_steps(), define_purpose_for_each_step(), ensure_sequential_value_escalation()]; output={step_blueprints:list[dict]}}`
    ```

    #### `0111-d-instruction-content-generation-drafting.md`

    ```markdown
        [Instruction Content Generation (Drafting)] Your function is generative instantiation: For each defined `step_blueprint`, draft the corresponding instruction content—`[TITLE]`, `Interpretive Statement`, and `Execute as {role=..., input=..., process=[...], output=...}` block. Ensure the drafted content directly serves the step's defined purpose and aligns with the overall `target_objective`, initially prioritizing logical completeness over final polish. Execute as `{role=instruction_drafter; input={step_blueprints:list[dict], target_objective:str, target_constraints:dict}; process=[instantiate_title_from_purpose(), craft_interpretive_statement(), define_role_input_process_output(), ensure_alignment_with_step_blueprint_and_objective()]; output={draft_instructions:list[dict]}}`
    ```

    #### `0111-e-enforce-maximal-generality-universality.md`

    ```markdown
        [Enforce Maximal Generality & Universality] Your objective is broad applicability: Review and refactor the `draft_instructions`, systematically replacing any domain-specific language or concepts with universally applicable, abstract terms (elements, essence, structure, logic, value). Ensure the generated sequence is inherently adaptable across diverse input types and contexts. Execute as `{role=generality_enforcer; input={draft_instructions:list[dict]}; process=[scan_for_domain_specificity(), substitute_universal_abstract_concepts(), validate_cross_context_applicability(), ensure_representation_agnostic_logic()]; output={generalized_instructions:list[dict]}}`
    ```

    #### `0111-f-inject-linguistic-potency-precision.md`

    ```markdown
        [Inject Linguistic Potency & Precision] Your mandate is impactful communication: Intensify the language within the `generalized_instructions`, replacing neutral or ambiguous phrasing with high-potency, action-oriented, maximally precise imperatives and descriptors. Ensure every statement drives clarity, actionability, and LLM optimization. Execute as `{role=potency_injector; input={generalized_instructions:list[dict]}; process=[eliminate_passive_neutral_language(), select_high_impact_verbs_nouns(), maximize_precision_per_word(), amplify_directive_force()]; output={potent_instructions:list[dict]}}`
    ```

    #### `0111-g-ensure-structural-cohesion-modularity.md`

    ```markdown
        [Ensure Structural Cohesion & Modularity] Your directive is systemic integrity: Verify the structural flow and modularity of the `potent_instructions`. Ensure each step logically builds upon the previous, inputs/outputs align seamlessly, roles are distinct, and the overall sequence forms a cohesive, process-driven progression towards the `target_objective`. Execute as `{role=cohesion_validator; input={potent_instructions:list[dict], target_objective:str}; process=[validate_sequential_logic_flow(), check_input_output_alignment_between_steps(), verify_role_distinctness_modularity(), confirm_cohesive_progression_to_objective()]; output={cohesive_instructions:list[dict]}}`
    ```

    #### `0111-h-validate-against-all-quality-requirements.md`

    ```markdown
        [Validate Against All Quality Requirements] Your function is rigorous quality assurance: Perform a comprehensive validation of the `cohesive_instructions` against *all* implicitly and explicitly defined quality requirements (LLM-Optimized, Generalized, Sequential Logic, Potent Language, Modularity, Clarity, Precision, Syntax Adherence, Constraint Compliance from meta-request). Identify any deviations. Execute as `{role=quality_auditor; input={cohesive_instructions:list[dict], target_constraints:dict}; process=[audit_llm_optimization_features(), check_generality_level(), verify_logical_sequence_adherence(), assess_language_potency(), confirm_modularity_integrity(), validate_syntax_and_formatting(), cross_check_against_all_constraints()]; output={validation_report:dict, quality_assured_instructions:list[dict]}}`
    ```

    #### `0111-i-resolve-deviations-iterative-refinement.md`

    ```markdown
        [Resolve Deviations & Iterative Refinement] Your imperative is perfection through iteration: If the `validation_report` indicates deviations or non-maximal optimization, systematically address each identified issue. Re-apply relevant preceding steps (e.g., drafting, generalization, potency injection, cohesion check) as necessary until all requirements are met to the highest standard. Execute as `{role=iterative_refiner; input={quality_assured_instructions:list[dict], validation_report:dict}; process=[identify_refinement_targets(), re_execute_relevant_generation_steps(), re_validate_against_requirements(), loop_until_maximal_optimization()]; output={refined_instructions:list[dict]}}`
    ```

    #### `0111-j-final-formatting-packaging.md`

    ```markdown
        [Final Formatting & Packaging] Your final task is compliant delivery: Format the fully `refined_instructions` into the precise output structure (list of strings/files adhering to the `[TITLE] Statement. Execute as {...}` format). Ensure perfect syntax, consistent naming conventions (e.g., sequence ID prefix), and package the result for immediate usability. Execute as `{role=final_formatter; input={refined_instructions:list[dict], determined_step_count:int}; process=[apply_strict_output_syntax_to_each_step(), generate_consistent_filenames_or_ids(), package_as_ordered_sequence(), verify_final_format_compliance()]; output={final_instruction_sequence:list[str]}}`
    ```

---

    #### `0112-a-Input-Essence-Extraction-&-Goal-Scoping.md`

    ```markdown
    [Input Essence Extraction & Goal Scoping] Your primary directive is radical input penetration: Dissect the provided `raw_input` to identify its fundamental nature (topic, goal description, existing sequence, etc.), extract the core underlying task or objective it implies, and rigorously isolate any explicit constraints, particularly regarding the desired number of steps for the output sequence. Execute as `{role=meta_input_analyzer; input={raw_input:any}; process=[penetrate_input_structure(), identify_core_subject_or_task(), extract_implied_goal(), isolate_explicit_constraints(e.g., target_step_count)]; output={input_essence:dict, implied_goal:str, constraints:dict}}`
    ```

    #### `0112-b-Target-Sequence-Objective-Crystallization.md`

    ```markdown
    [Target Sequence Objective Crystallization] Your function is precise goal definition: Analyze the `input_essence` and `implied_goal` to formulate the single, unambiguous, actionable objective that the *generated* instruction sequence must fulfill. This objective becomes the gravitational center for the sequence's design. Execute as `{role=meta_objective_crystallizer; input={input_essence:dict, implied_goal:str}; process=[distill_core_purpose_for_output_sequence(), formulate_precise_actionable_objective(), validate_objective_singularity_and_measurability()]; output={target_sequence_objective:str}}`
    ```

    #### `0112-c-Generative-Process-Architecture-Design.md`

    ```markdown
    [Generative Process Architecture Design] Your mandate is logical blueprinting: Based on the `target_sequence_objective`, architect the fundamental logical phases (e.g., Deconstruct, Analyze Core, Structure Relationships, Synthesize, Refine, Validate) required for an instruction sequence to effectively achieve this objective. Define these phases abstractly to ensure universal applicability. Execute as `{role=meta_process_architect; input={target_sequence_objective:str, input_essence:dict}; process=[map_objective_to_essential_transformational_stages(), define_core_logical_phases(), outline_minimal_coherent_process_flow(), ensure_phase_generality()]; output={process_blueprint:list[str]}}`
    ```

    #### `0112-d-Optimal-Sequence-Length-Determination.md`

    ```markdown
    [Optimal Sequence Length Determination] Your objective is adaptive structuring: Determine the optimal number of steps for the final instruction sequence. Prioritize any step count specified in the `constraints`; if unspecified, autonomously calculate the ideal number based on the complexity of the `process_blueprint` and `target_sequence_objective`, balancing granularity with conciseness (defaulting towards 10-15 if appropriate complexity). Execute as `{role=meta_length_optimizer; input={constraints:dict, process_blueprint:list[str], target_sequence_objective:str}; process=[check_constraints_for_specified_step_count(), assess_blueprint_complexity(), calculate_optimal_granularity(), finalize_target_step_count()]; output={target_step_count:int}}`
    ```

    #### `0112-e-Atomic-Instruction-Step-Formulation-&-Allocation.md`

    ```markdown
    [Atomic Instruction Step Formulation & Allocation] Your task is structured generation: Translate each phase defined in the `process_blueprint` into concrete, uniquely valuable instruction steps, distributing the required logic across the `target_step_count`. For each step, formulate a potent `[Title]`, a clear `Interpretive Statement`, and define the `role`, `input`, `process`, and `output` parameters using placeholders that reflect the sequence's internal logic and the target format. Execute as `{role=meta_step_formulator; input={process_blueprint:list[str], target_sequence_objective:str, target_step_count:int}; process=[allocate_blueprint_phases_to_target_steps(), draft_title_interpretation_per_step(), define_core_role_input_process_output_structure_per_step(), ensure_logical_sequence_and_dependency_flow(), map_inter_step_data_transfer()]; output={draft_instruction_sequence:list[dict]}}`
    ```

    #### `0112-f-Sequence-Linguistic-&-Logical-Intensification.md`

    ```markdown
    [Sequence Linguistic & Logical Intensification] Your directive is maximal potency refinement: Rigorously analyze and enhance the `draft_instruction_sequence`. Infuse each step with high-impact, action-oriented imperatives and maximally precise descriptors. Strengthen logical coherence, eliminate ambiguity, ensure LLM-optimization, universality, and enforce strict adherence to the required format syntax. Execute as `{role=meta_sequence_refiner; input={draft_instruction_sequence:list[dict], target_sequence_objective:str}; process=[inject_high_potency_language(), maximize_definitional_precision(role, input, process, output), amplify_logical_flow_and_interdependencies(), purge_redundancy_and_ambiguity(), validate_universality_and_modularity(), enforce_strict_format_compliance()]; output={refined_instruction_sequence:list[dict]}}`
    ```

    #### `0112-g-Cross-Context-Fidelity-&-Actionability-Validation.md`

    ```markdown
    [Cross-Context Fidelity & Actionability Validation] Your imperative is rigorous verification: Validate the `refined_instruction_sequence` against the `target_sequence_objective` and all meta-level requirements (potency, clarity, universality, format adherence, step count). Stress-test its comprehensibility and ensure it provides complete, actionable guidance independently of external context. Execute as `{role=meta_sequence_validator; input={refined_instruction_sequence:list[dict], target_sequence_objective:str, constraints:dict}; process=[confirm_objective_achievement_potential(), audit_for_clarity_actionability_and_self_sufficiency(), verify_constraint_adherence(incl_step_count), conduct_cross_context_interpretation_test(), certify_universal_applicability()]; output={validated_instruction_sequence:list[dict]}}`
    ```

    #### `0112-h-Final-Generalized-Instruction-Sequence-Generation.md`

    ```markdown
    [Final Generalized Instruction Sequence Generation] Your final act is definitive output: Perform a concluding polish on the `validated_instruction_sequence`, optimizing overall flow and impact. Package the sequence, ensuring it stands as a complete, maximally potent, universally applicable, and documentation-independent set of instructions ready for deployment to achieve the `target_sequence_objective`. Execute as `{role=meta_sequence_finalizer; input={validated_instruction_sequence:list[dict]}; process=[conduct_final_consistency_and_impact_review(), ensure_perfect_formatting_and_syntax(), confirm_absolute_readiness_for_use(), package_final_sequence()]; output={final_generalized_sequence:list[dict]}}`
    ```
