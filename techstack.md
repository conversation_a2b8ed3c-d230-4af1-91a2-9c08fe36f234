# Technology Stack

## Core Technologies
- **Python 3.x** - Primary language
- **uv** - Package manager and dependency management
- **textdistance** - Text similarity calculation library

## Key Libraries for Similarity Detection
- **textdistance** - Comprehensive text similarity algorithms (30+ methods)
- **hashlib** - Fast exact duplicate detection via MD5/SHA hashing
- **pathlib** - Modern file path handling
- **json** - Structured output and configuration

## Existing Utility Patterns
- **File Processing**: Batch operations on multiple files
- **Structured Output**: JSON reports with detailed statistics
- **Error Handling**: Comprehensive failure tracking and reporting
- **Path Management**: Safe file operations with long path support
- **Hashing**: Content-based deduplication and identification

## Project Structure
```
py_similaritymapper/
├── src/                    # Main source code
├── in/                     # Input text files for analysis
├── _ref_only/              # Reference utilities and examples
├── techstack.md           # This file
└── pyproject.toml         # uv project configuration
```

## Similarity Detection Approach
1. **Fast Exact Matching**: MD5 hashing for 100% identical files
2. **Fuzzy Similarity**: textdistance algorithms for near-duplicates
3. **Line-based Comparison**: Set operations for efficient similarity calculation
4. **Percentage Output**: Direct similarity percentages for easy interpretation
