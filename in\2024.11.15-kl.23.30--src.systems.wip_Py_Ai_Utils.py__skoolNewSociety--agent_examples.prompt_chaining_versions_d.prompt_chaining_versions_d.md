# Project Files Documentation for `prompt_chaining_versions_d`

### File Structure

```
├── v3.py
├── v4.py
```
### 1. `v3.py`

#### `v3.py`

```python
import os
import logging
from typing import List, Dict, Optional, Union
import concurrent.futures
from openai import OpenAI
import numpy as np

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Initialize OpenAI Client
def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set.")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# --- Centralized Memory Manager ---
class MemoryManager:
    def __init__(self):
        self.memory_store = []

    def add_memory(self, text: str, embedding: np.ndarray):
        self.memory_store.append({"text": text, "embedding": embedding})

    def retrieve_relevant_context(self, query: str, top_k: int = 3) -> List[str]:
        query_embedding = self.get_embedding(query)
        similarities = [
            (self.cosine_similarity(query_embedding, mem["embedding"]), mem["text"])
            for mem in self.memory_store
        ]
        similarities.sort(reverse=True, key=lambda x: x[0])
        return [text for _, text in similarities[:top_k]]

    def get_embedding(self, text: str) -> np.ndarray:
        try:
            response = client.embeddings.create(input=text, model="text-embedding-ada-002")
            return np.array(response.data[0].embedding)
        except Exception as e:
            logging.error(f"Failed to generate embedding: {e}")
            return np.zeros(1536)  # Default to zero-vector if embedding fails

    @staticmethod
    def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

# --- Blueprint Manager ---
class BlueprintManager:
    def create_blueprint(self, initial_input: str, mode: str) -> str:
        mode_descriptions = {
            "Prompt Generation": "focused on generating concise, impactful prompts.",
            "Content Creation": "focused on detailed, structured content development.",
            "User Guidance": "focused on providing clear, actionable guidance for end-users.",
            "General": "balanced, high-quality response for general use."
        }
        mode_description = mode_descriptions.get(mode, "balanced response")
        return (f"Guiding Blueprint:\n"
                f"Mode: {mode} - {mode_description}\n"
                f"Topic: {initial_input}\n"
                f"Objective: Improve clarity, coherence, relevance, and usability.\n"
                f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n")

# --- Quality Evaluator ---
class QualityEvaluator:
    def assess_quality(self, response: str) -> bool:
        # Simplistic quality indicators for demonstration purposes
        quality_indicators = ["clear", "relevant", "concise", "logical"]
        return sum(indicator in response.lower() for indicator in quality_indicators) >= 2

# --- Agent Processor ---
class AgentProcessor:
    def __init__(self, memory_manager: MemoryManager, blueprint: str, meta_objective: str):
        self.memory_manager = memory_manager
        self.blueprint = blueprint
        self.meta_objective = meta_objective

    def generate_prompt(self, agent_prompt: str, current_response: str) -> str:
        context = "\n".join(self.memory_manager.retrieve_relevant_context(current_response))
        return (f"{agent_prompt}\n\nGuiding Blueprint:\n{self.blueprint}\n\n"
                f"Context:\n{context}\n\nCurrent Response:\n{current_response}\n\nRefined Response:")

    def apply_agent(self, agent: Dict[str, str], current_response: str) -> str:
        prompt = self.generate_prompt(agent["prompt"], current_response)
        try:
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Agent '{agent['role']}' failed: {e}")
            return current_response

    def parallel_apply_agents(self, agents: List[Dict[str, str]], current_response: str) -> str:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_to_agent = {
                executor.submit(self.apply_agent, agent, current_response): agent
                for agent in agents
            }
            results = []
            for future in concurrent.futures.as_completed(future_to_agent):
                results.append(future.result())
            return "\n".join(results)

# --- Unified Refinement Pipeline ---
class RefinementPipeline:
    def __init__(self, memory_manager: MemoryManager, blueprint_manager: BlueprintManager):
        self.memory_manager = memory_manager
        self.blueprint_manager = blueprint_manager
        self.evaluator = QualityEvaluator()

    def execute(self, initial_input: str, mode: str, meta_objective: str, depth: str) -> str:
        # Step 1: Create Blueprint
        blueprint = self.blueprint_manager.create_blueprint(initial_input, mode)

        # Step 2: Initialize Agent Processor
        agent_processor = AgentProcessor(self.memory_manager, blueprint, meta_objective)

        # Step 3: Define Refinement Chain
        refinement_chain = self.get_refinement_chain(mode, depth)

        # Step 4: Execute Refinement Chain
        current_response = initial_input
        for category, agents in refinement_chain.items():
            logging.info(f"Executing category: {category}")
            if len(agents) > 1:
                current_response = agent_processor.parallel_apply_agents(agents, current_response)
            else:
                for agent in agents:
                    current_response = agent_processor.apply_agent(agent, current_response)

            # Early Exit if Quality is Met
            if self.evaluator.assess_quality(current_response):
                logging.info("Quality threshold met, exiting early.")
                break

        return current_response

    @staticmethod
    def get_refinement_chain(mode: str, depth: str) -> Dict[str, List[Dict[str, str]]]:
        base_chain = {
            "Prompt Reformulation": [
                {"role": "Inquiry Formulator", "prompt": f"Rephrase the input for clarity and alignment with '{mode}'."}
            ],
            "Content Development": [
                {"role": "Structure Architect", "prompt": "Organize the response logically, ensuring coherence and depth."}
            ],
            "Quality Assurance": [
                {"role": "Standards Enforcer", "prompt": "Ensure compliance with high-quality writing standards."}
            ]
        }
        if depth == "deep":
            base_chain["Final Optimization"] = [
                {"role": "Performance Optimizer", "prompt": "Optimize the final output for readability and usability."}
            ]
        return base_chain

# --- Main Function ---
def main():
    # initial_input = "Create a highly optimized prompt demonstrating the best use of refinement."
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    mode = "Prompt Generation"
    meta_objective = "Maximize Clarity"
    depth = "moderate"

    memory_manager = MemoryManager()
    blueprint_manager = BlueprintManager()
    pipeline = RefinementPipeline(memory_manager, blueprint_manager)

    final_output = pipeline.execute(initial_input, mode, meta_objective, depth)

    print("\nInitial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
### 2. `v4.py`

#### `v4.py`

```python
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# Define Blueprint creation with mode-based customization
def create_guiding_blueprint(initial_input: str, mode: str) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users."
    }
    mode_description = mode_descriptions.get(mode, "defaulting to a general response focus.")

    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Enhance clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n"
            f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# Define grouped agent chains based on mode
def get_refinement_chain(mode: str) -> Dict[str, List[Dict[str, str]]]:
    chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint with topic, objective, and structure based on the input."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": f"Rephrase the data as a comprehensive question, focusing on '{mode}'."},
            {"role": "Objective Clarifier", "prompt": f"Define the primary objective based on the Blueprint, aligned with '{mode}'."},
            {"role": "Topic Alignment", "prompt": "Review and realign with the original topic and Blueprint if needed."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": f"Add relevant background for enriched response accuracy, with '{mode}' as focus."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint."},
            {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint and topic before further development."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback."},
            {"role": "Topic Alignment", "prompt": "Ensure content remains aligned with the original topic and Blueprint."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability."},
            {"role": "Topic Consistency Tracker", "prompt": "Ensure content aligns with the original question and topic."}
        ],
        "Final Optimization": [
            {"role": "Performance Optimizer", "prompt": "Optimize for efficiency without sacrificing clarity."},
            {"role": "Documentation Curator", "prompt": "Provide clear documentation focusing on usability."},
            {"role": "Quality Assessor", "prompt": "Perform a final quality check, allowing early exit if quality is sufficient."},
            {"role": "Final Topic Consistency Checker", "prompt": "Ensure the response aligns fully with the original topic and Blueprint."}
        ]
    }

    # Modify chain activation based on mode
    if mode == "Prompt Generation":
        # Emphasize clarity, conciseness, and immediate relevance
        chain["Prompt Reformulation"].append({"role": "Conciseness Optimizer", "prompt": "Ensure prompt is concise and impactful."})
    elif mode == "Content Creation":
        # Emphasize detailed structuring and background information
        chain["Content Development"].append({"role": "Detail Enhancer", "prompt": "Add necessary details to support a thorough response."})
    elif mode == "User Guidance":
        # Emphasize user-centered clarity and step-by-step guidance
        chain["User Experience and Readability"].append({"role": "Guidance Clarity Agent", "prompt": "Ensure instructions are clear and actionable for end-users."})

    return chain

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

# Categorize the prompt and select relevant agent chains
def categorize_prompt(prompt: str) -> List[str]:
    categories = []
    if any(keyword in prompt.lower() for keyword in ["clarity", "coherence", "reformulation"]):
        categories.append("Prompt Reformulation")
    if any(keyword in prompt.lower() for keyword in ["background", "content development", "context"]):
        categories.append("Content Development")
    if any(keyword in prompt.lower() for keyword in ["quality assurance", "standards", "error handling"]):
        categories.append("Quality Assurance")
    if any(keyword in prompt.lower() for keyword in ["user experience", "readability"]):
        categories.append("User Experience and Readability")
    if any(keyword in prompt.lower() for keyword in ["optimization", "performance"]):
        categories.append("Final Optimization")
    return categories if categories else ["Blueprint Creation"]

def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, iteration: int) -> str:
    re_anchor_message = f"{blueprint}\n\n" if iteration % 3 == 0 else ""
    prompt = f"{agent['prompt']}\n\nTopic Context from Blueprint:\n{blueprint}\n\n{re_anchor_message}Current Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response

def apply_agent_chain(blueprint: str, initial_input: str, mode: str) -> str:
    current_response = initial_input
    refinement_chain = get_refinement_chain(mode)
    selected_categories = categorize_prompt(initial_input)

    logging.info(f"Selected categories based on prompt: {selected_categories}")

    for category in selected_categories:
        logging.info(f"Starting group: {category}")
        agents = refinement_chain.get(category, [])
        for i, agent in enumerate(agents):
            current_response = apply_agent(agent, blueprint, current_response, i)
            if category == "Final Optimization" and i + 1 >= 5 and assess_response_quality(current_response):
                logging.info("Early exit after quality criteria met.")
                return current_response
    return current_response

def main():
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    mode = "Prompt Generation"  # User-selected mode
    blueprint = create_guiding_blueprint(initial_input, mode)

    logging.info(f"Starting process in '{mode}' mode...")
    final_output = apply_agent_chain(blueprint, initial_input, mode)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
