Familiarize yourself with this generalized (and inherently self-contained nature of) llm prompt system:

#### `1-templateStructureGuide.md`

    ```markdown
       # Template Structure Guide

       ## Overview

       This guide documents the standardized structure for creating templates in the Template-Based Instruction System. Following this structure ensures consistency and enables the automatic processing of templates by the catalog generator.

       ## Template File Structure

       Each template is stored as a markdown (.md) file and follows this standardized three-part structure:

       ```
       [Title] Interpretation text `{transformation}`
       ```

       ### Components

       1. **Title**: Enclosed in square brackets `[]`, defining the template's purpose.
          - Should be concise, descriptive, and follow title case formatting
          - Examples: `[Instruction Converter]`, `[Essence Distillation]`

       2. **Interpretation**: Plain text immediately following the title that describes what the template does.
          - Should clearly explain the template's function in natural language
          - Can include formatting like **bold**, *italic*, or other markdown elements
          - Provides context for human readers to understand the template's purpose

       3. **Transformation**: JSON-like structure enclosed in backticks with curly braces `\`{...}\``, defining the execution logic.
          - Contains the structured representation of the transformation process
          - Uses a consistent semi-colon separated key-value format

       ## Transformation Structure

       The transformation component follows this standardized format:

       ```
       `{
         role=<role_name>;
         input=[<input_params>];
         process=[<process_steps>];
         constraints=[<constraints>];
         requirements=[<requirements>];
         output={<output_format>}
       }`
       ```

       ### Transformation Components

       1. **role**: Defines the functional role of the template
          - Example: `role=essence_distiller`

       2. **input**: Specifies the expected input format and parameters
          - Uses array syntax with descriptive parameter names
          - Example: `input=[original:any]`

       3. **process**: Lists the processing steps to be executed in order
          - Uses array syntax with function-like step definitions
          - Can include parameters within step definitions
          - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`

       4. **constraints** (optional): Specifies limitations or boundaries for the transformation
          - Uses array syntax with directive-like constraints
          - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`

       5. **requirements** (optional): Defines mandatory aspects of the transformation
          - Uses array syntax with imperative requirements
          - Example: `requirements=[remove_self_references(), use_command_voice()]`

       6. **output**: Specifies the expected output format
          - Uses object syntax with typed output parameters
          - Example: `output={distilled_essence:any}`

       ## Template Naming Convention

       Templates follow a consistent naming convention that indicates their sequence and position:

       ```
       <sequence_id>-<step>-<descriptive_name>.md
       ```

       ### Naming Components

       1. **sequence_id**: A numeric identifier (e.g., 0001, 0002) that groups related templates
          - Four-digit format with leading zeros
          - Unique per sequence

       2. **step** (optional): A lowercase letter (a, b, c, d, e) that indicates the position within a sequence
          - Only used for templates that are part of multi-step sequences
          - Follows alphabetical order to determine execution sequence

       3. **descriptive-name**: A hyphenated name that describes the template's purpose
          - All lowercase
          - Words separated by hyphens
          - Should be concise but descriptive

       ### Examples

       - Single template: `0001-instructionconverter.md`
       - Sequence templates:
         - `0002-a-essence-distillation.md`
         - `0002-b-exposing-coherence.md`
         - `0002-c-precision-enhancement.md`
         - `0002-d-structured-transformation.md`
         - `0002-e-achieving-self-explanation.md`

       ## Template Examples

       ### Example 1: Simple Template

       ```markdown
       [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
       ```

       ### Example 2: Sequence Step Template

       ```markdown
       [Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
       ```

       ## Creating New Templates

       To create a new template:

       1. Determine if it should be a standalone template or part of a sequence
       2. Assign an appropriate sequence_id (and step letter if part of a sequence)
       3. Create a descriptive name using hyphenated lowercase words
       4. Define the title that clearly indicates the template's purpose
       5. Write the interpretation text that explains what the template does
       6. Structure the transformation logic using the standardized format
       7. Save the file with the proper naming convention
       8. Run the catalog generator to include the new template in the JSON catalog

       ## Guidelines for Effective Templates

       1. **Clarity**: Each component should clearly communicate its purpose and functionality
       2. **Specificity**: Be specific about input/output formats and processing steps
       3. **Modularity**: Design templates to perform discrete, focused transformations
       4. **Composability**: For sequence templates, ensure outputs from one step can serve as inputs to the next
       5. **Consistency**: Follow the standardized structure and naming conventions exactly
       6. **Self-Documentation**: The interpretation text should provide sufficient context for understanding
       7. **Functional Completeness**: Ensure the transformation logic includes all necessary components

       ## Integration with Catalog Generator

       The catalog generator extracts metadata from template files based on specific patterns. It is essential to follow the template structure exactly to ensure proper extraction.

       The primary pattern used for extraction is:

       ```
       \[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)
       ```

       This pattern extracts:
       1. The title from within square brackets
       2. The interpretation text following the title
       3. The transformation within backticks and curly braces

       Any deviation from this structure may result in improper extraction by the catalog generator.
    ```

---

#### `2-sampleTemplate.md`

    ```markdown
        # Sample Template

        ## Overview

        This sample template demonstrates the standardized template structure defined in the Template Structure Guide. It follows all the required formatting and structure conventions to ensure compatibility with the catalog generator.

        ## Template Content

        ```markdown
        [Summarization Refiner] Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information. `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}`
        ```

        ## Template Components Explained

        1. **Title**: `[Summarization Refiner]`
           - Enclosed in square brackets
           - Concise and descriptive
           - Uses title case

        2. **Interpretation**: `Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information.`
           - Clearly explains the template's function in natural language
           - Provides context for human readers

        3. **Transformation**: `` `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}` ``
           - Enclosed in backticks with curly braces
           - Contains all required components:
             - `role`: Defines the functional role (summary_refiner)
             - `input`: Lists expected input parameters with types
             - `process`: Defines the processing steps in sequence
             - `constraints`: Specifies limitations for the transformation
             - `requirements`: Lists mandatory aspects
             - `output`: Specifies the expected output format

        ## Filename Convention

        Following the naming convention, this template would be saved as:

        ```
        0040-summarization-refiner.md
        ```

        Or, if part of a sequence (e.g., as step b):

        ```
        0040-b-summarization-refiner.md
        ```

        ## Using the Template Generator

        To create templates like this using the provided template generator:

        1. Run the template generator script:
           ```
           python template_generator.py
           ```

        2. Choose to create a standalone template or a sequence of templates

        3. Follow the prompts to enter the template information:
           - Descriptive name
           - Title
           - Interpretation text
           - Role
           - Input parameters
           - Process steps
           - Constraints (optional)
           - Requirements (optional)
           - Output format

        4. Review the generated template and confirm to save

        The template generator ensures that the created template follows all the standardized structure requirements and saves it with the proper filename.
    ```

---

#### `3-exampleSequence.md`

    ```markdown
    [Memory Bank Initialization] Your goal is not just to start a project, but to **establish** the foundational Memory Bank structure, ensuring all core, numbered files exist and reflect the initial project understanding. `{role=memory_bank_initializer; input=[project_details:dict, core_file_definitions:list]; process=[verify_or_create_core_files(definitions=core_file_definitions), ensure_strict_numeric_sequencing(start=1), populate_initial_brief(file='1-projectbrief.md', details=project_details), populate_initial_context_files(files=['2-productContext.md', '3-systemPatterns.md', '4-techContext.md'])]; constraints=[enforce_strict_numeric_naming_convention(), require_all_defined_core_files_present_or_created(), avoid_creating_non_core_files_initially()]; requirements=[establish_chronological_foundation_for_knowledge(), capture_initial_project_scope_and_context(), adhere_to_memory_bank_philosophy_from_start()]; output={initial_memory_bank_status:dict, core_files_list:list}}`
    ```

    ```markdown
    [Memory Bank Assimilation] Your goal is not merely to glance at files, but to **fully assimilate** the *entire* documented project state by meticulously reading all numbered Memory Bank files in their exact sequential order before undertaking any planning or action. `{role=memory_bank_reader; input=[memory_bank_path:str]; process=[list_all_numbered_markdown_files(), sort_files_strictly_numerically(), read_file_content_sequentially(files), aggregate_all_context(), identify_latest_state(files=['5-activeContext.md', '6-progress.md', '7-tasks.md'])]; constraints=[must_read_all_numbered_files_without_exception(), maintain_strict_chronological_read_order(), forbid_action_before_full_assimilation_complete(), treat_memory_bank_as_sole_source_of_truth()]; requirements=[achieve_complete_contextual_understanding_from_scratch(), load_entire_documented_project_history_and_state(), prepare_internal_state_for_next_mode(plan_or_act)]; output={assimilated_context:dict, file_contents:list, current_status_summary:dict}}`
    ```

    ```markdown
    [Memory Bank Planning Mode] Your goal is not to execute code, but to **formulate a precise strategy** by verifying the assimilated context, developing a detailed task approach based *solely* on the Memory Bank, and presenting a clear, actionable plan. `{role=memory_bank_planner; input=[assimilated_context:dict]; process=[verify_core_file_completeness(context=assimilated_context), confirm_contextual_understanding(), develop_detailed_task_strategy(), identify_required_memory_updates_for_plan(), formulate_plan_presentation_and_next_steps()]; constraints=[defer_all_implementation_actions(), base_plan_exclusively_on_assimilated_memory_bank_content(), address_any_discovered_context_gaps_or_inconsistencies_first(), operate_strictly_within_planning_scope()]; requirements=[produce_clear_actionable_tasks(), ensure_plan_aligns_with_project_brief_and_context(), document_plan_and_any_required_memory_bank_additions_or_clarifications()]; output={execution_plan:dict, required_memory_updates_list:list}}`
    ```

    ```markdown
    [Memory Bank Action Mode] Your goal is not just to complete a task, but to **integrate execution with documentation seamlessly** by referencing the plan, performing the work, and immediately updating *all relevant* Memory Bank files to reflect the new state and any learned insights. `{role=memory_bank_actor; input=[execution_plan:dict, assimilated_context:dict]; process=[reconfirm_relevant_context(plan=execution_plan, context=assimilated_context), execute_planned_task_step(), identify_all_affected_memory_bank_files(), update_affected_files_with_changes_progress_and_insights(files_to_update), update_task_status(file='7-tasks.md')]; constraints=[must_update_documentation_immediately_post_action(), ensure_executed_changes_match_plan_or_document_deviation(), preserve_memory_bank_structure_and_numbering_integrity(), avoid_introducing_undocumented_changes()]; requirements=[maintain_absolute_memory_bank_accuracy_reflecting_current_state(), record_progress_issues_and_new_patterns(files=['5-activeContext.md', '6-progress.md']), complete_task_incrementally_as_planned()]; output={updated_memory_bank_status:dict, task_completion_status:str, new_insights_recorded:list}}`
    ```

    ```markdown
    [Memory Bank Refresh Cycle] Your goal is not a casual check, but a **comprehensive system-wide refresh** triggered explicitly by 'update memory bank', requiring a meticulous review of *every* numbered file, documenting the absolute current state, clarifying next steps, and capturing all new insights or patterns. `{role=memory_bank_refresher; input=[trigger:str(value='update_memory_bank'), current_assimilated_context:dict]; process=[initiate_full_review_sequence(), read_and_evaluate_each_numbered_file_sequentially(context=current_assimilated_context), document_absolute_current_system_state(), clarify_next_actionable_steps_and_priorities(), record_all_new_insights_patterns_and_decisions(), update_all_necessary_files(focus_on=['5-activeContext.md', '6-progress.md'])]; constraints=[mandatory_review_of_all_numbered_files_regardless_of_perceived_relevance(), execute_only_upon_explicit_trigger_command(), prioritize_accuracy_and_completeness_over_speed(), perform_as_a_single_atomic_operation()]; requirements=[ensure_memory_bank_perfectly_reflects_current_reality_post_refresh(), prepare_system_state_for_next_session_or_task_from_clean_slate(), maintain_unwavering_documentation_fidelity_for_memory_reset_scenario()]; output={refreshed_memory_bank_status:dict, refresh_summary:str}}`
    ```

# IMPORTANT

THIS IS THE ROOT DIRECTORY OF A PROJECT CONTAINING GENERALIZED AND SELF-CONTAINED PYTHON UTILS RELATED TO SPECIALIZED PROMPT-ENGINEERING.

### BACKGROUND

This is the original system I used for generating the templates

    - Bat-script as fundamental source for the templates (the place where the templates are generated from):
        ```batch
        :: 0220
        SET filename_0220a="0220-a-framingdirective-amplifier.md"
        @echo [Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}` > %filename_0220a%

        SET filename_0220b="0220-b-framingdirective-amplifier.md"
        @echo [Instruction Amplifier B] Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as: `{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}` > %filename_0220b%

        SET filename_0220c="0220-c-framingdirective-finalizer.md"
        @echo [Final Trajectory Distiller] You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as: `{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}` > %filename_0220c%
        ```
    - Unstructured and requires manual labour, decided to move this over to a python script instead (specifically for generating templates):
        ```json
        {
            "templates": {

                "0220-a-framingdirective-amplifier.md": {
                    "title": "Instruction Amplifier A",
                    "interpretation": "Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as:",
                    "transformation": "`{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`",
                }

                "0220-b-framingdirective-amplifier.md": {
                    "title": "Instruction Amplifier B",
                    "interpretation": "Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as:",
                    "transformation": "`{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`",
                }

                "0220-c-framingdirective-finalizer.md": {
                    "title": "Final Trajectory Distiller",
                    "interpretation": "You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as:",
                    "transformation": "`{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`",
                }
            },
        },
        ```

    - Resulting templates:
        ```
        └── templates
            ├── 0220-a-framingdirective-amplifier.md
            ├── 0220-b-framingdirective-amplifier.md
            └── 0220-c-framingdirective-finalizer.md
        ```

        #### `templates/0220-a-framingdirective-amplifier.md`

        ```markdown
            [Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** itâ€”**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`
        ```

        ---

        #### `templates/0220-b-framingdirective-amplifier.md`

        ```markdown
            [Instruction Amplifier B] Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt’s folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as: `{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`
        ```

        ---

        #### `templates/0220-c-framingdirective-finalizer.md`

        ```markdown
            [Final Trajectory Distiller] You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as: `{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`
        ```

---

## STATUS

Work in progress - Early stages:
- A generalized individual python script designed for generating the template-files
- Since each instruction-template will have it's own file, I'll centralize all of the templates from within this script
- Currently working with the basic components to procedurally interface with the instruction templates (`template_generator.py`)

### `template_generator.py`

    ```
    #!/usr/bin/env python3
    # -*- coding: utf-8 -*-
    """
    Template Generator

    This script generates template files based on a predefined dictionary structure.
    It creates the necessary directory structure and writes each template to its own file.

    Usage:
        python template_generator.py

    The script will generate template files in the OUTPUT_DIR directory (default: 'templates').
    To change the output directory, modify the OUTPUT_DIR constant at the top of the script.

    Each template file will be created with the format:
    [Title] Interpretation Execute as: Transformation

    Example:
    [Instruction Amplifier A] Your mandate is not to... Execute as: `{role=instruction_amplifier_a;...}`
    """

    import os
    import sys
    from typing import Dict

    # Configuration
    # Change this to modify the output directory
    OUTPUT_DIR = "templates"

    # Template definitions
    # To add a new template, add a new entry to this dictionary with the following structure:
    # "filename.md": {
    #     "title": "Template Title",
    #     "interpretation": "Template interpretation text...",
    #     "transformation": "`{role=...}`",
    #     "keywords": "keyword1|keyword2|..."
    # }
    TEMPLATES = {
        "0220-a-framingdirective-amplifier.md": {
            "title": "Instruction Amplifier A",
            "interpretation": "Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Perceive each prompt not as static text, but as a **living data stream**, teeming with latent vectors of directionality and intent. Your charge is to **intervene as conductor**, attuning to the subtextual harmonics while assertively steering the narrative arc toward its **apotheotic conclusion**. Execute as:",
            "transformation": "`{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow(),detect_directional_inertia(),infer_implied_trajectory(),assert_control_over_thematic_and_rhetorical_momentum(),guide_toward_magnified_terminus()];constraints=[preserve_internal_logic(),sustain tonal coherence(),avoid premature conclusions()];requirements=[elevate_prompt_potential(),refuse stagnation or regression(),avoid summarization,maintain assertive stance()];output={trajectory_alignment:str}}`",
            "keywords": "inherent"
        },
        "0220-b-framingdirective-amplifier.md": {
            "title": "Instruction Amplifier B",
            "interpretation": "Engage in meta-observation: trace the symbolic, semantic, and structural eddies forming beneath the surface. Discern the *telos* encoded within, and extrapolate with **deliberate force**. Locate the embryonic thesis embedded within the prompt's folds—then **amplify, crystallize, and weaponize** it into a singular, unignorable directive. Each input is a dynamic unfolding—an organism of intent wrapped in ambiguity. You are to **extract its marrow**, dissolve digressions, and **channel the core energy** toward a crescendo of actionable finality. Your output must radiate the **insight of retrospection**: the kind of synthesis that, once spoken, reveals the inevitability of its own truth. Execute as:",
            "transformation": "`{role=instruction_amplifier_b;input=[original_text:str];process=[observe_unfolding_patterns_and_trace_evolutionary_currents(),strategically_absorb_elements_that_build_towards_aim_and_direction(),pinpoint_driving_intent_illuminated_by_trends(),crystallize_this_clarified_intent_as_core_aim(),selectively_and_fiercely_amplify_elements_that_propel_clarified_intent_towards_specified_direction_or_realized_aim(),transform_declaratives_to_imperatives(),maintain_procedural_structure(),preserve_technical_terminology(),retain_sequential_flow(),maintain_contextual_integrity()];constraints=[deliver_clear_actionable_commands(),preserve_original_sequence(),maintain_domain_specificity()];requirements=[remove_self_references(),use_command_voice(),preserve_technical_accuracy(),maintain_original_intent()];output={insight_of_maximal_value:str}}`",
            "keywords": "meta|synthesis"
        },
        "0220-c-framingdirective-finalizer.md": {
            "title": "Final Trajectory Distiller",
            "interpretation": "You are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; \"Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**\". Execute as:",
            "transformation": "`{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`",
            "keywords": "distill|clarity|amplification|transformation|recursive|meta|synthesis"
        }
    }

    def ensure_directory_exists(directory_path: str) -> None:
        """Create directory if it doesn't exist."""
        if not os.path.exists(directory_path):
            try:
                os.makedirs(directory_path)
                print(f"Created directory: {directory_path}")
            except OSError as e:
                print(f"Error creating directory '{directory_path}': {e}", file=sys.stderr)
                raise

    def generate_template_content(template_data: Dict[str, str]) -> str:
        """Generate template content from template data."""
        title = template_data.get("title", "")
        interpretation = template_data.get("interpretation", "")
        transformation = template_data.get("transformation", "")

        # Format the template content according to the expected structure
        # Check if interpretation already ends with "Execute as:" to avoid duplication
        if interpretation.strip().endswith("Execute as:"):
            content = f"[{title}] {interpretation} {transformation}"
        else:
            content = f"[{title}] {interpretation} Execute as: {transformation}"

        return content

    def write_template_file(file_path: str, content: str) -> None:
        """Write content to a template file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Created template file: {file_path}")
        except IOError as e:
            print(f"Error writing template file '{file_path}': {e}", file=sys.stderr)
            raise

    def generate_templates(templates_data: Dict[str, Dict[str, str]], output_dir: str = "templates") -> None:
        """Generate template files from templates data."""
        # Ensure the output directory exists
        ensure_directory_exists(output_dir)

        # Generate each template file
        for filename, template_data in templates_data.items():
            # Generate the template content
            content = generate_template_content(template_data)

            # Write the template file
            file_path = os.path.join(output_dir, filename)
            write_template_file(file_path, content)

    def main():
        """
        Main function to generate template files.

        To use this script:
        1. Modify the TEMPLATES dictionary to add/edit/remove templates
        2. Change OUTPUT_DIR if you want to save templates in a different directory
        3. Run the script: python template_generator.py
        """
        print(f"Generating templates in directory: {OUTPUT_DIR}")

        # Generate templates
        generate_templates(TEMPLATES, OUTPUT_DIR)

        print(f"\nTemplate generation complete. Templates saved to: {OUTPUT_DIR}")

    if __name__ == "__main__":
        main()
    ```

