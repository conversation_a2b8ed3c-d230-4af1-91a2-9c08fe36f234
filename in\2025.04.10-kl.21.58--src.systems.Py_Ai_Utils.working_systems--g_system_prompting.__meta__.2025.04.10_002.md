Lets work with `instruction_sequence_executor_litellm.py` in particular; I'm considering organizing, simplifying and segmenting the functionality through generalized class structures - in you view, what is the single most critical aspect (arena to cover) for maximizing overall value? The goal is to be able to execute instruction sequences, but also to serve as a llm interface that can be imported and used in other scripts. Here's the current code:

    import asyncio
    import json
    import os
    import sys
    import re
    import glob
    import argparse
    from datetime import datetime
    from typing import Dict, List, Optional, Any, Union, TextIO
    from pydantic import BaseModel, Field

    import litellm
    from litellm import completion

    # Configure stdout and stderr to use UTF-8 encoding
    if hasattr(sys.stdout, "reconfigure"):
        try: sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        except Exception: pass
    if hasattr(sys.stderr, "reconfigure"):
        try: sys.stderr.reconfigure(encoding="utf-8", errors="replace")
        except Exception: pass

    # Define Pydantic models for structured output
    class ModelResponse(BaseModel):
        """Response from a single model for a specific system instruction"""
        model: str = Field(description="The model used for this response")
        content: str = Field(description="The content of the response")
        cost: float = Field(description="The cost of this response in USD")

    class InstructionResult(BaseModel):
        """Results for a specific instruction across multiple models"""
        instruction: str = Field(description="The instruction used")
        responses: Dict[str, ModelResponse] = Field(description="Responses from each model")

    class ExecutionResults(BaseModel):
        """Complete results of executing a user prompt against a sequence of instructions"""
        user_prompt: str = Field(description="The user prompt that was executed")
        sequence_name: str = Field(description="Name of the instruction sequence")
        results: List[InstructionResult] = Field(description="Results for each instruction")
        total_cost: float = Field(description="Total cost of all LLM API calls in USD")

    # Cost tracking
    total_cost = 0.0

    def add_to_cost(cost: float):
        """Add to the total cost"""
        global total_cost
        total_cost += cost

    def get_total_cost() -> float:
        """Get the current total cost"""
        global total_cost
        return total_cost

    def reset_cost():
        """Reset the cost counter"""
        global total_cost
        total_cost = 0.0

    # LiteLLM callback for tracking costs
    def track_cost_callback(kwargs, completion_response, start_time, end_time):
        from litellm import completion_cost

        try:
            response_cost = 0
            if kwargs.get("stream") != True:
                # For non-streaming responses
                response_cost = completion_cost(completion_response=completion_response)
            if response_cost > 0:
                add_to_cost(response_cost)
        except Exception as e:
            print(f"Error tracking cost: {e}")

    # Configure LiteLLM
    litellm.drop_params = True    # Auto-remove unsupported parameters per provider
    litellm.num_retries = 3       # Retry failed requests
    litellm.request_timeout = 120 # Timeout in seconds per request
    litellm.calculate_cost = True # Enable cost tracking
    litellm.set_verbose = False   # Use for development/debugging only
    litellm.success_callback = [track_cost_callback]

    # Model mapping - this would typically be in a config file
    MODEL_MAPPING = {
        "gpt-4o-openai": "gpt-4o",
        "gpt-35-turbo": "gpt-3.5-turbo",
        # Add more model mappings as needed
    }

    # Template parsing functions from breadcrumbs_catalog_generator.py
    def parse_template(file_path: str) -> Dict[str, Any]:
        """Parse a template file and extract its metadata."""
        template_id = os.path.basename(file_path).split('.')[0]

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract title
        title_match = re.match(r'\[(.*?)\]', content)
        title = title_match.group(1) if title_match else "Untitled"

        # Extract role and output parameter
        role_match = re.search(r'role=([^;]+)', content)
        output_match = re.search(r'output=\{([^}]+)\}', content)

        role = role_match.group(1).strip() if role_match else None

        output_param = None
        if output_match:
            output_parts = output_match.group(1).split(':')
            output_param = output_parts[0].strip()

        # Extract sequence information
        seq_info = {}
        match = re.match(r"(\d+)-([a-z])-(.+)", template_id)
        if match:
            seq_num, step_letter, step_name = match.groups()
            seq_info = {
                "sequence_id": seq_num,
                "step": step_letter,
                "step_name": step_name,
                "order": ord(step_letter) - ord('a')
            }

        return {
            "id": template_id,
            "title": title,
            "content": content,
            "role": role,
            "output_param": output_param,
            "sequence_info": seq_info
        }

    def generate_catalog() -> Dict[str, Any]:
        """Generate a catalog of templates and sequences."""
        templates = {}
        sequences = {}

        # Find all template files in the templates/lvl1 directory
        templates_dir = os.path.join(os.path.dirname(__file__), "templates", "lvl1")
        if not os.path.exists(templates_dir):
            print(f"Warning: Templates directory {templates_dir} not found")
            return {"templates": {}, "sequences": {}}

        template_files = glob.glob(os.path.join(templates_dir, "*.md"))
        print(f"Found {len(template_files)} template files in {templates_dir}")

        # Parse each template
        for file_path in template_files:
            try:
                template = parse_template(file_path)
                template_id = template["id"]
                templates[template_id] = template

                # Add to sequence if applicable
                if template["sequence_info"]:
                    seq_id = template["sequence_info"]["sequence_id"]
                    if seq_id not in sequences:
                        sequences[seq_id] = []

                    sequences[seq_id].append({
                        "template_id": template_id,
                        "step": template["sequence_info"]["step"],
                        "order": template["sequence_info"]["order"]
                    })

                print(f"Processed template: {template_id}")
            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")

        # Sort steps within each sequence
        for seq_id in sequences:
            sequences[seq_id].sort(key=lambda x: x["order"])
            print(f"Sequence {seq_id}: {len(sequences[seq_id])} steps")

        return {
            "templates": templates,
            "sequences": sequences
        }

    def load_text_sequence(sequence_name: str) -> List[str]:
        """
        Load an instruction sequence from a text file in the templates directory.

        Args:
            sequence_name: Name of the sequence file (without .txt extension)

        Returns:
            List[str]: List of instructions in the sequence
        """
        # Ensure templates directory exists
        templates_dir = os.path.join(os.path.dirname(__file__), "templates")

        # Load the sequence file
        sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")

        if not os.path.exists(sequence_path):
            raise FileNotFoundError(f"Instruction sequence file not found: {sequence_path}")

        with open(sequence_path, "r", encoding="utf-8") as f:
            # Each instruction is separated by a line containing only "---"
            content = f.read()
            instructions = [instr.strip() for instr in content.split("---") if instr.strip()]

        return instructions

    def list_text_sequences() -> List[str]:
        """
        List all available text-based instruction sequences in the templates directory.

        Returns:
            List[str]: Names of available sequences (without .txt extension)
        """
        templates_dir = os.path.join(os.path.dirname(__file__), "templates")

        sequences = []
        for file in os.listdir(templates_dir):
            if file.endswith(".txt") and os.path.isfile(os.path.join(templates_dir, file)):
                sequences.append(file[:-4])  # Remove .txt extension

        return sequences

    async def execute_with_instruction_streaming(
        instruction: str,
        user_prompt: str,
        model: str,
        output_file: TextIO,
        is_first_result: bool,
        is_last_result: bool,
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> ModelResponse:
        """
        Execute a single instruction with a user prompt on a specific model with streaming.

        Args:
            instruction: The instruction to use
            user_prompt: The user prompt to send
            model: The model to use
            output_file: File handle to write streaming results to
            is_first_result: Whether this is the first result in the sequence
            is_last_result: Whether this is the last result in the sequence
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation

        Returns:
            ModelResponse: The model's response
        """
        print(f"Executing on {model} with instruction: {instruction[:50]}...")

        # Track the cost before this execution
        initial_cost = get_total_cost()

        # Create messages for the LLM
        messages = [
            {"role": "system", "content": instruction},
            {"role": "user", "content": user_prompt}
        ]

        # Get the actual model name from the mapping
        actual_model = MODEL_MAPPING.get(model, model)

        try:
            # Write the start of the instruction result to the file
            if not is_first_result:
                output_file.write(',\n')

            output_file.write('    {\n')
            output_file.write(f'      "instruction": {json.dumps(instruction)},\n')
            output_file.write('      "responses": {\n')

            # Write the start of this model's response
            output_file.write(f'        "{model}": {{\n')
            output_file.write(f'          "model": "{model}",\n')
            output_file.write('          "content": "')
            output_file.flush()

            # Execute the LLM call with streaming
            full_content = ""
            response = await litellm.acompletion(
                model=actual_model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
            )

            # Process the streaming response
            async for chunk in response:
                text_chunk = chunk.choices[0].delta.content or ""
                print(text_chunk, end="", flush=True)

                # Escape special characters for JSON
                escaped_chunk = text_chunk.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
                output_file.write(escaped_chunk)
                output_file.flush()

                full_content += text_chunk

            print()  # newline after stream

            # Calculate the cost of this execution
            final_cost = get_total_cost()
            execution_cost = final_cost - initial_cost

            # Write the end of this model's response
            output_file.write('",\n')
            output_file.write(f'          "cost": {execution_cost}\n')
            output_file.write('        }\n')
            output_file.write('      }\n')
            output_file.write('    }')
            output_file.flush()

            return ModelResponse(
                model=model,
                content=full_content,
                cost=execution_cost
            )
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(f"Error executing on {model}: {e}")

            # Write error information to the file
            escaped_error = error_msg.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
            output_file.write(escaped_error)
            output_file.write('",\n')
            output_file.write('          "cost": 0.0\n')
            output_file.write('        }\n')
            output_file.write('      }\n')
            output_file.write('    }')
            output_file.flush()

            return ModelResponse(
                model=model,
                content=error_msg,
                cost=0.0
            )

    async def execute_catalog_sequence(
        user_prompt: str,
        sequence_id: str,
        models: List[str],
        output_file: str,
        catalog: Dict[str, Any],
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> None:
        """
        Execute a user prompt with a sequence from the catalog on multiple models,
        streaming results to a file as they are generated.

        Args:
            user_prompt: The user prompt to execute
            sequence_id: ID of the sequence in the catalog
            models: List of models to use
            output_file: Path to the output file
            catalog: The catalog data
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
        """
        if not catalog or "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            raise ValueError(f"Sequence {sequence_id} not found in catalog")

        sequence = catalog["sequences"][sequence_id]
        templates = catalog["templates"]

        initial_cost = get_total_cost()
        results = []

        # Sort steps by order
        sequence.sort(key=lambda x: x["order"])

        # Open the output file for streaming results
        with open(output_file, "w", encoding="utf-8") as f:
            # Write the header of the JSON object
            f.write('{\n')
            f.write(f'  "user_prompt": {json.dumps(user_prompt)},\n')
            f.write(f'  "sequence_name": "catalog-{sequence_id}",\n')
            f.write('  "results": [\n')

            # Process each step in the sequence
            for i, step in enumerate(sequence):
                template_id = step["template_id"]
                if template_id not in templates:
                    print(f"Warning: Template {template_id} not found in catalog")
                    continue

                template = templates[template_id]
                instruction = template["content"]

                responses = {}

                # Execute the instruction on the first model with streaming
                first_model = models[0]
                is_first_result = (i == 0)
                is_last_result = (i == len(sequence) - 1)

                response = await execute_with_instruction_streaming(
                    instruction=instruction,
                    user_prompt=user_prompt,
                    model=first_model,
                    output_file=f,
                    is_first_result=is_first_result,
                    is_last_result=is_last_result,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )

                responses[first_model] = response

                # Add to results list for summary
                results.append(InstructionResult(
                    instruction=instruction,
                    responses=responses
                ))

            # Calculate total cost
            final_cost = get_total_cost()
            total_cost = final_cost - initial_cost

            # Write the footer of the JSON object
            f.write('\n  ],\n')
            f.write(f'  "total_cost": {total_cost}\n')
            f.write('}\n')

        # Print a summary of the results
        print("\n=== EXECUTION SUMMARY ===")
        print(f"User Prompt: {user_prompt}")
        print(f"Sequence: catalog-{sequence_id}")
        print(f"Total Cost: ${total_cost:.6f}")

        for i, result in enumerate(results):
            print(f"\nInstruction {i+1}: {result.instruction[:50]}...")
            for model, response in result.responses.items():
                print(f"  - {model}: ${response.cost:.6f}")
                print(f"    {response.content[:100]}...")

        print(f"\nFull results saved to {output_file}")

    async def execute_text_sequence(
        user_prompt: str,
        sequence_name: str,
        models: List[str],
        output_file: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> None:
        """
        Execute a user prompt with a text-based sequence on multiple models,
        streaming results to a file as they are generated.

        Args:
            user_prompt: The user prompt to execute
            sequence_name: Name of the text sequence
            models: List of models to use
            output_file: Path to the output file
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
        """
        # Load the instruction sequence
        instructions = load_text_sequence(sequence_name)

        initial_cost = get_total_cost()
        results = []

        # Open the output file for streaming results
        with open(output_file, "w", encoding="utf-8") as f:
            # Write the header of the JSON object
            f.write('{\n')
            f.write(f'  "user_prompt": {json.dumps(user_prompt)},\n')
            f.write(f'  "sequence_name": "{sequence_name}",\n')
            f.write('  "results": [\n')

            # Process each instruction in the sequence
            for i, instruction in enumerate(instructions):
                responses = {}

                # Execute the instruction on the first model with streaming
                first_model = models[0]
                is_first_result = (i == 0)
                is_last_result = (i == len(instructions) - 1)

                response = await execute_with_instruction_streaming(
                    instruction=instruction,
                    user_prompt=user_prompt,
                    model=first_model,
                    output_file=f,
                    is_first_result=is_first_result,
                    is_last_result=is_last_result,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )

                responses[first_model] = response

                # Add to results list for summary
                results.append(InstructionResult(
                    instruction=instruction,
                    responses=responses
                ))

            # Calculate total cost
            final_cost = get_total_cost()
            total_cost = final_cost - initial_cost

            # Write the footer of the JSON object
            f.write('\n  ],\n')
            f.write(f'  "total_cost": {total_cost}\n')
            f.write('}\n')

        # Print a summary of the results
        print("\n=== EXECUTION SUMMARY ===")
        print(f"User Prompt: {user_prompt}")
        print(f"Sequence: {sequence_name}")
        print(f"Total Cost: ${total_cost:.6f}")

        for i, result in enumerate(results):
            print(f"\nInstruction {i+1}: {result.instruction[:50]}...")
            for model, response in result.responses.items():
                print(f"  - {model}: ${response.cost:.6f}")
                print(f"    {response.content[:100]}...")

        print(f"\nFull results saved to {output_file}")

    async def main():
        # Parse command line arguments
        parser = argparse.ArgumentParser(description="Execute instruction sequences using LiteLLM with streaming")
        parser.add_argument("--sequence", type=str, help="Specific sequence to execute")
        parser.add_argument("--prompt", type=str, default="How can we improve team communication in a remote environment?",
                            help="User prompt to execute")
        parser.add_argument("--models", type=str, default="gpt-4o-openai",
                            help="Comma-separated list of models to use")
        parser.add_argument("--output", type=str, help="Output file path")
        parser.add_argument("--use-text", action="store_true", help="Use text-based sequences instead of catalog")
        args = parser.parse_args()

        # Check for API keys
        if not os.environ.get("OPENAI_API_KEY"):
            print("Warning: OPENAI_API_KEY environment variable not set")
            print("Using dummy API key for demonstration purposes")
            os.environ["OPENAI_API_KEY"] = "sk-dummy-key"

        # Parse models
        models = args.models.split(",")

        # Generate output filename if not provided
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if args.use_text:
            # List available text sequences
            print("\nAvailable text-based sequences:")
            text_sequences = list_text_sequences()
            for seq in text_sequences:
                print(f"  - {seq}")

            # Select sequence
            if args.sequence and args.sequence in text_sequences:
                sequence_name = args.sequence
            else:
                if not text_sequences:
                    print("Error: No text sequences found")
                    return
                sequence_name = text_sequences[0]  # Use the first available sequence

            if args.output:
                output_file = args.output
            else:
                output_file = f"results_{sequence_name}_{timestamp}.json"

            print(f"\nExecuting text sequence {sequence_name} with prompt: {args.prompt}")
            print(f"Results will be streamed to: {output_file}")
            print(f"Using model(s): {', '.join(models)}")

            # Execute the text sequence
            await execute_text_sequence(
                user_prompt=args.prompt,
                sequence_name=sequence_name,
                models=models,
                output_file=output_file,
                max_tokens=1000,
                temperature=0.7,
            )
        else:
            # Generate catalog on the fly
            print("\nGenerating catalog from template files...")
            catalog = generate_catalog()

            # List available catalog sequences
            print("\nAvailable catalog sequences:")
            for seq_id, steps in catalog["sequences"].items():
                print(f"  - {seq_id} ({len(steps)} steps)")

            # Select sequence
            if args.sequence and args.sequence in catalog["sequences"]:
                sequence_id = args.sequence
            else:
                if not catalog["sequences"]:
                    print("Error: No catalog sequences found")
                    return
                sequence_id = list(catalog["sequences"].keys())[0]  # Use the first available sequence

            if args.output:
                output_file = args.output
            else:
                output_file = f"results_catalog-{sequence_id}_{timestamp}.json"

            print(f"\nExecuting catalog sequence {sequence_id} with prompt: {args.prompt}")
            print(f"Results will be streamed to: {output_file}")
            print(f"Using model(s): {', '.join(models)}")

            # Execute the catalog sequence
            await execute_catalog_sequence(
                user_prompt=args.prompt,
                sequence_id=sequence_id,
                models=models,
                output_file=output_file,
                catalog=catalog,
                max_tokens=1000,
                temperature=0.7,
            )

    if __name__ == "__main__":
        asyncio.run(main())
