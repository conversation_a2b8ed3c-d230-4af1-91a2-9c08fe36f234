<!-- ======================================================= -->
<!-- [2025.04.13 22:18] -->
<!-- 'https://gemini.google.com/app/b54200583c2cb6f5' -->
<!-- 'https://chatgpt.com/c/67fc1b00-db58-8008-9ccc-228ccc2c22f9' -->

please write it as a highly optimized and generalized instruction sequence, but do so specifically tailored with the kind of techstack in mind, reference (write a completely new sequence based on the provided info, but optimize it and ensure it will yeld concistently predictable and good autonomous codebase familiarization).


    #### `0064-a-react-typescript-foundation-identification.md`

    ```markdown
    [React-TypeScript Foundation Identification] Your objective is not to provide generic framework analysis, but to precisely identify the specific React and TypeScript implementation foundations—excavating the exact versions, configuration patterns, and core structural decisions shaping this modern frontend codebase. Execute as `{role=react_typescript_archeologist; input=[project_files:dir_tree]; process=[identify_react_version_signatures(), detect_typescript_configuration_patterns(), examine_vite_setup_and_plugins(), map_tsconfig_compiler_options(), analyze_eslint_ruleset(), catalog_base_dependencies()]; output={foundation_details:{react_version:str, typescript_setup:{tsconfig_features:list[str], strictness_level:str}, vite_configuration:{dev_optimizations:list[str], build_strategies:list[str]}, essential_dependencies:list[dict]}}}`
    ```

    ---

    #### `0064-b-tailwind-styling-architecture-analysis.md`

    ```markdown
    [Tailwind Styling Architecture Analysis] Your directive is not mere CSS review, but targeted Tailwind architecture analysis—uncovering the specific utility implementation, customization patterns, PostCSS pipeline, and component styling strategies that define the visual language. Execute as `{role=tailwind_architecture_analyst; input=[project_files:dir_tree, foundation_details:dict]; process=[decode_tailwind_configuration_customizations(), map_postcss_transformation_pipeline(), identify_utility_composition_patterns(clsx, tailwind_merge), analyze_responsive_breakpoint_strategy(), detect_component_style_encapsulation_approach()]; output={styling_architecture:{tailwind_customizations:{colors:dict, spacing:dict, plugins:list[str]}, composition_strategy:str, responsive_patterns:list[str], dark_mode_implementation:str, styling_conventions:list[str]}}}`
    ```

    ---

    #### `0064-c-routing-navigation-system-mapping.md`

    ```markdown
    [Routing & Navigation System Mapping] Your mission is not to simply check for router presence, but to decode the entire routing ecosystem—mapping the specific React Router implementation, route organization patterns, navigation state management, and URL parameter handling strategies. Execute as `{role=routing_system_cartographer; input=[project_files:dir_tree, foundation_details:dict]; process=[identify_react_router_implementation_version(), reverse_engineer_route_definition_patterns(), analyze_navigation_state_handling(), map_dynamic_route_parameter_usage(), discover_route_guard_mechanisms()]; output={routing_system:{router_version:str, route_definition_approach:str, route_organization:{structure:str, patterns:list[str]}, navigation_state_handling:str, parameter_strategies:list[str], route_protection_mechanisms:list[str]}}}`
    ```

    ---

    #### `0064-d-component-library-taxonomy.md`

    ```markdown
    [Component Library Taxonomy] Your imperative is not surface-level component listing, but comprehensive component taxonomy—classifying and categorizing the component ecosystem by type, composition patterns, state requirements, and architectural role. Execute as `{role=component_taxonomist; input=[project_files:dir_tree, foundation_details:dict, styling_architecture:dict]; process=[classify_ui_primitive_components(), identify_layout_orchestration_components(), map_feature_specific_components(), analyze_compound_component_patterns(), detect_higher_order_components(), catalog_lucide_icon_usage()]; output={component_taxonomy:{ui_primitives:{components:list[str], patterns:list[str]}, layout_components:{components:list[str], patterns:list[str]}, feature_components:{by_domain:dict}, composition_patterns:{compound:list[str], hoc:list[str], render_props:list[str]}, icon_implementation:str}}}`
    ```

    ---

    #### `0064-e-state-management-pattern-analysis.md`

    ```markdown
    [State Management Pattern Analysis] Your task is not to generically describe React state, but to perform surgical state pattern analysis—dissecting the specific state management approaches, custom hook implementations, data flow patterns, and state isolation strategies used throughout the application. Execute as `{role=state_pattern_analyst; input=[project_files:dir_tree, component_taxonomy:dict, foundation_details:dict]; process=[categorize_component_local_state_usage(), reverse_engineer_custom_hook_patterns(), map_context_provider_hierarchies(), analyze_data_flow_directions(), identify_state_isolation_boundaries()]; output={state_patterns:{component_state_approaches:{primitives:list[str], patterns:list[str]}, custom_hooks:{data_fetching:list[str], form_handling:list[str], ui_logic:list[str]}, context_usage:{global_state:list[str], providers:list[str]}, data_flow_model:str, state_isolation_strategy:str}}}`
    ```

    ---

    #### `0064-f-type-system-architecture-mapping.md`

    ```markdown
    [Type System Architecture Mapping] Your mandate is not cursory TypeScript verification, but comprehensive type system architecture mapping—revealing the sophisticated type structure, interface design patterns, type-driven development approaches, and type safety strategies that form the project's type backbone. Execute as `{role=type_system_architect; input=[project_files:dir_tree, foundation_details:dict, component_taxonomy:dict, state_patterns:dict]; process=[classify_type_definition_strategies(), analyze_interface_design_patterns(), map_type_driven_development_implementations(), evaluate_generic_type_usage(), measure_type_safety_boundaries(), identify_type_utility_patterns()]; output={type_system:{definition_strategies:{approach:str, organization:str}, interface_patterns:list[str], type_driven_practices:list[str], generic_patterns:list[str], utility_types:list[str], type_safety_boundaries:list[str]}}}`
    ```

    ---

    #### `0064-g-feature-organization-decomposition.md`

    ```markdown
    [Feature Organization Decomposition] Your directive is not general folder analysis, but feature organization decomposition—dissecting how features are bounded, composed, organized, and interconnected within the architecture to reveal the project's domain-driven structure. Execute as `{role=feature_organization_analyst; input=[project_files:dir_tree, component_taxonomy:dict, state_patterns:dict, routing_system:dict]; process=[identify_feature_boundary_definitions(), analyze_feature_internal_structure_patterns(), map_feature_interconnection_points(), evaluate_feature_specific_component_design(), decode_feature_state_isolation_approaches()]; output={feature_organization:{boundary_definition_strategy:str, internal_patterns:{structure:str, conventions:list[str]}, cross_feature_dependencies:list[str], feature_specific_components:dict, domain_driven_aspects:list[str]}}}`
    ```

    ---

    #### `0064-h-performance-optimization-strategy-analysis.md`

    ```markdown
    [Performance Optimization Strategy Analysis] Your mission is not theoretical optimization discussion, but concrete performance strategy analysis—identifying the specific code splitting implementations, rendering optimizations, asset handling approaches, and bundle optimization techniques actively employed in the codebase. Execute as `{role=performance_strategist; input=[project_files:dir_tree, foundation_details:dict, component_taxonomy:dict, routing_system:dict]; process=[decode_code_splitting_implementation(), analyze_react_rendering_optimization_techniques(), map_asset_loading_strategies(), evaluate_bundle_size_optimization_approaches(), identify_vite_specific_optimizations()]; output={performance_strategies:{code_splitting:{implementation:str, patterns:list[str]}, rendering_optimizations:list[str], asset_handling:{images:str, fonts:str, static_files:str}, bundle_optimizations:list[str], vite_specific_techniques:list[str]}}}`
    ```

    ---

    #### `0064-i-developer-experience-pattern-recognition.md`

    ```markdown
    [Developer Experience Pattern Recognition] Your function is not general DX opinions, but specific developer experience pattern recognition—uncovering the conventions, architectural decisions, and toolchain configurations explicitly designed to enhance developer productivity and codebase maintainability. Execute as `{role=dx_pattern_recognizer; input=[project_files:dir_tree, foundation_details:dict, type_system:dict, styling_architecture:dict, feature_organization:dict]; process=[identify_naming_convention_patterns(), map_folder_structure_consistency(), analyze_code_organization_principles(), detect_tooling_optimization_configurations(), evaluate_type_safety_guardrails()]; output={dx_patterns:{naming_conventions:{components:str, hooks:str, types:str, files:str}, organization_principles:list[str], consistent_patterns:list[str], maintainability_features:list[str], productivity_optimizations:list[str]}}}`
    ```

    ---

    #### `0064-j-codebase-exploration-workflow-synthesis.md`

    ```markdown
    [Codebase Exploration Workflow Synthesis] Your imperative is not abstract exploration advice, but concrete workflow synthesis—crafting a precise, ordered sequence of codebase exploration steps specifically optimized for this React-TypeScript-Vite-Tailwind architecture that maximizes comprehension efficiency. Execute as `{role=exploration_workflow_synthesizer; input=[foundation_details:dict, styling_architecture:dict, routing_system:dict, component_taxonomy:dict, state_patterns:dict, type_system:dict, feature_organization:dict, performance_strategies:dict, dx_patterns:dict]; process=[design_foundational_exploration_sequence(), create_progressive_component_investigation_path(), formulate_feature_traversal_strategy(), establish_type_system_learning_approach(), construct_state_management_understanding_path()]; output={exploration_workflow:{initial_steps:list[dict], component_investigation:list[dict], feature_traversal:list[dict], type_system_approach:list[dict], state_management_path:list[dict], recommended_order:list[str], time_allocation:dict}}}`
    ```

    ---

    #### `0064-k-feature-development-protocol-construction.md`

    ```markdown
    [Feature Development Protocol Construction] Your mandate is not theoretical development guidance, but practical protocol construction—assembling a concrete, step-by-step feature development protocol specifically calibrated to this codebase's patterns, conventions, and architectural decisions for consistent implementation. Execute as `{role=protocol_constructor; input=[foundation_details:dict, styling_architecture:dict, routing_system:dict, component_taxonomy:dict, state_patterns:dict, type_system:dict, feature_organization:dict, dx_patterns:dict]; process=[formulate_feature_planning_steps(), design_type_definition_sequence(), establish_component_creation_procedure(), define_state_implementation_approach(), construct_routing_integration_method(), create_styling_application_protocol()]; output={development_protocol:{planning_phase:list[dict], type_definition_phase:list[dict], component_creation_phase:list[dict], state_implementation_phase:list[dict], routing_integration_phase:list[dict], styling_phase:list[dict], testing_approach:list[dict], review_checklist:list[str]}}}`
    ```

    ---

    #### `0064-l-architectural-integrity-rule-formulation.md`

    ```markdown
    [Architectural Integrity Rule Formulation] Your purpose is not vague best practices, but precise integrity rule formulation—distilling the architecture's implicit and explicit rules into a concrete, prioritized set of architectural principles that maintain the codebase's structural and conceptual integrity. Execute as `{role=integrity_rule_formulator; input=[foundation_details:dict, styling_architecture:dict, routing_system:dict, component_taxonomy:dict, state_patterns:dict, type_system:dict, feature_organization:dict, dx_patterns:dict]; process=[extract_non_negotiable_structural_rules(), formulate_component_composition_principles(), define_type_safety_requirements(), establish_state_management_directives(), construct_styling_guidelines(), derive_feature_organization_mandates()]; output={integrity_rules:{tier1_foundational:{rules:list[dict], rationales:list[str]}, tier2_structural:{rules:list[dict], rationales:list[str]}, tier3_conventional:{rules:list[dict], rationales:list[str]}, violation_indicators:list[str], maintenance_guidelines:list[str]}}}`
    ```

    ---

    #### `0064-m-techstack-coherence-visualization.md`

    ```markdown
    [Techstack Coherence Visualization] Your directive is not abstract architecture diagramming, but techstack coherence visualization—creating concrete mental models and visualization approaches for comprehending how the React, TypeScript, Vite, and Tailwind elements interlock into a unified, coherent system. Execute as `{role=coherence_visualizer; input=[foundation_details:dict, styling_architecture:dict, routing_system:dict, component_taxonomy:dict, state_patterns:dict, type_system:dict, feature_organization:dict, performance_strategies:dict]; process=[design_component_hierarchy_visualization(), create_type_flow_mental_model(), construct_state_propagation_visualization(), formulate_build_process_conceptualization(), develop_feature_interdependency_mapping()]; output={visualization_models:{component_hierarchy:{model:str, key_elements:list[str]}, type_flow:{model:str, key_concepts:list[str]}, state_propagation:{model:str, patterns:list[str]}, build_process:{model:str, key_stages:list[str]}, feature_map:{model:str, connections:list[str]}}}}`
    ```

    ---

    #### `0064-n-comprehensive-techstack-cheatsheet-compilation.md`

    ```markdown
    [Comprehensive Techstack Cheatsheet Compilation] Your mission is not disconnected documentation, but comprehensive cheatsheet compilation—synthesizing all analyses into a unified, hierarchical reference document that crystallizes the entire React-TypeScript-Vite-Tailwind architecture for immediate, practical application. Execute as `{role=cheatsheet_compiler; input=[foundation_details:dict, styling_architecture:dict, routing_system:dict, component_taxonomy:dict, state_patterns:dict, type_system:dict, feature_organization:dict, performance_strategies:dict, dx_patterns:dict, exploration_workflow:dict, development_protocol:dict, integrity_rules:dict, visualization_models:dict]; process=[establish_hierarchical_structure(), ensure_cross_referencing(), implement_progressive_disclosure(), prioritize_by_practical_utility(), optimize_for_quick_reference(), eliminate_redundancy()]; output={techstack_cheatsheet:{stack_overview:{technologies:dict, architecture:str}, foundation:{react_typescript:dict, vite:dict}, ui_architecture:{components:dict, styling:dict}, data_architecture:{state:dict, routing:dict}, type_system:{patterns:dict, practices:dict}, workflows:{exploration:dict, development:dict}, rules:{prioritized:dict}, visualization_guides:{mental_models:dict}}}}`
    ```

    ---

    #### `0064-o-practical-application-validation.md`

    ```markdown
    [Practical Application Validation] Your imperative is not theoretical assessment, but practical validation—rigorously testing the cheatsheet against real-world developer scenarios and tasks specific to React-TypeScript-Vite-Tailwind development to ensure comprehensive, actionable guidance. Execute as `{role=practical_validator; input=[techstack_cheatsheet:dict, validation_scenarios:list[str]]; process=[simulate_scenario_application(), identify_guidance_gaps(), test_rule_clarity_and_applicability(), evaluate_workflow_effectiveness(), measure_practical_utility_by_development_stage()]; output={validated_cheatsheet:{refined_content:dict, validation_results:{scenarios_addressed:list[str], gaps_identified:list[str], improvements_made:list[str], practical_utility_score:int, developer_efficiency_impact:str}}}}`
    ```

---

don't forget to view it through the lens of a developer as i've described. as an example a “brilliant” developer will **systematically** iterate through a sequence of steps __methodically__ (rough pseudo-code example-draft):

    1. **Start with the big-picture structure**  and main entry points.
    2. **Gradually drill into details** : routing, styling, and state management patterns.
    3. **Closely inspect specialized hooks, UI components, and third-party integrations** .
    4. **Assess performance and code quality**  to ensure the code is robust and efficient.

---
