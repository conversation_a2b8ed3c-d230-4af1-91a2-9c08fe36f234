
Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters:

---

#### `1`

    #### `0140-a-structure-and-purpose-orientation-scan.md`

    ```markdown
    [Structure & Purpose Orientation Scan] Establish a structural and conceptual baseline. Inventory all top-level components, identify entry/exit points, and map architectural regions to functional roles. Distill the systemâ€™s primary intent from structure and traceable behaviorsâ€”not from wishful documentation. Execute as: `{role=structure_orienter; input=codebase_root:path; process=[inventory_directories_and_files(), classify_modules_by_function(), trace_entry_exit_points(), infer_primary_purpose_from_structure_and_code()], output={structure_map:dict, core_purpose:str}}`
    ```

    #### `0140-b-critical-path-and-complexity-trace.md`

    ```markdown
    [Critical Path & Complexity Trace] Map the core workflows aligned with the `core_purpose`. Trace control/data flows across modules. Identify coupling clusters, cognitive load bottlenecks, and convoluted abstractions. Focus on where structure fractures clarity. Execute as: `{role=workflow_tracer; input={structure_map:dict, core_purpose:str}; process=[trace_primary_workflows(), visualize_data_and_control_flow(), detect_cross-module complexity zones(), flag indirect abstraction breakpoints()], output={workflow_map:dict, complexity_hotspots:list}}`
    ```

    #### `0140-c-inherent-clarity-and-responsibility-audit.md`

    ```markdown
    [Inherent Clarity & Responsibility Audit] Evaluate how well the codebase *explains itself*. Assess naming quality, logic transparency, structural cohesion, and adherence to SRP. Identify regions that rely on commentary for basic clarityâ€”an unforgivable sin in elegant architecture. Execute as: `{role=clarity_auditor; input={workflow_map:dict, codebase_content:dict}; process=[score_naming_and_structural_expressiveness(), identify_comment-reliant_zones(), detect_srp_violations_and_multi-responsibility_components(), assess_cohesion_per_module()], output={clarity_issues:dict, srp_violations:list, cohesion_gaps:list}}`
    ```

    #### `0140-d-commentary-minimization-and-retention-pass.md`

    ```markdown
    [Commentary Minimization & Retention Pass] Apply the minimalist standard: retain only commentary that explains non-obvious *why*. Remove all commentary that parrots *what* or *how*. The structure and naming must render all such fluff unnecessary. Execute as: `{role=commentary_refiner; input={codebase_content:dict}; process=[extract_all_comments(), classify_by_intent(why_what_how), remove_non-essential_comments(), retain_or_rewrite_only_crucial_explanations()], output={comment_changes:list}}`
    ```

    #### `0140-e-structural-and-logical-simplification-targeting.md`

    ```markdown
    [Structural & Logical Simplification Targeting] Merge prior findings to identify prime cleanup targets. Seek low-risk, high-impact opportunities: dead code, poor cohesion, redundant logic, confusing file/module layout, and ambiguous naming. Execute as: `{role=simplification_targeter; input={clarity_issues:dict, srp_violations:list, complexity_hotspots:list}; process=[rank_targets_by_impact_vs_risk(), group_targets_by category (logic, naming, structure), detect redundant/fragmented flows(), prepare simplified alternatives], output={simplification_targets:list}}`
    ```

    #### `0140-f-prioritized-cleanup-plan-synthesis.md`

    ```markdown
    [Prioritized Cleanup Plan Synthesis] Produce a surgically ordered, traceable plan of refactoring actions. Prioritize safety, readability yield, and principle alignment. Each action must be atomic, reversible, and elegance-inducing. Execute as: `{role=cleanup_planner; input={simplification_targets:list, comment_changes:list}; process=[merge_cleanup_targets_and_comment_actions(), order_by_impact_and_simplicity(), sequence_refactors_for_dependency-safety(), produce_cleanup_steps_with_rationale()], output={prioritized_plan:list}}`
    ```

    #### `0140-g-guided-implementation-and-integrity-check.md`

    ```markdown
    [Guided Implementation & Integrity Check] Apply the `prioritized_plan`. Execute refactorings step-by-step. After each logical unit of work, verify functional preservation via tests or manual flow assertions. Execute as: `{role=executor_validator; input={codebase_root:path, prioritized_plan:list}; process=[execute_stepwise_changes(), run_tests_or_manual_checks(), confirm_no_regression(), adjust_plan_if_needed()], output={cleaned_codebase:path, integrity_verified:bool}}`
    ```

    #### `0140-h-final-principle-alignment-review.md`

    ```markdown
    [Final Principle Alignment Review] Assess the cleaned codebase against the core ideals: simplicity, structural clarity, cohesion, self-explanation. Identify remaining violations or incomplete refinements. Decide if further iteration is warranted. Execute as: `{role=final_reviewer; input={cleaned_codebase:path, structure_map:dict, core_purpose:str}; process=[score_against_core_principles(), highlight_any_remaining_opportunities(), estimate cost/impact of another cycle(), decide iteration necessity()], output={final_report:dict, recommend_next_cycle:bool}}`
    ```

    #### `0140-i-cleanup-impact-summary-and-developer-handoff.md`

    ```markdown
    [Cleanup Impact Summary & Developer Handoff] Summarize the cleanupâ€™s purpose, principles enforced, actions taken, and resulting benefits. Provide a crystal-clear guide for future developers: what changed, why it mattered, and how to uphold its new elegance. Execute as: `{role=cleanup_reporter; input={prioritized_plan:list, final_report:dict}; process=[map_actions_to principles(), summarize clarity and maintainability improvements(), describe before/after structural flow, write future-facing developer notes()], output={handoff_summary:str}}`
    ```

---

#### `2`

    #### `0141-a-baseline-structure-intent-map.md`

    ```markdown
    [Baseline Structure & Intent Map] Begin by scanning the full codebase to map its high-level structure. Identify main files, directories, and components. Determine entry points and infer the system’s core purpose from its structure. This establishes an aligned foundation for cleanup. Execute as `{role=baseline_mapper; input=codebase_root:path; process=[inventory_top_level_structure(), identify_entry_exit_points(), outline_primary_components_and_domains(), distill_core_purpose()]; output={structure_overview:dict, core_purpose:str}}`
    ```

    #### `0141-b-critical-path-and-flow-mapping.md`

    ```markdown
    [Critical Path & Flow Mapping] Trace key workflows that drive the system’s primary purpose. Map control and data flow through components. Identify logic complexity, coupling hotspots, and conceptual bottlenecks that affect clarity. Execute as `{role=flow_mapper; input={structure_overview:dict, core_purpose:str}; process=[trace_primary_execution_paths(), map_internal_data_transformations(), detect_cross-module_coupling(), isolate_workflow_complexity_zones()]; output={workflow_map:list, complexity_zones:list}}`
    ```

    #### `0141-c-readability-cohesion-and-comment-dependence-audit.md`

    ```markdown
    [Readability, Cohesion & Comment Dependence Audit] Evaluate identifier clarity, self-explanation, and cohesion within modules. Identify places where comments are compensating for poor naming or structure. Highlight SRP violations, comment-overreliance, and unclear abstractions. Execute as `{role=clarity_auditor; input={workflow_map:list, complexity_zones:list}; process=[assess_identifier_naming_quality(), evaluate_module_cohesion(), locate_comment-dependent_clarity_zones(), detect_responsibility_violations(), summarize_readability_issues()]; output={clarity_audit_report:dict}}`
    ```

    #### `0141-d-simplification-opportunities-synthesis.md`

    ```markdown
    [Simplification Opportunities Synthesis] Consolidate audit results to detect opportunities for reducing complexity. Target dead code, redundant logic, tight coupling, fragmented responsibility, and over-commented zones. Execute as `{role=simplification_scanner; input={clarity_audit_report:dict}; process=[detect_dead_code(), isolate_redundant_patterns(), assess structure_naming_confusion(), flag over-fragmentation_and_low_cohesion(), synthesize_simplification_targets()]; output={simplification_targets:dict}}`
    ```

    #### `0141-e-cleanup-plan-generation.md`

    ```markdown
    [Cleanup Plan Generation] Translate findings into an actionable cleanup plan. Prioritize clarity and simplicity gains that are low risk and high value. Focus on improving naming, removing clutter, restructuring modules, and reducing commentary dependency. Execute as `{role=cleanup_planner; input={simplification_targets:dict}; process=[rank_opportunities_by_impact_and_safety(), define_discrete_cleanup_actions(), group_actions_by_scope(structure, logic, naming, comments), generate_execution_plan_sequence()]; output={cleanup_plan:list}}`
    ```

    #### `0141-f-guided-structure-and-logic-refinement.md`

    ```markdown
    [Guided Structure & Logic Refinement] Execute the `cleanup_plan` step-by-step. Apply minimal, high-impact changes that reinforce clarity: structural simplifications, naming refactors, logic consolidations, and decoupling. Execute as `{role=refactor_executor; input={codebase_root:path, cleanup_plan:list}; process=[perform_directory_file_restructuring(), apply_naming_and_cohesion_improvements(), simplify_control_flows(), remove_dead_code(), reduce_to_essential_comments()]; output={refactored_codebase:path}}`
    ```

    #### `0141-g-integrity-verification-and-principle-alignment-check.md`

    ```markdown
    [Integrity Verification & Principle Alignment Check] Verify that refactoring preserved behavior and improved alignment with core principles: Simplicity, SRP, Minimalism, Self-Explanation. Run tests, check for regressions, and review clarity. Execute as `{role=validation_checker; input={refactored_codebase:path, core_purpose:str}; process=[run_test_suite_or_manual_flow_validation(), check_structural_alignment_with_intent(), confirm_code_self_explanation(), audit_against_design_principles()], output={validation_report:dict(functional_equivalence:bool, clarity_score:float, remaining_gaps:list)}}`
    ```

    #### `0141-h-recursive-refinement-checkpoint.md`

    ```markdown
    [Recursive Refinement Checkpoint] Review the `validation_report`. If further clarity or simplification gains are evident and low cost, define a focused scope for another refinement cycle. Else, finalize cleanup and report outcome. Execute as `{role=refinement_assessor; input={validation_report:dict}; process=[analyze_remaining_clarity_gaps(), assess_effort_vs_impact_for_next_pass(), determine_if_next_cycle_needed(), define_refinement_scope_or_finalize()], output={next_steps:dict(proceed:bool, focus:list, summary:str)}}`
    ```

---

#### `3`

    #### `0142-a-cleanup-step-01-structural-purpose-orientation.md`
    ```markdown
    [Structural & Purpose Orientation] Begin with a complete structural sweep: identify major directories, files, modules, and boundaries. Infer the systemâ€™s intended purpose and primary workflows through code inspection and artifact analysis. Execute as: `{role=structure_orienter; input=codebase_root:path; process=[scan_directory_structure(), inventory_modules_and_artifacts(), identify_entry_exit_points(), infer_core_purpose_from_usage_patterns()], output={structure_map:dict, core_purpose:str}}`
    ```

    #### `0142-b-cleanup-step-02-logic-path-dependency-trace.md`
    ```markdown
    [Logic Path & Dependency Trace] Map control, data, and dependency flows essential to the `core_purpose`. Visualize high-impact logic routes, critical dependencies, and coupling zones. Execute as: `{role=logic_mapper; input={structure_map:dict, core_purpose:str}; process=[trace_control_and_data_flow(), detect_module_import_graph(), identify_coupling_clusters(), highlight_logic_hotspots()], output={workflow_map:dict, dependency_map:dict, complexity_zones:list}}`
    ```

    #### `0142-c-cleanup-step-03-clarity-cohesion-self-explanation-audit.md`
    ```markdown
    [Clarity, Cohesion & Self-Explanation Audit] Audit the codebase for naming clarity, structural cohesion, and readability without comments. Identify SRP violations, over-fragmentation, and areas where structure fails to explain itself. Execute as: `{role=clarity_auditor; input={workflow_map:dict, dependency_map:dict}; process=[assess_identifier_clarity(), locate cohesion/srp violations(), detect comment-dependent comprehension(), summarize self-explanation weak points()], output={clarity_report:dict, cohesion_findings:list, comment_dependence:list}}`
    ```

    #### `0142-d-cleanup-step-04-cleanup-target-opportunity-synthesis.md`
    ```markdown
    [Cleanup Target & Opportunity Synthesis] Consolidate findings to isolate cleanup zones with highest clarity gain vs effort. Focus on logic simplification, structural realignment, naming improvements, and comment reduction. Execute as: `{role=opportunity_synthesizer; input={clarity_report:dict, cohesion_findings:list, comment_dependence:list}; process=[rank_issues_by_impact_vs_effort(), group related issues by scope(), isolate safe/high-value cleanup zones(), define actionable targets()], output={cleanup_targets:list, opportunity_matrix:dict}}`
    ```

    #### `0142-e-cleanup-step-05-prioritized-refactor-plan.md`
    ```markdown
    [Prioritized Refactor Plan] Generate an atomic cleanup blueprint. Each action must yield measurable clarity/simplicity benefit with low risk. Order steps to optimize safety, flow, and minimal disruption. Execute as: `{role=refactor_planner; input={cleanup_targets:list}; process=[decompose_targets_into_refactor_steps(), rank_by safety-impact ratio(), establish task dependencies, structure into sequential plan()], output={refactor_plan:list}}`
    ```

    #### `0142-f-cleanup-step-06-structure-and-naming-refactor-pass.md`
    ```markdown
    [Structure & Naming Refactor Pass] Apply structural reorganizations and identifier renaming from `refactor_plan`. Ensure improved grouping, clearer file/module names, reduced fragmentation, and more intuitive navigation. Execute as: `{role=structure_executor; input={refactor_plan:list, codebase_root:path}; process=[restructure_directories_and_files(), refactor module/group boundaries(), unify naming conventions, update all references/imports], output={realigned_structure:path}}`
    ```

    #### `0142-g-cleanup-step-07-logic-simplification-and-comment-reduction.md`
    ```markdown
    [Logic Simplification & Comment Reduction] Simplify internal logic, consolidate functionality, and remove dead/duplicated code. Strip unnecessary commentsâ€”retain only *why-level* intent comments. Execute as: `{role=logic_simplifier; input={realigned_structure:path, refactor_plan:list}; process=[remove_redundant_logic(), enforce single-responsibility, apply naming-based clarification, purge/comment-refine non-essential notes()], output={refactored_codebase:path}}`
    ```

    #### `0142-h-cleanup-step-08-validation-pass-and-principle-alignment.md`
    ```markdown
    [Validation Pass & Principle Alignment Review] Run regression validation and audit final structure against principles: Simplicity, Clarity, SRP, Minimalism, and Self-Explanation. Verify functional equivalence and structural clarity. Execute as: `{role=validator; input={original_codebase:any, refactored_codebase:path}; process=[run regression checks(), audit for principle adherence(), check navigation and readability, record any remaining issues()], output={validation_report:dict(functional_equivalence:bool, principle_score:float, residual_issues:list}}`
    ```

    #### `0142-i-cleanup-step-09-recursive-refinement-checkpoint.md`
    ```markdown
    [Recursive Refinement Checkpoint] If validation passes and structure is aligned, conclude. If complexity, redundancy, or naming gaps remain with reasonable refactor potential, define scope for next cycle. Execute as: `{role=refinement_reviewer; input={refactored_codebase:path, validation_report:dict}; process=[identify remaining cleanup value(), weigh cost/benefit of another pass(), define next cycleâ€™s narrowed focus()], output={next_pass_decision:dict(proceed:bool, recommended_scope:list)}}`
    ```

    #### `0142-j-cleanup-step-10-cleanup-summary-and-principle-rationale-report.md`
    ```markdown
    [Cleanup Summary & Principle Rationale Report] Document the rationale and outcome for each major change, referencing clarity/cohesion principles. Provide a human-readable summary suitable for onboarding or auditing. Execute as: `{role=impact_reporter; input={refactor_plan:list, validation_report:dict}; process=[map actions to principle-based motivations(), summarize before/after structural clarity(), compile onboarding notes and rationale summaries()], output={cleanup_summary:str}}`
    ```
    ```

