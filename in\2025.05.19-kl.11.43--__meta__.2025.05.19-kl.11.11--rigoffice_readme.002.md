# CONTEXT

RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).

# SOLUTION

i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *"Complexity is the enemy of execution" - Optimized for engineers, by engineers*.

## WORKFLOW

This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).

# REQUIREMENTS

i want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the "canvas" (of `README.md`) with unusefull noise (unneccessary bloat).

# OBJECTIVE

consolidate and extract the core components of the  `README.md` variations provided below - then produce a **finalized** `README.md` **IN LESS THAN <100 LINES OF CODE** based on all parameters and the variations provided below:

    # Dir `readme_variations`

    ### File Structure

    ```
    ├── README_v01.md
    ├── README_v02.md
    ├── README_v03.md
    ├── README_v04.md
    ├── README_v05.md
    ├── README_v06.md
    ├── README_v07.md
    ├── README_v08.md
    ├── README_v09.md
    ├── README_v10.md
    ├── README_v11.md
    ├── README_v12.md
    ├── README_v13.md
    └── README_v14.md
    ```

    ---

    #### `README_v01.md`

    ```markdown
        # RigOfficeDownloader

        ## Overview
        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

        ## Key Features
        - Three-stage workflow: Documents -> Files -> Downloads
        - Interactive menu for flexible execution
        - User control points via Markdown interfaces
        - Smart file organization with subfolder support
        - Configurable filter chains

        ## Setup & Usage
        1. Run `py_venv_init.bat` to create the Python environment
        2. Run `RigOfficeDownloader-v4.bat` to start the application
        3. Use the interactive menu to configure and execute workflow steps

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Preserves document context and relationships

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser (for Selenium automation)
    ```

    ---

    #### `README_v02.md`

    ```markdown
        # RigOfficeDownloader

        ## The Problem
        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
        - Tedious and repetitive
        - Error-prone
        - A poor use of skilled engineering time

        ## The Solution
        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
        1. **Document Retrieval**: Automatically scrapes document metadata
        2. **File Metadata**: Fetches file information for selected documents
        3. **Smart Downloads**: Downloads files with intelligent naming and organization

        ## How It Works
        - Uses Selenium to automate web interactions with RigDoc
        - Exports data to Markdown for user review and selection
        - Applies configurable filters to pre-select relevant documents
        - Organizes downloads with consistent naming patterns

        ## Getting Started
        1. Run `py_venv_init.bat` to set up the environment
        2. Run `RigOfficeDownloader-v4.bat` to launch the application
        3. Follow the interactive menu prompts

        ## Impact
        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.
    ```

    ---

    #### `README_v03.md`

    ```markdown
        # RigOfficeDownloader

        ## Technical Overview
        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

        ## Architecture
        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown
        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads
        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

        ## Workflow Steps
        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information
        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing
        3. **Selection Import**: `md_table_to_json()` imports user selections
        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents
        5. **File Selection**: Export/import cycle for user selection of files
        6. **Download Process**: `download_files()` retrieves selected files with smart naming

        ## Filter Chain System
        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

        ## Setup Instructions
        1. Run `py_venv_init.bat` to initialize the Python environment
        2. Run `RigOfficeDownloader-v4.bat` to execute the application

        ## Version History
        - v1: Basic document retrieval and download
        - v2: JSON/Markdown conversion and user selection
        - v3: Improved error handling and field organization
        - v4: Subfolder support, filter chains, field ordering
    ```

    ---

    #### `README_v04.md`

    ```markdown
        # RigOfficeDownloader
        > Automate document retrieval from NOV's RigDoc system

        ## What This Tool Does
        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

        ## Quick Start Guide
        1. **Setup**: Run `py_venv_init.bat` to create the Python environment
        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application
        3. **Configure**: Enter your rig number and search URLs when prompted
        4. **Run**: Follow the numbered menu to execute each step of the workflow

        ## Workflow Steps Explained
        0. **Change Parameters**: Update rig number and search URLs
        1. **Configure Filters**: Set up automatic document/file selection rules
        2. **Fetch Documents**: Retrieve document metadata from RigDoc
        3. **Review Documents**: Edit the Markdown file to select which documents to process
        4. **Import Selections**: Load your document selections
        5. **Fetch Files**: Get file metadata for selected documents
        6. **Review Files**: Edit the Markdown file to select which files to download
        7. **Import File Selections**: Load your file selections
        8. **Download Files**: Retrieve the selected files

        ## Tips for Success
        - Use filters to automatically pre-select relevant documents
        - Review the Markdown files carefully before proceeding to the next step
        - Files will be organized in subfolders based on '/' in their generated names

        ## Need Help?
        Check the source code comments for detailed information about each function and workflow step.
    ```

    ---

    #### `README_v05.md`

    ```markdown
        # RigOfficeDownloader

        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

        ```
        Documents -> Files -> Downloads
        ```

        ## Features
        - Three-stage workflow with user control points
        - Interactive menu for flexible execution
        - Configurable filter chains for automatic selection
        - Smart file organization with subfolder support
        - Markdown interfaces for document/file review

        ## Quick Start
        ```
        1. Run py_venv_init.bat
        2. Run RigOfficeDownloader-v4.bat
        3. Follow the interactive menu
        ```

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Handles errors gracefully

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser
    ```

    ---

    #### `README_v06.md`

    ```markdown
        # RigOfficeDownloader

        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.

        ## Key Features
        - **Three-Stage Workflow**: Document selection → File selection → Download
        - **Metadata Preservation**: Structured naming with revision/case numbers
        - **Interactive Review**: Markdown tables for manual inclusion flags
        - **Smart Organization**: Automatic subfolder creation via naming patterns
        - **Filter System**: Pattern-based inclusion/exclusion rules
        - **Browser Automation**: Smart waiting strategies and session management

        ## Workflow Process
        ```python
        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json
        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true
        3. Import Selections     # Update JSON with user choices
        4. Fetch Files           # Get file listings → <rig>-b-files.json
        5. Export Files MD       # <rig>-b-files.md - Set item_download=true
        6. Import File Choices   # Update file selections
        7. Download Files        # Auto-organized with subfolder support
        ```

        ## Configuration (CONFIG Section)
        ```python
        {
            "rig_number": "R0000.020",  # Target rig ID
            "search_urls": [            # Predefined search templates
                "https://rigdoc.nov.com/search/rigsearch?q=...",
                "https://rigdoc.nov.com/search/rigsearch?q=..."
            ],
            "filters": [                # Sequential processing rules
                {
                    "type": "docs",     # Apply to documents/files
                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching
                    "field": "item_include",     # Field to modify
                    "value": True       # Set True/False based on match
                }
            ]
        }
        ```

        ## Setup & Usage
        ```bash
        # Initialize environment
        py_venv_init.bat
        py_venv_pip_install.bat

        # Run modes
        RigOfficeDownloader-v4.py [rig_number] [mode]

        Modes:
        --auto         # Full automation
        --interactive  # Step-by-step control
        --config       # Modify search templates/filters
        ```

        ## Key Configuration Patterns
        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)
        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)
        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern

        ## Requirements
        - Chrome Browser + ChromeDriver
        - Active RigDoc credentials
        - Python 3.8+ with dependencies from requirements.txt
        - Network access to rigdoc.nov.com

        ## Advanced Features
        - **Path Sanitization**: Auto-clean special chars while preserving /
        - **Deduplication**: Hash-based conflict resolution
        - **Field Ordering**: Customizable JSON/Markdown columns
        - **Smart Scrolling**: Progressive page loading detection

        > Reduces documentation prep time by 60-75% compared to manual retrieval
        > Version 4.0 | Active development with subfolder support
    ```

    ---

    #### `README_v07.md`

    ```markdown

        ## Overview
        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.
        - Use case: curate, review, and batch-download rig-related documents and technical files.

        ## Directory Structure

        * `outputs/` (BASE\_OUTPUT): All results stored here
        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)
        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

        ## Pipeline Overview

        1. Change search parameters (rig number, URLs)
        2. Configure filter chain (add, edit, delete, toggle, reorder filters)
        3. Fetch docs (scrape data from rigdoc.nov.com)
        4. Export docs to Markdown (for selection/editing)
        5. Import docs from Markdown (sync edited selection)
        6. Fetch candidate files linked to selected docs
        7. Export file list to Markdown (for editing/selecting files for download)
        8. Import updated file list from Markdown
        9. Download marked files (PDFs only, via Chrome)

        ## Manual Editing

        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
        * Set `item_include` (docs) and `item_download` (files) fields

        ## Running the Tool

        ```bash
        python rigdocscraper.py
        ```

        * Interactive menu enables step selection (numbers/comma/space-separated)
        * Supports adjusting parameters, filter configuration, and reviewing batch steps
        * Prompts will guide through editing, import/export, and download procedures

        ## Troubleshooting

        * Requires functioning Chrome installation; verify webdriver-manager compatibility
        * Common issues: browser launch failures, login/captcha requirements, file permissions
        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress
    ```

    ---

    #### `README_v08.md`

    ```markdown
        # RigOfficeDownloader

        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.

        ---

        ## 1. Overview

        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.

        ---

        ## 2. The Problem

        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.
        - The repetitive process is **error-prone** and distracts from real engineering work.

        ---

        ## 3. The Solution

        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.
        - Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.

        ---

        ## 4. Key Features

        - **Three-Stage Workflow**
          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.
          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.
          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.

        - **Interactive Menu**: Allows step-by-step or fully automated runs.
        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.
        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.
        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.
        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.

        ---

        ## 5. Workflow Steps

        1. **Change Parameters**: (Optional) Update rig number, search URLs.
        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.
        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).
        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.
        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.
        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).
        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.
        8. **Import Updated Files**: Sync file selection from Markdown to JSON.
        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.

        ---

        ## 6. Architecture & Technology

        - **Core Stack**:
          - **Python 3.6+**
          - **Selenium** for browser automation
          - **BeautifulSoup** for HTML parsing
          - **JSON/Markdown** for data storage and user edits
        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.
        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.
        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.

        ---

        ## 7. Directory Structure

    ```

    ---

    #### `README_v09.md`

    ```markdown
        ## Intent
        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.

        ## Key Project Aspects

        ### Primary Problem
        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.

        ### Solution Approach
        A Python-based utility that:
        1. Automatically scrapes document metadata from RigOffice
        2. Extracts file information from those documents
        3. Downloads and organizes selected files based on user criteria

        ### Current State
        Functional working prototype that:
        - Uses a 3-step workflow (document metadata → file metadata → download)
        - Stores intermediate results in JSON format
        - Allows user intervention between steps
        - Provides progress feedback

        ### Critical Next Steps
        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches
        2. **Implement file hash checking** to prevent redundant downloads
        3. **Improve progress visibility** during lengthy scraping operations

        ### Core Technical Pattern
        A single-file, modular approach using:
        - Selenium for browser automation
        - JSON for data storage
        - Three-stage processing with user control points
        - Incremental updates to avoid redundant work

        ### Key Success Metrics
        - Reduce documentation gathering time by 75%+
        - Ensure reliable retrieval of required documentation
        - Organize files in a way that streamlines workflow
        - Support both broad searches (by rig number) and targeted searches (by specific documents)
    ```

    ---

    #### `README_v10.md`

    ```markdown
        ### RigOfficeDownloader Utility Workflow

        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:

        1. Fetch Documents
        - The utility starts by scraping document metadata from predefined search URLs
        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
        - Each document entry includes metadata like title, document number, revision, etc.
        - All documents are initially marked with item_include=False
        - Each document gets an item_generated_name for better identification

        2. Export Documents to Markdown
        - The JSON data is exported to a Markdown table: <rig>-a-docs.md
        - This allows the user to easily review and edit which documents to include
        - The user is expected to edit the Markdown file and set item_include=true for desired documents

        3. Import Updated Document Data
        - After the user edits the Markdown file, the utility imports the changes back to the JSON file
        - This updates which documents are marked for file retrieval

        4. Fetch Files for Selected Documents
        - For each document with item_include=true, the utility scrapes file metadata
        - File data is saved to <rig>-b-files.json
        - Each file is initially marked with item_download=False
        - Files inherit the document's item_generated_name with additional identifiers

        5. Export Files to Markdown
        - The file data is exported to a Markdown table: <rig>-b-files.md
        - The user reviews and edits which files to download by setting item_download=true

        6. Import Updated File Data
        - After editing, the utility imports the changes back to the JSON file
        - This updates which files are marked for download

        7. Download Selected Files
        - Files with item_download=true are downloaded
        - Files are named according to their item_generated_name + extension
        - The utility supports creating subfolders based on '/' in the item_generated_name
        - Files are saved to the outputs/downloads/<rig> directory

        Interactive Menu
        - The utility provides an interactive menu where the user can choose which steps to execute
        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed
        - The user can also update the rig number and search URLs through this menu

        Key Features
        - Automatic document and file metadata scraping
        - User-friendly Markdown editing interface
        - Customizable file naming with item_generated_name
        - Support for subfolder organization in downloads
        - Deduplication of documents and files
        - Configurable field ordering for JSON and Markdown exports

        Technical Implementation
        - Uses Selenium with Chrome WebDriver for web scraping
        - Implements smart waiting strategies for page loading
        - Handles browser sessions with proper cleanup
        - Provides progress feedback during operations
        - Sanitizes filenames for valid paths
    ```

    ---

    #### `README_v11.md`

    ```markdown
        # RigOfficeDownloader

        ## Overview
        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.

        ## Key Features
        - **Three-Stage Workflow**: Documents → Files → Downloads
        - **Interactive Menu**: Choose which steps to execute
        - **User Control Points**: Review and select documents/files via Markdown interfaces
        - **Smart File Organization**: Subfolder support based on naming patterns
        - **Configurable Filters**: Apply filter chains to automatically select relevant documents

        ## Workflow
        1. **Document Retrieval**: Scrapes document metadata from RigDoc
        2. **Document Selection**: Exports to Markdown for user review and selection
        3. **File Metadata**: Fetches file information for selected documents
        4. **File Selection**: Exports to Markdown for user review and selection
        5. **Download**: Downloads selected files with intelligent naming and organization

        ## Setup
        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment
        2. The script will:
           - Find available Python installations
           - Create a virtual environment
           - Install required packages from requirements.txt

        ## Usage
        1. Run `RigOfficeDownloader-v4.bat` to start the application
        2. Use the interactive menu to:
           - Configure search parameters
           - Run specific workflow steps
           - Apply filter chains
           - Review and select documents/files

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Handles errors gracefully
        - Preserves document context and relationships

        ## File Structure
        ```
        outputs/
        ├── data/           # JSON and Markdown files for documents and files
        └── downloads/      # Downloaded files organized by rig number
        ```

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser (for Selenium automation)
    ```

    ---

    #### `README_v12.md`

    ```markdown
        # RigOfficeDownloader

        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

        ```
        Documents -> Files -> Downloads
        ```

        ## Overview
        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.

        ## Key Features
        - Three-stage workflow with user control points
        - Interactive menu for flexible execution
        - Configurable filter chains for automatic selection
        - Smart file organization with subfolder support
        - Markdown interfaces for document/file review

        ## Workflow
        1. **Document Retrieval**: Scrapes document metadata from RigDoc
        2. **Document Selection**: Exports to Markdown for user review and selection
        3. **File Metadata**: Fetches file information for selected documents
        4. **File Selection**: Exports to Markdown for user review and selection
        5. **Download**: Downloads selected files with intelligent naming and organization

        ## Setup
        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment
        2. Run `RigOfficeDownloader-v4.bat` to start the application

        ## Usage
        Choose from the interactive menu:
        ```
        [0] Change search parameters
        [1] Configure filter chain
        [2] Fetch docs (scrape initial data)
        [3] Export docs (to Markdown for editing)
        [4] Import updated doc data
        [5] Fetch files (prepare files for download)
        [6] Export files (to Markdown for editing)
        [7] Import updated file data
        [8] Download files
        ```

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Handles errors gracefully

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser (for Selenium automation)

        ## Version History
        - v1: Basic document retrieval and download
        - v2: JSON/Markdown conversion and user selection
        - v3: Improved error handling and field organization
        - v4: Subfolder support, filter chains, field ordering
    ```

    ---

    #### `README_v13.md`

    ```markdown
        # RigOfficeDownloader

        > **Automate** document retrieval from NOV’s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.

        ---

        ## Overview

        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV’s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.

        **Key Highlights**
        - **Three-Stage Workflow**: Documents → Files → Downloads
        - **Interactive Menu**: Run the entire workflow or individual steps on demand
        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables
        - **Subfolder Support**: Organize downloads with nested folder paths
        - **Filter Chains**: Automate selection based on glob-style patterns
        - **Single-File Simplicity**: All core logic in one script (~500 lines)

        ---

        ## The Problem

        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:
        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders
        - **Error-Prone**: Risk of missing crucial docs or misplacing files
        - **Time Consuming**: Delays the start of high-value engineering tasks

        ---

        ## The Solution

        RigOfficeDownloader streamlines the entire process through an **automated workflow**:
        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)
        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`
        3. **Import** updated doc data back into JSON
        4. **Fetch** file metadata for included docs
        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`
        6. **Import** updated file data
        7. **Download** files with subfolder paths derived from the generated names

        ---

        ## Workflow Steps in Detail

        1. **Change / Configure**
           - Input your **rig number** and **search URLs**.
           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).

        2. **Fetch Documents**
           - Scrape document metadata from RigDoc URLs.
           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.

        3. **Export & Review Docs**
           - Creates `<rig>-a-docs.md`.
           - Manually set `item_include=true` for relevant docs.

        4. **Import Updated Docs**
           - Reads user changes back into JSON.

        5. **Fetch File Metadata**
           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).
           - `item_download=false` by default.

        6. **Export & Review Files**
           - Creates `<rig>-b-files.md`.
           - Set `item_download=true` for desired files.

        7. **Import Updated Files**
           - Sync file selections back into JSON.

        8. **Download Files**
           - Retrieves selected files, applying `item_generated_name` for naming.
           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.

        ---

        ## Directory Structure

    ```

    ---

    #### `README_v14.md`

    ```markdown
        # RigOfficeDownloader
        *Automated Document Retrieval for NOV RigDoc*

        ## Overview
        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.

        ## Key Features
        ```
        ◼ Three-Stage Workflow  ◼ Interactive Menu  ◼ Human Checkpoints
        ◼ Metadata-Powered File Organization  ◼ Pattern-Based Filter Chains
        ◼ Markdown Review Interface  ◼ Smart Browser Automation
        ```

        ## Workflow Architecture
        **1. Document Retrieval**
        ```python
        fetch_docs() → <rig>-a-docs.json  # Scrape metadata
        json_to_md_table() → <rig>-a-docs.md  # Edit item_include=true
        md_table_to_json()  # Commit selections
        ```
        **2. File Processing**
        ```python
        fetch_files() → <rig>-b-files.json  # Get file metadata
        json_to_md_table() → <rig>-b-files.md  # Set item_download=true
        md_table_to_json()  # Finalize choices
        ```
        **3. Smart Download**
        ```python
        download_files()  # Auto-organize with:
        • /subfolders from item_generated_name
        • Sanitized filenames
        • Deduplication
        ```

        ## Getting Started
        ```bash
        # 1. Initialize environment
        py_venv_init.bat

        # 2. Launch utility
        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]
        ```
        **First-Run Configuration**
        ```python
        CONFIG = {
            "rig_number": "R0000.020",
            "search_urls": PROJECTINFO_GAD,  # Prebuilt equipment templates
            "filters": [{
                "type": "docs",
                "pattern": "*G000*",
                "field": "item_include",
                "value": True
            }]
        }
        ```

        ## File Organization
        ```
        outputs/
        ├── data/               # Metadata control files
        │   ├── R1234-a-docs.json
        │   ├── R1234-a-docs.md
        │   ├── R1234-b-files.json
        │   └── R1234-b-files.md
        └── downloads/          # Final organized files
            └── R1234/
                ├── Drilling/
                │   └── G0001_REV03--CASE45-DrillFloor.pdf
                └── Topdrive/
                    └── ASM-00_REV12--CASE32-TopDriveAssembly.dwg
        ```

        ## Interactive Control
        ```
        [0] Change rig number/search URLs    [4] Import doc selections
        [1] Configure filter chain           [5] Fetch file metadata
        [2] Fetch documents                  [6] Export files to Markdown
        [3] Export docs to Markdown          [7] Import file selections
        [8] Download files                   [9] Advanced configuration
        ```

        ## Why This Tool?
        | Manual Process               | Automated Solution           |
        |------------------------------|-------------------------------|
        | 4-6 hours per project setup  | <1 hour with 75% automation  |
        | Error-prone file management  | Metadata-driven organization |
        | No version control           | JSON/Markdown audit trail    |
        | Context loss between teams   | Standardized naming schema   |

    ```
