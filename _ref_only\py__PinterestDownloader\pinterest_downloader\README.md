# Pinterest Downloader

## Overview
Pinterest content downloader with rich CLI interface, supporting images, videos, boards, and profiles using gallery-dl.

## Features
- **Multiple download formats**: Images & Videos, Images Only, Videos Only, with quality options
- **Clipboard integration**: Auto-detects Pinterest URLs from clipboard
- **Rich progress tracking**: Real-time download progress with visual indicators
- **Interactive CLI**: User-friendly prompts and selections
- **Comprehensive logging**: Detailed operation logs with automatic cleanup
- **Board/Profile support**: Download entire boards or user profiles
- **Powered by gallery-dl**: Reliable and feature-rich downloading engine

## Quick Start
Run `run.bat` to start the interactive downloader (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "URL1" "URL2" -op "output/path"
```

## Additional Tools
- `gallerydl_upgrade.bat` - Upgrade gallery-dl to the latest version

## Dependencies
Managed via `pyproject.toml` with uv package manager.
