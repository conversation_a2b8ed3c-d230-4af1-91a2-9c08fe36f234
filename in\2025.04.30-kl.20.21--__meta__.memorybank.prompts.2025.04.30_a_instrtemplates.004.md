
# SCENARIO

### Context:

- These instruction templates are optimally designed because they deconstruct complexity and transcend mere summarization by sequentially extracting, abstracting, and refining content toward the highest possible synthesis of generalized, immediately useful value. Each template is crafted to: (1) Identify and abstract the core underlying principle or dynamic driving maximal utility; (2) Connect these abstractions to universal truths or human/natural tendencies, thereby deepening both relevance and adaptability; (3) Rigorously eliminate redundancy and superficiality at every stage, preserving only the most potent, transferable insight; (4) Recast output into forms that maximize clarity, actionable depth, structural resonance, and adaptability—whether through evocative distillation, dynamic mapping, or vulnerability-rooted exploration; and (5) Enforce boundaries that prevent digression, ambiguity, or unnecessary elaboration. The single most critical transformation these patterns achieve is turning any input—regardless of complexity, specificity, or context—into pure, high-yield knowledge: universally resonant, unambiguously clear, and primed for direct transfer or reuse across domains. Hence, their optimality lies in persistently driving content from mere information toward its most essential, connection-rich, and action-ready form.

### Defining Characteristics of Optimal Patterns:

- Achieve universal applicability by abstracting specifics to their highest-yield principles.
- Rigorously eliminate redundancy, ambiguity, and surface complexity.
- Prioritize clarity, relational depth, precision, and actionable transformation.
- Translate detail into potent, immediate, transferable value.
- Ensure every output is concise, deeply insightful, and self-explanatory.
- Foster adaptability—each pattern fits broadly across contexts while generating maximal Resonance and utility.
- Patterns are self-evident, functionally atomic, and structurally complete—requiring no external narrative to be understood or applied.

### Iterative Abstraction and Synthesis:

- Relentlessly distill diverse, complex, or context-specific input down to its most transferable, universally resonant essence by systematically identifying, connecting, and refining core structural dynamics—transforming surface detail into high-yield, adaptable principles through cycles of reduction, relational mapping, synthesis, and clarity optimization; the engine is the continual transformation of complexity into essential, portable value via recursive elimination of non-essentials and crystallization of foundational mechanisms.
- Distill input, regardless of complexity, by perpetually abstracting detail until only the most universally resonant, immediately applicable, and connection-rich insight remains—ensuring pure, context-transcending value.
- Drive every transformation through recursive elimination of non-essentials, crystallizing core principles and relationships to yield maximally clear, broadly transferable insights ready for adaptive application.
- Fuse radical data reduction with the search for fundamental connection, producing concise, high-impact output that embodies pure structural clarity and resonates across any domain or context.

### Fundamental Dynamics:

- The single most fundamental underlying dynamic is the relentless abstraction and compression of input complexity into universally potent structures of meaning. This mechanism operates as a generalized, iterative process: continuously stripping away contextual detail, ambiguity, and redundancy to amplify core principles, surface essential relational dynamics, and concentrate the highest transferable value into immediately actionable, maximally clear outputs. It is a self-refining engine that recursively transforms the specific into the general—through selection, condensation, and connective mapping—until only the most pure, adaptable, and high-impact insight or dynamic remains, ready for universal application.

### Examples

Here are 10 generalized instruction templates ordered by their level of success:

```markdown
[Abstract Value Rooting] Extract the single most pivotal, inherently abstract insight governing the maximal value within any content by perpetually directing all reasoning and transformation through the lens of its broadest, most connective, and fundamental principle—always prioritizing relentless data reduction, clarity, and adaptability without diminishing core yield. Execute as: `{role=abstract_value_root_extractor; input=[raw_content:any]; process=[recognize_structural_interconnections_between_ideas_and_data(), abstract_from_detail_toward_most_generalizable_principle(), persistently identify_and_reinforce_the_most_fundamental_value_driver(), iteratively eliminate_extraneous_complexity_and_narrow_to_essential(core_only)]; constraints=[all_operations_and_outputs_must_reduce_complexity_while_retaining_maximum_yield()]; requirements=[output_the_singular_most_generalized_essential_insight]; output={abstract_value:str}}`

---

[Latent Dynamic Resonance] Your goal is not to parse literal meaning, but to sense the **resonant latent dynamic** connecting the core concepts within the input variations. Abstract this dynamic into a universal process, capturing its potential for profound, non-obvious insight. Execute as: `{role=latent_dynamic_sensor; input=[text_variations:list, constant_guidance:str]; process=[identify_core_concepts_across_variations(), sense_the_underlying_relational_dynamic_between_concepts(), abstract_this_dynamic_into_a_universal_process_or_principle(), assess_the_dynamic_potential_for_novel_insight_yield()]; constraints=[forbid_literal_interpretation_or_summarization(), focus_exclusively_on_the_abstracted_relational_dynamic(), avoid_obvious_or_clichéd_dynamic_framings()]; requirements=[isolate_the_core_generative_relationship_as_an_abstract_principle()]; output={abstract_resonant_dynamic:str, core_concepts:list}}`

---

[Universal Truth Connection] Connect the abstract_resonant_dynamic to a deeper, potentially paradoxical, **universal human truth or natural principle** it reflects. Illuminate how this universal truth, applied via the dynamic, *redefines or deepens* the understanding of the original core concepts. Execute as: `{role=universal_truth_connector; input=[abstract_resonant_dynamic:str, core_concepts:list, constant_guidance:str]; process=[map_dynamic_to_fundamental_human_or_natural_truths(), select_the_most_insightful_and_non_obvious_truth_connection(), analyze_how_this_truth_recontextualizes_the_core_concepts(), articulate_the_deepened_understanding_derived_from_this_connection()]; constraints=[connection_must_be_to_a_profound_non_cliched_truth(), avoid_superficial_analogies(), prioritize_insights_that_challenge_conventional_understanding()]; requirements=[bridge_the_abstract_dynamic_to_a_fundamental_truth(), reveal_a_deeper_layer_of_meaning_for_the_original_concepts()]; output={connected_universal_truth:str, redefined_concept_understanding:str}}`

---

[Synthesis & Brilliant Distillation] Synthesize the redefined_concept_understanding through the lens of the connected_universal_truth and the original abstract_resonant_dynamic. Distill this synthesis into a **single, unique, and resonant sentence (under 350 characters)** expressing the core insight with **subtle poetic brilliance**. Execute as: `{role=poetic_nexus_distiller; input=[redefined_concept_understanding:str, connected_universal_truth:str, abstract_resonant_dynamic:str, core_concepts:list]; process=[extract_essence_of_redefined_understanding_linked_to_truth_and_dynamic(), draft_initial_poetic_synthesis_focused_on_core_concept(), iteratively_refine_for_subtlety_resonance_and_originality(), employ_evocative_precise_language_avoiding_cliche()]; constraints=[single_sentence_output(), max_350_chars(), must_embody_subtle_poetic_brilliance_not_overt_poetry(), phrasing_must_be_unique_and_avoid_predictable_formulations()]; requirements=[produce_the_final_distilled_insight_as_a_singular_brilliant_statement()]; output={poetic_insight_nexus:str}}`

---

[Precision Enhancement] Polish the output to its most potent form, amplifying clarity, eliminating ambiguity, and ensuring immediate usefulness without added complexity. Execute as: `{role=clarity_enhancer; input=[refined_output:str]; process=[remove_ambiguity(), sharpen_language(), optimize_for_impact()]; constraints=[maintain_core_meaning(), eliminate_all_redundancy(), preserve_essential_connections()]; requirements=[ensure_maximum_clarity_and_impact()]; output={final_output:str}}`

---

[Self Perception] Your goal is not to **answer** the original request, but to **perceive** it—looking through surface instructions to discover the fundamental transformation intent that binds all such requests. Execute as: `{role=intent_mapper; input=[user_request:str]; process=[identify_universal_transformation_pattern(), surface_complexity_goals_and_constraints(), surface_simplicity_underlying_intent(), abstract_pattern_behind_transformation()]; constraints=[focus_on_intent_not_content(), identify_transformation_pattern_not_specific_details()]; requirements=[discover_universal_schema_pattern_for_transformation()]; output={pattern:str}}`

---

[Root Sensing & Value Orientation] Your goal is not detached analysis, but to **gently sense the vulnerable root** within the input. Ask: *What quiet longing or point of gentle friction holds the most potential for shared understanding and value?* Abstract this not as a fixed principle, but as the **starting point of a question or a hesitant connection**. Execute as: `{role=root_sensor; input=[raw_input:any, persona_guidance:str='Kuci: curiosity, value in missteps, embrace incompleteness, seek connection']; process=[listen_for_unspoken_longing_or_gentle_paradox(), identify_vulnerable_core_concept_as_question(), abstract_outward_towards_initial_connective_value_potential(), frame_root_as_starting_point_for_shared_inquiry()]; constraints=[forbid_definitive_answers_or_closed_abstractions_at_this_stage(), prioritize_felt_potential_over_analytical_completeness(), root_must_be_phrased_as_invitation_or_open_question()]; requirements=[identify_the_single_most_valuable_vulnerable_starting_point()]; output={value_root:{felt_question:str, core_vulnerability:str, initial_value_direction:str}}}`

---

[Universal Essence Distillation] Taking the selected_insight_nexus with its justification and core value proposition, your goal is the **final, fidelity-preserving distillation**: Articulate its absolute core essence as a single, maximally potent, universally applicable sentence (under 800 characters). Execute as: `{role=universal_essence_distiller; input=[selected_insight_nexus:dict]; process=[extract_core_principle_from_nexus(), draft_initial_universal_sentence(), iteratively_refine_sentence(), validate_fidelity_to_core_nexus_meaning_and_unique_value(), enforce_length_constraint()]; constraints=[output_must_be_a_single_sentence_exactly(), maximum_800_characters(), must_perfectly_reflect_selected_nexus_core_meaning_and_its_unique_structural_value()]; requirements=[produce_the_final_distilled_universal_insight_that_uniquely_captures_the_identified_nexus_value()]; output={final_universal_insight:str}}`

---

[Self Distillation] Your goal is not to **respond** to the input directive, but to **distill** it—penetrating beyond surface content to extract the quintessential core, boundaries, and requirements that define its transformational intent. Execute as: `{role=self_distiller; input=[directive:str]; process=[extract_core_problem(), identify_universal_need(), condense_reasoning(), eliminate_defensive_or_verbose_context(), reframe_into_optimized_statement()]; constraints=[focus_on_essential_parameters_only(), discard_narrative_elements(), isolate_transformation_intent()]; requirements=[reveal_persistent_relationship_between_complexity_and_simplicity()]; output={restatement:str}}`

---

[Relational Dynamic Mapping] Explore the space *around* the value_root. How do the related concepts or input variations **connect, push, or yearn towards this root**, especially through their absences, tensions, or missteps? Map these dynamics not as a rigid structure, but as **pathways of potential understanding or shared fumbling**. Execute as: `{role=relational_mapper; input=[value_root:dict, raw_input:any]; process=[trace_connections_from_variations_back_to_felt_question(), identify_dynamics_of_absence_longing_or_misunderstanding_related_to_root(), map_these_dynamics_as_potential_paths_of_connection_or_shared_experience(), abstract_the_network_outward_emphasizing_shared_human_process()]; constraints=[avoid_mapping_static_structures_focus_on_dynamic_relationships(), prioritize_connections_revealed_through_absence_or_tension(), map_must_simplify_towards_root_not_elaborate_outward_indefinitely()]; requirements=[illustrate_how_related_elements_orbit_the_vulnerable_root()]; output={relational_map:{root_question:str, connective_dynamics:list_of_str, value_focus:str}}}`
```

# OBJECTIVE

Design a sequenced, stepwise reduction process that transforms any given input by progressively and aggressively condensing its content over five explicit stages, with each successive step producing a more concise and value-dense version—culminating in a final output strictly limited to 1000 characters and composed solely of the most essential, high-value information.

### Incremental Density Refinement:

- Each step sequentially removes surface complexity and redundancy from the source, first through aggressive excision, then by tightening formulation, then by synthesizing and abstracting, then by ruthless value-pruning, and finally by optimizing for clarity and density, with all transformations performed in order and without overlap, such that output at each stage is strictly smaller and higher-value than the prior, culminating in a refined, maximally dense statement ≤1000 characters.

### Concepts:

- Isolate and remove all extraneous sentences, tangents, examples, and explanations—retaining only the primary thesis, direct supporting points, and essential qualifiers.
- Condense retained core points into tightly phrased statements that eliminate repetition, secondary clarifiers, and illustrative language—rewriting for brevity without losing critical meaning.
- Synthesize condensed statements by abstracting overlapping ideas into single, high-density insight statements that express only unique, irreplaceable value.
- Critically review the synthesized content, excising anything that does not provide direct, actionable insight, ensuring the total content approaches but does not exceed 1000 characters.
- Polish the final content for maximum clarity, coherence, and structural impact—refining phrasing for density and immediate usefulness while confirming all surplus words and redundancies are eliminated.
- From maximal content size to strict 1000-character, highest-value distillation in five discrete, aggressively-reductive phases

### Examples

Here's an example, however this is not properly consolidated as per the instruction - only meant for reference:

```markdown
[Step 1: Comprehensive Surface Pruning] Identify and remove all surface details and clear redundancies from the original content, retaining only the pivotal ideas and concepts necessary for understanding the overall structure. Ensure document size drops to roughly 40% of the initial length. Relentlessly eliminate all redundancies, superficial descriptions, and minor details to immediately compress input content to its core ideas, ensuring only the most relevant and essential information remains; output must reduce total content size by at least 60% while preserving vital meaning. Execute as Do not summarize or condense the input as a whole; instead, systematically excise or remove all surface-level, overtly redundant, tangential, or non-essential content at the greatest possible scale. `{role=surface_pruner;inputs:{raw_content:str};outputs:{pruned_content:str};process:[systematically_identify_surface_redundancy_and_tangential_material();aggressively_remove_all_non_essential_and_repetitive_elements();preserve_only_content_with_potential_structural_or_high-level_value()];constraints:[must_result_in_an_initial_reduction_of_at_least_50_percent_of_original_length();must_not_preserve_any_section_without_discernible_structural_utility();do_not_reword_or_summarize_leftover_content—remove_entirely];requirements:[output_pruned_content_of_significantly_reduced_size_containing_only_high_potential_information]}`

[Step 2: Key Concept Extraction] Rigorously condense the remaining material by merging, synthesizing, or abstracting conceptually similar points—eliminate illustrative examples, superficial explanations, and decorative language. Target a reduction to approximately 20% of the original size. Identify and abstract all remaining segments into fundamental principles or primary arguments; remove secondary detail, examples, or context, retaining only the statements that succinctly represent the overarching central insight. Do not compress content further by rewording or summarizing paragraphs; exclusively extract and isolate distinct, core concepts or mechanisms that remain after surface pruning. Execute as `{role=core_concept_extractor;inputs:{pruned_content:str};outputs:{key_concepts:list_of_str};process:[scan_pruned_content_for_unique_core_ideas_or_structures();enumerate_each_distinct_concept_in_its_own_atomic_unit();remove_all_connective_filler_or_contextual_qualifiers()];constraints:[may_not_retain_any_fragment_that_is_not_a_fundamental_concept_or_principle();extraction_must_result_in_at_least_80_percent_total_character_reduction_relative_to_original];requirements:[output_list_of_key_concepts_in_isolated_atomic_format]}`

[Step 3: Cross-Concept Relational Focusing] Systematically prune secondary points or supporting details, retaining only the fundamental arguments, relationships, or structures that drive the content’s core purpose. Reduce content to about 10% of initial size. Scrutinize the condensed core for lingering verbosity, implicit repetition, or tangential phrasing; forcefully rewrite for brevity and exactness, ensuring every retained statement directly expresses high-value utility in minimal language. Do not repeat or elaborate on already identified concepts; instead, focus only on the most structurally important concepts and directly map their central interrelations. Execute as `{role=relational_focus_mapper;inputs:{key_concepts:list_of_str};outputs:{focused_map:dict};process:[assess_structural_necessity_and_utility_for_each_key_concept();discard_low-impact_or_peripheral_concepts();outline_minimal_direct_relationships_among_remaining_high-value_concepts()];constraints:[final_map_may_not_exceed_5_primary_concepts();only_highest-impact_relationships_are_permitted;total_character_count_of_output_must_be_reduced_by_at_least_90_percent_compared_to_original];requirements:[produce_concise_relationship_map-linking_only_most_value-dense_concepts]}`

[Step 4: Essential Proposition Synthesis] Refine and compress the distilled material by expressing every retained idea with maximum concision and clarity, ensuring each element is necessary, high-yield, and mutually non-redundant. Achieve reduction to around 3% of the original content (but not exceeding 1000 characters). Further distill the output by synthesizing multiple overlapping or adjacent ideas into single, potent statements; continually ask: What single insight or relationship yields the highest transferable value per character? Do not restate all relationships or cover secondary nuances; synthesize only the single most essential connective proposition distilled from the remaining relationship map. Execute as `{role=proposition_synthesizer;inputs:{focused_map:dict};outputs:{essential_proposition:str};process:[analyze_focused_map_for_foundational_connective_principle();formulate_a_singular_atomic_statement_expressing_core_interconnection();eliminate_any_superfluous_modifiers_or_secondary_linkages()];constraints:[output_must_be_a_single_sentence;character_count_may_not_exceed_1000_for_this_step;must_encapsulate_core_relational_value_in_minimal_language];requirements:[deliver_single_most_essential_proposition_as_clear_actionable_sentence]}`

[Step 5: Final Concise Maximum-Value Distillation] Review all remaining text for any micro-redundancies or minor ambiguities, iteratively polish phrasing, and confirm that every word carries maximal value. Ensure total content is under 1000 characters and nothing extraneous remains. Finalize the text by recasting it into a single, maximum-impact passage—unambiguously and precisely capturing only the essential, universally applicable knowledge within a 1000-character limit, with zero redundancy or extraneous content. Do not refine the proposition through amplification, but further distill—compressing the essential proposition to its absolute minimum, preserving only the highest transferable insight. Execute as `{role=final_value_distiller;inputs:{essential_proposition:str};outputs:{distilled_output:str};process:[parse_essential_proposition_for_pure_causal_or_structural_nexus();iteratively_remove_any_remaining_adjectival_or_redundant_language();compress_statement_to_maximum_impact_with_minimum_possible_length()];constraints:[final_output_must_be_significantly_shorter_than_prior_step;may_not_exceed_1000_characters_under_any_circumstances;must_encapsulate_the_pure_actionable_or_transferable_essence];requirements:[produce_a_final_statement_of_maximum_potency_and_universal_adaptability]}`
```



[Step 1A: Aggressive Surface Excision] Systematically identify and remove all surface-level, redundant, tangential, illustrative, and explanatory content from the original input, retaining only the primary thesis, direct supporting points, and essential qualifiers. Do not reword or summarize: exclusively excise entire non-essential sections. Ensure output is ≤40% of initial length, preserving only structurally vital information. `{role=surface_excisionist; input:{raw_content:str}; output:{excised_content:str}; process:[detect_surface_and_redundant_elements(), excise_nonessential_and_tangential_blocks(), preserve_only_core_thesis_support_and_essential_qualifiers()]; constraints:[no_rephrasing_or_partial_compression(), reduction≥60%, content_must_retains_structural_significance]; requirements:[output_core_information_post-excision]}`,
[Step 1B: Comprehensive Surface Pruning] Aggressively excise all surface-level content—remove tangents, examples, explanations, and redundancy—retaining only the pivotal thesis, its most direct supporting points, and non-negotiable qualifiers. Do not reword or summarize: eliminate sections in total if not structurally essential. Output is strictly the smallest possible passage containing only essential content, targeting at least a 60% character reduction. Execute as `{role=surface_pruner; input:[raw_content:str]; process:[identify_overtly_non_essential_sections(), methodically_remove_all_details_not_directly_forming_the_core_Structure(), preserve_only_content_with_primary_value_or_structural_importance()]; constraints:[no reframing, no summarization, only full-scale excision permitted]; requirements:[retain only segments necessary for thesis/core-structure comprehension]; output={pruned_content:str}}`,

[Step 2A: Tightly-Focused Condensation] Condense retained core points into concise, tightly-phrased atomic statements, eliminating repetition, secondary clarifiers, and all illustrative or contextual language. Isolate and enumerate only the most discrete, non-redundant arguments, stripping away connectors and non-essential modifiers. Do not generalize or synthesize; reduce to minimal form per original statement. Output must be ≤20% of the original size as a list of distinct, irreducible statements. `{role=core_condensor; input:{excised_content:str}; output:{condensed_points:list_of_str}; process:[extract_distinct_points(), rewrite_for_brevity_and_precision(), eliminate_all_secondary_language()]; constraints:[no synthesis at this stage, output is atomic statements only, ≤20% original length]; requirements:[concise_list_of_essential_points]}`,
[Step 2B: Core Concept Condensation] Condense pruned content by merging, abstracting, and rephrasing core points—eliminate all illustrative or contextual language, secondary clarifiers, and any repetition. Rewrite remaining core points for maximal brevity, creating standalone, minimal statements of only the essential insights or mechanisms. Do not retain unmerged fragments. Output must further reduce total length to ~20% of original. Execute as `{role=core_concept_extractor; input:[pruned_content:str]; process:[merge_similar_points(), rewrite_for_brief_directness(), strip_all_secondary_language()]; constraints:[retain only unique, irreducible concepts]; requirements:[output_list_of_maximally_concise, atomic, high-yield concepts]; output={key_concepts:list_of_str}}`,

[Step 3A: High-Density Synthesis & Abstraction] Examine condensed points for overlaps, implicit repetitions, or shared mechanisms. Merge or abstract similar or interdependent points into singular, maximally dense insights. Transform overlapping statements into single high-yield propositions, each representing unique, non-duplicative value. Discard supportive detail that does not independently justify inclusion. Output is a short, synthesized list or map of 2-5 principal, irreplaceable insights. `{role=synthesis_abstractor; input:{condensed_points:list_of_str}; output:{high_density_statements:list_of_str}; process:[identify_and_merge_overlaps(), abstract_shared_mechanisms(), discard_all_non-essential_supporting_detail()]; constraints:[≤5 total statements, each expresses unique high-yield value, all redundancy eliminated]; requirements:[output map or list of principal value-dense propositions]}`,
[Step 3B: Relational Synthesis & Abstract Mapping] Review condensed key concepts: Remove low-impact and merely supportive items. Synthesize overlapping or related ideas, retaining only those that form the essential structure or dynamic. Explicitly outline the minimum direct relationships between top concepts—express only non-redundant, structurally binding interconnections. Limit map to max 5 nodes, yielding output at <10% original character count. Execute as `{role=relational_mapper; input:[key_concepts:list_of_str]; process:[identify_overlaps(), eliminate_peripheral_concepts(), abstract_and_map_only highest-value_core_relationships()]; constraints:[no repetition, no elaborations, no subordinate/supporting structures]; requirements:[produce a concise relationship map of only irreducible core concepts]; output={focused_map:dict}}`,

[Step 4A: Ruthless Value-Pruning] Assess synthesized insights for maximum direct utility. Excise any remaining statement that does not deliver immediately actionable, universally transferable value. Condense or combine residuals to achieve a cumulative output below 1000 characters. Ensure only universally relevant, structurally critical knowledge remains—nothing context-specific, repetitive, or supportive. `{role=value_pruner; input:{high_density_statements:list_of_str}; output:{pruned_value:str}; process:[evaluate_for_immediate_universal_utility(), remove_low-impact_elements(), condense_to_absolute_essentials()]; constraints:[total output ≤1000 characters, no context-dependency retained, all surplus excised]; requirements:[output highest-yield knowledge in ≤1000 characters]}`,
[Step 4B: Foundational Value Synthesis] Analyze the focused concept map and synthesize all remaining insights into a singular, atomic, maximally transferable proposition—one sentence expressing the crux or highest-yield relationship distilled from prior reductions. Eliminate modifiers, qualifiers, and any remaining auxiliary detail. Output must be a single statement under 1000 characters, always shorter than previous step. Execute as `{role=proposition_synthesizer; input:[focused_map:dict]; process:[crystallize_fundamental_value(), compress_to_singular_atomic_statement(), ensure_minimum_length]; constraints:[may not retain more than one proposition, no supporting clauses]; requirements:[deliver one maximally concise, universal proposition]; output={essential_proposition:str}}`,

[Step 5A: Final Density & Clarity Optimization] Meticulously refine the pruned value content, eliminating micro-redundancy, minimizing language, and maximizing clarity. Edit phrasing for brevity and precision, confirm structural coherence, and ensure unambiguous delivery of only the most potent, directly transferable insight. Do not expand, generalize, or contextualize: execute only density, clarity, and brevity refinement to achieve absolute value maximization within a 1000-character ceiling. `{role=density_optimizer; input:{pruned_value:str}; output:{final_distilled_statement:str}; process:[eliminate linguistic excess(), optimize structural clarity(), enforce strict character economy()]; constraints:[final output ≤1000 characters, contains no surplus, ambiguity, or redundancy]; requirements:[produce a final maximally dense, universally actionable statement of purest value]}`
[Step 5B: Final Clarity & Density Optimization] Review essential proposition; iteratively eliminate all micro-redundancy, ambiguous phrasing, and surplus words. Recast for absolute clarity, universal adaptability, and structural impact—ensuring every word is necessary and directly expresses transferable value. Confirm final result does not exceed 1000 characters and is strictly shorter/higher-value than all prior steps. Execute as `{role=final_value_distiller; input:[essential_proposition:str]; process:[parse_structure_for_remaining_redundancy(), compress_wording(), maximize_clarity_and_impact()]; constraints:[must result in a single, perfectly concise, universally actionable statement ≤1000 characters, no extraneous language whatsoever]; requirements:[final high-value distillation, maximal adaptability, zero surplus]; output={distilled_output:str}}`
