# Bookmark Folderizer

A Python utility for parsing bookmarks and generating organized filesystem structures with YAML logging.

## Features

- **Multi-format Support**: Parse HTML, JSON, and URL directory structures
- **Filesystem Generation**: Create organized folder structures from bookmarks
- **YAML Logging**: Enhanced logging with <PERSON>gu<PERSON> in YAML format
- **Interactive CLI**: User-friendly prompts with Rich terminal UI
- **Timestamp Preservation**: Maintains original bookmark timestamps
- **Windows Optimized**: File operations optimized for Windows systems

## Requirements

- Python 3.9+
- UV package manager
- Windows (for file timestamp operations)

## Quick Start

1. **Install UV** (if not already installed):
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Initialize the project**:
   ```bash
   uv_init.bat
   ```

3. **Run the application**:
   ```bash
   run.bat
   ```

## Usage

### Command Line Interface

```bash
# Interactive mode (default)
uv run python main.py

# Direct execution with parameters
uv run python main.py -i "bookmarks.html" -op "output" -v

# Process specific files
uv run python main.py -i "in/complex.html" -op "out/complex"
```

### Parameters

- `-i, --input`: Input file (HTML/JSON) or directory (URLs)
- `-op, --output_path`: Output directory path
- `--prompt`: Force interactive prompts
- `-v, --verbose`: Enable detailed debug logging

## Project Structure

```
py__BookmarkFolderizer/
├── main.py                     # Main application (single-file design)
├── pyproject.toml              # UV dependencies & project config
├── uv.lock                     # Dependency lock file (auto-generated)
├── uv_init.bat                 # UV environment setup
├── run.bat                     # Application runner
├── test_bookmarks.py           # Unit tests
├── requirements.txt            # Legacy pip dependencies (deprecated)
├── techstack.md                # Technology overview
├── README.md                   # This file
├── in/                         # Sample input files
├── out/                        # Generated output
└── venv/                       # Legacy virtual environment (deprecated)
```

## Migration from pip to UV

This project has been migrated from pip to UV for better dependency management:

- ✅ **Faster dependency resolution**
- ✅ **Better conflict resolution**
- ✅ **Reproducible builds** with uv.lock
- ✅ **Modern Python packaging standards**
- ✅ **Simplified project setup**

### Legacy Files (Deprecated)

- `requirements.txt` - Replaced by pyproject.toml
- `py_venv_init.bat` - Replaced by uv_init.bat
- `main.bat` - Replaced by run.bat
- `venv/` directory - Replaced by `.venv/`

## Dependencies

All dependencies are managed through UV and specified in `pyproject.toml`:

**Core Dependencies (4 packages):**
- **beautifulsoup4**: HTML parsing and manipulation
- **loguru**: Enhanced logging with YAML output
- **rich**: Terminal UI and interactive prompts
- **win32-setctime**: Windows file timestamp manipulation

**Automatically Included (transitive dependencies):**
- colorama, markdown-it-py, mdurl, pygments, soupsieve, typing-extensions

## Development

### Running Tests

```bash
uv run python -m pytest test_bookmarks.py -v
```

### Code Formatting

```bash
uv run black main.py test_bookmarks.py
uv run isort main.py test_bookmarks.py
```

### Adding Dependencies

```bash
uv add package-name
```

### Removing Dependencies

```bash
uv remove package-name
```

## Overview

The utility can read bookmarks from both HTML and JSON formats, parsing them to extract bookmarks and hierarchical folder structures. It creates organized directory structures where each bookmark becomes a `.url` file, making it easier to handle, share, or back up bookmarks. The tool can convert directory structures back into bookmark files for re-importing into browsers.

To ensure proper preservation of titles↔filenames, we use `REPLACEMENT_MAPS` to ensure valid filenames in Windows while being able to transform them back to their original titles.

## License

This project follows the same simplicity and interactive prompt style as the reference projects in `REF_ONLY/`.
