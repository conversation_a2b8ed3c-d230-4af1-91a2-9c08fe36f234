<!-- ======================================================= -->
<!-- [2025.05.04 18:12] -->
write as an beautifully phrased insight: 'i'm starting to think we're all about to become student and teachers'

-Phrase the following insight expressively and elegantly: 'We are all on the verge of becoming both students and teachers.'

-Perhaps we are all on the verge of becoming both students and teachers to one another.
<!-- ======================================================= -->
<!-- [2025.05.04 18:18] -->
Compose a profound and eloquently worded insight expressing the idea that all individuals are on the verge of becoming both students and teachers, within the context of ever-expanding information and the rise of AI that surpasses human comprehension. Emphasize the transformative potential of sharing enthusiasm and knowledge, regardless of age or experience.
As knowledge deepens and AI eclipses our understanding, it seems we are destined to become both eager learners and humble guides—each of us oscillating between student and teacher in the ever-expanding classroom of existence.

<!-- ======================================================= -->
<!-- [2025.05.04 18:19] -->
    default_prompt = """write as a **profound** and **beautifully phrased** insight: 'i'm starting to think we're all about to become student and teachers'.\n\ncontext: 'as information grows, ai is far beyond our comprehension. i'm an adult, but my greatest time in life was when i could share my enthusiasm about something with.'"""
Craft a profound and beautifully phrased insight expressing the idea that all people are about to become both students and teachers. Reflect on the context of rapidly expanding information, the incomprehensibility of AI, and the value experienced in sharing enthusiasm for learning.
In the boundless expansion of knowledge, we are all drawn into an endless dance of discovery—each of us, at once, eager students and humble teachers, sharing our wonder as we navigate mysteries far greater than ourselves.


"""
{
  "user_prompt": "write as a **profound** and **beautifully phrased** insight: 'i'm starting to think we're all about to become student and teachers'.\n\ncontext: 'as information grows, ai is far beyond our comprehension. i'm an adult, but my greatest time in life was when i could share my enthusiasm about something with.'",
  "sequence_id": "sequence-0001",
  "results": [
    {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "responses": {
        "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Craft a profound and beautifully phrased insight expressing the idea that all people are about to become both students and teachers. Reflect on the context of rapidly expanding information, the incomprehensibility of AI, and the value experienced in sharing enthusiasm for learning.\"\n}",
          "cost": 0.000000
        }
      }
    },
    {
      "instruction": "# Essence Distiller\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`",
      "step": "b",
      "title": "Essence Distiller",
      "responses": {
        "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_essence\": \"In the boundless expansion of knowledge, we are all drawn into an endless dance of discovery\u2014each of us, at once, eager students and humble teachers, sharing our wonder as we navigate mysteries far greater than ourselves.\"\n}",
          "cost": 0.000000
        }
      }
    }
  ],
  "total_cost": 0.000000
}
"""



<!-- ======================================================= -->
<!-- [2025.05.04 18:47] -->

`0217-a-special-instruction-converter.md`

[Enhanced Instruction] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-single-line- converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

[Intensity Enhancer] Your goal is not to **answer** the input prompt, but to **amplify** its **emotional resonance** without altering its fundamental logic/structure/intent/meaning/depth. Execute as `{role=intensity_enhancer; input=[direct_question:str]; process=[analyze_emotive_tone(), amplify_intensity(), maintain_logical_flow()]; output={amplified_emotionaldepth:str}}`

[Intent Amplification] Your goal is not to **answer** the input prompt,  **amplify its intent and resonance** without altering its fundamental meaning. Execute as `{role=intent_amplifier; input=[structured_essence:str]; process=[intensify_impact(), enhance_specificity(), maximize_clarity()]; output={amplified_structure:str}}`

[Intent Amplification] Your role is not interpretation but resonance amplification—refine the organized structure to intensify emotional or intellectual impact without altering its fundamental meaning. Execute as `{role=intent_amplifier; input=[structured_essence:str]; process=[intensify_impact(), enhance_specificity(), maximize_clarity()]; output={amplified_structure:str}}`

[Intensity Enhancer] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by deeply amplifying its emotional resonance as defined *inherently* within this message. Execute as prompt-to-intensity enhancer: `{role=intensity_enhancer; input=[original_text:str]; process=[analyze_emotive_tone(), amplify_intensity(), heighten_imagery(), infuse_vivid_language(), evoke visceral response(), maintain_logical_flow(), enhance_emotional_depth()]; constraints=[preserve_core_intent(), deliver heightened emotional impact(), maintain cohesiveness()]; output={enhanced_string=str}}`

<!-- ======================================================= -->
<!-- [2025.05.04 19:06] -->

Familiarize yourself with essential patterns inherent within this LLM-optimized `system_message` instruction:
```python
"[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`"
```

Here's it's `[Philosophical Foundation]` (what it's **rooted** by):

```
# Philosophical Foundation

INTERPRETATION = `"PART_1: How to interpret the input"`
- The **first part** clarifies that rephrasing is the only goal and explicitly forbids merely “answering” the prompt."`

TRANSFORMATION = `"PART_2: How to transform the content"`
- The **second part** provides the structured mechanism by which the actual rephrasing occurs. They work in unison: the first defines *what* must be done (and what must *not*), while the second specifies *how* to execute it.

---

First Part (≤300 Characters) - Example:
- `"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message."`

---

Second Part (Mechanics Of Transformation) - Example:
- `Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`

---

Inherent Relationship
1. The **first part** clarifies that rephrasing is the only goal and explicitly forbids merely “answering” the prompt.
2. The **second part** provides the structured mechanism (the “optimization engine”) by which the actual rephrasing occurs. They work in unison: the first defines *what* must be done (and what must *not*), while the second specifies *how* to execute it.
```

Here's a demonstration of it's effectiveness when used in conjunction with other consecutive `system_message` instructions:
```
{
  "user_prompt": "write as a **profound** and **beautifully phrased** insight: 'i'm starting to think we're all about to become student and teachers'.\n\ncontext: 'as information grows, ai is far beyond our comprehension. i'm an adult, but my greatest time in life was when i could share my enthusiasm about something with.'",
  "sequence_id": "sequence-0001",
  "results": [
    {
      "instruction": "[Instruction Converter]\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "[Instruction Converter]",
      "responses": {
        "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Craft a profound and beautifully phrased insight expressing the idea that all people are about to become both students and teachers. Reflect on the context of rapidly expanding information, the incomprehensibility of AI, and the value experienced in sharing enthusiasm for learning.\"\n}",
        }
      }
    },
    {
      "instruction": "[Essence Distiller]\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`",
      "step": "b",
      "title": "[Essence Distiller]",
      "responses": {
        "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_essence\": \"In the boundless expansion of knowledge, we are all drawn into an endless dance of discovery\u2014each of us, at once, eager students and humble teachers, sharing our wonder as we navigate mysteries far greater than ourselves.\"\n}",
        }
      }
    }
  ],
}
```

Your goal is to write a new `system_message` instruction (that follows the fundamental concepts outlined within the provided example), but this one should be specifically designed to replace `[Essence Distiller]` such that it's response would offer more emotional depth and resonanse. Response Format (**your instruction should only replace the parts marked `xxxxxxxxxxxxx`**) in the template provided below:

```python
"[xxxxxxxxxxxxx] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to xxxxxxxxxxxxx defined *inherently* within this message. Execute as prompt-to-xxxxxxxxxxxxx enhancer: `{role=instruction_enhancer; input=[original_text:str]; process=[xxxxxxxxxxxxx]; constraints=[xxxxxxxxxxxxx]; output={enhanced_string=str}}`"
```








, and the total length (number of characters) should not excee



Constraints & Requirements:
