# Dir `llm-schema-wip/memory-bank`

### File Structure

```
├── activeContext.md
├── productContext.md
├── progress.md
├── projectbrief.md
├── systemPatterns.md
└── techContext.md
```

---

#### `activeContext.md`

```markdown
    # Active Context: LLM Schema System

    ## Current Work Focus

    The current focus is on developing and refining the meta-sequence generator (Sequence 0190), which is a self-referential instruction sequence designed to transform arbitrary input into schema-compliant instruction sequences. This represents the culmination of the system's capabilities, enabling it to:

    1. Analyze any input request
    2. Extract the core transformation intent
    3. Design a minimal, coherent sequence of instruction steps
    4. Generate schema-compliant instructions for each step
    5. Validate the entire sequence for integrity and potency
    6. Produce a deployment-ready instruction sequence

    The meta-sequence is designed to be self-referential, meaning it can analyze itself and produce structurally similar sequences that follow the same universal schema pattern.

    ## Recent Changes

    1. **Meta-Sequence Development**: Created Sequence 0190, a self-referential system that generates new instruction sequences from arbitrary inputs.

    2. **Error Handling Improvements**: Enhanced error handling with detailed categorization (rate limits, timeouts, auth errors, context length), recovery suggestions, and structured error output.

    3. **Cost Tracking Integration**: Added comprehensive cost tracking and reporting for LLM API calls.

    4. **Template Validation**: Implemented schema validation to ensure templates follow the correct format.

    5. **Configuration System**: Refined the configuration system for model and provider selection.

    6. **Documentation Updates**: Created detailed documentation including README and analysis-lineage documents.

    ## Next Steps

    1. **Complete Meta-Sequence Testing**: Thoroughly test the meta-sequence (0190) with diverse inputs to ensure it can generate high-quality instruction sequences for various use cases.

    2. **Enhance Template Catalog**: Expand the template catalog with additional specialized instruction sequences for common use cases.

    3. **Improve Output Formatting**: Refine the output JSON format for easier consumption by downstream systems.

    4. **Create Visualization Tools**: Develop tools to visualize instruction sequences and their execution flows.

    5. **Add User Interface**: Consider creating a simple web UI for sequence design and execution.

    6. **Optimize Performance**: Analyze and optimize performance for large sequences and complex transformations.

    7. **Explore Integration Options**: Investigate integration with other systems and workflows.

    ## Active Decisions and Considerations

    1. **Schema Evolution**: Whether to evolve the universal schema to include additional fields like requirements, constraints, etc., or keep it minimal.

    2. **Provider-Specific Optimizations**: How to handle provider-specific optimizations without compromising the provider-agnostic architecture.

    3. **Error Recovery Strategies**: The best approach for recovering from various types of LLM API errors.

    4. **Cost Optimization**: Strategies for optimizing LLM API costs while maintaining quality.

    5. **Sequence Composition**: Rules and patterns for effective sequence composition.

    ## Important Patterns and Preferences

    1. **Oppositional Framing**: Instructions use oppositional framing ("not X, but Y") to clarify the transformation's purpose and scope.

    2. **Process Atomicity**: Each process step is expressed as an atomic function call (e.g., `extract_core()`).

    3. **Snake Case Roles**: Role identifiers are always in snake_case (e.g., `role=intent_perceiver`).

    4. **Type Annotations**: Inputs and outputs are type-annotated (e.g., `input=[original_request:any]`).

    5. **Self-Contained Instructions**: Each instruction must be self-contained, with no reliance on external context.

    6. **Structural Determinism**: Sequences follow a predictable structure with clear dependencies between steps.

    7. **Explicit IO Interfaces**: Clear definition of input and output interfaces between steps.

    ## Learnings and Project Insights

    1. **Schema Power**: The universal schema pattern has proven to be a powerful tool for creating deterministic, reliable LLM interactions. Its structured approach enables complex transformations while maintaining clarity and predictability.

    2. **Sequence Efficiency**: Breaking complex transformations into sequences of atomic steps has shown to be more effective than attempting to perform all transformations in a single prompt.

    3. **Provider Abstraction Value**: The abstraction layer for LLM providers has been valuable for testing sequences across different models and providers.

    4. **Meta-Generation Capability**: The system's ability to generate new sequences from arbitrary inputs represents a significant advancement, enabling the creation of specialized sequences for diverse use cases.

    5. **Error Categorization**: Detailed error categorization and recovery suggestions significantly improve the user experience when dealing with LLM API issues.

    6. **Cost Awareness**: Real-time cost tracking provides valuable feedback for optimizing LLM API usage.

    7. **Self-Referential Design**: The meta-sequence's self-referential design enables it to generate sequences that mirror its own structure, creating a recursive pattern of schema-compliant instructions.
```

---

#### `productContext.md`

```markdown
    # Product Context: LLM Schema System

    ## Why This Project Exists

    The LLM Schema System addresses a critical gap in LLM prompt engineering: the lack of structure, determinism, and reliability in traditional conversational approaches to LLM interactions. As LLMs become increasingly integrated into systems and workflows, there's a need for a more structured, function-like approach to prompting that enables predictable, repeatable outcomes.

    ## Problems It Solves

    1. **Prompt Inconsistency**: Traditional prompt engineering often relies on ad-hoc, narrative approaches that yield inconsistent results. This system provides a structured schema that enforces consistency.

    2. **Unpredictable Outputs**: Conversational LLM interactions can produce varying outputs for similar inputs. The schema approach creates more deterministic, reliable transformations.

    3. **Limited Composability**: Most prompting techniques don't enable easy composition of multiple transformations. This system's sequence-based approach allows for complex, multi-step transformations.

    4. **Poor Self-Documentation**: Prompts often lack clarity about their intended function and process. The schema makes the transformation process explicit and self-documenting.

    5. **Provider Lock-in**: Many prompt techniques are tailored to specific LLM providers. This system abstracts away provider differences, enabling cross-provider compatibility.

    6. **Inefficient Cost Management**: Without structured approaches, LLM API usage can be inefficient and costly. This system tracks and optimizes cost.

    ## How It Should Work

    The system functions as a framework for defining, organizing, and executing structured LLM interactions:

    1. **Template Definition**: Users define templates following the universal schema pattern, with each template specifying a role, input/output requirements, process steps, and constraints.

    2. **Sequence Organization**: Templates are organized into sequences, with each sequence forming a pipeline of transformations where outputs from one step feed into inputs for the next.

    3. **Execution Engine**: The execution engine processes these sequences against user inputs, handling the interaction with various LLM providers through an abstraction layer.

    4. **Meta-Generation**: The system includes a meta-sequence that can generate new instruction sequences from arbitrary inputs, following the same schema pattern.

    5. **Validation & Tracking**: The system validates templates against the schema and tracks costs of LLM API calls.

    ## User Experience Goals

    1. **Clarity & Precision**: Users should be able to define exactly what transformation they want the LLM to perform, without ambiguity or reliance on the LLM's interpretation.

    2. **Determinism**: The same input should reliably produce structurally similar outputs across different runs and models.

    3. **Composability**: Users should be able to chain transformations together to create complex workflows.

    4. **Self-Documentation**: The schema itself should document the intended transformation, making it easy to understand and modify.

    5. **Cross-Provider Compatibility**: Users should be able to run the same templates against different LLM providers without significant modifications.

    6. **Cost Efficiency**: The system should optimize LLM API usage and provide transparency about costs.

    7. **Self-Extension**: Advanced users should be able to leverage the meta-sequence capability to generate new templates and sequences for novel use cases.

    8. **Integration Readiness**: The system's outputs should be structured in a way that facilitates integration with other software systems.
```

---

#### `progress.md`

```markdown
    # Progress: LLM Schema System

    ## What Works

    ### Core Engine
    - âœ… Template parsing and catalog generation
    - âœ… Sequence retrieval and execution
    - âœ… Provider abstraction through LiteLLM
    - âœ… Cost tracking and reporting
    - âœ… Error handling with categorization and recovery suggestions
    - âœ… Streaming responses for real-time feedback
    - âœ… CLI interface with comprehensive options

    ### Template System
    - âœ… Universal schema pattern definition
    - âœ… Template validation
    - âœ… Markdown-based template files
    - âœ… JSON-based catalog structure
    - âœ… Multiple sequence collections (different IDs)

    ### Meta-Sequence Generator
    - âœ… Self-referential instruction sequence (0190)
    - âœ… Capability to transform arbitrary input into schema-compliant sequences
    - âœ… Schema integrity validation

    ### Documentation
    - âœ… README with usage instructions
    - âœ… Analysis lineage with architectural insights
    - âœ… Template and schema documentation
    - âœ… Memory bank files for project continuity

    ## What's Left to Build

    ### Core Engine
    - ðŸ”„ Enhanced output formatting for easier downstream consumption
    - ðŸ”„ Performance optimizations for large sequences
    - ðŸ”„ More sophisticated error recovery strategies
    - ðŸ”„ Improved provider-specific parameter handling

    ### Template System
    - ðŸ”„ Schema evolution for additional fields (requirements, constraints, etc.)
    - ðŸ”„ More sophisticated template validation
    - ðŸ”„ Automated template testing

    ### Meta-Sequence Generator
    - ðŸ”„ Comprehensive testing with diverse inputs
    - ðŸ”„ Refinement based on testing results
    - ðŸ”„ Specialized meta-sequences for common use cases

    ### Tooling & Integration
    - ðŸ”„ Visualization tools for sequence design and execution flow
    - ðŸ”„ Web UI for sequence design and execution
    - ðŸ”„ API for programmatic access
    - ðŸ”„ Integration examples with common workflows

    ## Current Status

    ### Development Phase
    The project is currently in the **refinement phase**, with core functionality implemented and working. The focus is now on testing, improving, and extending the existing capabilities, particularly the meta-sequence generator.

    ### Key Achievements
    1. Established a universal schema pattern for structured LLM interactions
    2. Implemented a full execution pipeline from template files to results
    3. Supported multiple LLM providers and models
    4. Created a self-referential meta-sequence generator

    ### Current Challenges
    1. Ensuring the meta-sequence consistently produces high-quality instruction sequences
    2. Balancing schema complexity with usability
    3. Optimizing performance and cost for production use
    4. Making the system accessible to users with different expertise levels

    ## Evolution of Project Decisions

    ### Initial Approach
    The project initially focused on defining a standard schema for LLM instructions that would make interactions more deterministic and reliable. The schema was designed to be clear, explicit, and self-documenting.

    ### Core Pattern Development
    Through experimentation, the universal schema pattern emerged:
    ```
    [Title] Interpretation. Execute as: `{role=role_name; input=[...]; process=[...]; output={...}}`
    ```
    This pattern proved to be powerful for creating structured, function-like LLM interactions.

    ### Sequence-Based Processing
    As the project evolved, it became clear that breaking complex transformations into sequences of atomic steps was more effective than attempting to perform everything in a single prompt. This led to the development of the sequence-based processing approach.

    ### Provider Abstraction
    The need to support multiple LLM providers led to the adoption of LiteLLM as an abstraction layer, allowing the system to work with different providers without significant code changes.

    ### Meta-Sequence Development
    The most recent evolution has been the development of the meta-sequence generator (0190), which represents the culmination of the system's capabilities. This self-referential sequence can generate new instruction sequences from arbitrary inputs, following the same universal schema pattern.

    ## Next Milestone Goals

    ### Short-Term (1-2 Weeks)
    1. Complete comprehensive testing of the meta-sequence generator
    2. Address any issues identified during testing
    3. Document best practices for sequence design
    4. Create additional examples of specialized sequences

    ### Medium-Term (1-2 Months)
    1. Develop visualization tools for sequence design and execution
    2. Create a simple web UI for interacting with the system
    3. Implement more sophisticated error recovery strategies
    4. Expand the template catalog with additional specialized sequences

    ### Long-Term (3+ Months)
    1. Develop an API for programmatic access
    2. Create integration examples with common workflows
    3. Explore advanced use cases and applications
    4. Consider packaging the system for easier distribution and deployment
```

---

#### `projectbrief.md`

```markdown
    # Project Brief: LLM Schema System

    ## Core Purpose

    The LLM Schema System transforms LLM interactions from conversational exchanges into structured, function-like operations by applying a schema-driven approach to prompt engineering. It treats the LLM less like a conversational partner and more like a highly specialized function or component within a system - achieving this through extreme clarity, role definition, process guidance, and explicit constraints.

    ## Key Requirements

    1. **Schema-Driven Architecture**: All templates must follow a consistent schema pattern:
       ```
       [Title] Interpretation. Execute as: `{role=role_name; input=[...]; process=[...]; output={...}}`
       ```

    2. **Sequence-Based Processing**: Templates should be organized into sequences, where each sequence forms a multi-step transformation pipeline. Each step in a sequence builds on the output of the previous step.

    3. **Template Validation**: System must enforce schema compliance for all templates.

    4. **Provider Abstraction**: Support multiple LLM providers (OpenAI, Anthropic, Google, Deepseek) through a centralized configuration system.

    5. **Cost Tracking**: Track and report estimated costs of LLM API calls.

    6. **Meta-Sequence Generation**: Create a system that can transform any input into highly optimized system_message instructions that are LLM-optimized, universally applicable, and adhere to the established schema format.

    ## Project Goals

    1. Create a framework that enables more deterministic, reliable, and powerful LLM interactions.

    2. Treat LLMs as specialized components within a larger system rather than as conversational partners.

    3. Develop a universally translatable schema for seamless usability across formats (Markdown, JSON, LLM, etc.)

    4. Establish a self-referential system that can generate new instruction sequences using its own schema.

    5. Consolidate the schema system and define new instruction sequences that allow transforming any input into sequences that follow the exact structure of this schema.

    ## Design Principles

    1. **Atomicity**: Each directive is indecomposable; one step, one transformation.

    2. **Consolidation**: Remove overlap and redundancy for instruction economy.

    3. **Determinism**: Transformations yield predictable, schema-conformant results.

    4. **LLM-Optimization**: Formulate steps for maximal LLM interpretability and execution.

    5. **Meta-Recursion**: The instruction set can act upon itself as input, yielding structurally similar output.

    6. **Schema Discipline**: All outputs strictly adhere to the Universal Instruction Schema.

    7. **Self-Containment**: Output is independently actionable, with no reliance on external context.

    8. **Structural Ordering**: Process sequences are organized for clarity and consistency.

    9. **Universality**: Instructions are agnostic to input type and context, enabling generalization.
```

---

#### `systemPatterns.md`

```markdown
    # System Patterns: LLM Schema System

    ## System Architecture

    The LLM Schema System follows a clear pipeline architecture with these major components:

    ```mermaid
    graph TD
        A[Template Files] --> B[Catalog Generator]
        B --> C[Template Catalog]
        C --> D[Sequence Retrieval]
        D --> E[Execution Engine]
        F[User Input] --> E
        G[Provider/Models] <--> E
        E --> H[Results]

        subgraph "Template Management"
        A
        B
        C
        D
        end

        subgraph "Execution Engine"
        E
        G
        end

        subgraph "I/O"
        F
        H
        end
    ```

    ### Core Components

    1. **Template Files (.md)**: Individual Markdown files that define transformations following the universal schema pattern.

    2. **Catalog Generator**: Processes template files into a structured catalog for easy access and organization.

    3. **Template Catalog (.json)**: Structured representation of templates and sequences, serving as the primary data structure for the system.

    4. **Sequence Retrieval**: Logic for retrieving ordered sequences of templates from the catalog.

    5. **Execution Engine**: Orchestrates the execution of sequences against LLM providers.

    6. **Provider/Model Abstraction**: Handles interaction with different LLM APIs through LiteLLM.

    7. **Results Processing**: Formats and structures the outputs from LLM API calls.

    ## Key Technical Decisions

    1. **Markdown as Template Format**: Templates are defined in Markdown files, making them human-readable and easily editable.

    2. **JSON as Catalog Format**: The template catalog is stored as a JSON file, enabling structured access and manipulation.

    3. **LiteLLM for Provider Abstraction**: The system uses LiteLLM as an abstraction layer for interacting with different LLM providers.

    4. **Pydantic for Data Validation**: Pydantic models are used for validating and structuring data.

    5. **Async Execution**: The execution engine uses async/await for handling concurrent LLM API calls.

    6. **Streaming Responses**: LLM responses are streamed to provide real-time feedback.

    7. **Cost Tracking**: A dedicated component tracks and reports estimated costs of LLM API calls.

    ## Design Patterns

    1. **Pipeline Pattern**: The system processes data through a series of transformations, with each step building on the output of the previous step.

    2. **Template Method Pattern**: The universal schema defines a "template" that all transformations must follow.

    3. **Factory Pattern**: The catalog generator creates structured representations of templates from raw Markdown files.

    4. **Strategy Pattern**: Different LLM providers can be swapped in and out without changing the core execution logic.

    5. **Configuration Object**: A centralized configuration class manages settings and provider selection.

    6. **Decorator Pattern**: LiteLLM callbacks can be added to extend functionality without modifying the core code.

    7. **Command Pattern**: Each template represents a discrete command that the LLM should execute.

    ## Component Relationships

    1. **Template Files → Catalog Generator**: Template files are processed by the catalog generator to create the catalog JSON.

    2. **Catalog → Sequence Retrieval**: The catalog provides the structured data that sequence retrieval functions access.

    3. **Sequence → Execution Engine**: Sequences are passed to the execution engine for processing against LLM providers.

    4. **Execution Engine → Provider Abstraction**: The execution engine uses the provider abstraction to interact with LLM APIs.

    5. **User Input → Execution Engine**: User input serves as the primary input for the execution engine.

    6. **Execution Engine → Results**: The execution engine produces structured results from LLM responses.

    ## Critical Implementation Paths

    1. **Template Processing Pipeline**:
       ```
       Template Files (.md) → Catalog Generator → Template Catalog (.json) → Sequence Retrieval → System Instruction Extraction → LLM Execution
       ```

    2. **Execution Flow**:
       ```
       User Prompt + Sequence ID → Load Sequence → Process Each Step → Stream Results → Output JSON
       ```

    3. **Provider Selection Logic**:
       ```
       User Provider/Model Selection → Config Resolution → LiteLLM Parameterization → API Call
       ```

    4. **Error Handling Path**:
       ```
       API Error → Error Categorization → Recovery Suggestion → Structured Error Output
       ```

    5. **Cost Tracking Path**:
       ```
       LLM API Response → Cost Extraction → Accumulation → Total Cost Reporting
       ```

    ## Schema Pattern Structure

    The universal schema pattern is the core architectural element that gives the system its power:

    ```
    [Title] Interpretation. Execute as: `{role=role_name; input=[...]; process=[...]; output={...}}`
    ```

    Breaking this down:

    1. **Title**: A concise label enclosed in square brackets that identifies the transformation's purpose.

    2. **Interpretation**: A description of what the transformation does, often using an oppositional framing ("not X, but Y").

    3. **Transformation Block**: A code-like block enclosed in backticks that defines:
       - **role**: A snake_case identifier for the function being performed
       - **input**: The expected input format and types
       - **process**: An ordered list of steps to perform, expressed as function calls
       - **output**: The expected output format and types

    This schema enables:
    - **Deterministic Processing**: By clearly defining inputs, outputs, and process steps
    - **Composability**: Templates can be chained together in sequences
    - **Self-Documentation**: The schema itself documents how the transformation works
    - **Consistency**: All templates follow the same structure, making them easier to understand and use
```

---

#### `techContext.md`

```markdown
    # Technical Context: LLM Schema System

    ## Technologies Used

    ### Core Technologies

    1. **Python**: The system is written in Python, leveraging its async capabilities and extensive ecosystem for AI/ML.

    2. **LiteLLM**: Used as an abstraction layer for interacting with different LLM providers, handling API calls, streaming responses, and cost tracking.

    3. **Pydantic**: Employed for data validation and structuring, particularly for the output models and configuration.

    4. **Asyncio**: The system uses async/await patterns for efficient handling of LLM API calls.

    5. **JSON**: Used for the catalog format and for structured output of execution results.

    6. **Markdown**: Templates are defined in markdown files, with a specific schema structure.

    ### LLM Providers

    The system supports multiple LLM providers through LiteLLM:

    1. **OpenAI Models**:
       - GPT-3.5 Turbo and variants
       - GPT-4 and variants (GPT-4o, GPT-4.1)

    2. **Anthropic Models**:
       - Claude-3 (Opus, Sonnet, Haiku)
       - Claude-3.7 (via OpenRouter)

    3. **Google Models**:
       - Gemini Pro
       - Gemini Flash
       - Gemini 2.0/2.5

    4. **Deepseek Models**:
       - Deepseek Reasoner
       - Deepseek Coder
       - Deepseek Chat

    ## Development Setup

    ### Environment Setup

    The project includes an initialization script to set up the Python environment:

    ```
    py_venv_init.bat
    ```

    This script creates a virtual environment and installs the required dependencies.

    ### Dependencies

    Key dependencies include:

    1. **litellm**: Abstraction layer for LLM API calls
    2. **pydantic**: Data validation and structuring
    3. **openai**: Official OpenAI API client
    4. **anthropic**: Official Anthropic API client
    5. **google**: Google API services

    ### Project Structure

    ```
    llm-schema-wip/
    ├── .gitignore
    ├── README.md
    ├── analysis-lineage.md
    ├── GOAL.md
    ├── notes/                 # Documentation and notes
    └── src/
        ├── litellm_sequence_executor.py  # Main execution engine
        ├── py_venv_init.bat             # Environment setup script
        ├── requirements.txt             # Dependencies list
        ├── output/                      # Execution results
        └── templates/
            └── lvl1/
                ├── templates_lvl1_md_catalog_generator.py  # Catalog generator
                ├── templates_lvl1_md_catalog.json          # Template catalog
                └── md/                                     # Template files
                    └── *.md                                # Individual templates
    ```

    ## Technical Constraints

    ### API Limits

    The system is constrained by the limits of the underlying LLM providers:

    1. **Rate Limits**: Provider-specific API call rate limits
    2. **Context Length**: Maximum token limits for each model
    3. **Token Generation Limits**: Maximum tokens that can be generated in a response
    4. **API Timeouts**: Request timeouts (set to 120 seconds by default)

    ### Error Handling

    The system includes specific error handling for common LLM API issues:

    1. **Rate Limit Errors**: Detection and suggestions for retry
    2. **Timeout Errors**: Suggestions for reducing prompt size or increasing timeout
    3. **Authentication Errors**: Guidance for API key issues
    4. **Context Length Errors**: Suggestions for reducing input size or using models with larger context windows

    ### Performance Considerations

    1. **Streaming**: Responses are streamed for real-time feedback
    2. **Concurrent Processing**: Async execution for handling multiple models
    3. **Cost Tracking**: Real-time tracking of API costs
    4. **Retry Logic**: Built-in retry mechanism for failed API calls (3 retries by default)

    ## Tool Usage Patterns

    ### Command Line Interface

    The main entry point is `litellm_sequence_executor.py` which accepts various command-line arguments:

    ```
    python litellm_sequence_executor.py [options]
    ```

    Key options include:

    - `--sequence`: Sequence ID to execute
    - `--prompt`: User prompt text
    - `--models`: Comma-separated list of models to use
    - `--provider`: Provider to use with its default model
    - `--temperature`: Override model temperature
    - `--max-tokens`: Override maximum tokens generated

    ### Template Creation

    New templates are created by:

    1. Creating a markdown file in `src/templates/lvl1/md/`
    2. Following the schema pattern: `[Title] Interpretation. Execute as: {role=X; input=Y; process=[...]; output=Z}`
    3. Regenerating the catalog with `--force-regenerate`

    ### Sequence Execution

    The typical execution flow is:

    1. Select a sequence by ID
    2. Provide a user prompt
    3. Choose model(s) to execute against
    4. Execute the sequence
    5. View the results in the output JSON file

    ### Integration Points

    The system is designed for integration through:

    1. **Command-Line Interface**: For direct execution
    2. **JSON Output**: Structured for programmatic consumption
    3. **Extensible Architecture**: New providers and models can be added to the Config class
```

