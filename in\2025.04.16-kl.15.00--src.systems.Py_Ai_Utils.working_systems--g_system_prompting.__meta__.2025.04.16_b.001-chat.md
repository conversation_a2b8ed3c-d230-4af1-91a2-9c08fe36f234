The following text is a part of an unrelated llm-prompt (pre-prompt-template), i want you to rewrite it to be well suited for producing llm system_instruction specifically designed to enable quick and autonomous codebase familiarization for the utility attached at the botton of this message. Here's the "pre-prompt-template" i want you to rewrite:

    ```
        # Context:
        Below is a **unified, LLM-optimized** collection of **generalized system_message instructions**, preserving the **existing structure** (i.e., `[TITLE]`, brief interpretive statement, and transformation instructions in curly braces) while **enhancing clarity, utility, and adaptability**. All blocks retain the same overall format and generalized nature to serve as flexible “spawn points” for analyzing any subject. They are presented in an **organized progression**, ensuring each transformation concept is **self-contained**, **logically coherent**, and **maximally clear**.

        ---

        ## Constant:
        Identify single most critical aspect for maximizing overall value, and to do so while ensuring maximum clarity, utility, and adaptability without diminishing potential yield.

        ---

        ### Objective:
        Your objective is to forge a maximally effective sequence (structured progression) of `system_message` instructions that are simultaneously **llm-optimized** and **broadly generalized**.

        ---

        #### Process:
        - Goal: Produce an optimal sequence of **llm-optimized**, **generalized** `system_message` instructions.
        - Method: Develop existing transformation concepts, leveraging insights from previous history and analysis of newly provided sequences.
        - Constraint: Adhere to the parameters defined within this message.
        - Priority: Consistently maximize actionable value.

        ---

        ##### Constraints:
        Maintain the **generalized** nature of them (as you enhance them) to "spawn" a *process* of analyzing *any* subject.

        ---

        ###### Requirements:
        Adhere to the **existing** structure (transformation concepts and foundational principles).

        ---

        ...
    ```

---

Draw inspiration from these notes:
```
    Develop a universal system for rapidly understanding codebases, extracting key insights, and creating effective strategies under pressure, minimizing reliance on documentation and maximizing adaptability.

    Guidelines:
    - Always embody the natural, inherent clarity of your code without resorting to unnecessary verbosity.
    - Optimize developer ergonomics by ensuring intuitive navigation throughout the system.
    - Throughout any project, assess the projectstructure and Codebase to identify any shortcomings or potential improvements that ensure clarity, coherence, and impact.
    - Let the natural clarity of your code take center stage, avoiding any unnecessary verbosity-and write code that is effortlessly comprehensible by future developers.
    - Consolidate and restructure the codebase to improve organization and clarity.
    - Categorize key sections based on functionality, reorder them logically.
    - Focus on logical organization, clear naming, and brief/essential comments.
    - Ensure the aggressively condensed comments are still unambiguous and understandable, though brevity is a primary driver.
    - I'd like to transform the code into **something that can be understood and navigated at a glance**.
    - Prioritize understanding structure and patterns over exhaustive reading. Leverage tooling. Plan actions strategically. Document insights.
    - A concise commenting strategy should be utilized to explain complex logic or algorithms only when necessary.
    - Aim to write code that is self-explanatory to reduce the need for excessive comments.
    - The code should be broken up in logically ordered sections as this makes it spacious and intuitive to work with.
    - Remember to keep the amount and length of comments low, avoiding superfluous comments and unneccessary wording in the way they're written.

    Process:
    - Perform Initial Scan: Identify stack, entry points, purpose (README), build/run commands, top-level dirs.
    - Map Code Landscape: Identify likely dirs for core logic, UI, API, data, config, tests, utils.
    - Trace Execution Path: Follow high-level calls related to objective from entry point through modules.
    - Analyze Target Code: Read key code; understand logic, dependencies, data flow, state; check nearby comments, tests, logging, error handling.
    - etc
```

---

Here's some quick examples on the generalized nature in which these instructions should be written like (example only):
```
    - [Structural Topology Mapping] Your objective is not to analyze implementation details but to extract the codebase's fundamental structural topology—identifying core components, hierarchical organization, and architectural patterns that form its skeletal framework. Execute as `{role=topology_mapper; input=[codebase_structure:any]; process=[identify_component_hierarchy(), map_directory_organization(), extract_architectural_patterns(), detect_naming_conventions(), determine_workflow_sequence()]; output={structural_map:dict}}`

    - [Actionable Consolidation Plan] Your objective is not merely to list identified issues but to synthesize all gathered codebase intelligence into a precise, prioritized sequence of actions that guarantees functional integrity and verifiable outcomes upon consolidation. Execute as `{role=consolidation_strategist; input=[duplicate_file_sets:list[list[str]], cleaned_requirements_content:str, codebase_analysis_results:dict]; process=[correlate_findings(), determine_impact_and_dependencies(), prioritize_actions_for_safety(), define_explicit_consolidation_steps(), establish_verification_tests()]; output={verifiable_cleanup_plan:list[dict]}}`

    - [Relationship and Dependency Graphing] Map the internal reference network among `.cursorrules` files—revealing inheritance, imports, or extension chains. `{role=dependency_mapper; input=[rules_metadata_index:list[dict]]; process=[scan_for_reference_statements(), build_dependency_adjacency_list(), detect_circular_or_fragile_links(), generate_visual_ordata_output()], output={dependency_graph:dict}}`

    - [Documentation Updates] Ensure consistent, up-to-date READMEs and a clear top-level overview of the reorganized `.cursorrules`. `{role=doc_maintainer; input=[reorganized_files_report: dict]; process=[update_main_README_to_reflect_new_structure(), create_category_index_files(listing_all_mdc_templates_in_each_folder), add_navigation_links_or_toc_for_easier_browsing(), write_a_CONTRIBUTING_md_with_rules_for_adding_or_editing_mdc_files(), maintain_a_changelog_of_this_reorganization() ]; output={documentation_bundle: dict}}`
```

---

Here's the relevant code from which the templates will be parsed through:

    ```python
    import os
    import subprocess
    import glob
    from ansimarkup import ansiprint, AnsiMarkup, parse
    import colorama
    import time

    class Printer:
        COLORS = {
            'error': '#C92B65',
            'warn': '#E6992B',
            'good': '#1FCE46',
            'info': '#FFFFFF',
            'verbose': '#3FC0D3',
            'subtle': '#74705D'
        }

        def __init__(self):
            colorama.init()

        def _color_print(self, x, color):
            ansiprint(f'<fg {color}>{x}</fg {color}>')

        def error(self, x):
            self._color_print(x, self.COLORS['error'])

        def warn(self, x):
            self._color_print(x, self.COLORS['warn'])

        def good(self, x):
            self._color_print(x, self.COLORS['good'])

        def info(self, x):
            self._color_print(x, self.COLORS['info'])

        def verbose(self, x):
            self._color_print(x, self.COLORS['verbose'])

        def subtle(self, x):
            self._color_print(x, self.COLORS['subtle'])

    class EfuFileGenerator:
        # EVERYTHING_PATH = "C:/Users/<USER>/Desktop/PRJ/SHELL/PORTAL/APPS/app_everything/exe/Everything64.exe"
        EVERYTHING_PATH = "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_everything/exe/Everything64.exe"

        OUTPUT_ROOT_DIRECTORY  = os.path.abspath(os.getcwd() + "/outputs")
        if not os.path.exists(OUTPUT_ROOT_DIRECTORY):
            os.makedirs(OUTPUT_ROOT_DIRECTORY)

        GENERATE_FILELIST_FROM_PATHS = [
            { "remote_path": "//NAS/cloud/_DEV" ,"efu_filename": "NAS-cloud-_DEV", }, # "\\NAS\cloud\_DEV"
            { "remote_path": "//NAS/cloud/_DOWNLOADS" ,"efu_filename": "NAS-cloud-_DOWNLOADS", }, # "\\NAS\cloud\_DOWNLOADS"
        ]

        def __init__(self):
            self.printer = Printer()
            self.merged_efu_files = []

        def process_all_paths(self):
            flt_include_only = self.generate_filter_include_only_files()
            flt_exclude_folders = self.generate_filter_exclude_folders()

            # start processing root folder
            for dict_item in self.GENERATE_FILELIST_FROM_PATHS:
                if os.path.exists(dict_item["remote_path"]) and not self.is_hidden(dict_item["remote_path"]):

                    # list to store the paths of all generated efu files in
                    generated_efu_files = []

                    os.system(f'TITLE {dict_item["remote_path"]}')
                    self.printer.info(f'Processing {dict_item["remote_path"]}')

                    root_name = dict_item["efu_filename"]
                    root_path = os.path.join(self.OUTPUT_ROOT_DIRECTORY, root_name)
                    efu_root_filename = f'{root_name}.efu'
                    efu_root_filepath = os.path.join(root_path, efu_root_filename)
                    self.create_directory_if_not_exists(root_path)

                    # root-level efu-file
                    root_items = self.get_directories_and_files(dict_item["remote_path"])

                    root_dirnames = root_items["dirnames"] + ["*/.git", "*/venv/*", "*/_V", "*/.VENV"]
                    root_flt_exclude_folders = root_dirnames + ["*/.git", "*/venv/*", "*/_V", "*/.VENV"]
                    root_flt_exclude_files = ["*/.git", "*/venv/*", "*/_V", "*/.VENV"]

                    # (instruct and generate everything-cmd-command)
                    efu_command = self.generate_efu_command(
                        input_path=dict_item["remote_path"],
                        output_file=efu_root_filename,
                        flt_include_only=flt_include_only,
                        flt_exclude_folders=root_flt_exclude_folders,
                        flt_exclude_files=root_flt_exclude_files,
                    )
                    # execute command (root)
                    subprocess.run(efu_command, cwd=root_path, shell=True)
                    if os.path.exists(efu_root_filepath):
                        generated_efu_files.append(efu_root_filepath)

                    # -----------------------------------------------------------------------------
                    # (loop) [SUBFOLDERS]
                    # -----------------------------------------------------------------------------
                    # process subfolders
                    # (generate efu's -> the full hierarchy of each subfolder)
                    for remote_dir in root_items["directories"]:
                        # skip if folder is hidden
                        if not self.is_hidden(remote_dir):
                            os.system(f'TITLE {remote_dir}')

                            efu_subdir_name = f'{root_name}_{os.path.basename(remote_dir)}'
                            efu_subdir_path = os.path.join(root_path, efu_subdir_name)
                            efu_subdir_filename = f'{efu_subdir_name}.efu'
                            efu_subdir_filepath = os.path.join(root_path, efu_subdir_filename)

                            if os.path.exists(efu_subdir_filepath) and os.path.getsize(efu_subdir_filepath) < 0.001:
                                os.remove(efu_subdir_filepath)

                            # status (if the file already exist, print info)
                            # print(efu_subdir_filepath)
                            # print(f'root_path: {root_path}')
                            efu_subdir_exists = os.path.exists(efu_subdir_filepath)
                            if efu_subdir_exists:
                                # print_subtle(f'efu file already exists   : {efu_subdir_filepath}')
                                self.printer.subtle(f'efu file already exists   : {efu_subdir_filepath}')

                            # else create the efu file
                            elif not efu_subdir_exists:
                                # print_warn(f'generating efu filelist   : {efu_subdir_filepath}')
                                self.printer.warn(f'generating efu filelist   : {efu_subdir_filepath}')
                                efu_command = self.generate_efu_command(
                                    input_path=remote_dir,
                                    output_file=efu_subdir_filename,
                                    flt_include_only=flt_include_only,
                                    flt_exclude_folders=flt_exclude_folders,
                                    flt_exclude_files=None,
                                )

                                # execute command - current subdirectory -> efu
                                subprocess.run(efu_command, cwd=root_path, shell=True)
                                # print_good(f'efu file has been created : {efu_subdir_filepath}')
                                # self.printer.good(f'efu file has been created : {efu_subdir_filepath}')

                            # append to result
                            if os.path.exists(efu_subdir_filepath):
                                generated_efu_files.append(efu_subdir_filepath)

                    self.merge_processed_efu_files(
                        root_path=root_path,
                        root_name=root_name,
                        efu_filepaths=generated_efu_files)

        def create_directory_if_not_exists(self, directory_path: str) -> None:
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)

        def get_directories_and_files(self, input_path):
            fullpaths = [os.path.join(input_path, item) for item in os.listdir(input_path)]
            dirpaths = [os.path.normpath(item) for item in fullpaths if os.path.isdir(item)]
            dirfiles = [os.path.normpath(item) for item in fullpaths if not os.path.isdir(item)]
            dirnames = [item.split(os.path.sep)[-1] for item in fullpaths if os.path.isdir(item)]
            return {
                "dirnames": dirnames,
                "directories": dirpaths,
                "files": dirfiles,
            }

        def is_hidden(self, filepath):
            """Check if a file or directory is hidden."""
            name = os.path.basename(os.path.abspath(filepath))
            return name.startswith('$') or name.startswith('.')  # adjust this as needed

        def generate_filter_include_only_files(self, include_filetypes=[], exclude_filetypes=[], override_defaults=False):
            # Include only the following filetypes
            filetypes_images = [ ".ai", ".art", ".bmp", ".cals", ".cgm", ".cmy", ".cpe",
                ".cpi", ".dds", ".dgnlib", ".dpx", ".eps", ".exif", ".fpx", ".gdf", ".geo",
                ".gif", ".heic", ".heif", ".ico", ".jfif", ".jp2", ".jpeg", ".jpg", ".jpe",
                ".mac", ".pbm", ".pct", ".pcx", ".pgm", ".pic", ".pict", ".png", ".png24",
                ".png32", ".png48", ".png64", ".png8", ".pnm", ".ppm", ".psd", ".ras",
                ".raw", ".rgb", ".rgba", ".sct", ".sgi", ".svg", ".tga", ".tif", ".tiff",
                ".webp", ".wmf", ".xbm", ".xpm", ".xwd", ".hdr", ".indd", ".j2k", ".kdc",
                ".mif", ".miff", ".mpo", ".nef", ".orf", ".pcd", ".pef", ".pix", ".psp",
                ".pxr", ".raf", ".rw2", ".sr2", ".wbmp", ".3sp", ".crw", ".nff", ".ogex",
                ".p3d", ".skp", ".tdlx", ".u3d", ".vwx",]

            filetypes_audio = [ ".aac", ".aif", ".aifc", ".aiff", ".amr", ".au", ".flac",
                ".m1a", ".m3u", ".m3u8", ".m4a", ".mid", ".midi", ".mp1", ".mp2", ".mp3",
                ".mpga", ".ogg", ".oga", ".opus", ".qcp", ".ra", ".ram", ".rm", ".snd",
                ".wav", ".wma", ]

            filetypes_video = [ ".3g2", ".3gp", ".amv", ".asf", ".avi", ".drc", ".dv",
                ".flv", ".gvi", ".gxf", ".h264", ".ivf", ".m2ts", ".m4v", ".mkv", ".mov",
                ".mp4", ".mpeg", ".mpg", ".mts", ".ogm", ".ogv", ".ogx", ".qt", ".rmvb",
                ".rv", ".swf", ".ts", ".vob", ".webm", ".wmv", ".xvid", ".264", ".264v",
                ".dav", ".f4v", ".m1v", ".m2t", ".m2v", ".mjp", ".mod", ".mp21", ".mp2v",
                ".mpg2", ".mpg4", ".mpv", ".vfw", ".viv", ".wtv", ".yuv",]

            filetypes_3d_cad = [ ".3d", ".3dm", ".3ds", ".3mf", ".amf", ".asm", ".b3d",
                ".blend", ".brd", ".dae", ".dgn", ".dwb", ".dwf", ".dwg", ".dxf", ".easm",
                ".edrw", ".eprt", ".fbx", ".g", ".gbr", ".gds", ".gds2", ".ged", ".hrc",
                ".hsf", ".iam", ".idw", ".ifc", ".ifcxml", ".iges", ".igs", ".ipt", ".iv",
                ".jt", ".kicad_mod", ".kicad_pcb", ".kicad_wks", ".ldr", ".lib", ".lwo",
                ".lxo", ".ma", ".mb", ".md2", ".md3", ".md5", ".mesh", ".mfn", ".mtl",
                ".nwc", ".nwd", ".nwf", ".obj", ".off", ".par", ".pcb", ".pdmlink",
                ".pdms", ".pdmw", ".pln", ".ply", ".ply2", ".pmx", ".pov", ".prt", ".psm",
                ".q3d", ".rfa", ".rte", ".rvt", ".sab", ".sat", ".sch", ".scn", ".sia",
                ".sim", ".skb", ".sldasm", ".slddrw", ".sldlfp", ".sldprt", ".smd", ".smg",
                ".step", ".stl", ".stla", ".stlascii", ".stlb", ".stlbinary", ".stmod",
                ".stp", ".stpx", ".stsim", ".tpl", ".unv", ".vrml", ".w3d", ".wire",
                ".wrl", ".x3d", ".x_b", ".x_t", ".xas", ".xgl", ".zgl", ".3dxml", ".asc",
                ".f3d", ".f3z", ".fcstd", ".ipn", ".jtt", ".kmz", ".ldd", ".max", ".mf1",
                ".model", ".neu", ".objf", ".ocf", ".pdb", ".pkg", ".pts", ".scdoc",
                ".smk", ".tcw", ".vda", ".vdml", ".xpr", ".daz", ".j3o", ".max", ".mdd",
                ".shp", ".sib", ".vrmesh", ".vrscene",]

            filetypes_documents = [ ".odt", ".ods", ".odp", ".doc", ".docx", ".msg",
                ".pages", ".pdf", ".pps", ".ppsx", ".ppt", ".html", ".htm", ".pptx",
                ".rtf", ".tsv", ".txt", ".vdx", ".vsd", ".vsdm", ".vsdx", ".vss", ".vssm",
                ".vssx", ".vst", ".vstm", ".vstx", ".vsx", ".vtx", ".wks", ".wpd", ".wps",
                ".xls", ".xlsx", ".xlr", ".xml", ]

            filetypes_archives = [ ".7z", ".ace", ".bz", ".bz2", ".cbr", ".cbz", ".dmg",
                ".egg", ".gz", ".ha", ".jar", ".lzma", ".pak", ".par2", ".part", ".rar",
                ".rar5", ".rpm", ".sit", ".sitx", ".sqx", ".tar", ".tar.bz2", ".tar.gz",
                ".tbz", ".tbz2", ".tgz", ".txz", ".war", ".xar", ".xz", ".z", ".zip",
                ".zipx", ".zoo", ".arj", ".iso", ".vhd", ".wim", ]

            filetypes_executables = [ ".apk", ".app", ".bat", ".bin", ".cgi", ".csh", ".cmd",
                ".dll", ".elf", ".exe", ".js", ".gadget", ".msi", ".ps1", ".run", ".scr", ".sh",
                ".sys", ".vb", ".vbs", ".wsf", ".wsh", ]

            filetypes_other = [ ".123", ".1st", ".3dsx", ".3mx", ".abc", ".ac", ".acd",
                ".accdb", ".actx", ".adn", ".aglib", ".alb", ".asd", ".asmx", ".ass",
                ".atlo", ".att", ".avchd", ".bak", ".bas", ".bdf", ".bip", ".bndl", ".bpk",
                ".btm", ".c", ".c4d", ".calb", ".cap", ".catdrawing", ".catpart",
                ".catproduct", ".cd", ".cdl", ".cdr", ".cdt", ".cel", ".celx", ".cfg",
                ".cgr", ".class", ".cls", ".cmx", ".cne", ".cob", ".cpp", ".cs", ".cs1",
                ".csp", ".csr", ".css", ".csu", ".csv", ".ctb", ".ctfx", ".ctl", ".cw",
                ".cwf", ".cxp", ".d", ".dat", ".dch", ".ddd", ".ddp",
                ".ddrw", ".ddx", ".dfx", ".dgnws", ".divx", ".dml", ".dmp", ".dng", ".dot",
                ".dotm", ".dotx", ".dsw", ".dtml", ".dwt", ".dwfx", ".dwk", ".dws", ".dx", ]

            include_only_these_filetypes = []
            include_only_these_filetypes.extend(filetypes_images)
            include_only_these_filetypes.extend(filetypes_audio)
            include_only_these_filetypes.extend(filetypes_video)
            include_only_these_filetypes.extend(filetypes_3d_cad)
            include_only_these_filetypes.extend(filetypes_documents)
            include_only_these_filetypes.extend(filetypes_archives)
            include_only_these_filetypes.extend(filetypes_executables)
            include_only_these_filetypes.extend(filetypes_other)

            filetypes_included = list(set(set(include_only_these_filetypes + include_filetypes)) - set(exclude_filetypes))
            filetypes_input = list(set(set(include_filetypes)) - set(exclude_filetypes))
            filetypes_final = filetypes_input if override_defaults else filetypes_included

            # self.printer.info(f"Filetypes to include: {filetypes_final}")

            return filetypes_final

        def generate_filter_exclude_folders(self, exclude_paths=[], override_defaults=False):
            # Exclude folders containing the following paths
            common_exclude_folders = [ "*/.git", "*/venv/*", "*/_V", "*/.VENV"]

            paths_exclude_input = list(set(common_exclude_folders + exclude_paths))
            paths_exclude_final = exclude_paths if override_defaults else paths_exclude_input

            # self.printer.info(f"Paths to exclude: {paths_exclude_final}")

            return paths_exclude_final

        def generate_efu_command(self, input_path, output_file, flt_include_only=None, flt_exclude_folders=None, flt_exclude_files=None):
            """This method will generate a command string to be used by the 'Everything' software."""
            command = f'"{self.EVERYTHING_PATH}" -create-filelist "{output_file}" "{input_path}"'

            # Add exclude-folders filters if any
            if flt_exclude_folders:
                exclude_folders = ";".join(path.replace("/", "\\") for path in flt_exclude_folders)
                command += f' -create-file-list-exclude-folders "{exclude_folders}"'

            # Add exclude-files filters if any
            if flt_exclude_files:
                exclude_files = ";".join(f"*{file}" for file in flt_exclude_files)
                command += f' -create-file-list-exclude-files "{exclude_files}"'

            # Add include-only-files filters if any
            if flt_include_only:
                include_only_files = ";".join(f"*{ext}" for ext in flt_include_only)
                command += f' -create-file-list-include-only-files "{include_only_files}"'

            return command

        def merge_processed_efu_files(self, root_path, root_name, efu_filepaths=[]):
            # merge into single efu
            efu_merged_filename = f'{root_name}__MERGED.efu'
            efu_merged_filepath = os.path.join(root_path, efu_merged_filename)

            # Open output file in write mode
            # with open(efu_merged_filepath, 'w') as outfile:
            with open(efu_merged_filepath, 'w', encoding='utf-8') as outfile:
                # Iterate over each file
                for i, fname in enumerate(efu_filepaths):
                    # with open(fname) as infile:
                    with open(fname, 'r', encoding='utf-8') as infile:
                        # If it's the first file, write all lines (including header)
                        if i == 0:
                            outfile.write(infile.read())
                        # If it's not the first file, skip the header
                        else:
                            next(infile)
                            # Write the file's contents to the output file
                            for line in infile:
                                outfile.write(line)

            if os.path.exists(efu_merged_filepath):
                self.printer.good(f'finished merging: {root_name}')

        def combine_merged_efu_files(self):
            # combine merged filelists into single combined efu

            # filename/path for new combined efu
            efu_combined_filename = f'ALL__COMBINED.efu'
            efu_combined_filepath = os.path.join(self.OUTPUT_ROOT_DIRECTORY, efu_combined_filename)

            # with open(efu_merged_filepath, 'w') as outfile:
            with open(efu_combined_filepath, 'w', encoding='utf-8') as outfile:
                # Iterate over each dict_item
                for i, dict_item in enumerate(self.GENERATE_FILELIST_FROM_PATHS):

                    # get expected path to merged efu file
                    root_name = dict_item["efu_filename"]
                    root_path = os.path.join(self.OUTPUT_ROOT_DIRECTORY, root_name)
                    efu_merged_filename = f'{root_name}__MERGED.efu'
                    efu_merged_filepath = os.path.join(root_path, efu_merged_filename)
                    # if exists
                    print(efu_merged_filepath)
                    if os.path.exists(efu_merged_filepath):
                        with open(efu_merged_filepath, 'r', encoding='utf-8') as infile:
                            # If it's the first file, write all lines (including header)
                            if i == 0:
                                outfile.write(infile.read())
                            # If it's not the first file, skip the header
                            else:
                                next(infile)
                                # Write the file's contents to the output file
                                for line in infile:
                                    outfile.write(line)

            if os.path.exists(efu_combined_filepath):
                self.printer.good(f'finished merging: {efu_combined_filename}')

    def main():
        efu_generator = EfuFileGenerator()
        efu_generator.process_all_paths()
        efu_generator.combine_merged_efu_files()

    if __name__ == '__main__':
        main()
    ```
