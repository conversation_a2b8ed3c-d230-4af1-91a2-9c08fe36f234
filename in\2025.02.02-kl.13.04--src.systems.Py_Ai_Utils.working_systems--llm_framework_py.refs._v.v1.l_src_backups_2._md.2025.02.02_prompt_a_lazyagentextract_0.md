Your goal is to familiarize yourself with the provided framework and express in a single sentence exactly what it is and what makes this uniquely suitable for enhancing prompts in a concistent manner:

    # Project Files Documentation for `Prompt Optimizer Agent_d`

    ### File Structure

    ```
    ├── abilities.py
    ├── app_init.py
    ├── init_db.py
    ├── main.py
    ├── models.py
    ├── routes.py
    ├── run.py
    └── run_migrations.py
    ```


    #### `abilities.py`

    ```python
    from flask import render_template_string, session, redirect, url_for
    import sqlite3

    def flask_app_authenticator(allowed_domains=None, allowed_users=None, logo_path=None, app_title=None, custom_styles=None, session_expiry=None):
        def auth_decorator():
            return None  # Always allow access without authentication
        return auth_decorator

    def apply_sqlite_migrations(engine, model_base, migrations_dir):
        """Apply SQLite migrations to the database."""
        pass  # We'll implement this if needed

    class llm:
        @staticmethod
        def optimize_prompt(prompt, target_model="gpt-4"):
            """Placeholder for prompt optimization logic"""
            return prompt

        @staticmethod
        def analyze_prompt(prompt):
            """Placeholder for prompt analysis logic"""
            return {
                "complexity": "medium",
                "clarity": "high",
                "suggestions": []
            } ```


    #### `app_init.py`

    ```python
    from flask import Flask
    from models import db
    import os

    def create_initialized_flask_app():
        app = Flask(__name__, static_folder='static')
        app.secret_key = os.urandom(24)

        # Initialize database
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///local.db'
        db.init_app(app)

        # Register routes
        from routes import register_routes
        register_routes(app)

        return app```


    #### `init_db.py`

    ```python
    from app_init import create_initialized_flask_app
    from models import db
    import os

    def init_db():
        app = create_initialized_flask_app()

        # Create database directory if it doesn't exist
        db_dir = os.path.join(os.path.dirname(__file__), 'instance')
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)

        with app.app_context():
            db.create_all()
            print("Database initialized successfully!")

    if __name__ == "__main__":
        init_db() ```


    #### `main.py`

    ```python
    import logging
    from gunicorn.app.base import BaseApplication
    from app_init import create_initialized_flask_app

    # Flask app creation should be done by create_initialized_flask_app to avoid circular dependency problems.
    app = create_initialized_flask_app()

    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    class StandaloneApplication(BaseApplication):
        def __init__(self, app, options=None):
            self.application = app
            self.options = options or {}
            super().__init__()

        def load_config(self):
            # Apply configuration to Gunicorn
            for key, value in self.options.items():
                if key in self.cfg.settings and value is not None:
                    self.cfg.set(key.lower(), value)

        def load(self):
            return self.application


    if __name__ == "__main__":
        options = {
            "bind": "0.0.0.0:8080",
            "loglevel": "info",
            "accesslog": "-",
            "timeout": 120,
            "preload": True,
            "workers": 2,
            "worker_class": "gthread",
            "threads": 10,
            "max_requests": 300,
            "max_requests_jitter": 50
        }
        StandaloneApplication(app, options).run()```


    #### `models.py`

    ```python
    from flask_sqlalchemy import SQLAlchemy

    db = SQLAlchemy()

    class AllowList(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        email = db.Column(db.String(120), unique=True, nullable=False)
        role = db.Column(db.String(50), default='Admin')  # Added role field
        from_allowed_ending = db.Column(db.Boolean, default=False)  # Added from_allowed_ending field

        def is_email_allowed(self, email_to_check):
            """Check if an email is allowed based on exact match or wildcard pattern."""
            if '@' not in self.email or '@' not in email_to_check:
                return False

            pattern_domain = self.email.split('@')[1]
            email_domain = email_to_check.split('@')[1]

            # Check for exact match
            if self.email == email_to_check:
                return True

            # Check for wildcard domain match (e.g., *@domain.com)
            if self.email.startswith('*@') and email_domain == pattern_domain:
                return True

            return False

    class ChatbotSettings(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        moderation_prompt = db.Column(db.Text, default="")
        model_selection = db.Column(db.String(50), default="gpt-4o")
        temperature = db.Column(db.Float, default=0.7)  # New field for temperature

        @classmethod
        def get_settings(cls):
            settings = cls.query.first()
            if not settings:
                settings = cls()
                db.session.add(settings)
                db.session.commit()
            return settings

    class ChatMessage(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        message = db.Column(db.Text, nullable=False)
        is_ai = db.Column(db.Boolean, default=False)
        timestamp = db.Column(db.DateTime, default=db.func.current_timestamp())
        model = db.Column(db.String(50))  # The AI model used for this message

    class AllowedEmailEndings(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        email_ending = db.Column(db.String(120), unique=True, nullable=False)

    class BlockList(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        email = db.Column(db.String(120), unique=True, nullable=False)```


    #### `routes.py`

    ```python
    from models import ChatbotSettings, ChatMessage, AllowList, AllowedEmailEndings, BlockList, db
    from abilities import llm
    from datetime import datetime, timedelta
    from flask import render_template, jsonify, url_for, request, redirect, session, flash
    from flask import current_app as app
    import logging
    from datetime import datetime

    def register_routes(app):
        # Main routes
        @app.route("/")
        def home_route():
            settings = ChatbotSettings.query.first()
            if not settings:
                settings = ChatbotSettings()
                db.session.add(settings)
                db.session.commit()
            return render_template("home.html", title="Home", settings=settings)

        @app.route("/prompt-enhancer")
        def prompt_enhancer_route():
            return render_template("prompt_enhancer.html", title="Prompt Enhancer")

        @app.route("/company-admins")
        def company_admins_route():
            allowed_emails = AllowList.query.all()
            allowed_endings = AllowedEmailEndings.query.all()
            blocked_emails = BlockList.query.all()

            for email in allowed_emails:
                email.is_blocked = any(blocked.email == email.email for blocked in blocked_emails)
                email.role = "Owner" if email.id == 1 else email.role

            return render_template("company_admins.html", title="Company Admins", allowed_emails=allowed_emails, allowed_endings=allowed_endings)

        @app.route("/simulator")
        def ai_chat_route():
            user_id = session['user']['email']
            chat_history = ChatMessage.query.filter_by(user_id=user_id).order_by(ChatMessage.timestamp).all()
            return render_template("ai_chat.html", title="AI Chat Simulator", chat_history=chat_history)

        # Settings API routes
        @app.route("/api/update_settings", methods=["POST"])
        def update_settings():
            try:
                settings = ChatbotSettings.query.first()
                if not settings:
                    settings = ChatbotSettings()
                    db.session.add(settings)

                settings.moderation_prompt = request.form.get("moderation_prompt", "")
                settings.model_selection = request.form.get("model_selection", "gpt-4o")
                settings.temperature = float(request.form.get("temperature", 0.7))

                db.session.commit()
                return jsonify({"status": "success", "message": "Settings updated successfully"})
            except Exception as e:
                db.session.rollback()
                app.logger.error(f"Error updating settings: {str(e)}")
                return jsonify({"status": "error", "message": "An error occurred while updating settings"}), 500

        # Prompt Enhancement API route
        @app.route("/api/enhance_prompt", methods=["POST"])
        def enhance_prompt():
            try:
                original_prompt = request.json.get("prompt")

                if not original_prompt:
                    return jsonify({"error": "No prompt provided"}), 400

                settings = ChatbotSettings.get_settings()

                # Calculate maximum allowed length for enhanced prompt
                max_length = len(original_prompt) * 20

                enhancement_instructions = """
                You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines:
                - Deliver concise, precise results for quick understanding
                - Focus on brevity while preserving the core message
                - Use clear, unambiguous language
                - Present information logically
                - The enhanced prompt MUST NOT exceed twice the length of the original prompt
                - Generate a brief title (max 50 chars) that captures the main purpose/topic
                - Structure the response in a nested dialogue format to facilitate context-rich, layered conversations
                - Support complex reasoning through hierarchical organization of ideas

                Original prompt length: """ + str(len(original_prompt)) + """ characters
                Maximum allowed length: """ + str(max_length) + """ characters
                """

                response = llm(
                    prompt=f"{enhancement_instructions}\n\nOriginal Prompt: {original_prompt}\n\nProvide both a title and enhanced prompt structured in a nested dialogue format:",
                    response_schema={
                        "type": "object",
                        "properties": {
                            "title": {"type": "string", "maxLength": 50},
                            "enhanced_prompt": {"type": "string"},
                            "context_layers": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "level": {"type": "integer"},
                                        "context": {"type": "string"}
                                    }
                                }
                            }
                        },
                        "required": ["title", "enhanced_prompt", "context_layers"]
                    },
                    image_url=None,
                    model=settings.model_selection,
                    temperature=settings.temperature
                )

                title = response["title"][:50]  # Ensure title length limit
                enhanced_prompt = response["enhanced_prompt"]
                context_layers = response["context_layers"]

                # Format the enhanced prompt with nested context
                formatted_prompt = enhanced_prompt + "\n\nContext Layers:\n"
                for layer in context_layers:
                    formatted_prompt += f"\nLevel {layer['level']}: {layer['context']}"

                # Ensure the enhanced prompt doesn't exceed maximum length
                if len(formatted_prompt) > max_length:
                    formatted_prompt = formatted_prompt[:max_length]
                    app.logger.warning(f"Enhanced prompt was truncated to meet length requirement")

                return jsonify({
                    "title": title,
                    "enhanced_prompt": formatted_prompt,
                    "original_length": len(original_prompt),
                    "enhanced_length": len(formatted_prompt),
                    "context_layers": context_layers
                })
            except Exception as e:
                app.logger.error(f"Error enhancing prompt: {str(e)}")
                return jsonify({"error": "An error occurred while enhancing the prompt"}), 500

        # Admin management API routes
        @app.route("/api/company_admins/create", methods=["POST"])
        def api_create_company_admin():
            email = request.json.get('email')
            if not email:
                return jsonify({"status": "error", "message": "Email is required"}), 400

            try:
                new_email = AllowList(email=email)
                db.session.add(new_email)
                db.session.commit()
                return jsonify({"status": "success", "message": "Email added to allow list successfully"})
            except Exception as e:
                db.session.rollback()
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/api/allowed_email_endings/create", methods=["POST"])
        def api_create_allowed_email_ending():
            email_ending = request.json.get('email_ending')
            if not email_ending:
                return jsonify({"status": "error", "message": "Email ending is required"}), 400
            # Remove '@' if it's included in the email ending
            email_ending = email_ending.lstrip('@')

            try:
                new_ending = AllowedEmailEndings(email_ending=email_ending)
                db.session.add(new_ending)
                db.session.commit()
                return jsonify({"status": "success", "message": "Email ending added successfully"})
            except Exception as e:
                db.session.rollback()
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/api/delete_admin/<int:admin_id>", methods=["DELETE"])
        def delete_admin(admin_id):
            try:
                if admin_id == 1:
                    return jsonify({"status": "error", "message": "You are not allowed to delete the super admin."}), 403
                admin = AllowList.query.get(admin_id)
                if admin:
                    if admin.email == session['user']['email']:
                        return jsonify({"status": "error", "message": "You are not allowed to delete yourself as an admin."}), 403
                    db.session.delete(admin)
                    db.session.commit()
                    return jsonify({"status": "success", "message": "Admin deleted successfully"})
                else:
                    return jsonify({"status": "error", "message": "Admin not found"}), 404
            except Exception as e:
                db.session.rollback()
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/api/block_admin/<int:admin_id>", methods=["POST"])
        def block_admin(admin_id):
            try:
                admin = AllowList.query.get(admin_id)
                if admin:
                    if admin.email == session['user']['email']:
                        return jsonify({"status": "error", "message": "You cannot block yourself"}), 403
                    blocked = BlockList(email=admin.email)
                    db.session.add(blocked)
                    db.session.commit()
                    return jsonify({"status": "success", "message": "Admin blocked successfully"})
                else:
                    return jsonify({"status": "error", "message": "Admin not found"}), 404
            except Exception as e:
                db.session.rollback()
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/api/unblock_admin/<int:admin_id>", methods=["POST"])
        def unblock_admin(admin_id):
            try:
                admin = AllowList.query.get(admin_id)
                if admin:
                    blocked = BlockList.query.filter_by(email=admin.email).first()
                    if blocked:
                        db.session.delete(blocked)
                        db.session.commit()
                        return jsonify({"status": "success", "message": "Admin unblocked successfully"})
                    else:
                        return jsonify({"status": "error", "message": "Admin was not blocked"}), 400
                else:
                    return jsonify({"status": "error", "message": "Admin not found"}), 404
            except Exception as e:
                db.session.rollback()
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/api/delete_email_ending/<int:ending_id>", methods=["DELETE"])
        def delete_email_ending(ending_id):
            try:
                ending = AllowedEmailEndings.query.get(ending_id)
                if ending:
                    # Get all admins with this email ending
                    admins_to_remove = AllowList.query.filter(
                        AllowList.email.like(f"%@{ending.email_ending}"),
                        AllowList.id != 1  # Exclude the owner (ID 1)
                    ).all()

                    # Delete the admins
                    removed_count = len(admins_to_remove)
                    for admin in admins_to_remove:
                        db.session.delete(admin)

                    # Delete the email ending
                    db.session.delete(ending)
                    db.session.commit()

                    message = f"Email ending deleted successfully. {removed_count} admin{'s' if removed_count != 1 else ''} removed."
                    return jsonify({"status": "success", "message": message})
                else:
                    return jsonify({"status": "error", "message": "Email ending not found"}), 404
            except Exception as e:
                db.session.rollback()
                app.logger.error(f"Error deleting email ending: {str(e)}")
                return jsonify({"status": "error", "message": str(e)}), 500

        # Chat API routes
        @app.route("/api/chat", methods=["POST"])
        def chat():
            user_message = request.json.get("message")
            user_id = session['user']['email']
            if not user_message:
                return jsonify({"error": "No message provided"}), 400

            # Get chatbot settings
            settings = ChatbotSettings.get_settings()

            try:
                # Get chat history
                chat_history = ChatMessage.query.filter_by(user_id=user_id).order_by(ChatMessage.timestamp).limit(10).all()
                chat_context = "\n".join([f"{'AI' if msg.is_ai else 'User'}: {msg.message}" for msg in chat_history])

                # Prepare chat instructions
                chat_instructions = f"""
                Instructions for AI Assistant:
                {settings.moderation_prompt}

                Please provide a helpful and informative response to the user's message.
                """

                response = llm(
                    prompt=f"{chat_instructions}\n\n{chat_context}\nUser: {user_message}\nAI:",
                    response_schema={
                        "type": "object",
                        "properties": {
                            "response": {"type": "string"}
                        },
                        "required": ["response"]
                    },
                    image_url=None,
                    model=settings.model_selection,
                    temperature=settings.temperature
                )

                ai_response = response["response"]

                # Save user message
                user_chat = ChatMessage(
                    user_id=user_id,
                    message=user_message,
                    is_ai=False,
                    model=settings.model_selection
                )
                db.session.add(user_chat)

                # Save AI message
                ai_chat = ChatMessage(
                    user_id=user_id,
                    message=ai_response,
                    is_ai=True,
                    model=settings.model_selection
                )
                db.session.add(ai_chat)
                db.session.commit()

                return jsonify({
                    "response": ai_response,
                    "model": settings.model_selection
                })
            except Exception as e:
                app.logger.error(f"Error in chat API: {str(e)}")
                return jsonify({"error": "An error occurred while processing your request"}), 500

        @app.route("/api/clear_chats", methods=["POST"])
        def clear_chats():
            try:
                user_id = session['user']['email']
                ChatMessage.query.filter_by(user_id=user_id).delete()
                db.session.commit()
                return jsonify({"status": "success", "message": "All chats cleared successfully"})
            except Exception as e:
                db.session.rollback()
                app.logger.error(f"Error clearing chats: {str(e)}")
                return jsonify({"status": "error", "message": "An error occurred while clearing chats"}), 500

        # Authentication routes
        @app.route("/logout")
        def logout():
            session.clear()
            return redirect(url_for('home_route'))

        # Error handlers
        @app.errorhandler(401)
        def unauthorized(e):
            return render_template('unauthorized.html'), 401

        @app.errorhandler(404)
        def page_not_found(e):
            return render_template('404.html'), 404

        @app.errorhandler(500)
        def internal_server_error(e):
            return render_template('500.html'), 500

        @app.route('/optimize', methods=['POST'])
        def optimize_prompt():
            data = request.get_json()
            prompt = data.get('prompt', '')

            # Store the original prompt
            message = ChatMessage(message=prompt, is_ai=False)
            db.session.add(message)

            # Optimize the prompt
            optimized = llm.optimize_prompt(prompt)

            # Store the optimized prompt
            ai_message = ChatMessage(message=optimized, is_ai=True, model="local")
            db.session.add(ai_message)
            db.session.commit()

            return jsonify({
                'optimized_prompt': optimized,
                'analysis': llm.analyze_prompt(prompt)
            })

        @app.route('/history')
        def get_history():
            messages = ChatMessage.query.order_by(ChatMessage.timestamp.desc()).all()
            return jsonify([{
                'message': msg.message,
                'is_ai': msg.is_ai,
                'timestamp': msg.timestamp.isoformat(),
                'model': msg.model
            } for msg in messages])```


    #### `run.py`

    ```python
    from app_init import create_initialized_flask_app

    if __name__ == "__main__":
        app = create_initialized_flask_app()
        app.run(host="0.0.0.0", port=8080, debug=True) ```


    #### `run_migrations.py`

    ```python
    import sqlite3
    import os

    def run_migrations():
        db_path = os.path.join('instance', 'app.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        migrations_dir = 'migrations'
        migration_files = sorted([f for f in os.listdir(migrations_dir) if f.endswith('.sql')])

        for migration_file in migration_files:
            print(f"Applying migration: {migration_file}")
            with open(os.path.join(migrations_dir, migration_file), 'r') as f:
                sql = f.read()
                try:
                    cursor.executescript(sql)
                    conn.commit()
                    print(f"Successfully applied {migration_file}")
                except sqlite3.Error as e:
                    print(f"Error applying {migration_file}: {e}")
                    conn.rollback()

        conn.close()
        print("All migrations completed!")

    if __name__ == "__main__":
        run_migrations() ```
