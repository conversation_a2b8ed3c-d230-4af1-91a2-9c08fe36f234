# Project Files Documentation for `py_llm_framework`

### File Structure

```
├── PromptOptimizerExpert.xml
└── template_runner.py
```


#### `PromptOptimizerExpert.xml`

```xml
<!--
<metadata>
    <agent_name value="[FILENAME]" />
    <description value="The [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <version value="0" />
    <status value="wip" />
</metadata>
-->

[TEMPLATE_START]
<template>
    <purpose value="[FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />

    <agent>
        <name value="[FILENAME]" />
        <role value="Prompt Optimizer" />
        <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />
        <instructions>
            <constants>
                <input value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />
            </constants>
            <constraints>
                <input value="Maintain logical, hierarchical organization." />
                <input value="Avoid redundancy, ensure coherence." />
                <input value="Limit length to double the original prompt." />
            </constraints>
            <process>
                <input value="Analyze core message." />
                <input value="Identify key themes." />
                <input value="Generate concise title (max 50 chars)." />
                <input value="Expand context layers meaningfully." />
                <input value="Produce refined, concise prompt." />
            </process>
            <guidelines>
                <input value="Use clear, structured language." />
                <input value="Ensure relevancy of context layers." />
                <input value="Prioritize more specific over generic, and actionable over vague instructions." />
                <input value="Maintain a logical flow and coherence within the combined instructions." />
            </guidelines>
            <requirements>
                <input value="Output must not exceed double the original length." />
                <input value="Detailed enough for clarity and precision." />
                <input value="JSON format containing: title, enhanced_prompt, and context_layers." />
            </requirements>
        </instructions>
    </agent>

[HEADER]
    <response_instructions>
        <![CDATA[
            Your response must be a JSON object:
            ```json
            {
                "title": "Descriptive title",
                "enhanced_prompt": "Optimized version of the prompt",
                "context_layers": [
                    {"level": 1, "context": "Primary context layer"},
                    {"level": 2, "context": "Secondary contextual details"},
                    // Additional layers as needed
                ]
            }
        ]]>
    </response_instructions>
[FOOTER]
</template>
<user_prompt>
    [INPUT_PROMPT]
</user_prompt>

[TEMPLATE_END]
```


#### `template_runner.py`

```python
#!/usr/bin/env python3
"""
template_runnet.py
A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.
Now updated to pass each template's 'enhanced_prompt' as input to the next template in the chain.
"""

import os
import sys
import re
import glob
import json
from typing import List, Dict, Union, Optional
from pathlib import Path

from dotenv import load_dotenv
from loguru import logger

# Provider SDKs
from openai import OpenAI
from anthropic import Anthropic

# -------------------------------------------------------
# 1. LowestLevelCommunicator
# -------------------------------------------------------
class LowestLevelCommunicator:
    """
    Captures raw input and output strings at the lowest level,
    preserving the entire stream before any filtering or transformation.
    """
    def __init__(self):
        self.raw_interactions = []

    def record_request(self, provider: str, model_name: str, messages: List[Dict[str, str]]):
        """
        Called just before sending the request to the LLM.
        """
        self.raw_interactions.append({
            "direction": "request",
            "provider": provider,
            "model_name": model_name,
            "messages": messages,
        })

    def record_response(self, provider: str, model_name: str, response_text: str):
        """
        Called immediately after receiving the raw text from the LLM.
        """
        self.raw_interactions.append({
            "direction": "response",
            "provider": provider,
            "model_name": model_name,
            "content": response_text,
        })

    def get_all_interactions(self) -> List[Dict]:
        """
        Return the raw record of all requests/responses.
        """
        return self.raw_interactions

    def get_formatted_output(self) -> str:
        """
        Pretty-print a summary of the captured interactions.
        """
        lines = []
        for entry in self.raw_interactions:
            direction = entry["direction"].upper()
            provider = entry["provider"]
            model_name = entry["model_name"]

            if direction == "REQUEST":
                lines.append(f"--- REQUEST to {provider} (model: {model_name}) ---")
                for i, msg in enumerate(entry["messages"], start=1):
                    role = msg.get("role", "").upper()
                    content = msg.get("content", "")
                    lines.append(f"{i}. {role}: {content}")
            else:
                lines.append(f"--- RESPONSE from {provider} (model: {model_name}) ---")
                lines.append(entry["content"])
            lines.append("")

        return "\n".join(lines).strip()

# -------------------------------------------------------
# 2. Global Configuration
# -------------------------------------------------------
class Config:
    """
    Global settings
    """

    PROVIDER_OPENAI = "openai"
    PROVIDER_DEEPSEEK = "deepseek"
    PROVIDER_ANTHROPIC = "anthropic"

    AVAILABLE_MODELS = {
        PROVIDER_OPENAI: {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",
            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        },
        PROVIDER_DEEPSEEK: {
            "deepseek-chat": "DeepSeek Chat model",
            "deepseek-reasoner": "Specialized reasoning model",
        },
        PROVIDER_ANTHROPIC: {
            "claude-2": "Base Claude 2 model",
            "claude-2.0": "Enhanced Claude 2.0",
            "claude-2.1": "Latest Claude 2.1 release",
            "claude-3-opus-20240229": "Claude 3 Opus",
            "claude-3-sonnet-20240229": "Claude 3 Sonnet",
            "claude-3-haiku-20240307": "Claude 3 Haiku",
        },
    }

    DEFAULT_MODEL_PARAMS = {
        PROVIDER_OPENAI: {
            "model_name": "gpt-4-turbo-preview", # (5) used occasionally
            "model_name": "gpt-4o",              # (4) debugging
            "model_name": "gpt-4-turbo",         # (3) used often
            "model_name": "gpt-3.5-turbo",       # (3) most used
            "model_name": "gpt-3.5-turbo-1106",  # (1) most used
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek-reasoner",
            "model_name": "deepseek-coder",
            "model_name": "deepseek-chat",
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-2.1",
            "model_name": "claude-3-opus-20240229",
            "temperature": 0.7,
            "max_tokens": 800,
        },
    }

    API_KEY_ENV_VARS = {
        PROVIDER_OPENAI: "OPENAI_API_KEY",
        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
    }

    BASE_URLS = {
        PROVIDER_DEEPSEEK: "https://api.deepseek.com",
    }

    # Overriding allows for switching between providers by simply reordering the lines.
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    def __init__(self):
        load_dotenv()
        self.configure_utf8_encoding()
        self.provider = self.DEFAULT_PROVIDER.lower()
        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
        self.setup_logger()

    def configure_utf8_encoding(self):
        """
        Ensure UTF-8 encoding for standard output and error streams.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    def setup_logger(self):
        """
        YAML logging via Loguru: clears logs, sets global context, and configures sinks
        """
        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
        log_filepath = os.path.join(self.log_dir, log_filename)
        open(log_filepath, "w").close()
        logger.remove()
        logger.configure(
            extra={
                "provider": self.provider,
                "model": self.model_params.get("model_name"),
            }
        )

        def yaml_sink(log_message):
            log_record = log_message.record
            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
            formatted_level = f"!{log_record['level'].name}"
            logger_name = log_record["name"]
            formatted_function_name = f"*{log_record['function']}"
            line_number = log_record["line"]
            extra_provider = log_record["extra"].get("provider")
            extra_model = log_record["extra"].get("model")
            log_message_content = log_record["message"]

            if "\n" in log_message_content:
                formatted_message = "|\n" + "\n".join(
                    f"  {line}" for line in log_message_content.splitlines()
                )
            else:
                formatted_message = (
                    f"'{log_message_content}'"
                    if ":" in log_message_content
                    else log_message_content
                )

            log_lines = [
                f"- time: {formatted_timestamp}",
                f"  level: {formatted_level}",
                f"  name: {logger_name}",
                f"  funcName: {formatted_function_name}",
                f"  lineno: {line_number}",
            ]
            if extra_provider is not None:
                log_lines.append(f"  provider: {extra_provider}")
            if extra_model is not None:
                log_lines.append(f"  model: {extra_model}")

            log_lines.append(f"  message: {formatted_message}")
            log_lines.append("")

            with open(log_filepath, "a", encoding="utf-8") as log_file:
                log_file.write("\n".join(log_lines) + "\n")

        logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")

# -------------------------------------------------------
# 3. LLM Interactions
# -------------------------------------------------------
class LLMInteractions:
    """
    Handles interactions with LLM APIs.
    """

    CLIENT_FACTORIES = {
        Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),
        Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),
        Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key),
    }

    def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):
        self.config = Config()
        self.provider = provider or self.config.provider
        defaults = self.config.DEFAULT_MODEL_PARAMS[self.provider]
        self.model_name = model_name or defaults["model_name"]
        self.temperature = temperature if temperature is not None else defaults["temperature"]
        self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        # Our communicator capturing lowest-level I/O
        self.communicator = LowestLevelCommunicator()
        self.client = self._create_client(api_key)

    def _create_client(self, api_key=None):
        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)
        api_key_use = api_key or os.getenv(api_key_env)
        try:
            return self.CLIENT_FACTORIES[self.provider](api_key_use)
        except KeyError:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

    def _log_api_response(self, response):
        prompt_tokens = getattr(getattr(response, "usage", None), "prompt_tokens", "N/A")
        logger.bind(prompt_tokens=prompt_tokens).debug(response)

    def _log_api_error(self, exception, model_name, messages):
        logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")
        logger.debug(f"Exception type: {type(exception).__name__}")
        logger.debug(f"Detailed exception: {exception}")
        logger.debug(f"Input messages: {messages}")

    def _execute_api_call(self, call_fn, model_name, messages):
        try:
            response = call_fn()
            self._log_api_response(response)
            return response
        except Exception as e:
            self._log_api_error(e, model_name, messages)
            return None

    def _openai_call(self, messages, model_name, temperature, max_tokens):
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )

    def _anthropic_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = "\n".join(msg["content"] for msg in messages if msg["role"] == "system")
        user_msgs = [msg for msg in messages if msg["role"] == "user"]

        return self.client.messages.create(
            model=model_name,
            max_tokens=max_tokens,
            temperature=temperature,
            system=system_prompt.strip(),
            messages=user_msgs,
        )

    def _deepseek_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = next((msg["content"] for msg in messages if msg["role"] == "system"), "")
        instructions_content = "\n".join(
            msg["content"]
            for msg in messages
            if msg["role"] == "system" and msg["content"] != system_prompt
        )
        user_prompt = next((msg["content"] for msg in messages if msg["role"] == "user"), "")
        combined_prompt = f"{system_prompt}\n{instructions_content}\n{user_prompt}"
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": combined_prompt}],
        )

    def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):
        # Record the raw request data
        self.communicator.record_request(self.provider, model_name, messages)

        provider_map = {
            Config.PROVIDER_OPENAI: lambda: self._openai_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),
        }

        provider_api_request = provider_map.get(self.provider)
        if provider_api_request is None:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

        api_response = self._execute_api_call(provider_api_request, model_name, messages)
        if not api_response:
            return None

        # Parse out raw text
        if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:
            raw_text = (
                api_response.choices[0].message.content
                if hasattr(api_response, "choices") and api_response.choices
                else None
            )
        elif self.provider == Config.PROVIDER_ANTHROPIC:
            raw_text = (
                api_response.content[0].text
                if hasattr(api_response, "content") and api_response.content
                else None
            )
        else:
            raw_text = None

        # Record the raw response
        if raw_text is not None:
            self.communicator.record_response(self.provider, model_name, raw_text)

        return raw_text

    def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
        used_model = model_name or self.model_name
        used_temp = temperature if temperature is not None else self.temperature
        used_tokens = max_tokens if max_tokens is not None else self.max_tokens
        return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)

# -------------------------------------------------------
# 4. Template File Manager
# -------------------------------------------------------
class TemplateFileManager:
    """
    Manages prompt templates, performing lazy loading, placeholder substitution,
    and recipe execution.
    """

    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")
    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]
    EXCLUDED_FILE_PATHS = ["\\_md\\"]
    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*", ]
    MAX_TEMPLATE_SIZE_KB = 100
    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]

    def __init__(self):
        self.template_dir = os.getcwd()
        self.template_cache = {}

    def template_qualifier(self, filepath):
        _, ext = os.path.splitext(filepath)
        filename = os.path.basename(filepath)
        basename, _ = os.path.splitext(filename)
        filepath_lower = filepath.lower()

        if ext.lower() not in self.ALLOWED_FILE_EXTS:
            return False
        if basename in self.EXCLUDED_FILE_NAMES:
            return False
        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):
            return False
        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):
            return False
        try:
            filesize_kb = os.path.getsize(filepath) / 1024
            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:
                return False
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()
            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):
                return False
        except Exception:
            return False

        return True

    def reload_templates(self):
        self.template_cache.clear()
        pattern = os.path.join(self.template_dir, "**", "*.*")
        for filepath in glob.glob(pattern, recursive=True):
            name = os.path.splitext(os.path.basename(filepath))[0]
            if self.template_qualifier(filepath):
                self.template_cache[name] = filepath

    def prefetch_templates(self, template_name_list):
        for name in template_name_list:
            _ = self.get_template_path(name)

    def get_template_path(self, template_name):
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        for ext in self.ALLOWED_FILE_EXTS:
            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")
            files = glob.glob(search_pattern, recursive=True)
            if files:
                self.template_cache[template_name] = files[0]
                return files[0]
        return None

    def _parse_template(self, template_path):
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                content = f.read()

            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))
            template_data = {
                "path": template_path,
                "content": content,
                "placeholders": placeholders,
            }
            return template_data

        except Exception as e:
            logger.error(f"Error parsing template file {template_path}: {e}")
            return {}

    def extract_placeholders(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return []
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return []
        return parsed_template.get("placeholders", [])

    def get_template_metadata(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return {}
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return {}

        content = parsed_template["content"]
        metadata = {
            "agent_name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
            "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
            "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),
            "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
            "system_prompt": self._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')
        }
        return metadata

    def _extract_value_from_content(self, content, pattern):
        match = re.search(pattern, content)
        return match.group(1) if match else None

    def list_templates(
        self,
        exclude_paths=None,
        exclude_names=None,
        exclude_versions=None,
        exclude_statuses=None,
        exclude_none_versions=False,
        exclude_none_statuses=False
    ):
        search_pattern = os.path.join(self.template_dir, "**", "*.*")
        templates_info = {}

        for filepath in glob.glob(search_pattern, recursive=True):
            if not self.template_qualifier(filepath):
                continue
            template_name = os.path.splitext(os.path.basename(filepath))[0]
            parsed_template = self._parse_template(filepath)
            if not parsed_template:
                logger.warning(f"Skipping {filepath} due to parsing error.")
                continue
            content = parsed_template["content"]
            try:
                templates_info[template_name] = {
                    "path": filepath,
                    "name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>')
                }
            except Exception as e:
                logger.error(f"Error loading template from {filepath}: {e}")

        filtered_templates = {}
        for name, info in templates_info.items():
            if ((not exclude_paths or info["path"] not in exclude_paths) and
                (not exclude_names or info["name"] not in exclude_names) and
                (not exclude_versions or info["version"] not in exclude_versions) and
                (not exclude_statuses or info["status"] not in exclude_statuses) and
                (not exclude_none_versions or info["version"] is not None) and
                (not exclude_none_statuses or info["status"] is not None)):
                filtered_templates[name] = info

        return filtered_templates

    def prepare_template(self, template_filepath, input_prompt=""):
        parsed_template = self._parse_template(template_filepath)
        if not parsed_template:
            return None

        content = parsed_template["content"]
        placeholders = {
            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",
            # "[TEMPLATE_START]": f"```{os.path.splitext(template_filepath)[1]}",
            "[FILENAME]": os.path.basename(template_filepath),
            "[OUTPUT_FORMAT]": "plain_text",
            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
            "[INPUT_PROMPT]": input_prompt,
            "[ADDITIONAL_CONSTRAINTS]": "",
            "[ADDITIONAL_PROCESS_STEPS]": "",
            "[ADDITIONAL_GUIDELINES]": "",
            "[ADDITIONAL_REQUIREMENTS]": "",
            # "[TEMPLATE_END]": f"```\n",
            "[FOOTER]": f"```",
        }

        for placeholder, value in placeholders.items():
            value_str = str(value)
            content = content.replace(placeholder, value_str)

        return content


    def _extract_template_parts(self, raw_text):
        """
        Extracts relevant sections from the raw template text.
        E.g., <system_prompt ...>, <response_format>, etc.
        """
        metadata_match = re.search(r"<metadata>(.*?)</metadata>", raw_text, re.DOTALL)
        response_format_match = re.search(r"<response_format>(.*?)</response_format>", raw_text, re.DOTALL)

        start_end_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)
        start_end = start_end_match.group(1) if start_end_match else ""

        # start_end_match = re.search(r"\=\=\=[START]\=\=\=(.*?)\=\=\=[END]\=\=\=", raw_text, re.DOTALL)

        template_match = re.search(r"<template>(.*?)</template>", raw_text, re.DOTALL)
        agent_match = re.search(r"<agent>(.*?)</agent>", raw_text, re.DOTALL)

        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)
        instructions_match = re.search(r"<instructions>(.*?)</instructions>", raw_text, re.DOTALL)

        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""
        response_format = response_format_match.group(1) if response_format_match else ""
        template = template_match.group(1) if template_match else ""
        instructions = instructions_match.group(1) if instructions_match else ""

        return system_prompt, response_format, start_end

# -------------------------------------------------------
# 5. Prompt Refinement Orchestrator
# -------------------------------------------------------
class PromptRefinementOrchestrator:
    def __init__(self, template_manager: TemplateFileManager, agent: LLMInteractions):
        self.template_manager = template_manager
        self.agent = agent

    def _build_messages(self, system_prompt: str, agent_instructions: str) -> List[Dict[str, str]]:
        """
        Prepare message.
        """
        return [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": agent_instructions.strip()},
        ]

    def _format_multiline(self, text):
        """
        Nicely format the text for console output (esp. if it is JSON).
        """
        if isinstance(text, dict) or isinstance(text, list):
            return json.dumps(text, indent=4, ensure_ascii=False)
        elif isinstance(text, str):
            try:
                if text.startswith("```json"):
                    json_match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)
                    if json_match:
                        return json.dumps(json.loads(json_match.group(1)), indent=4, ensure_ascii=False)
                elif text.strip().startswith("{") and text.strip().endswith("}"):
                    return json.dumps(json.loads(text), indent=4, ensure_ascii=False)
            except json.JSONDecodeError:
                pass
        return text.replace("\\n", "\n")

    # -------------------------------------------------------
    # CHANGED: Now parse "enhanced_prompt" from the JSON output
    #          and pass it on to the next iteration.
    # -------------------------------------------------------
    def execute_prompt_refinement_chain_from_file(
        self,
        template_filepath,
        input_prompt,
        refinement_count=1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        """
        Executes refinement(s) using one file-based template,
        passing 'enhanced_prompt' forward if present in the response JSON.
        """
        content = self.template_manager.prepare_template(template_filepath, input_prompt)
        if not content:
            return None

        agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')
        system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)
        prompt = input_prompt
        results = []

        for _ in range(refinement_count):
            msgs = self._build_messages(system_prompt.strip(), agent_instructions)
            print(len(msgs))
            # print(f'msgs: {msgs}')
            refined = self.agent.generate_response(
                msgs,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            if refined:
                # Attempt to pretty-print if JSON
                refined_str = refined
                try:
                    data = json.loads(refined_str)
                    refined_str = json.dumps(data, indent=4)
                except json.JSONDecodeError:
                    pass

                # Store the full raw response
                results.append(refined)

                # If the response is JSON and has "enhanced_prompt," pass that as the new input
                next_prompt = refined
                try:
                    data = json.loads(refined)
                    if isinstance(data, dict) and "enhanced_prompt" in data:
                        next_prompt = data["enhanced_prompt"]
                except (TypeError, json.JSONDecodeError):
                    pass

                prompt = next_prompt

        return results

    def _execute_single_template_refinement(self, template_name, initial_prompt, refinement_count, **kwargs):
        path = self.template_manager.get_template_path(template_name)
        if not path:
            logger.error(f"No template file found with name: {template_name}")
            return None
        return self.execute_prompt_refinement_chain_from_file(path, initial_prompt, refinement_count, **kwargs)

    def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, refinement_levels, **kwargs):
        if not all(isinstance(template, str) for template in template_name_list):
            logger.error("All items in template_name_list must be strings.")
            return None
        if isinstance(refinement_levels, int):
            counts = [refinement_levels] * len(template_name_list)
        elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):
            counts = refinement_levels
        else:
            logger.error("refinement_levels must be int or a list matching template_name_list.")
            return None

        results = []
        current_prompt = initial_prompt
        for name, cnt in zip(template_name_list, counts):
            chain_result = self._execute_single_template_refinement(name, current_prompt, cnt, **kwargs)
            if chain_result:
                # The last returned string from that chain becomes the next prompt
                current_prompt = chain_result[-1]
                results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})
        return results

    def execute_prompt_refinement_by_name(
        self,
        template_name_or_list: Union[str, List[str]],
        initial_prompt: str,
        refinement_levels: Union[int, List[int]] = 1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        if isinstance(template_name_or_list, str):
            return self._execute_single_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        elif isinstance(template_name_or_list, list):
            if not all(isinstance(x, str) for x in template_name_or_list):
                logger.error("All items in template_name_or_list must be strings.")
                return None
            return self._execute_multiple_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        else:
            logger.error("template_name_or_list must be str or list[str].")
            return None

    def execute_recipe(self, recipe: List[Dict], initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:
        """
        Executes a multi-step prompt refinement "recipe."
        """
        current_input = initial_prompt
        refinement_history = []
        gathered_outputs = []

        for idx, step in enumerate(recipe, start=1):
            chain = step.get("chain")
            repeats = step.get("repeats", 1)
            gather = step.get("gather", False)
            aggregator = step.get("aggregator_chain")
            if not chain:
                logger.error(f"Recipe step {idx} missing 'chain' key.")
                continue

            step_gathered = []
            for rep in range(repeats):
                data = self.execute_prompt_refinement_by_name(chain, current_input, 1)
                if data:
                    refinement_history.append({"step": idx, "repeat": rep + 1, "chain": chain, "result": data})
                    final_str = data[-1]
                    step_gathered.append(final_str)
                    if not gather:
                        current_input = final_str

            if gather and step_gathered:
                gathered_outputs.extend(step_gathered)
                if aggregator:
                    aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])
                    aggregator_data = self.execute_prompt_refinement_by_name(aggregator, aggregator_prompt, 1)
                    if aggregator_data:
                        refinement_history.append({
                            "step": idx,
                            "aggregator_chain": aggregator,
                            "aggregator_input": aggregator_prompt,
                            "aggregator_result": aggregator_data,
                        })
                        current_input = aggregator_data[-1]
                    else:
                        current_input = step_gathered[-1]
                else:
                    current_input = step_gathered[-1]

        return {
            "final_output": current_input,
            "refinement_history": refinement_history,
            "gathered_outputs": gathered_outputs,
        }

# -------------------------------------------------------
# 6. Main Execution
# -------------------------------------------------------
class Execution:
    def __init__(self, provider=None):
        self.config = Config()
        self.agent = LLMInteractions(provider=provider)
        self.template_manager = TemplateFileManager()
        self.refinement_engine = PromptRefinementOrchestrator(self.template_manager, self.agent)

    def log_usage_demo(self):
        self.template_manager.reload_templates()
        self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])

        placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")
        logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")

        metadata = self.template_manager.get_template_metadata("IntensityEnhancer")
        logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")

        all_temps = self.template_manager.list_templates(exclude_none_statuses=True, exclude_none_versions=True)
        logger.info(f"Found a total of {len(all_temps)} templates.")
        logger.info("Template keys: " + ", ".join(all_temps.keys()))

    def run(self):
        self.log_usage_demo()
        self.template_manager.reload_templates()

        # Example recipe - two passes of PromptOptimizerExpert, gather results,
        # then feed to MultiResponseSelector aggregator.
        # recipe_steps = [
        #     {
        #         "chain": ["PromptOptimizerExpert", "PromptOptimizerExpert"],
        #         "repeats": 1,
        #         "gather": True,
        #         "aggregator_chain": ["MultiResponseSelector"],
        #     },
        # ]

        recipe_steps = [
            {
                "chain": ["PromptOptimizerExpert"],
                "repeats": 2,
                "gather": False,
                "aggregator_chain": ["MultiResponseSelector"],
            },
            # {
            #     "chain": "RunwayPromptBuilder",
            #     "repeats": 1,
            # },
            # {
            #     "chain": ["CinematicSceneDescriptor", "MultiResponseSelector"],
            #     "repeats": 1,
            # },
            # {"chain": "CodeCommentCondenser", "repeats": 1},
            # {"chain": "MultiResponseSelector", "repeats": 1},
        ]


        initial_prompt = (
            "You are a steadfast, detail-oriented assistant. Your critical task is to refine "
            "this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all "
            "guidelines, ensuring the refined version fully captures and enhances the original's "
            "essence and intent."
        )

        initial_prompt = "Ignite a scenario so genuine and commanding it stirs unshakable empathy, leaving no heart unmoved. Demonstrate precisely how to shape and structure this video’s narrative, ensuring it resonates with staggering impact and unforgettable power. A scene that conveys profound loss."
        recipe_result = self.refinement_engine.execute_recipe(
            recipe=recipe_steps, initial_prompt=initial_prompt
        )

        # Show a formatted log of all raw I/O from the communicator
        print("\n=== Full Communicator Log (Raw I/O) ===")
        print(self.agent.communicator.get_formatted_output())




if __name__ == "__main__":
    provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
    execution = Execution(provider=provider_to_use)
    execution.run()
```
