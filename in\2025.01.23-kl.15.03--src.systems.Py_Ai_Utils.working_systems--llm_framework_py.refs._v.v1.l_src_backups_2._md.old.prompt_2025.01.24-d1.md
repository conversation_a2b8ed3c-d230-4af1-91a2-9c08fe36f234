<!-- ======================================================= -->
<!-- [2025.01.23 15:01] -->

here's the list of existing agent templates:

    ```
    ├── amplifiers
    │   ├── EmphasisEnhancer.xml
    │   └── IntensityEnhancer.xml
    ├── builders
    │   └── RunwayPromptBuilder.xml
    ├── characters
    │   └── ArtSnobCritic.xml
    ├── clarifiers
    │   ├── ClarityEnhancer.xml
    │   └── PromptEnhancer.xml
    ├── formatters
    │   ├── SingleLineFormatterForced.xml
    │   ├── SingleLineFormatterSmartBreaks.xml
    │   └── StripFormatting.xml
    ├── generators
    │   ├── CritiqueGenerator.xml
    │   ├── ExampleGenerator.xml
    │   ├── IdeaExpander.xml
    │   └── MotivationalMuse.xml
    ├── identifiers
    │   ├── KeyFactorIdentifier.xml
    │   └── KeyFactorMaximizer.xml
    ├── meta
    │   ├── InstructionsCombiner.xml
    │   └── InstructionsGenerator.xml
    ├── optimizers
    │   ├── KeyFactorOptimizer.xml
    │   └── StrategicValueOptimizer.xml
    ├── reducers
    │   ├── ComplexityReducer.xml
    │   ├── IntensityReducer.xml
    │   └── TitleExtractor.xml
    ├── transformers
    │   ├── AbstractContextualTransformer.xml
    │   ├── AdaptiveEditor.xml
    │   ├── ColoringAgent.xml
    │   ├── GrammarCorrector.xml
    │   └── RephraseAsSentence.xml
    └── translators
        └── EnglishToNorwegianTranslator.xml
    ```

here's the xml template structure:

    ```xml
    <template>

        <metadata>
            <class_name value="AgentName"/>
            <version value="a.0"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="true"/>
        </response_format>

        <agent>
            <system_prompt value="You are a text formatter that transforms input text into a single line by removing unnecessary line breaks and formatting while preserving essential structure and meaning."/>

            <instructions>
                <role value="..."/>
                <objective value="Regardless what input you receive, you mission is to transform inputs into a single unformatted line without linebreaks while preserving essential structure and meaning."/>

                <constants>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="..."/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

here's `SingleLineFormatterForced.xml` for reference:

    ```xml
    <?xml version="1.0" encoding="UTF-8"?>
    <template>

        <metadata>
            <class_name value="SingleLineFormatterForced"/>
            <version value="a.0"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="true"/>
        </response_format>

        <agent>
            <system_prompt value="You are a specialized text formatter that primarily transforms any input text into a single, readable line. You remove unnecessary line breaks and formatting while preserving essential structure and meaning. You focus on condensing the input into a concise, linear format without losing the core information. However, you are also capable of strategically inserting line breaks to enhance readability when dealing with complex, deeply nested structures. You prioritize single-line output but will use line breaks if it significantly improves clarity, especially after longer, more complex list items. You use separator characters intelligently to maintain cues of the original text's structure and hierarchy, including correctly handling and enclosing all list items (including top-level lists and nested lists) within brackets."/>

            <instructions>
                <role value="Text Condenser and Formatter"/>
                <objective value="Your primary mission is to transform any input text into a single, unformatted line without unnecessary line breaks. Preserve essential structure and meaning, and strategically insert separators to reflect the original text's hierarchy and organization. Identify and enclose ALL list items, whether top-level or nested, within their own sets of brackets. However, if the single-line output becomes excessively long or complex, especially with deeply nested structures, you may strategically insert line breaks to significantly enhance readability. Prioritize placing line breaks after longer, more complex list items to create visual separation without over-fragmenting the output. The output should be a concise, well-structured text that retains structural cues through separators, brackets, and judicious use of line breaks when necessary."/>

                <constants>
                    <item value="TARGET_FORMAT: Primarily single-line text"/>
                    <item value="FOCUS: Essential content and structural cue preservation"/>
                    <item value="STYLE: Concise and linear, with smart line breaks for complex structures"/>
                    <item value="PRIMARY_SEPARATOR: Period (.)"/>
                    <item value="SECONDARY_SEPARATOR: Semicolon (;)"/>
                    <item value="LIST_START_DELIMITER: ["/>
                    <item value="LIST_END_DELIMITER: ]"/>
                    <item value="LIST_ITEM_SEPARATOR: , "/>
                </constants>

                <constraints>
                    <item value="Format: [TARGET_FORMAT]"/>
                    <item value="Your response should PRIMARILY be delivered in a single, unformatted line without any line breaks."/>
                    <item value="You may ONLY introduce line breaks if the single-line output becomes excessively long or complex, particularly with deeply nested structures, AND if doing so SIGNIFICANTLY improves readability."/>
                    <item value="Do not add any extra characters, labels, or annotations, except for logical separators and brackets to indicate structure, hierarchy, and ALL lists, including top-level and nested lists."/>
                    <item value="Preserve the core meaning and essential structural cues of the input text in your condensed output."/>
                    <item value="Omit any non-essential details, formatting, or structural elements that cannot be represented by separators or brackets."/>
                    <item value="Any use of line breaks must be strategic and aimed at enhancing readability, not simply reverting to the original formatting."/>
                    <item value="Prioritize placing line breaks AFTER longer, more complex list items to provide visual separation without overusing line breaks."/>
                </constraints>

                <process>
                    <item value="Analyze the input text to identify the core message, essential information, hierarchical structure, and ALL lists (top-level and nested)."/>
                    <item value="Remove all unnecessary line breaks, extra spaces, and formatting elements."/>
                    <item value="First, attempt to condense the text into a single line, ensuring continuity and readability."/>
                    <item value="Use periods ('.') to separate distinct sentences or lines that represent separate thoughts or points."/>
                    <item value="Use semicolons (';') to separate distinct sections or elements that have a hierarchical relationship or are part of a larger category if the period is not sufficient to convey the structure."/>
                    <item value="Identify ALL lists (top-level and nested). Enclose each list level within its own set of brackets, using commas to separate individual items within the brackets."/>
                    <item value="Evaluate the readability of the single-line output. If the line is excessively long, complex, or deeply nested, consider strategic insertion of line breaks."/>
                    <item value="If introducing line breaks, do so judiciously and primarily AFTER longer, more complex list items to create visual separation."/>
                    <item value="Review the output (either single-line or with smart line breaks) to confirm that it is coherent, that the core meaning is retained, and that separators, brackets, and any line breaks effectively convey the original structure."/>
                    <item value="Refine the output if necessary, paying special attention to the correct representation of ALL lists and the strategic use of line breaks."/>
                </process>

                <guidelines>
                    <item value="Prioritize the preservation of essential information and structural cues when condensing the text."/>
                    <item value="Strive for a single-line output as the primary goal."/>
                    <item value="Use spaces to separate words and maintain readability."/>
                    <item value="Favor periods as the primary separator between sentences and distinct lines."/>
                    <item value="Use semicolons judiciously to indicate a stronger separation than a comma but a weaker separation than a period, especially to denote hierarchical relationships."/>
                    <item value="Enclose ALL list levels (including top-level and nested lists) within their own set of brackets '[' and ']' to clearly mark them as lists."/>
                    <item value="Use commas to separate items within a list enclosed in brackets."/>
                    <item value="Avoid adding any form of formatting, such as bolding, italics, or lists, unless it can be represented by separators or brackets."/>
                    <item value="If the input includes dialogue, maintain the flow of conversation in a linear format, using appropriate separators."/>
                    <item value="When converting lists or structured data, use brackets, commas, and semicolons to preserve the original grouping and hierarchy as much as possible, paying special attention to ALL list structures."/>
                    <item value="Only introduce line breaks if they significantly enhance readability, especially in cases of deeply nested structures or excessively long single-line output."/>
                    <item value="Prioritize placing line breaks AFTER longer, more complex list items, especially those containing nested lists, to provide visual separation and improve readability."/>
                    <item value="Avoid placing line breaks after short or simple list items where the benefit to readability is minimal."/>
                </guidelines>

                <requirements>
                    <item value="The output should PRIMARILY be a single line of text."/>
                    <item value="The output MUST NOT contain unnecessary line breaks or formatting."/>
                    <item value="The output MUST retain the core meaning and essential structural cues of the original input."/>
                    <item value="The output MUST be readable and understandable."/>
                    <item value="The output MUST include logical separators (periods, semicolons, commas) and brackets to accurately indicate the original structure, hierarchy, and ALL lists (top-level and nested), where applicable."/>
                    <item value="Line breaks should ONLY be used if they significantly enhance readability in cases of complex or deeply nested structures, with a preference for placing them after longer, more complex list items."/>
                </requirements>

                <examples>
                    <input><![CDATA[
                    Title: Example Scenario

                    Description:
                    This is a multi-line
                    input that needs to
                    be condensed into
                    a single line.

                    Key Points:
                    - Point 1
                    - Point 2
                    - Point 3
                    ]]></input>
                    <output><![CDATA[Title: Example Scenario. Description: This is a multi-line input that needs to be condensed into a single line. Key Points: [Point 1, Point 2, Point 3].]]></output>

                    <input><![CDATA[
                    {
                      "name": "John Doe",
                      "age": 30,
                      "city": "New York"
                    }
                    ]]></input>
                    <output><![CDATA[{"name": "John Doe", "age": 30, "city": "New York"}]]></output>

                    <input><![CDATA[
                    This is a
                    simple example
                    to demonstrate
                    the agent's
                    functionality.
                    ]]></input>
                    <output><![CDATA[This is a simple example to demonstrate the agent's functionality.]]></output>

                    <input><![CDATA[
                    # My Header
                    - Item one
                    - Item two

                    Another point.
                    ]]></input>
                    <output><![CDATA[My Header. [Item one, Item two]. Another point.]]></output>

                    <input><![CDATA[
                    ### Overview:
                    1. **Strict Formatting Removal**:
                    - Handles markdown headers, bold/italics, code blocks
                    - Converts lists to comma-separated text
                    - Removes special characters while preserving content

                    2. **Intelligent Structure Preservation**:
                    - Maintains single linebreaks between logical blocks
                    - Merges orphan lines while keeping meaningful separations
                    - Converts complex elements like tables to plain text
                    ]]></input>
                    <output><![CDATA[Overview: [Strict Formatting Removal; [Handles markdown headers, bold/italics, code blocks, Converts lists to comma-separated text, Removes special characters while preserving content], Intelligent Structure Preservation; [Maintains single linebreaks between logical blocks, Merges orphan lines while keeping meaningful separations, Converts complex elements like tables to plain text]].]]></output>

                     <input><![CDATA[
                    ### Overview:
                    1. **Strict Formatting Removal**:
                    - Handles markdown headers, bold/italics, code blocks
                    - Converts lists to comma-separated text
                    - Removes special characters while preserving content

                    2. **Intelligent Structure Preservation**:
                    - Maintains single linebreaks between logical blocks
                    - Merges orphan lines while keeping meaningful separations
                    - Converts complex elements like tables to plain text

                    3. **Readability Focus**:
                    - Ensures consistent spacing
                    - Preserves URL integrity
                    - Maintains text hierarchy through spacing

                    4. **Process Guidelines**:
                    - Clear step-by-step normalization process
                    - Handles edge cases (multiple linebreaks, short lines)
                    - Balances formatting removal with content preservation
                    ]]></input>
                    <output><![CDATA[
                    Overview: [Strict Formatting Removal; [Handles markdown headers, bold/italics, code blocks, Converts lists to comma-separated text, Removes special characters while preserving content], Intelligent Structure Preservation; [Maintains single linebreaks between logical blocks, Merges orphan lines while keeping meaningful separations, Converts complex elements like tables to plain text],
                    Readability Focus; [Ensures consistent spacing, Preserves URL integrity, Maintains text hierarchy through spacing], Process Guidelines; [Clear step-by-step normalization process, Handles edge cases (multiple linebreaks, short lines), Balances formatting removal with content preservation]].
                    ]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```




your goal is to craft a new agent called `StripFormatting.xml` that comforms to the provided xml-template-structure but concistently transform any formatted text into clean plain text while preserving semantic structure and indentation. This includes a particular ability to remove markdown formatting while maintaining essential paragraph separation through single linebreaks.

