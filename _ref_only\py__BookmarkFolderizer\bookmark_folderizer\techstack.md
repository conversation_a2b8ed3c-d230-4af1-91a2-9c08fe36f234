# Technology Stack

## Core Framework
- **Python 3.9+** - Primary language
- **Single-file architecture** - Consolidated main.py (1090 lines)

## Key Dependencies (Optimized)
**Core Dependencies (4 packages):**
- **beautifulsoup4** - HTML parsing and manipulation
- **loguru** - Enhanced logging with YAML output
- **rich** - Terminal UI/formatting and interactive prompts
- **win32-setctime** - Windows file timestamp manipulation

**Automatically Included (transitive):**
- colorama, markdown-it-py, mdurl, pygments, soupsieve, typing-extensions

## Development Tools
- **uv** - Modern Python package manager (replaces pip)
- **virtual environment** - Isolated dependencies (.venv)
- **batch scripts** - Windows automation

## Project Structure
- Monolithic single-file design (`main.py`)
- YAML-based logging via Loguru
- Interactive CLI with Rich prompts
- Batch processing support
- Windows-optimized file operations
- UV-based dependency management

## Migration from pip to UV
- ✅ Faster dependency resolution
- ✅ Better conflict resolution
- ✅ Reproducible builds with uv.lock
- ✅ Modern Python packaging standards
- ✅ Simplified project setup

## Current State
- Uses uv + pyproject.toml for dependency management
- Virtual environment setup via uv_init.bat
- Main execution via run.bat with UV
- Updated Sublime Text project configuration
- Unit tests in test_bookmarks.py
