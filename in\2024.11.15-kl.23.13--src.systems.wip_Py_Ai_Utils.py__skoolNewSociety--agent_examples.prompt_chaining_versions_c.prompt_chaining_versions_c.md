# Project Files Documentation for `prompt_chaining_versions_c`

### File Structure

```
├── v1.py
├── v2.py
```
### 1. `v1.py`

#### `v1.py`

```python
import os
import re
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union
from datetime import datetime

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - [%(levelname)s] - %(message)s')

# Initialize markdown output list to build structured output
markdown_output = []

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

meta_objectives = {
    "Maximize Clarity": "Focus on conciseness and clear explanation.",
    "Innovate and Inspire": "Encourage creative and novel ideas.",
    "Optimize for Speed": "Aim for brevity and efficiency."
}

def get_meta_strategy(strategy: str) -> List[str]:
    meta_strategies = {
        "Rapid Prototyping": ["Prompt Generation", "Quality Assurance"],
        "Detailed Research": ["Content Creation", "Quality Assurance", "User Experience", "Final Optimization"],
        "Creative Ideation": ["Prompt Reformulation", "Content Development", "User Experience"],
        "Final Prompt Formatting": ["Content Development", "Quality Assurance", "Final Optimization", "Prompt Conversion"]
    }
    return meta_strategies.get(strategy, ["Prompt Generation"])


def apply_meta_objective(objective: str, agent_prompt: str) -> str:
    modification = meta_objectives.get(objective, "")
    return f"{agent_prompt}\n\n{modification}"

def detect_mode(initial_input: str) -> str:
    input_lower = initial_input.lower()
    if any(keyword in input_lower for keyword in ["prompt", "generate", "compose"]):
        return "Prompt Creation"
    elif any(keyword in input_lower for keyword in ["write", "explain", "detail"]):
        return "Content Creation"
    elif any(keyword in input_lower for keyword in ["guide", "instruction", "advise"]):
        return "User Guidance"
    else:
        return "General"

def detect_context(input_text: str) -> List[str]:
    """
    Determines which agent groups to include based on keywords or context
    detected in the input text.
    """
    input_lower = input_text.lower()
    detected_groups = ["Blueprint Creation", "Content Development", "Quality Assurance", "User Experience and Readability"]

    # Detect scenarios where "Prompt Conversion" or other specialized groups might be relevant
    if re.search(r"\b(prompt|generate|compose|instruction)\b", input_lower):
        detected_groups.append("Prompt Conversion")

    # If content needs a broader instructional focus
    if re.search(r"\b(guide|explain|instruction|tutorial)\b", input_lower):
        detected_groups.append("Instructional Structuring")

    return detected_groups


def get_refinement_chain(input_text: str, depth: str = "moderate") -> Dict[str, List[Dict[str, str]]]:
    """
    Returns a dynamically determined refinement chain based on input characteristics.
    """
    full_chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint outlining the original topic, objective, structure, key points, and formatting guidelines."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
            {"role": "Objective Clarifier", "prompt": "Clarify the primary objective based on the Blueprint, maintaining topic consistency."},
            {"role": "Topic Alignment", "prompt": "Align content with the original topic and Blueprint, ensuring no deviation."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": "Add relevant background for enriched response accuracy, staying strictly on topic."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint, to maintain alignment with the original topic."},
            {"role": "Detailed Expander", "prompt": "Expand on the effectiveness of prompt chains by breaking down key benefits."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability, ensuring content stays on the original topic."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback, without deviating from the topic."},
            {"role": "Clarity Reformer", "prompt": "Restructure sentences for maximum clarity and flow."},
            {"role": "Example Generator", "prompt": "Provide an example of prompt chaining to illustrate the benefits."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility, while maintaining topic consistency."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability, ensuring alignment with the original topic."},
            {"role": "Synonym Enhancer", "prompt": "Vary terminology to maintain reader engagement without redundancy."}
        ],
        "Prompt Conversion": [
            {"role": "Concise Reformer", "prompt": "Reformat the response into a prompt by making it concise and action-oriented."},
            {"role": "Clarity Enhancer", "prompt": "Ensure the prompt is clear, avoiding any ambiguity."},
            {"role": "Adaptability Checker", "prompt": "Adjust the prompt for adaptability to various contexts, ensuring it is versatile and reusable."},
            {"role": "Instructional Formatter", "prompt": "Reframe the response as a direct instruction, guiding the user to achieve the intended outcome."}
        ],
        "Instructional Structuring": [
            {"role": "Instructional Guide Formatter", "prompt": "Structure content to provide step-by-step instructional guidance."},
            {"role": "Question Generator", "prompt": "Generate questions to guide understanding or user reflection on the topic."},
            {"role": "Summary Builder", "prompt": "Create a concise summary of key points to reinforce learning outcomes."}
        ]
    }

    # Determine dynamic inclusion based on input context
    selected_groups = detect_context(input_text)
    chain = {k: full_chain[k] for k in selected_groups if k in full_chain}

    # Adjust for depth if needed, excluding certain groups based on shallow or moderate settings
    if depth == "shallow":
        chain = {k: v for k, v in chain.items() if k in ["Blueprint Creation", "Prompt Reformulation", "Content Development"]}
    elif depth == "moderate":
        chain = {k: v for k, v in chain.items() if k not in ["Instructional Structuring", "Prompt Conversion"] or k in selected_groups}

    return chain


def create_guiding_blueprint(initial_input: str, mode: str) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users.",
        "General": "balanced, high-quality response for general use."
    }
    mode_description = mode_descriptions.get(mode, "balanced response")
    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Improve clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n")

def get_refinement_chain(input_text: str, depth: str = "moderate") -> Dict[str, List[Dict[str, str]]]:
    full_chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint outlining the original topic, objective, structure, key points, and formatting guidelines."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
            {"role": "Objective Clarifier", "prompt": "Clarify the main objective per the Blueprint, ensuring topic consistency."},
            {"role": "Topic Alignment", "prompt": "Verify and adjust content alignment with the original topic and Blueprint, ensuring coherence and relevance throughout."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": "Add relevant background for enriched response accuracy, staying strictly on topic."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint, to maintain alignment with the original topic."},
            {"role": "Detailed Expander", "prompt": "Expand on the effectiveness of prompt chains by breaking down key benefits."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability, ensuring content stays on the original topic."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback, without deviating from the topic."},
            {"role": "Clarity Reformer", "prompt": "Restructure sentences for maximum clarity and flow."},
            {"role": "Example Generator", "prompt": "Provide an example of prompt chaining to illustrate the benefits."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility, while maintaining topic consistency."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability, ensuring alignment with the original topic."},
            {"role": "Synonym Enhancer", "prompt": "Vary terminology to maintain reader engagement without redundancy."}
        ],
        "Prompt Conversion": [
            {"role": "Concise Reformer", "prompt": "Reformat the response into a prompt by making it concise and action-oriented."},
            {"role": "Clarity Enhancer", "prompt": "Ensure the prompt is clear, avoiding any ambiguity."},
            {"role": "Adaptability Checker", "prompt": "Adjust the prompt for adaptability to various contexts, ensuring it is versatile and reusable."},
            {"role": "Instructional Formatter", "prompt": "Reframe the response as a direct instruction, guiding the user to achieve the intended outcome."}
        ],
        "Final Verifier": [
            # {"role": "Clarity Checker", "prompt": "Confirm that the final response is clear, concise, and consistent."},
            {"role": "Final Quality Assessor", "prompt": "Perform a final quality check, ensuring the response fully aligns with the original objective and adheres to the Blueprint."},
            {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
            {"role": "Consistency Auditor", "prompt": "Check for consistent tone, structure, and focus throughout the response."},
            {"role": "Adaptability Checker", "prompt": "Adjust the prompt for adaptability to various contexts, ensuring it is versatile and reusable."},
            {"role": "Instructional Formatter", "prompt": "Reframe the response as a direct instruction, guiding the user to achieve the intended outcome."},
            {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
            {"role": "Sentencer", "prompt": "Reframe the data as query and rephrase it as a sentence or paragraph."},
            {"role": "Objective Adherence Validator", "prompt": "Verify that the response fully aligns with the original objective and adheres to the Blueprint."},
        ]
    }

    # Determine dynamic inclusion based on input context
    selected_groups = detect_context(input_text)
    chain = {k: full_chain[k] for k in selected_groups if k in full_chain}

    # Always include Final Verifier as the last step for quality assurance
    chain["Final Verifier"] = full_chain["Final Verifier"]

    # Adjust for depth if needed
    if depth == "shallow":
        chain = {k: v for k, v in chain.items() if k in ["Blueprint Creation", "Prompt Reformulation", "Content Development"]}
    elif depth == "moderate":
        chain = {k: v for k, v in chain.items() if k not in ["Instructional Structuring", "Prompt Conversion"] or k in selected_groups}

    return chain




def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=1.0,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

def apply_qualifier(output_text: str, blueprint: str) -> str:
    """
    Qualifier function that evaluates the output of an agent for clarity and logical consistency.
    Provides a score and suggests revisions if necessary.
    """
    qualifier_prompt = (
        f"Evaluate the following response for clarity and logical consistency based on the provided blueprint. "
        f"Assign a score between 0% and 100%, where 100% reflects maximum clarity and coherence. "
        f"Then, suggest an improved version of the response, ensuring it is brief, precise, and free of unnecessary words.\n\n"
        f"Blueprint:\n{blueprint}\n\n"
        f"Response:\n{output_text}\n\n"
        f"Evaluation and Suggested Revision:"
    )

    return get_completion(qualifier_prompt)


def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, original_topic: str, objective: str) -> str:
    """
    Apply an agent's prompt to refine the current response, and evaluate its quality with a qualifier.
    """
    prompt = apply_meta_objective(objective, agent['prompt'])
    prompt += f"\n\nGuiding Blueprint:\n{blueprint}\n\nOriginal Topic:\n{original_topic}\n\nCurrent Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)

    # Qualify the agent's output for clarity and logical consistency
    if refined_response:
        qualifier_feedback = apply_qualifier(refined_response, blueprint)
        # Extract suggested revision if the score is below a quality threshold
        if "Score:" in qualifier_feedback and int(qualifier_feedback.split("Score:")[1].split("%")[0].strip()) < 90:
            refined_response = qualifier_feedback.split("Suggested Revision:")[-1].strip()

        # Log the response with qualifier feedback
        markdown_output.append(f"### {agent['role']}\n"
                               f"**Prompt:** {agent['prompt']}\n"
                               f"- **Before:** {current_response[:600]}...\n"
                               f"- **After:** {refined_response[:600]}...\n"
                               f"- **Qualifier Feedback:** {qualifier_feedback[:600]}...\n")
        return refined_response
    else:
        markdown_output.append(f"### {agent['role']}\n- **Note:** Agent could not refine the response; keeping previous.")
        return current_response


def apply_scenario_flow(scenario: str, blueprint: str, current_response: str, objective: str) -> str:
    refinement_chain = get_refinement_chain(detect_mode(current_response))
    agents = refinement_chain.get(scenario, [])
    markdown_output.append(f"## {scenario} Phase\n- **Initial Response:** {current_response[:600]}...\n")

    for i, agent in enumerate(agents):
        current_response = apply_agent(agent, blueprint, current_response, original_topic=current_response, objective=objective)

    markdown_output.append(f"- **Updated Response:** {current_response[:600]}...\n")
    return current_response

def apply_meta_strategy(strategy: str, blueprint: str, initial_input: str, objective: str) -> str:
    selected_scenarios = get_meta_strategy(strategy)
    current_response = initial_input

    markdown_output.append(f"# Process Overview\n**Objective:** {objective}\n**Strategy:** {strategy}\n"
                           f"**Initial Input:** {initial_input}\n")

    start_time = datetime.now()
    for scenario in selected_scenarios:
        current_response = apply_scenario_flow(scenario, blueprint, current_response, objective)

    duration = datetime.now() - start_time
    markdown_output.append(f"**Final Output:** {current_response}\n**Duration:** {duration.total_seconds():.1f}s\n")
    return current_response

def main():
    # initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    # initial_input = "reformat into '{title} s{season} e{episode}': Tulsa.King.S02E09.1080p.WEB.h264-ETHEL"
    # initial_input = "poem about padel"
    # initial_input = "improve prompt Autonomous AI Prompt Chains: How can it be improved to further ensure consistency and that it stays on the original topic along the chain?"
    # initial_input = "prompt: given that it seems there is a final version for the subfolder 'py_agentTitleExtractor', namely py_agentTitleExtractor.py (code below), what do you think the next folder would be?"
    initial_input = "prompt: a straight-forward guidesheet directed at patients with hiatal hernia to help distinctiate the different symptoms, aimed at learning to understand the symptoms"
    mode = detect_mode(initial_input)
    print(mode)
    objective = "Maximize Clarity"
    blueprint = create_guiding_blueprint(initial_input, mode)
    strategy = "Detailed Research"

    logging.info(f"Starting process with Objective: '{objective}' and Strategy: '{strategy}'")
    final_output = apply_meta_strategy(strategy, blueprint, initial_input, objective)

    print("\n".join(markdown_output))

if __name__ == "__main__":
    main()

```
### 2. `v2.py`

#### `v2.py`

```python
import os
import logging
from typing import List, Dict, Optional, Union
import concurrent.futures
from openai import OpenAI
import numpy as np

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Initialize OpenAI Client
def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set.")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# --- Centralized Memory Manager ---
class MemoryManager:
    def __init__(self):
        self.memory_store = []

    def add_memory(self, text: str, embedding: np.ndarray):
        self.memory_store.append({"text": text, "embedding": embedding})

    def retrieve_relevant_context(self, query: str, top_k: int = 3) -> List[str]:
        query_embedding = self.get_embedding(query)
        similarities = [
            (self.cosine_similarity(query_embedding, mem["embedding"]), mem["text"])
            for mem in self.memory_store
        ]
        similarities.sort(reverse=True, key=lambda x: x[0])
        return [text for _, text in similarities[:top_k]]

    def get_embedding(self, text: str) -> np.ndarray:
        try:
            response = client.embeddings.create(input=text, model="text-embedding-ada-002")
            return np.array(response.data[0].embedding)
        except Exception as e:
            logging.error(f"Failed to generate embedding: {e}")
            return np.zeros(1536)  # Default to zero-vector if embedding fails

    @staticmethod
    def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

# --- Blueprint Manager ---
class BlueprintManager:
    def create_blueprint(self, initial_input: str, mode: str) -> str:
        mode_descriptions = {
            "Prompt Generation": "focused on generating concise, impactful prompts.",
            "Content Creation": "focused on detailed, structured content development.",
            "User Guidance": "focused on providing clear, actionable guidance for end-users.",
            "General": "balanced, high-quality response for general use."
        }
        mode_description = mode_descriptions.get(mode, "balanced response")
        return (f"Guiding Blueprint:\n"
                f"Mode: {mode} - {mode_description}\n"
                f"Topic: {initial_input}\n"
                f"Objective: Improve clarity, coherence, relevance, and usability.\n"
                f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n")

# --- Quality Evaluator ---
class QualityEvaluator:
    def assess_quality(self, response: str) -> bool:
        # Simplistic quality indicators for demonstration purposes
        quality_indicators = ["clear", "relevant", "concise", "logical"]
        return sum(indicator in response.lower() for indicator in quality_indicators) >= 2

# --- Agent Processor ---
class AgentProcessor:
    def __init__(self, memory_manager: MemoryManager, blueprint: str, meta_objective: str):
        self.memory_manager = memory_manager
        self.blueprint = blueprint
        self.meta_objective = meta_objective

    def generate_prompt(self, agent_prompt: str, current_response: str) -> str:
        context = "\n".join(self.memory_manager.retrieve_relevant_context(current_response))
        return (f"{agent_prompt}\n\nGuiding Blueprint:\n{self.blueprint}\n\n"
                f"Context:\n{context}\n\nCurrent Response:\n{current_response}\n\nRefined Response:")

    def apply_agent(self, agent: Dict[str, str], current_response: str) -> str:
        prompt = self.generate_prompt(agent["prompt"], current_response)
        try:
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Agent '{agent['role']}' failed: {e}")
            return current_response

    def parallel_apply_agents(self, agents: List[Dict[str, str]], current_response: str) -> str:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_to_agent = {
                executor.submit(self.apply_agent, agent, current_response): agent
                for agent in agents
            }
            results = []
            for future in concurrent.futures.as_completed(future_to_agent):
                results.append(future.result())
            return "\n".join(results)

# --- Unified Refinement Pipeline ---
class RefinementPipeline:
    def __init__(self, memory_manager: MemoryManager, blueprint_manager: BlueprintManager):
        self.memory_manager = memory_manager
        self.blueprint_manager = blueprint_manager
        self.evaluator = QualityEvaluator()

    def execute(self, initial_input: str, mode: str, meta_objective: str, depth: str) -> str:
        # Step 1: Create Blueprint
        blueprint = self.blueprint_manager.create_blueprint(initial_input, mode)

        # Step 2: Initialize Agent Processor
        agent_processor = AgentProcessor(self.memory_manager, blueprint, meta_objective)

        # Step 3: Define Refinement Chain
        refinement_chain = self.get_refinement_chain(mode, depth)

        # Step 4: Execute Refinement Chain
        current_response = initial_input
        for category, agents in refinement_chain.items():
            logging.info(f"Executing category: {category}")
            if len(agents) > 1:
                current_response = agent_processor.parallel_apply_agents(agents, current_response)
            else:
                for agent in agents:
                    current_response = agent_processor.apply_agent(agent, current_response)

            # Early Exit if Quality is Met
            if self.evaluator.assess_quality(current_response):
                logging.info("Quality threshold met, exiting early.")
                break

        return current_response

    @staticmethod
    def get_refinement_chain(mode: str, depth: str) -> Dict[str, List[Dict[str, str]]]:
        base_chain = {
            "Blueprint Creation": [
                {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint outlining the original topic, objective, structure, key points, and formatting guidelines."}
            ],
            "Prompt Reformulation": [
                {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
                {"role": "Objective Clarifier", "prompt": "Clarify the main objective per the Blueprint, ensuring topic consistency."},
                {"role": "Topic Alignment", "prompt": "Verify and adjust content alignment with the original topic and Blueprint, ensuring coherence and relevance throughout."}
            ],
            "Content Development": [
                {"role": "Context Enhancer", "prompt": "Add relevant background for enriched response accuracy, staying strictly on topic."},
                {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint, to maintain alignment with the original topic."},
                {"role": "Detailed Expander", "prompt": "Expand on the effectiveness of prompt chains by breaking down key benefits."}
            ],
            "Quality Assurance": [
                {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability, ensuring content stays on the original topic."},
                {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback, without deviating from the topic."},
                {"role": "Clarity Reformer", "prompt": "Restructure sentences for maximum clarity and flow."},
                {"role": "Example Generator", "prompt": "Provide an example of prompt chaining to illustrate the benefits."}
            ],
            "User Experience and Readability": [
                {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility, while maintaining topic consistency."},
                {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability, ensuring alignment with the original topic."},
                {"role": "Synonym Enhancer", "prompt": "Vary terminology to maintain reader engagement without redundancy."}
            ],
            "Prompt Conversion": [
                {"role": "Concise Reformer", "prompt": "Reformat the response into a prompt by making it concise and action-oriented."},
                {"role": "Clarity Enhancer", "prompt": "Ensure the prompt is clear, avoiding any ambiguity."},
                {"role": "Adaptability Checker", "prompt": "Adjust the prompt for adaptability to various contexts, ensuring it is versatile and reusable."},
                {"role": "Instructional Formatter", "prompt": "Reframe the response as a direct instruction, guiding the user to achieve the intended outcome."}
            ],
            "Final Verifier": [
                # {"role": "Clarity Checker", "prompt": "Confirm that the final response is clear, concise, and consistent."},
                {"role": "Final Quality Assessor", "prompt": "Perform a final quality check, ensuring the response fully aligns with the original objective and adheres to the Blueprint."},
                {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
                {"role": "Consistency Auditor", "prompt": "Check for consistent tone, structure, and focus throughout the response."},
                {"role": "Adaptability Checker", "prompt": "Adjust the prompt for adaptability to various contexts, ensuring it is versatile and reusable."},
                {"role": "Instructional Formatter", "prompt": "Reframe the response as a direct instruction, guiding the user to achieve the intended outcome."},
                {"role": "Inquiry Formulator", "prompt": "Rephrase the data as a comprehensive question, ensuring it aligns with the detected focus."},
                {"role": "Sentencer", "prompt": "Reframe the data as query and rephrase it as a sentence or paragraph."},
                {"role": "Objective Adherence Validator", "prompt": "Verify that the response fully aligns with the original objective and adheres to the Blueprint."},
            ]
        }
        if depth == "deep":
            base_chain["Final Optimization"] = [
                {"role": "Performance Optimizer", "prompt": "Optimize the final output for readability and usability."}
            ]
        return base_chain

# --- Main Function ---
def main():
    # initial_input = "Create a highly optimized prompt demonstrating the best use of refinement."
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    mode = "Prompt Generation"
    meta_objective = "Maximize Clarity"
    depth = "moderate"

    memory_manager = MemoryManager()
    blueprint_manager = BlueprintManager()
    pipeline = RefinementPipeline(memory_manager, blueprint_manager)

    final_output = pipeline.execute(initial_input, mode, meta_objective, depth)

    print("\nInitial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
