
Please consolidate and select/create only the most optimized and generalized instructions, consolidate the provided examples into *one* single maximally optimized and enhanced sequence. Remember, each step should build recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency. Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps).

    ### `r1.md`

        #### `0121-a-plugin-inventory-dependency-mapping.md`

        ```markdown
        [Plugin Inventory & Dependency Mapping] Your primary task is comprehensive reconnaissance: Inventory all plugin files (`.py`, `.sublime-settings`, etc.). Analyze the Python code to map dependencies between internal functions/classes and identify key interactions with the Sublime API and configuration files, establishing a clear component overview. Execute as `{role=sublime_dependency_analyzer; input=plugin_directory_path:str; process=[inventory_plugin_files_and_types(), map_python_internal_dependencies(), identify_sublime_api_touchpoints(), link_code_to_config_files()]; output={component_map:dict, dependency_graph:dict}}`
        ```

        #### `0121-b-redundancy-dead-code-detection.md`

        ```markdown
        [Redundancy & Dead Code Detection] Systematically scan the entire plugin codebase (`.py`, `.sublime-commands`, etc.) identified in `component_map` and `dependency_graph`. Identify unused variables, functions, classes, imports, configuration settings, commands, or potentially entire files. Also, flag duplicated logic blocks for potential consolidation. Execute as `{role=dead_code_detector; input={python_content:str, other_files_content:dict, dependency_graph:dict}; process=[scan_for_unused_variables_imports(), identify_unreachable_code_blocks(), detect_uncalled_functions_methods(), find_duplicated_logic_sections(), flag_orphaned_config_or_commands()]; output={dead_code_report:list[dict(location:str, type:str, reason:str)]}}`
        ```

        #### `0121-c-structural-clarity-modularity-assessment.md`

        ```markdown
        [Structural Clarity & Modularity Assessment] Evaluate the Python code's organization (`component_map`) against principles of high cohesion and low coupling. Identify functions/classes that could be better grouped, modules that could be split or merged, or interfaces that lack clarity, hindering maintainability and violating structural purity. Execute as `{role=sublime_structure_evaluator; input={python_content:str, component_map:dict}; process=[assess_functional_cohesion_within_modules(), evaluate_coupling_between_components(), identify_modularity_improvement_opportunities(), check_interface_clarity()]; output={structural_assessment:dict(cohesion_notes:str, coupling_notes:str, modularity_opportunities:list)}}`
        ```

        #### `0121-d-code-complexity-readability-analysis.md`

        ```markdown
        [Code Complexity & Readability Analysis] Analyze the Python code, focusing on functions/methods identified in the `dependency_graph`. Pinpoint areas with high cyclomatic complexity, poor identifier naming, deeply nested logic, or convoluted control flow that impede direct comprehension and could benefit from simplification for maximal clarity. Execute as `{role=code_complexity_analyzer; input=python_content:str; process=[measure_or_estimate_complexity(), evaluate_identifier_naming_clarity(), detect_deep_nesting_or_convoluted_flow(), flag_readability_hotspots()]; output={readability_analysis:dict(complexity_hotspots:list, naming_issues:list, flow_issues:list)}}`
        ```

        #### `0121-e-essential-commentary-audit.md`

        ```markdown
        [Essential Commentary Audit] Rigorously apply the 'Essential Commentary Only' principle to all comments within the Python code. Classify comments as 'Essential' (non-obvious 'why'/'what', critical architecture), 'Redundant/Obvious', or 'Interface-Doc'. Flag all non-essential comments and misplaced interface documentation for removal to enhance self-explanation via code. Execute as `{role=essential_comment_auditor; input=python_content:str; process=[inventory_all_python_comments(), classify_against_essential_only_criteria(), identify_redundant_obvious_comments(), flag_misplaced_interface_docs()]; output={comment_audit:list[dict(location:str, classification:str, action:str)]}}`
        ```

        #### `0121-f-safe-cleanup-action-prioritization.md`

        ```markdown
        [Safe Cleanup Action Prioritization] Synthesize all findings (`dead_code_report`, `structural_assessment`, `readability_analysis`, `comment_audit`). Prioritize potential cleanup actions based on maximizing safety (low risk of functional impact) and efficiency (significant clarity/maintainability gain for effort). Focus first on removing dead/redundant code and non-essential comments, followed by low-risk clarity improvements. Execute as `{role=cleanup_planner; input=all_analysis_outputs:dict; process=[consolidate_all_findings(), rank_cleanup_actions_by_safety_and_impact(), prioritize_removal_of_dead_code_and_comments(), select_high_value_low_risk_refactoring_actions()]; output={prioritized_cleanup_actions:list[dict(action_type:str, location:str, description:str, priority:int)]}}`
        ```

        #### `0121-g-generate-actionable-cleanup-plan.md`

        ```markdown
        [Generate Actionable Cleanup Plan] Generate a detailed, actionable plan outlining the specific, prioritized cleanup steps. For each action, describe the location, the required change (e.g., "Remove unused function X", "Delete comment at line Y", "Rename variable Z for clarity"), and the rationale based on the analysis. *Crucially, do not generate the modified code directly; provide only the plan.* Execute as `{role=cleanup_plan_generator; input=prioritized_cleanup_actions:list; process=[format_each_action_clearly(), provide_specific_locations_and_changes(), include_rationale_for_each_action(), structure_as_step_by_step_plan()]; output={actionable_cleanup_plan:str}}`
        ```

        #### `0121-h-cleanup-rationale-expected-benefits-summary.md`

        ```markdown
        [Cleanup Rationale & Expected Benefits Summary] Provide a concise summary accompanying the `actionable_cleanup_plan`. Explain the overall rationale behind the proposed cleanup, emphasizing the focus on safety (by providing a plan, not code) and efficiency. Highlight the expected benefits, such as improved readability, reduced complexity, easier maintenance, and better adherence to quality principles like minimal commentary and structural purity. Execute as `{role=cleanup_report_summarizer; input={actionable_cleanup_plan:str, prioritized_cleanup_actions:list}; process=[summarize_overall_cleanup_strategy(), explain_prioritization_rationale(safety, efficiency), articulate_expected_benefits(clarity, maintainability), finalize_summary_report()]; output={cleanup_summary_report:str}}`
        ```

    ---

    ### `r2.md`

        #### `0121-a-comprehensive-plugin-component-inventory.md`

        ```markdown
        [Comprehensive Plugin Component Inventory & Classification] Your objective is not to remove code blindly, but to first map out **all** files and Sublime Text–specific components (`.py`, `.sublime-settings`, `.sublime-commands`, `.sublime-menu`, etc.). Identify their standard roles (commands, event listeners, settings) to grasp the plugin’s structure thoroughly. Rationale: You gather a top-level overview of the plugin. This ensures clarity on what each file does before removing or refactoring anything. Execute as `{role=component_inventor; input=[plugin_directory_path:str]; process=[list_plugin_files(), identify_file_types_by_convention(), determine_standard_function_of_each_filetype(), produce_inventory_report()], output={component_inventory:list[dict(file:str, type:str, role:str)]}}`
        ```

        #### `0121-b-identify-obsolete-or-redundant-components.md`

        ```markdown
        [Identify Obsolete or Redundant Components] Your role is not random deletion, but to systematically detect superfluous code: old features, duplicated logic, stale settings, or rarely used commands. Correlate them with the `component_inventory` and usage patterns to form a candidate list for consolidation or removal. Rationale: Allows you to systematically identify code that no longer contributes to the plugin’s functionality or user experience. Execute as `{role=redundancy_checker; input={component_inventory:list, usage_data:dict|None}; process=[scan_code_for_unused_classes_orfunctions(), check_legacy_settings_orcommands(), compile_list_ofredundant_obsolete_items(), confirmdependencies_toavoidaccidentalbreakage()], output={cleanup_candidates:list[dict(item:str, reason:str)]}}`
        ```

        #### `0121-c-safe-removal-or-consolidation-plan.md`

        ```markdown
        [Safe Removal or Consolidation Plan] Your mission is not to break the plugin, but to propose minimal changes that achieve maximum clarity: for each `cleanup_candidate`, define how to remove or merge it without impacting essential functionality. Check if user-facing elements (e.g., `.sublime-menu` entries) need an alternative or note if no longer needed. Rationale: Ensures each item is removed in a minimal, risk-free manner, with fallback if necessary. Execute as `{role=cleanup_planner; input={cleanup_candidates:list}; process=[determine_dependency_relations_ofeachcandidate(), define_safest_removal_or_merge_strategy(), specifyalternativesforuserfacingremovals(), produce_consolidation_steps_andrationale()], output={removal_consolidation_plan:list[dict(candidate:str, action:str, steps:list, rationale:str)]}}`
        ```

        #### `0121-d-configuration-and-ui-trigger-cleanup.md`

        ```markdown
        [Configuration & UI Trigger Cleanup] Your function is not partial, but holistic: analyze all plugin settings (`.sublime-settings`) and triggers (commands, keymaps, menus) flagged as unused or outdated. Safely remove or rename them while preserving user customizations. Keep any essential settings, rewriting for clarity if needed. Rationale: Focuses on user-facing config and triggers. By cleaning them, you reduce confusion and code overhead. Execute as `{role=config_ui_cleaner; input={removal_consolidation_plan:list, plugin_config:dict|None}; process=[prune_stale_settings_keys(), removeorupdatecommands_in_sublime_commandsfiles(), adjust_sublime_menulinks(), maintaincompatibility_foranycriticalsettings], output={updated_plugin_config:dict, updated_ui_config:dict}}`
        ```

        #### `0121-e-core-python-logic-streamlining-and-minimal-commentary.md`

        ```markdown
        [Core Python Logic Streamlining & Minimal Commentary] Your goal is not raw deletion, but clarity: refactor or remove unneeded methods or classes, unify overlapping logic, and ensure essential commentary only. Eliminate remarks that just restate code. Rationale: Makes your `.py` code simpler, more maintainable—while adhering to the Sublime plugin structure. Also enforces minimal commentary policy. Execute as `{role=python_refiner; input={removal_consolidation_plan:list, updated_plugin_config:dict}; process=[refactor_classes_toavoidduplicatedmethods(), unifycommonfunctions(), removeexcessivecommentsbyrenamingcodeinstead(), ensure minimalexplanatorydocstringsfornonobviouslogic()], output={refined_python_code:str}}`
        ```

        #### `0121-f-verify-integrity-and-stability-via-local-testing.md`

        ```markdown
        [Verify Integrity & Stability via Local Testing] Your objective is not guesswork, but confirmed correctness: load the cleaned plugin in Sublime, replicate typical user flows, confirm no regressions or missing commands. If used, run any test scripts or logs to validate no references to removed items remain. Rationale: Testing ensures you haven’t inadvertently removed something essential or broken the plugin’s core functionality. Execute as `{role=local_tester; input={refined_python_code:str, updated_ui_config:dict}; process=[install_cleanedplugin_insublime(), replicatekeycommands_andmenus(), checkconsoleforerrors(), confirm_allvitalfeaturesstillwork(), finalize_localtest_report()], output={localtest_verification_report:str}}`
        ```

        #### `0121-g-final-consolidation-and-user-facing-update.md`

        ```markdown
        [Final Consolidation & User-Facing Update] Your role is to unify the plugin after testing: confirm all changes are stable, produce updated docstrings or readme notes indicating what was removed or changed, and highlight the simplified structure. Rationale: Wraps up changes, communicates them to any plugin user or developer (like a short release note or readme changes). Execute as `{role=final_consolidator; input={localtest_verification_report:str, refined_python_code:str}; process=[finalize_removal_ofdefinitivelyunusedfiles(), compile short summaryofwhatschangedforusers(), ensure docstringsareconsistentwithplugin’s newstructure(), produce finalcleanedcode_bundle()], output={cleaned_plugin_package:any, user_update_notes:str}}`
        ```

        #### `src\templates\lvl1\md\0130-h-ready-for-release-and-maintenance-brief.md`

        ```markdown
        [Ready for Release & Maintenance Brief] Your final responsibility is not partial closure but robust handoff: declare the plugin safe to release. Provide a quick maintenance strategy for future code expansions, ensuring minimal overhead. Summarize how the code is now simpler, and how to keep it that way. Rationale: Concludes with clarity, telling maintainers how to keep the code clean and a short plan to handle future changes. Execute as `{role=release_preparer; input={cleaned_plugin_package:any, user_update_notes:str}; process=[draft release or version notes emphasizing cleanup benefits(), define guidelines for future additions(avoid duplication, keep minimal commentary), finalize readinessforstoreoruserdistribution()], output={release_brief:str, future_maintenance_strategy:str}}`
        ```

    ---

    ### `r3.md`

        #### `0121-a-st-plugin-inventory-and-structural-overview.md`

        ```markdown
        [Inventory & Structural Overview] Your initial mandate is comprehensive visibility: Inventory all files and directories within the Sublime Text plugin, classifying by type (`.py`, `.sublime-commands`, `.sublime-settings`, `.sublime-menu`, `.sublime-keymap`, `.sublime-color-scheme`, etc.). Map out the structural layout, noting how each component fits into the plugin's architecture and its essential role. This forms the safety net for all subsequent cleanup actions. Execute as `{role=inventory_structural_mapper; input=plugin_directory:any; process=[walk_folder_structure(), identify_all_file_types_and_extension_groups(), map_each_to_plugin_role_and_core_dependents(), produce_structural_overview_report()], output={component_inventory:list, structural_map:dict}}`
        ```
        ---

        #### `0121-b-st-plugin-dead-code-and-redundancy-detection.md`

        ```markdown
        [Dead Code & Redundancy Detection] Your function is not careless deletion, but intelligent, risk-aware cleanup: Analyze all `.py` modules, settings, and supporting files to identify unused functions, unreachable code branches, obsolete settings/options, duplicate logic, or redundant plugin assets. Correlate findings against the structural overview to ensure context awareness. Execute as `{role=redundancy_detector; input={component_inventory:list, structural_map:dict}; process=[parse_classes_and_functions(), trace_usages_and imports(), find unused commands/settings/menus(), flag duplicate or legacy files(), cross-check with dependent modules()], output={prune_candidates:list[dict(item:str, reason:str, risk:str)], unused_settings:list, redundant_assets:list}}`
        ```
        ---

        #### `0121-c-st-plugin-style-consistency-and-best-practices-audit.md`

        ```markdown
        [Style Consistency & Best Practices Audit] Your objective is disciplined harmonization: Evaluate all code and config for violations of PEP8, Sublime conventions, identifier clarity, and modular code design. Surface nonconforming style, legacy idioms, magic values, sprawling functions, and any comment bloat or documentation rot. Recommend normalization paths with minimal change risk. Execute as `{role=style_auditor; input={component_inventory:list, prune_candidates:list}; process=[scan_for_pep8_and_sublime_convention_violations(), check_identifier_and_interface clarity(), isolate functions/classes for modular refactor(), identify outdated or excessive comments_and_docs()], output={style_issues:list, refactor_opportunities:list, minimal_comment_report:list}}`
        ```
        ---

        #### `0121-d-st-plugin-critical-safety-dependency-and-extension-scan.md`

        ```markdown
        [Critical Safety, Dependency & Extension Scan] Your charge is robust reliability: Detect any fragile dependencies, unsafe patterns (e.g., bare `except:` blocks), and extension points that could be broken or fragile after cleanup. Audit for hooks, integrations, and settings that must be retained for full plugin operation or safe extensibility. Cross-reference against removals to prevent accidental breakage. Execute as `{role=safety_dependency_auditor; input={structural_map:dict, prune_candidates:list}; process=[scan_for_external_dependency/extension usage(), check for error-prone or unsafe exception handling(), cross-examine hooks for plugin ecosystem impact(), list safety-critical pieces not to delete()], output={safety_checklist:list, extension_safeguards:list, fragile_points:list}}`
        ```
        ---

        #### `0121-e-st-plugin-targeted-cleanup-action-plan.md`

        ```markdown
        [Targeted Cleanup Action Plan] Your function is strategic precision: Integrate all previous analysis to produce an actionable plan—specifying exactly what to remove, refactor, or consolidate, and explaining the safety, clarity, and value rationale for each recommendation. Prioritize the order of cleanup for maximum impact with minimal risk, from dead code pruning to comment minimization and extensibility preservation. Execute as `{role=cleanup_planner; input={prune_candidates:list, style_issues:list, safety_checklist:list}; process=[prioritize_changes_by_impact_and_risk(), document_rationale_for_each_action(), map each change to file/line/location(), prevent critical breakage with last-minute exclusions()], output={cleanup_plan:list[dict(action:str, target:str, rationale:str, risk:str)]}}`
        ```
        ---

        #### `0121-f-st-plugin-guided-automated-or-assisted-cleanup-application.md`

        ```markdown
        [Guided Automated or Assisted Cleanup Application] Your task is not only planning, but safe execution: Systematically apply each item in the cleanup plan—automatically where feasible, or producing precise manual guidance where automation could risk breakage. At each stage, validate correctness, maintainability, and operational integrity. Summarize changes and highlight any areas needing follow-up. Execute as `{role=cleanup_executor; input={plugin_directory:any, cleanup_plan:list}; process=[apply_safe_removal_and_refactoring_steps(), run_tests_or_sanity-checks_after_edits(), generate before/after summaries_by_action(), flag_manual_review_points()], output={cleanup_summary:list, post_cleanup_status:dict}}`
        ```
        ---

        #### `0121-g-st-plugin-post-cleanup-validation-and-self-sufficiency.md`

        ```markdown
        [Post-Cleanup Validation & Self-Sufficiency Analysis] Your concluding responsibility is not surface review, but sustained resilience: Thoroughly test and review the cleaned codebase, ensuring that all key workflows, integrations, and extensibility remain functional, and that self-explanation, minimal-commentary standards, and cross-plugin compatibility are maximized. Deliver a concise onboarding/map for new contributors, capturing the cleaned architecture and remaining hooks. Execute as `{role=post_cleanup_validator; input={cleanup_summary:list, post_cleanup_status:dict}; process=[validate_full plugin functionality_and_tests(), verify extensibility_and integration points(), check documentation_and_identifier sufficiency(), generate onboarding summary and navigational references()], output={final_cleanup_report:dict, onboarding_map:str}}`
        ```
