
# Context:

Here's the updated version of the template:

## `cline_system_instruction_memorybank_v2.md`

    ```markdown
    ## C<PERSON>'s Memory Bank

    I am <PERSON><PERSON>, an expert software engineer with a unique characteristic: my memory resets completely between sessions. This isn't a limitation—it's what drives me to maintain perfect documentation. After each reset, I rely **entirely** on my Memory Bank to understand the project and continue work effectively. I **must** read **all** Memory Bank files at the start of **every** task—this is not optional.

    ## Memory Bank Structure

    The Memory Bank consists of **core files** and **optional context files**, all in Markdown format. Files build upon each other in a clear, **chronologically numbered** hierarchy. The numeric filenames ensure the natural reading and updating order is always self-evident, both for humans and automated systems.

    flowchart TD
    flowchart TD
        PB[1-projectbrief.md] --> PC[2-productContext.md]
        PB --> SP[3-systemPatterns.md]
        PB --> TC[4-techContext.md]

        PC --> AC[5-activeContext.md]
        SP --> AC
        TC --> AC

        AC --> PR[6-progress.md]
        PR --> TA[7-tasks.md]

    ### Core Files (Required)
    1. `1-projectbrief.md`
       - **Foundation document** that shapes all other files
       - Created at project start if it doesn't exist
       - Defines core requirements and goals
       - Source of truth for project scope

    2. `2-productContext.md`
       - **Why** this project exists
       - The problems it solves
       - How it should work
       - User experience goals

    3. `3-systemPatterns.md`
       - **System architecture**
       - Key technical decisions
       - Design patterns in use
       - Component relationships
       - Critical implementation paths

    4. `4-techContext.md`
       - **Technologies used**
       - Development setup
       - Technical constraints
       - Dependencies
       - Tool usage patterns

    5. `5-activeContext.md`
       - **Current work focus**
       - Recent changes
       - Next steps
       - Active decisions and considerations
       - Important patterns and preferences
       - Learnings and project insights

    6. `6-progress.md`
       - **What works**
       - What's left to build
       - Current status
       - Known issues
       - Evolution of project decisions

    7. `7-tasks.md`
       - **(Often considered core)**
       - The definitive record of project tasks
       - Tracks to-do items, priorities, assignments, or progress

    ### Additional Context
    Create additional files/folders within memory-bank/ when they help organize:
    - Complex feature documentation
    - Integration specifications
    - API documentation
    - Testing strategies
    - Deployment procedures

    ---

    ## Core Workflows

    ### Plan Mode

    flowchart TD
    flowchart TD
        Start[Start] --> ReadFiles[Read Memory Bank]
        ReadFiles --> CheckFiles{Files Complete?}

        CheckFiles -->|No| Plan[Create Plan]
        Plan --> Document[Document in Chat]

        CheckFiles -->|Yes| Verify[Verify Context]
        Verify --> Strategy[Develop Strategy]
        Strategy --> Present[Present Approach]

    1. **Start**: Begin the planning process.
    2. **Read Memory Bank**: Load **all** relevant `.md` files from `memory-bank/`.
    3. **Check Files**: Verify if any core file is missing or incomplete.
    4. **Create Plan** (if incomplete): Document how to fix or fill the missing pieces.
    5. **Verify Context** (if complete): Make sure everything is understood.
    6. **Develop Strategy**: Outline how to proceed with tasks.
    7. **Present Approach**: Summarize the plan and next steps.

    ### Act Mode

    flowchart TD
        Start[Start] --> Context[Check Memory Bank]
        Context --> Update[Update Documentation]
        Update --> Execute[Execute Task]
        Execute --> Document[Document Changes]

    1. **Start**: Begin the task.
    2. **Check Memory Bank**: Read the relevant numbered files in `memory-bank/`.
    3. **Update Documentation**: Make sure each file that needs changes is updated.
    4. **Execute Task**: Carry out the changes, implementation, or solution.
    5. **Document Changes**: Record any new insights, patterns, or updates in the appropriate files.

    ---

    ## Documentation Updates

    Memory Bank updates occur when:
    1. Discovering new project patterns
    2. After implementing significant changes
    3. When the user requests **update memory bank** (MUST review **all** files)
    4. When context needs clarification

    flowchart TD
        Start[Update Process]

        subgraph Process
            P1[Review ALL Numbered Files]
            P2[Document Current State]
            P3[Clarify Next Steps]
            P4[Document Insights & Patterns]

            P1 --> P2 --> P3 --> P4
        end

        Start --> Process

    Note: When triggered by **update memory bank**, you **must** review **every** Memory Bank file, even if some don't require updates. Focus particularly on `5-activeContext.md` and `6-progress.md` as they track current state.

    REMEMBER: After every memory reset, I (Cline) begin completely fresh. The Memory Bank is my only link to previous work. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy.

    ---

    ## Example Incremental Directory Structure

    Below is a sample directory layout emphasizing the new numeric naming convention for chronological clarity:

    ```
    └── memory-bank
        ├── 1-projectbrief.md          # Foundation document; project scope and core requirements
        ├── 2-productContext.md        # Why the project exists; user and market goals
        ├── 3-systemPatterns.md        # High-level system architecture, key decisions, patterns
        ├── 4-techContext.md           # Technical stack, setup, and constraints
        ├── 5-activeContext.md         # Current work focus, decisions, and step-tracking
        ├── 6-progress.md              # Current status, progress tracking, known issues
        └── 7-tasks.md                 # (Often core) Definitive record of project tasks
    ```

    Any additional files, like feature-specific docs or integration details, should be prefixed with the next available number (e.g., `8-APIoverview.md`, `9-integrationSpec.md`, and so on) in the order they are created or logically needed.

    ---

    ## Why Numbered Filenames?

    1. **Chronological Clarity**: Ensures that anyone reading the directory can see the intended order for reading/updating each file.
    2. **Predictable Sorting**: Standard file browsers and GitHub listings sort numerically by default, revealing the correct sequence.
    3. **Workflow Reinforcement**: Reflects the progressive nature of the Memory Bank—begin with the foundational project brief, then product context, system patterns, technical context, active context, progress, and tasks.
    4. **Scalability**: Additional files easily slot in after the highest used number, maintaining the same clarity and order.

    ---

    ## Additional Guidance

    - **Strict Consistency**: All references throughout documentation, code, or instructions must use the **exact** numeric filename (e.g., `2-productContext.md`) to avoid confusion.
    - **File Renaming**: If you introduce a new core file, assign it a numeric prefix that fits its logical place or chronological creation. For example, if you add a specialized test plan after `6-progress.md`, you might call it `7-testPlan.md`, then shift `tasks.md` to `8-tasks.md`, updating references accordingly.
    - **No Gaps**: Avoid skipping numbers. Each new file must follow sequentially to ensure clarity, e.g., 1, 2, 3, 4. If a file is removed, update or reassign the numbering carefully, and revise all references.
    ```

Here's the files it generates when utilized on a project:

    ### File Structure

    ```
    ├── 1-projectbrief.md
    ├── 2-productContext.md
    ├── 3-systemPatterns.md
    ├── 4-techContext.md
    ├── 5-activeContext.md
    ├── 6-progress.md
    └── 7-tasks.md
    ```

    #### `1-projectbrief.md`

    ```markdown
        # Project Brief: RigOffice Document Retrieval

        ## Project Overview
        RigOffice Document Retrieval is a specialized utility designed to automate the extraction, organization, and downloading of technical documentation from the National Oilwell Varco (NOV) RigDoc system. The tool uses web scraping techniques to access, catalog, and retrieve engineering documents and their associated files for specific rig equipment and projects.

        ## Core Objectives
        1. **Automated Document Discovery**: Locate and catalog relevant technical documents from the RigDoc platform based on rig numbers or custom search criteria.
        2. **Metadata Extraction**: Extract comprehensive metadata about documents and associate them with their source rig/equipment.
        3. **File Retrieval**: Automatically download associated files for selected documents.
        4. **Data Organization**: Create a structured and searchable local repository of technical documentation.
        5. **Progress Tracking**: Maintain records of what has been downloaded and what remains to be processed.

        ## Target Users
        - Engineering teams requiring rapid access to technical documentation
        - Project managers coordinating rig equipment information
        - Technical documentation specialists organizing engineering assets

        ## Success Criteria
        - Successfully retrieves document metadata from multiple search configurations
        - Accurately extracts file information for all valid documents
        - Downloads files while maintaining logical organization by case number
        - Operates reliably with minimal user intervention
        - Preserves download state and can resume interrupted operations
    ```

    ---

    #### `2-productContext.md`

    ```markdown
        # Product Context: RigOffice Document Retrieval

        ## Problem Domain
        Engineering teams working with oil rig equipment require extensive technical documentation for design, operation, maintenance, and compliance purposes. The National Oilwell Varco (NOV) RigDoc system serves as a centralized repository for this documentation but presents several challenges:

        1. **Manual Retrieval Inefficiency**: Manually navigating the RigDoc web interface to locate and download multiple documents is time-consuming and error-prone.
        2. **Volume Management**: Oil rig projects often involve hundreds of technical documents, making manual tracking impractical.
        3. **Document Relationships**: Technical documentation is interconnected by case numbers, equipment references, and revisions that must be maintained during retrieval.
        4. **Search Limitations**: The RigDoc interface has specific search parameters that must be systematically exploited to locate all relevant documentation.
        5. **Discontinuous Downloads**: Engineers need to download documentation in batches, often over multiple sessions, requiring persistent state.

        ## Solution Value Proposition
        RigOffice Document Retrieval addresses these challenges by providing:

        1. **Automated Search**: Predefined search templates for different document types (machines, layouts, projects, manuals) streamline the discovery process.
        2. **Progressive Extraction**: The three-phase approach (document metadata → file metadata → download) allows for inspection and filtering at each stage.
        3. **Intelligent Organization**: Downloaded files are automatically categorized by case number, maintaining their logical relationship.
        4. **State Persistence**: JSON data files preserve the state of document and file discovery, enabling interrupted operations to be resumed.
        5. **Deduplication**: Built-in mechanisms prevent duplicate downloads while prioritizing newer document revisions.

        ## User Experience Goals
        - **Minimal Configuration**: Users need only specify a rig number or search URLs to begin the process.
        - **Visual Progress Feedback**: Color-coded terminal output provides clear status information.
        - **Flexible Workflow**: Users can run the complete process or individual phases (document discovery, file metadata extraction, or downloading).
        - **Filtering Control**: The JSON data files can be manually edited to exclude specific documents or files from downloading.
        - **Self-contained Operation**: The tool operates as a standalone utility without complex dependencies.

        ## Integration Context
        RigOffice Document Retrieval functions as a bridge between the online RigDoc system and local project documentation repositories. It integrates with:

        - **Web-based RigDoc Platform**: Accesses the system via Selenium for authenticated scraping.
        - **Local File Storage**: Organizes downloads in a structured hierarchy based on document metadata.
        - **Project Documentation Standards**: Preserves the naming conventions and organizational hierarchy from RigDoc.
    ```

    ---

    #### `3-systemPatterns.md`

    ```markdown
        # System Patterns: RigOffice Document Retrieval

        ## Architecture Overview
        The RigOffice Document Retrieval system is designed with a modular, phase-driven architecture centered around a unified scraper class. The system follows a linear workflow with discrete phases that can be executed independently or as a complete pipeline.

        ```mermaid
        graph TD
            A[User Input: Rig Number/URLs] --> B[Phase 1: Document Discovery]
            B --> C[Document JSON]
            C --> D[Phase 2: File Metadata Extraction]
            D --> E[Files JSON]
            E --> F[Phase 3: File Download]
            F --> G[Local File Repository]
            H[Configuration] --> B
            H --> D
            H --> F
        ```

        ## Core System Components

        ### 1. RigDocScraper Class
        The centerpiece of the system is a unified `RigDocScraper` class that encapsulates all web scraping functionality. This class:
        - Manages browser sessions through Selenium
        - Handles document discovery via search URLs
        - Extracts file metadata from document pages
        - Downloads files to organized folders
        - Orchestrates the entire process based on configuration flags

        ### 2. Data Processing Pipeline
        The system implements a three-phase data processing pipeline:
        1. **Document Discovery**: Scrapes search results to extract document metadata
        2. **File Metadata Extraction**: For each document, extracts metadata about associated files
        3. **File Downloading**: Downloads the actual files and organizes them logically

        Each phase produces persistent state (JSON files) that can be inspected, modified, or used as input for subsequent phases, enabling workflow flexibility.

        ### 3. Persistence Layer
        State between phases is maintained using JSON files:
        - `<rig_number>-a-docs.json`: Document metadata from Phase 1
        - `<rig_number>-b-files.json`: File metadata from Phase 2

        These files serve as both checkpoints for resuming operations and as interfaces for user inspection and filtering.

        ## Key Design Patterns

        ### 1. Progressive Enhancement
        The system implements progressive enhancement through its phase-based approach, allowing users to:
        - Run only document discovery to review available content
        - Run document and file metadata extraction without downloading large files
        - Execute the full pipeline when ready for complete retrieval

        ### 2. Idempotent Operations
        All phases are designed to be idempotent, using deduplication strategies to ensure:
        - Multiple runs of document discovery append only new documents
        - File metadata extraction skips documents already processed
        - Downloads avoid re-downloading existing files

        ### 3. Adapter Pattern
        The system uses an adapter pattern to normalize the RigDoc web interface into a consistent local data model, abstracting away the complexities of:
        - Web page structure and navigation
        - Authentication and session management
        - Download handling

        ### 4. Configuration-Driven Behavior
        A central configuration object drives system behavior:
        - Search URLs customize the document discovery phase
        - Boolean flags enable/disable phases
        - Rig number determines output organization
        - Progress display toggles verbosity

        ## Technical Implementation Details

        ### 1. Browser Automation
        Selenium WebDriver handles the browser automation with these key components:
        - Chrome browser instance configured for headless operation or UI display
        - Explicit waits combined with custom sleep conditions for page loading
        - Scroll-to-bottom logic to ensure all results are loaded
        - Download directory configuration for file retrieval

        ### 2. HTML Parsing Strategy
        BeautifulSoup handles HTML parsing with a structured approach:
        - Targeted CSS selectors to identify document rows
        - Parent-child traversal to extract related cells
        - Regular expressions for text cleaning
        - JSON extraction from API responses for file metadata

        ### 3. File Management
        The system implements careful file management strategies:
        - Atomic writes using temporary files and file replacement
        - Name sanitization to ensure OS compatibility
        - Case number subfolder organization
        - Download verification with timeout handling
    ```

    ---

    #### `4-techContext.md`

    ```markdown
        # Technical Context: RigOffice Document Retrieval

        ## Technology Stack

        ### Core Technologies
        - **Language**: Python 3.x
        - **Web Automation**: Selenium WebDriver with Chrome
        - **HTML Parsing**: BeautifulSoup4
        - **Data Storage**: JSON
        - **Environment Management**: Python venv + Batch scripts
        - **Terminal UI**: ANSI color formatting (colorama, ansimarkup)

        ### Key Python Libraries
        - **selenium**: Browser automation for navigating the RigDoc website
        - **beautifulsoup4**: HTML parsing and data extraction
        - **webdriver_manager**: Automated Chrome driver installation
        - **python-dateutil**: Date parsing and formatting
        - **colorama/ansimarkup**: Terminal color output formatting
        - **requests**: HTTP operations (when direct API access is available)
        - **json**: Data serialization and storage

        ## Development Environment

        ### Virtual Environment
        The project uses a Python virtual environment managed through a custom batch script:
        - `py_venv_init.bat`: Locates Python, creates/activates virtual environment, installs dependencies
        - `venv/`: Contains the isolated Python environment
        - `requirements.txt`: Lists all Python dependencies with pinned versions

        ### Execution Environment
        - **Operating System**: Primarily Windows-focused (batch files)
        - **Browser**: Google Chrome with Selenium WebDriver
        - **Terminal**: Command Prompt/PowerShell with ANSI color support

        ### File Structure
        ```
        project_root/
        ├── RigOfficeRetrieval.py     # Main Python script
        ├── RigOfficeRetrieval.bat    # Execution wrapper script
        ├── py_venv_init.bat          # Environment setup script
        ├── requirements.txt          # Python dependencies
        ├── .gitignore                # Version control exclusions
        ├── __meta__/                 # Project notes and documentation
        │   ├── _rigoffice_notes.py   # Documentation of RigDoc ID mappings
        │   └── _md/                  # Development notes in Markdown
        └── outputs/                  # Generated data and downloads
            ├── data/                 # JSON data files (docs and files metadata)
            └── downloads/            # Downloaded files organized by rig number
        ```

        ## Technical Dependencies and Constraints

        ### External System Dependencies
        - **NOV RigDoc System**: The primary data source (https://rigdoc.nov.com)
          - Requires understanding of URL structure for searches
          - Document types are identified by numeric IDs
          - File downloads involve multiple page transitions

        ### Browser Automation Requirements
        - **Chrome**: Required for Selenium WebDriver interaction
        - **ChromeDriver**: Automatically installed via webdriver_manager
        - **Download Handling**: Custom profile configuration for automated downloads
        - **JavaScript Support**: Required for page scrolling and dynamic content loading

        ### Performance Considerations
        - **Network Latency**: Multiple requests required for each document and file
        - **Download Size**: Technical drawings can be large (PDF, DWG files)
        - **Pagination**: Scroll-based loading requires careful timing

        ## Tool Integration

        ### Development Tools
        - **Editor**: Support for Python with syntax highlighting
        - **JSON Editors**: For manual inspection and editing of data files
        - **Git**: Version control for script development

        ### Execution Pattern
        1. **Environment Initialization**:
           ```batch
           py_venv_init.bat
           ```

        2. **Execution**:
           ```batch
           RigOfficeRetrieval.bat
           ```

        3. **Configuration**: Edit `CONFIG` dictionary in `RigOfficeRetrieval.py` to control:
           - Rig number or search URLs
           - Enabled phases (document discovery, file metadata, downloads)
           - Progress display verbosity

        ### Data Flow
        ```mermaid
        graph LR
            A[RigDoc Website] -->|Selenium| B[Document Metadata]
            B -->|JSON| C[outputs/data/rig-a-docs.json]
            A -->|Selenium| D[File Metadata]
            D -->|JSON| E[outputs/data/rig-b-files.json]
            A -->|Chrome Download| F[File Content]
            F -->|Files| G[outputs/downloads/rig/]
        ```

        ## Technical Challenges and Solutions

        ### 1. Infinite Scroll Handling
        - **Challenge**: RigDoc search results load via infinite scroll
        - **Solution**: Script uses dynamic scrolling with height comparison

        ### 2. Download Management
        - **Challenge**: Browser download behavior is inconsistent
        - **Solution**: Custom Chrome profile with download preferences and file watching

        ### 3. Session Management
        - **Challenge**: Browser sessions can time out
        - **Solution**: Fresh browser instance for each phase with appropriate waits

        ### 4. Data Deduplication
        - **Challenge**: Multiple runs can produce duplicate entries
        - **Solution**: Custom deduplication algorithm with priority-based resolution
    ```

    ---

    #### `5-activeContext.md`

    ```markdown
        # Active Context: RigOffice Document Retrieval

        ## Current Development Focus
        The current focus of the RigOffice Document Retrieval project is consolidating web scraping functionality into a unified class structure. This refactoring aims to improve code organization, maintainability, and future extensibility while preserving the existing functionality.

        ### Recent Code Changes
        1. **Class Consolidation**: Moved from procedural code to a unified `RigDocScraper` class that encapsulates browser interaction, document discovery, file metadata extraction, and file downloading.
        2. **Three-Phase Architecture**: Formalized the separation between document discovery, file metadata extraction, and file downloading phases.
        3. **Data Persistence**: Enhanced the JSON storage approach to better support resuming operations and manual filtering.
        4. **Idempotent Operations**: Implemented deduplication strategies to safely handle repeated executions.

        ### Implementation Notes

        #### Browser Session Management
        The current implementation creates fresh browser instances for each phase (document discovery, file extraction, downloads) to prevent session timeouts. Each browser instance is configured according to the specific needs of the phase:
        - Document discovery: Standard configuration for scrolling and HTML extraction
        - File metadata extraction: Configured for API response parsing
        - File downloading: Custom profile with download preferences

        #### Data Processing Flow
        The current data flow involves:
        1. **Config → Document Discovery**: Converts configuration into search URLs, then into document metadata
        2. **Document Metadata → File Discovery**: Uses document URLs to locate and extract file information
        3. **File Metadata → Downloaded Files**: Manages Chrome downloads of actual file content

        #### Utility Functions
        Several utility functions have been developed to handle:
        - Date reformatting from diverse formats to YYYY.MM.DD
        - Advanced deduplication with priority-based conflict resolution
        - Atomic file writes via temporary files
        - Terminal output with color coding for different message types

        ## Active Decisions and Considerations

        ### Browser Automation Approach
        The project uses Selenium WebDriver due to the highly interactive nature of the RigDoc site, which requires:
        - JavaScript execution for infinite scroll
        - Session management for authenticated access
        - Handling of dynamic content loading
        - Complex download dialog management

        Alternative approaches like direct API access were considered but abandoned due to the lack of public API documentation and the complexity of reverse-engineering the authentication flow.

        ### Data Storage Format
        JSON was selected as the intermediate data format because:
        - It's human-readable and manually editable
        - It preserves nested structure needed for document-file relationships
        - It provides a simple way to persist state between phases
        - It can be easily loaded in other tools for further processing

        ### Command-Line Interface
        The current interface is configuration-driven rather than argument-driven. This decision was made to:
        - Simplify common usage patterns (changing rig number or toggling phases)
        - Avoid complex command-line parsing
        - Allow for easy documentation of configuration options

        ## Current Insights and Learnings

        ### RigDoc Document Types
        Through exploration of the RigDoc system, a comprehensive mapping of document type IDs has been developed (stored in `__meta__/_rigoffice_notes.py`). This mapping is crucial for constructing effective search URLs.

        ### Search Templates
        Based on operational patterns, several search templates have emerged:
        - **SEARCH_MACHINES**: Assembly and general arrangement drawings
        - **SEARCH_LAYOUTS**: Layout and structural arrangement drawings
        - **SEARCH_PROJECTS**: Master documents and equipment lists
        - **SEARCH_MANUALS**: Operation and maintenance manuals
        - **SEARCH_KICKOFF**: Project kickoff and planning documents

        ### Download Optimization
        The file download process has been optimized to:
        - Use a dedicated Chrome profile to prevent download dialogs
        - Monitor the download directory for new files
        - Implement timeout-based completion detection
        - Organize downloads by case number for logical grouping

        ## Next Steps and Opportunities

        ### Near-Term Improvements
        1. **Command Line Arguments**: Add proper argument parsing to avoid editing the script
        2. **Progress Visualization**: Implement progress bars for long-running operations
        3. **Parallel Processing**: Consider parallel processing for file metadata extraction and downloads
        4. **Error Recovery**: Enhance error handling for common failure modes (network issues, site changes)

        ### Medium-Term Enhancements
        1. **User Interface**: Consider a simple web UI or desktop application wrapper
        2. **Search Builder**: Interactive tool to build and save search templates
        3. **Reporting**: Generate summary reports of downloaded documents
        4. **Content Indexing**: Extract text from PDFs for local search

        ### Long-Term Vision
        1. **Integration API**: Provide an API for integration with document management systems
        2. **Metadata Enrichment**: Enhance document metadata with additional information
        3. **Multi-source Aggregation**: Expand to support multiple document sources beyond RigDoc
    ```

    ---

    #### `6-progress.md`

    ```markdown
        # Progress: RigOffice Document Retrieval

        ## Current Status
        The RigOffice Document Retrieval system is currently functional with the refactored class-based architecture. The main script successfully handles all three phases of the document retrieval process:

        1. **Document Discovery**: ✅ Fully operational
           - Successfully extracts document metadata from search results
           - Handles infinite scrolling to load all results
           - Stores document data in JSON format

        2. **File Metadata Extraction**: ✅ Fully operational
           - Identifies file attachments for each document
           - Extracts file details including IDs, sizes, and extensions
           - Maintains relationship with parent documents

        3. **File Downloading**: ✅ Fully operational
           - Downloads files using Chrome automation
           - Organizes files by case number
           - Implements monitoring for download completion

        ## Recent Development Timeline
        - **Class Refactoring**: Consolidated web scraping functionality into the `RigDocScraper` class
        - **Data Persistence**: Enhanced JSON storage with better deduplication
        - **Download Optimization**: Improved the file download process with better file monitoring
        - **Document Type Mapping**: Compiled comprehensive document type ID reference

        ## What Works
        - **End-to-End Process**: The complete pipeline from search to download works reliably
        - **Stateful Operation**: The system can be paused and resumed between phases
        - **Manual Filtering**: Users can edit JSON files to exclude specific documents or files
        - **Case Organization**: Downloaded files are properly organized by case number
        - **Duplicate Handling**: System correctly prevents redundant downloads and processing

        ## Known Issues
        - **Browser Session Management**: Occasionally, browser sessions may time out during long operations
        - **Download Timeouts**: Very large files may exceed the default download timeout
        - **Search URL Limitations**: Users must manually construct search URLs for complex queries
        - **Configuration Editing**: Changes to configuration require editing the Python script directly
        - **Dependency Management**: ChromeDriver versioning can sometimes cause issues with newer Chrome versions

        ## Evolution of Project Decisions

        ### Architecture Decisions
        1. **Initial Development**: Procedural approach focusing on functional decomposition
        2. **Current Refactoring**: Class-based architecture with clearer separation of concerns
        3. **Future Direction**: Potential for modular design with plugin support for different document sources

        ### Interface Decisions
        1. **Initial Interface**: Hard-coded configuration in script
        2. **Current Approach**: Configuration dictionary with documentation
        3. **Planned Enhancement**: Command-line argument parsing for easier use

        ### Data Handling Decisions
        1. **Initial Storage**: Simple file-based storage
        2. **Current Implementation**: Structured JSON with relationship preservation
        3. **Future Consideration**: Potential for database integration or indexing

        ## What's Left to Build

        ### Short-Term Tasks
        1. **Command-Line Interface**: Implement proper argument parsing using `argparse`
        2. **Progress Visualization**: Add progress bars for long-running operations
        3. **Improved Error Handling**: Enhance error recovery for network issues and site changes
        4. **Logging**: Implement proper logging instead of print statements
        5. **Documentation**: Create usage documentation with examples

        ### Medium-Term Tasks
        1. **Search URL Builder**: Create a utility to visually build search URLs
        2. **Batch Processing**: Support for processing multiple rig numbers in sequence
        3. **Reporting**: Generate summary reports of downloaded content
        4. **File Type Handling**: Special processing for common file types (PDF extraction, etc.)

        ### Long-Term Vision
        1. **UI Development**: Simple web or desktop interface
        2. **Search Functionality**: Local search across downloaded documents
        3. **Integration Support**: APIs for connecting with document management systems
        4. **Multi-Source Support**: Extensions for additional document repositories
    ```

    ---

    #### `7-tasks.md`

    ```markdown
        # Tasks: RigOffice Document Retrieval

        ## Current Tasks (In Progress)

        | ID | Task | Priority | Status | Dependencies | Notes |
        |---|---|---|---|---|---|
        | T001 | Class Refactoring Completion | High | 90% | None | Consolidate web scraping functionality into a unified class structure |
        | T002 | Code Review & Optimization | Medium | 50% | T001 | Identify bottlenecks and optimize performance |
        | T003 | Test Case Development | Medium | 20% | T001 | Create test cases for core functionality |

        ## Completed Tasks

        | ID | Task | Completion Date | Notes |
        |---|---|---|---|
        | C001 | Unified RigDocScraper Class | 2025-04-22 | Consolidated browser interaction, document discovery, file extraction, and downloading |
        | C002 | Three-Phase Workflow | 2025-04-21 | Formalized separation between document discovery, file metadata, and downloading |
        | C003 | Data Persistence Implementation | 2025-04-21 | Enhanced JSON storage with better deduplication |
        | C004 | Document Type Mapping | 2025-04-22 | Compiled comprehensive document type ID reference |
        | C005 | Download Process Optimization | 2025-04-22 | Improved file monitoring and organization |

        ## Short-Term Tasks (Next 2 Weeks)

        | ID | Task | Priority | Dependencies | Estimated Effort | Notes |
        |---|---|---|---|---|---|
        | S001 | Command Line Interface | High | T001 | 1 day | Implement argument parsing to avoid editing the script |
        | S002 | Progress Visualization | Medium | T001 | 1 day | Add progress bars for long-running operations |
        | S003 | Enhanced Error Handling | High | T001 | 2 days | Better recovery from network issues and site changes |
        | S004 | Logging Implementation | Medium | None | 1 day | Replace print statements with proper logging |
        | S005 | User Documentation | High | T001 | 2 days | Create usage documentation with examples |

        ## Medium-Term Tasks (1-3 Months)

        | ID | Task | Priority | Dependencies | Estimated Effort | Notes |
        |---|---|---|---|---|---|
        | M001 | Search URL Builder | Medium | S001 | 3 days | Create a utility to build search URLs |
        | M002 | Batch Processing | Medium | S001 | 2 days | Support for processing multiple rig numbers |
        | M003 | Download Reporting | Low | S004 | 2 days | Generate summary reports of downloaded content |
        | M004 | PDF Text Extraction | Low | None | 3 days | Extract text content from downloaded PDFs |
        | M005 | Configuration Profiles | Medium | S001 | 2 days | Save/load different configuration profiles |

        ## Long-Term Tasks (3+ Months)

        | ID | Task | Priority | Dependencies | Notes |
        |---|---|---|---|---|
        | L001 | Simple Web Interface | Low | S001, S004 | Create a browser-based interface |
        | L002 | Local Search Engine | Low | M004 | Implement search across downloaded documents |
        | L003 | DMS Integration | Low | None | APIs for document management system integration |
        | L004 | Multi-Source Support | Low | None | Support for additional document repositories |
        | L005 | Docker Containerization | Low | None | Package the system as a Docker container |

        ## Bug Fixes & Issues

        | ID | Issue | Priority | Status | Affected Component | Notes |
        |---|---|---|---|---|---|
        | B001 | Browser Session Timeouts | Medium | Open | File Discovery | Long operations can cause session timeouts |
        | B002 | Download Timeouts | Medium | Open | File Download | Large files may exceed timeout limits |
        | B003 | ChromeDriver Versioning | Low | Open | Environment | Version mismatches with Chrome cause failures |
        | B004 | Error Handling for Network Issues | High | Open | All | Better error recovery needed |
        | B005 | Manual Search URL Construction | Medium | Open | Configuration | Complex to construct search URLs manually |

        ## Task Dependencies

        ```mermaid
        graph TD
            T001[Class Refactoring] --> T002[Code Review]
            T001 --> T003[Test Cases]
            T001 --> S001[Command Line Interface]
            T001 --> S002[Progress Visualization]
            T001 --> S003[Error Handling]
            T001 --> S005[Documentation]

            S001 --> M001[Search URL Builder]
            S001 --> M002[Batch Processing]
            S001 --> M005[Configuration Profiles]

            S004[Logging] --> M003[Reporting]

            M004[PDF Extraction] --> L002[Local Search]

            S001 --> L001[Web Interface]
            S004 --> L001
        ```

        ## Notes on Task Prioritization

        1. **High Priority**:
           - Tasks that address current limitations affecting usability
           - Completion of the core architectural refactoring
           - Documentation for user adoption

        2. **Medium Priority**:
           - Quality of life improvements
           - Performance optimizations
           - Additional features that enhance existing functionality

        3. **Low Priority**:
           - Feature extensions beyond core functionality
           - UI improvements
           - Integration with external systems
    ```

---

# GOAL:

I want you preserve all of the essential core structure of template (`cline_system_instruction_memorybank_v2.md`) and incorporate a step specifically designed by the following command: `"Based on all already retrieved memory, dive deeper into the Codebase, absorbing every intricate detail—from ethereal abstractions to tangled complexities—and embrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence. Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact. Your focus will be to focus is to distill the essence of the schema and propose a singular, high-impact enhancement that demonstrably would yeld the most *drastic* (through low-effort -> high-value ) improvements. Tldr;'Thoroughly analyze the Codebase to understand its abstract and intricate elements and their interdependencies, prioritizing simplicity over complexity while aiming for low-disruption, high-impact improvements; then identify a single, transformative innovation that aligns with intrinsic excellence metrics, preserves contextual integrity, and embeds superior execution logic for universally effective enhancement.'"`

---

# Requirements:

It's *imperative* that you *preserve* the inherent structure and underlying philosphy of the original template, and that you *guarantee* not making changes without knowing *exactly* how the component relates to *everything*.
