# Project Files Documentation for `models`

### File Structure

```
├── __init__.py
├── anthropic_models.py
├── gemini_models.py
├── openai_models.py
└── test.py
```
### 1. `__init__.py`

#### `__init__.py`

```python
import os
from typing import Dict, Any


import anthropic_models
import gemini_models
import openai_models


def get_model_provider(provider_name: str):
    if provider_name.lower() == "anthropic":
        return anthropic_models
    elif provider_name.lower() == "gemini":
        return gemini_models
    elif provider_name.lower() == "openai":
        return openai_models
    else:
        raise ValueError(f"Unsupported model provider: {provider_name}")


def run_model(
    provider_name: str,
    user_prompt: str,
    model_name: str = None,
    temperature: float = None,
    max_tokens: int = None,
    **kwargs,
) -> str:
    provider_module = get_model_provider(provider_name)

    if model_name is None:
        model_name = provider_module.DEFAULT_PARAMS["default_model"]
    if temperature is None:
        temperature = provider_module.DEFAULT_PARAMS["default_temperature"]
    if max_tokens is None:
        max_tokens = provider_module.DEFAULT_PARAMS["default_max_tokens"]

    request_args: Dict[str, Any] = {
        "model": model_name,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "prompt": user_prompt,
    }
    request_args.update(kwargs)

    response_text = provider_module.generate_text(**request_args)

    return response_text

```
### 2. `anthropic_models.py`

#### `anthropic_models.py`

```python
# anthropic_agent.py

import os
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv
from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

VALID_MODELS = {
    "claude-2": "Base Claude 2 model",
    "claude-2.0": "Enhanced Claude 2.0",
    "claude-2.1": "Latest Claude 2.1 release",
    "claude-3-opus-20240229": "Claude 3 Opus",
    "claude-3-sonnet-20240229": "Claude 3 Sonnet",
    "claude-3-haiku-20240307": "Claude 3 Haiku",
}

DEFAULT_PARAMS = {
    "default_model": VALID_MODELS["claude-2.1"],
    "default_temperature": 0.7,
    "default_max_tokens": 800,
}


class AnthropicAgent:
    """
    Encapsulates the logic for calling Anthropic's API,
    using sensible defaults or optional overrides.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ):
        load_dotenv()  # Loads environment variables from .env if present
        self.client = Anthropic(api_key=api_key or os.getenv("ANTHROPIC_API_KEY"))
        self.model = model or DEFAULT_PARAMS["default_model"]
        self.temperature = temperature if temperature is not None else DEFAULT_PARAMS["default_temperature"]
        self.max_tokens = max_tokens if max_tokens is not None else DEFAULT_PARAMS["default_max_tokens"]

    def generate_response(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ) -> str:
        """
        Generates a response from the Anthropic API, using either
        instance defaults or the overrides provided.
        """

        used_model = model or self.model
        used_temperature = temperature if temperature is not None else self.temperature
        used_max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        # System role is typically placed separately from user prompt
        system_prompt = "You are a helpful assistant."

        # Convert single prompt string to Anthropic-style message list
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]

        # Make the API request
        response = self.client.messages.create(
            model=used_model,
            max_tokens=used_max_tokens,
            temperature=used_temperature,
            system=system_prompt,
            messages=messages,
        )

        # Return just the text from the first chunk
        return response.content[0].text


def anthropic_test(agent: Optional[AnthropicAgent] = None):
    """
    Simple test function that demonstrates how to call the AnthropicAgent.
    """
    if agent is None:
        agent = AnthropicAgent()

    test_prompt = (
        "Determine the single key factor that maximizes overall value. "
        "Concentrate on the most critical element that, when addressed, "
        "yields the highest impact."
    )

    response = agent.generate_response(prompt=test_prompt, model="claude-3-opus-20240229")
    print(f"response: {response}")


if __name__ == "__main__":
    anthropic_test()

```
### 3. `gemini_models.py`

#### `gemini_models.py`

```python
# gemini_agent.py

import os
import google.generativeai as genai
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv

VALID_MODELS = {
    "gemini-pro": "Base Gemini Pro model",
    "gemini-pro-vision": "Gemini Pro model with vision capabilities",
    "gemini-1.5-pro-latest": "Gemini 1.5 Pro model latest",
    "gemini-1.0-pro-latest": "Gemini 1.0 Pro model latest",
    "gemini-1.5-flash-latest": "Gemini 1.5 Flash model latest",
}

DEFAULT_PARAMS = {
    "default_model": VALID_MODELS["gemini-pro"],
    "default_temperature": 0.7,
    "default_max_tokens": 800,
}


class GeminiAgent:
    """
    Encapsulates logic for calling Google's Generative AI (Gemini).
    Allows easy overrides for model, temperature, and max_tokens.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ):
        load_dotenv()  # Loads environment variables from .env if present
        genai.configure(api_key=api_key or os.getenv("GOOGLE_API_KEY"))

        self.model_name = model or DEFAULT_PARAMS["default_model"]
        self.temperature = temperature if temperature is not None else DEFAULT_PARAMS["default_temperature"]
        self.max_tokens = max_tokens if max_tokens is not None else DEFAULT_PARAMS["default_max_tokens"]

        # Create the GenerativeModel instance
        self.model_instance = genai.GenerativeModel(model_name=self.model_name)

    def generate_response(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ) -> str:
        """
        Generates a response from Gemini using either instance defaults or
        the overrides provided. A new chat session is started each time.
        """

        used_model = model or self.model_name
        used_temperature = temperature if temperature is not None else self.temperature
        used_max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        # If a model override is provided, create a temporary model instance
        if used_model != self.model_name:
            temp_model = genai.GenerativeModel(model_name=used_model)
        else:
            temp_model = self.model_instance

        # Start a new chat with a system role or initial instruction
        chat = temp_model.start_chat(
            history=[{"role": "system", "parts": ["You are a helpful assistant."]}],
        )

        # Send the user's prompt
        response = chat.send_message(prompt, temperature=used_temperature, max_output_tokens=used_max_tokens)
        return response.text


def gemini_test(agent: Optional[GeminiAgent] = None):
    """
    Simple test function that demonstrates how to call the GeminiAgent.
    """
    if agent is None:
        agent = GeminiAgent()

    user_prompt = (
        "Determine the single key factor that maximizes overall value. "
        "Concentrate on the most critical element that, when addressed, "
        "yields the highest impact."
    )

    # Example usage
    response = agent.generate_response(prompt=user_prompt, model="gemini-1.5-pro-latest")
    print(f"response: {response}")


if __name__ == "__main__":
    gemini_test()

```
### 4. `openai_models.py`

#### `openai_models.py`

```python
import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv
from openai import OpenAI

VALID_MODELS = {
    "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
    "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
    "gpt-4": "Latest GPT-4 stable release",
    "gpt-4-0125-preview": "Preview GPT-4 Turbo",
    "gpt-4-0613": "June 2023 GPT-4 snapshot",
    "gpt-4-1106-preview": "Preview GPT-4 Turbo",
    "gpt-4-turbo": "Latest GPT-4 Turbo release",
    "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
    "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
    "gpt-4o": "Base GPT-4o model",
    "gpt-4o-mini": "Lightweight GPT-4o variant",
}

DEFAULT_PARAMS = {
    "default_model": "gpt-3.5-turbo-1106",
    "default_temperature": 0.7,
    "default_max_tokens": 800,
}


class OpenAIAgent:
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ):
        """
        Creates an agent to interact with OpenAI's ChatCompletion.
        By default, uses environment variables or fallback defaults.
        """
        load_dotenv()  # load environment variables from .env if present
        self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.model = model or DEFAULT_PARAMS["default_model"]
        self.temperature = temperature or DEFAULT_PARAMS["default_temperature"]
        self.max_tokens = max_tokens or DEFAULT_PARAMS["default_max_tokens"]

    def generate_response(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ) -> str:
        """
        Generates a response from the OpenAI ChatCompletion API using
        either the instance's settings or the overrides provided.
        """
        used_model = model or self.model
        used_temperature = temperature if temperature is not None else self.temperature
        used_max_tokens = max_tokens if max_tokens is not None else self.max_tokens

        response = self.client.chat.completions.create(
            model=used_model,
            temperature=used_temperature,
            max_tokens=used_max_tokens,
            messages=messages,
        )
        return response.choices[0].message.content


def openai_test(agent: Optional[OpenAIAgent] = None):
    """
    Simple test function, demonstrating how to call the agent
    and get a response from your chosen OpenAI model.
    """
    if agent is None:
        agent = OpenAIAgent()

    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": "Determine the single key factor that maximizes overall value. "
                       "Concentrate on the most critical element that, when addressed, "
                       "yields the highest impact.",
        },
    ]
    response = agent.generate_response(messages)
    print(f"response: {response}")


if __name__ == "__main__":
    openai_test()

```
### 5. `test.py`

#### `test.py`

```python
# sample_usage.py

# from anthropic_agent import AnthropicAgent
# from gemini_agent import GeminiAgent
from openai_models import OpenAIAgent

def main():
    # Example prompt for all models
    prompt = (
        "Determine the single key factor that maximizes overall value. "
        "Concentrate on the most critical element that, when addressed, "
        "yields the highest impact."
    )

    # # 1. Anthropic
    # anthropic_agent = AnthropicAgent(
    #     # Optionally override defaults here, e.g.:
    #     # api_key="YOUR_ANTHROPIC_KEY",
    #     # model="claude-3-opus-20240229"
    # )
    # anthropic_response = anthropic_agent.generate_response(prompt)
    # print(f"Anthropic Response:\n{anthropic_response}\n")

    # # 2. Gemini
    # gemini_agent = GeminiAgent(
    #     # Optionally override defaults, e.g.:
    #     # api_key="YOUR_GOOGLE_API_KEY",
    #     # model="gemini-1.5-pro-latest"
    # )
    # gemini_response = gemini_agent.generate_response(prompt)
    # print(f"Gemini Response:\n{gemini_response}\n")

    # 3. OpenAI
    openai_agent = OpenAIAgent(
        # Optionally override defaults, e.g.:
        # api_key="YOUR_OPENAI_API_KEY",
        # model="gpt-4"
    )
    openai_response = openai_agent.generate_response(
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt},
        ]
    )
    print(f"OpenAI Response:\n{openai_response}\n")

if __name__ == "__main__":
    main()

```
