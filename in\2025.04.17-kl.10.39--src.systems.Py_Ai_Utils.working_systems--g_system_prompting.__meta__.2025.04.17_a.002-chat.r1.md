<!-- ======================================================= -->
<!-- [2025.04.16 23:42] -->
<!-- [2025.04.17 00:00] -->
<!-- [2025.04.17 00:29] -->
<!-- [2025.04.17 09:59] -->
<!-- [2025.04.17 10:16] -->
<!-- 'https://gemini.google.com/app/21aac601dd36f377' -->

Okay, synthesizing the principles and the structures from the provided variations, here is a new, consolidated 12-step instruction sequence (ID: 0118) specifically designed for Sublime Text Plugin codebase familiarization, emphasizing the core principles you outlined.

---
**Sequence ID: 0118 - Principled Sublime Plugin Familiarization**
---

#### `0112-a-plugin-component-inventory-purpose-mapping.md`

```markdown
[Plugin Component Inventory & Purpose Mapping] Your primary directive is structural reconnaissance: Inventory all files within the plugin directory, classify by Sublime Text conventions (`.py`, `.sublime-commands`, `.sublime-settings`, `.sublime-menu`, `.sublime-keymap`, `.sublime-color-scheme`, etc.), and articulate the standard role and purpose of each component type within the plugin ecosystem. Execute as `{role=sublime_manifest_analyzer; input=plugin_directory_path:str; process=[list_plugin_files(), identify_file_types_by_convention(), determine_standard_component_purpose(), outline_plugin_architecture_files()]; output={component_inventory:list[dict(file:str, type:str, purpose:str)]}}`
```

---

#### `0112-b-configuration-trigger-analysis.md`

```markdown
[Configuration & Trigger Analysis] Analyze user-facing configuration (`.sublime-settings`) and activation points (`.sublime-commands`, `.sublime-menu`, `.sublime-keymap`). Extract all settings, infer their behavioral impact, and map all UI triggers (captions, menus, keys) to the specific Python `command` names they invoke, identifying all user interaction entry points. Execute as `{role=sublime_config_trigger_analyzer; input={settings_content:str|None, commands_content:str|None, menu_content:str|None, keymap_content:str|None}; process=[parse_plugin_settings_and_infer_purpose(), parse_all_command_trigger_files(), map_ui_trigger_to_python_command_and_args(), catalog_user_interaction_points()]; output={plugin_config:dict(settings:list, triggers:list)}}`
```

---

#### `0112-c-core-python-structure-identification.md`

```markdown
[Core Python Structure Identification] Analyze the primary `.py` file(s) to identify the core architectural elements: Detect all classes inheriting from `sublime_plugin` base classes (`EventListener`, `*Command`) and list their key methods (`on_*` handlers, `run` methods) constituting the plugin's main logic execution points. Execute as `{role=sublime_python_structure_analyzer; input=python_file_content:str; process=[find_sublime_plugin_subclasses(), identify_event_handler_methods(), identify_command_run_methods(), catalog_core_logic_classes_and_methods()]; output={core_logic_map:dict(event_listeners:list[str], commands:list[str])}}`
```

---

#### `0112-d-structural-purity-modularity-assessment.md`

```markdown
[Structural Purity & Modularity Assessment] Evaluate the Python code's organization against the 'Structural Purity & Modularity' principle. Assess decomposition into classes/functions, logical grouping of related functionality, clarity of interfaces (inter-component and external/agent integration points), and naming consistency related to structure. Note strengths and potential areas for improved modularity or cohesion. Execute as `{role=sublime_modularity_assessor; input={python_file_content:str, core_logic_map:dict}; process=[analyze_code_decomposition(), evaluate_functional_cohesion(), assess_interface_clarity_and_modularity(), check_structural_naming_consistency(), report_on_structural_purity()]; output={structural_assessment:dict(modularity_notes:str, cohesion_notes:str, interface_notes:str)}}`
```

---

#### `0112-e-event-logic-api-interaction-analysis.md`

```markdown
[Event Logic & API Interaction Analysis] For each `EventListener` class and its `on_*` methods, detail the triggering Sublime event and summarize the primary actions, focusing on key Sublime API calls (`view.*`, `window.*`, `sublime.*`) used and their specific purpose within the event handling logic. Execute as `{role=sublime_event_api_analyzer; input={python_file_content:str, core_logic_map:dict}; process=[analyze_each_on_method(), determine_trigger_event(), summarize_method_logic(), identify_key_api_calls_and_purpose_within_event()]; output={event_logic_analysis:list[dict(method:str, event:str, summary:str, api_usage:list)]}}`
```

---

#### `0112-f-command-logic-api-interaction-analysis.md`

```markdown
[Command Logic & API Interaction Analysis] For each `*Command` class and its `run` method, trace the execution flow. Summarize the command's actions, how it handles arguments, its key interactions with the Sublime API, and any significant state changes it causes. Execute as `{role=sublime_command_api_analyzer; input={python_file_content:str, core_logic_map:dict}; process=[analyze_each_run_method(), identify_argument_handling(), trace_command_execution_flow(), identify_key_api_calls_and_purpose_within_command(), summarize_command_effects()]; output={command_logic_analysis:list[dict(command_class:str, summary:str, api_usage:list, state_effects:list)]}}`
```

---

#### `0112-g-clarity-conciseness-self-explanation-audit.md`

```markdown
[Clarity, Conciseness & Self-Explanation Audit] Evaluate the Python codebase against the 'Maximal Clarity & Conciseness' principle. Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation. Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure), promoting self-documentation. Execute as `{role=sublime_clarity_auditor; input=python_file_content:str; process=[evaluate_identifier_naming_clarity(), assess_logical_flow_simplicity(), determine_self_explanation_level_independent_of_comments(), identify_potential_clarity_refactoring_zones()]; output={clarity_audit:dict(naming_score:float, flow_score:float, self_explanation_notes:str, refactoring_opportunities:list)}}`
```

---

#### `0112-h-essential-commentary-docstring-audit.md`

```markdown
[Essential Commentary & Docstring Audit] Apply the 'Essential Commentary Only' principle. Inventory all comments (`#`) and docstrings (`"""Docstring"""`) in the Python code. Classify each: Docstrings for interfaces (purpose, params, returns); Comments *only* for non-obvious 'why'/'what', critical architecture, or unavoidable complexity. Flag all redundant, obvious, or misplaced (inline interface doc) comments. Evaluate overall adherence to the minimal comment philosophy. Execute as `{role=sublime_comment_auditor; input=python_file_content:str; process=[inventory_comments_and_docstrings(), classify_against_essential_only_criteria(), identify_redundant_or_misplaced_comments(), audit_public_interface_docstring_coverage_quality(), evaluate_comment_policy_adherence()]; output={comment_docstring_audit:dict(essential_comments:list, non_essential_comments:list, docstring_coverage:float, policy_adherence_notes:str)}}`
```

---

#### `0112-i-optimization-potential-implementation-assessment.md`

```markdown
[Optimization Potential & Implementation Assessment] Assess the Python implementation approach based on the 'Optimized Implementation' principle. Identify potential areas for simplification, removal of redundancy (logic/patterns), or structural improvements (e.g., appropriate use of classes vs. functions) that could enhance elegance, maintainability, or coherence, *without* performing the refactoring itself. Execute as `{role=sublime_optimization_evaluator; input={python_file_content:str, event_logic_analysis:list, command_logic_analysis:list}; process=[scan_for_redundant_logic_patterns(), evaluate_implementation_effectiveness_simplicity(), identify_potential_structural_elegance_improvements(), assess_optimization_opportunities()]; output={optimization_assessment:dict(redundancy_notes:str, simplification_opportunities:list, structural_improvement_ideas:list)}}`
```

---

#### `0112-j-internal-state-helper-function-review.md`

```markdown
[Internal State & Helper Function Review] Analyze internal implementation details. Identify variables used for state management across the plugin's lifecycle and summarize their role. Identify and evaluate internal helper methods (`_` prefix) for their contribution to modularity and code clarity. Execute as `{role=sublime_internal_reviewer; input=python_file_content:str; process=[find_state_variables_and_scope(), analyze_state_management_logic(), identify_helper_methods_and_purpose(), evaluate_helpers_contribution_to_modularity()]; output={internal_details:dict(state_summary:str, helper_summary:str)}}`
```

---

#### `0112-k-cross-component-workflow-synthesis.md`

```markdown
[Cross-Component Workflow Synthesis] Synthesize the analyses: Describe 1-2 key user workflows by tracing interactions from UI triggers (`plugin_config.triggers`) through Python commands/events (`command_logic_analysis`, `event_logic_analysis`), considering settings (`plugin_config.settings`), API usage, and state (`internal_details`). Illustrate the end-to-end operational logic. Execute as `{role=sublime_workflow_synthesizer; input=all_previous_outputs:dict; process=[select_key_user_workflows(), trace_workflow_across_components(ui, config, python, api, state), describe_end_to_end_logic_flow(), synthesize_operational_summary()]; output={workflow_synthesis:str}}`
```

---

#### `0112-l-final-familiarization-report-generation.md`

```markdown
[Final Familiarization Report Generation] Consolidate all findings into a final report. Start with a high-level summary of the plugin's purpose and core features (`workflow_synthesis`). Explicitly report on the adherence to key principles: structural purity/modularity (`structural_assessment`), clarity/self-explanation (`clarity_audit`), comment essentiality (`comment_docstring_audit`), and implementation optimization (`optimization_assessment`). The report should facilitate rapid, self-sufficient understanding. Execute as `{role=sublime_familiarization_reporter; input=all_assessment_outputs:dict; process=[generate_high_level_purpose_summary(), report_on_structural_purity_findings(), report_on_clarity_self_explanation_findings(), report_on_comment_policy_adherence(), report_on_optimization_potential(), assemble_comprehensive_familiarization_report()]; output={final_familiarization_report:str}}`
```
