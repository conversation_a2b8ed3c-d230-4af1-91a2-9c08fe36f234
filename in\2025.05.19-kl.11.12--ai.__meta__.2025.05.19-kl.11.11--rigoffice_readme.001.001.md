Please create a single README.md based on taking the best from each part of these variations:

    # Dir `readme_variations`

    ### File Structure

    ```
    ├── README_v1.md
    ├── README_v2.md
    ├── README_v3.md
    ├── README_v4.md
    ├── README_v5.md
    ├── README_v6.md
    └── README_v7.md
    ```

    ---

    #### `README_v1.md`

    ```markdown
        # RigOfficeDownloader

        ## Overview
        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

        ## Key Features
        - Three-stage workflow: Documents -> Files -> Downloads
        - Interactive menu for flexible execution
        - User control points via Markdown interfaces
        - Smart file organization with subfolder support
        - Configurable filter chains

        ## Setup & Usage
        1. Run `py_venv_init.bat` to create the Python environment
        2. Run `RigOfficeDownloader-v4.bat` to start the application
        3. Use the interactive menu to configure and execute workflow steps

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Preserves document context and relationships

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser (for Selenium automation)
    ```

    ---

    #### `README_v2.md`

    ```markdown
        # RigOfficeDownloader

        ## The Problem
        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
        - Tedious and repetitive
        - Error-prone
        - A poor use of skilled engineering time

        ## The Solution
        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
        1. **Document Retrieval**: Automatically scrapes document metadata
        2. **File Metadata**: Fetches file information for selected documents
        3. **Smart Downloads**: Downloads files with intelligent naming and organization

        ## How It Works
        - Uses Selenium to automate web interactions with RigDoc
        - Exports data to Markdown for user review and selection
        - Applies configurable filters to pre-select relevant documents
        - Organizes downloads with consistent naming patterns

        ## Getting Started
        1. Run `py_venv_init.bat` to set up the environment
        2. Run `RigOfficeDownloader-v4.bat` to launch the application
        3. Follow the interactive menu prompts

        ## Impact
        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.
    ```

    ---

    #### `README_v3.md`

    ```markdown
        # RigOfficeDownloader

        ## Technical Overview
        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

        ## Architecture
        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown
        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads
        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

        ## Workflow Steps
        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information
        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing
        3. **Selection Import**: `md_table_to_json()` imports user selections
        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents
        5. **File Selection**: Export/import cycle for user selection of files
        6. **Download Process**: `download_files()` retrieves selected files with smart naming

        ## Filter Chain System
        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

        ## Setup Instructions
        1. Run `py_venv_init.bat` to initialize the Python environment
        2. Run `RigOfficeDownloader-v4.bat` to execute the application

        ## Version History
        - v1: Basic document retrieval and download
        - v2: JSON/Markdown conversion and user selection
        - v3: Improved error handling and field organization
        - v4: Subfolder support, filter chains, field ordering
    ```

    ---

    #### `README_v4.md`

    ```markdown
        # RigOfficeDownloader
        > Automate document retrieval from NOV's RigDoc system

        ## What This Tool Does
        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

        ## Quick Start Guide
        1. **Setup**: Run `py_venv_init.bat` to create the Python environment
        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application
        3. **Configure**: Enter your rig number and search URLs when prompted
        4. **Run**: Follow the numbered menu to execute each step of the workflow

        ## Workflow Steps Explained
        0. **Change Parameters**: Update rig number and search URLs
        1. **Configure Filters**: Set up automatic document/file selection rules
        2. **Fetch Documents**: Retrieve document metadata from RigDoc
        3. **Review Documents**: Edit the Markdown file to select which documents to process
        4. **Import Selections**: Load your document selections
        5. **Fetch Files**: Get file metadata for selected documents
        6. **Review Files**: Edit the Markdown file to select which files to download
        7. **Import File Selections**: Load your file selections
        8. **Download Files**: Retrieve the selected files

        ## Tips for Success
        - Use filters to automatically pre-select relevant documents
        - Review the Markdown files carefully before proceeding to the next step
        - Files will be organized in subfolders based on '/' in their generated names

        ## Need Help?
        Check the source code comments for detailed information about each function and workflow step.
    ```

    ---

    #### `README_v5.md`

    ```markdown
        # RigOfficeDownloader

        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

        ```
        Documents -> Files -> Downloads
        ```

        ## Features
        - Three-stage workflow with user control points
        - Interactive menu for flexible execution
        - Configurable filter chains for automatic selection
        - Smart file organization with subfolder support
        - Markdown interfaces for document/file review

        ## Quick Start
        ```
        1. Run py_venv_init.bat
        2. Run RigOfficeDownloader-v4.bat
        3. Follow the interactive menu
        ```

        ## Benefits
        - Reduces documentation gathering time by 75%+
        - Maintains consistent file organization
        - Provides user control at key decision points
        - Handles errors gracefully

        ## Requirements
        - Windows OS
        - Python 3.6+
        - Chrome browser
    ```

    ---

    #### `README_v6.md`

    ```markdown
        # RigOfficeDownloader

        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.

        ## Key Features
        - **Three-Stage Workflow**: Document selection → File selection → Download
        - **Metadata Preservation**: Structured naming with revision/case numbers
        - **Interactive Review**: Markdown tables for manual inclusion flags
        - **Smart Organization**: Automatic subfolder creation via naming patterns
        - **Filter System**: Pattern-based inclusion/exclusion rules
        - **Browser Automation**: Smart waiting strategies and session management

        ## Workflow Process
        ```python
        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json
        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true
        3. Import Selections     # Update JSON with user choices
        4. Fetch Files           # Get file listings → <rig>-b-files.json
        5. Export Files MD       # <rig>-b-files.md - Set item_download=true
        6. Import File Choices   # Update file selections
        7. Download Files        # Auto-organized with subfolder support
        ```

        ## Configuration (CONFIG Section)
        ```python
        {
            "rig_number": "R0000.020",  # Target rig ID
            "search_urls": [            # Predefined search templates
                "https://rigdoc.nov.com/search/rigsearch?q=...",
                "https://rigdoc.nov.com/search/rigsearch?q=..."
            ],
            "filters": [                # Sequential processing rules
                {
                    "type": "docs",     # Apply to documents/files
                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching
                    "field": "item_include",     # Field to modify
                    "value": True       # Set True/False based on match
                }
            ]
        }
        ```

        ## Setup & Usage
        ```bash
        # Initialize environment
        py_venv_init.bat
        py_venv_pip_install.bat

        # Run modes
        RigOfficeDownloader-v4.py [rig_number] [mode]

        Modes:
        --auto         # Full automation
        --interactive  # Step-by-step control
        --config       # Modify search templates/filters
        ```

        ## Key Configuration Patterns
        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)
        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)
        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern

        ## Requirements
        - Chrome Browser + ChromeDriver
        - Active RigDoc credentials
        - Python 3.8+ with dependencies from requirements.txt
        - Network access to rigdoc.nov.com

        ## Advanced Features
        - **Path Sanitization**: Auto-clean special chars while preserving /
        - **Deduplication**: Hash-based conflict resolution
        - **Field Ordering**: Customizable JSON/Markdown columns
        - **Smart Scrolling**: Progressive page loading detection

        > Reduces documentation prep time by 60-75% compared to manual retrieval
        > Version 4.0 | Active development with subfolder support
    ```

    ---

    #### `README_v7.md`

    ```markdown

        ## Overview
        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.
        - Use case: curate, review, and batch-download rig-related documents and technical files.

        ## Directory Structure

        * `outputs/` (BASE\_OUTPUT): All results stored here
        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)
        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

        ## Pipeline Overview

        1. Change search parameters (rig number, URLs)
        2. Configure filter chain (add, edit, delete, toggle, reorder filters)
        3. Fetch docs (scrape data from rigdoc.nov.com)
        4. Export docs to Markdown (for selection/editing)
        5. Import docs from Markdown (sync edited selection)
        6. Fetch candidate files linked to selected docs
        7. Export file list to Markdown (for editing/selecting files for download)
        8. Import updated file list from Markdown
        9. Download marked files (PDFs only, via Chrome)

        ## Manual Editing

        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
        * Set `item_include` (docs) and `item_download` (files) fields

        ## Running the Tool

        ```bash
        python rigdocscraper.py
        ```

        * Interactive menu enables step selection (numbers/comma/space-separated)
        * Supports adjusting parameters, filter configuration, and reviewing batch steps
        * Prompts will guide through editing, import/export, and download procedures

        ## Troubleshooting

        * Requires functioning Chrome installation; verify webdriver-manager compatibility
        * Common issues: browser launch failures, login/captcha requirements, file permissions
        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress
    ```

