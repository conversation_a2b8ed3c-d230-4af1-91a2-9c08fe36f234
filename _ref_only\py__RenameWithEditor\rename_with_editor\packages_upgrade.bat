@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: Packages Upgrade Tool
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  Packages Upgrade Tool
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Show current package versions
ECHO [INFO] Current package versions:
uv run python -c "import filetype, loguru, rich, tqdm, xxhash; print(f'filetype: {filetype.__version__}'); print(f'loguru: {loguru.__version__}'); print(f'rich: {rich.__version__}'); print(f'tqdm: {tqdm.__version__}'); print(f'xxhash: {xxhash.__version__}')" 2>nul || ECHO Some packages not accessible
ECHO.

:: Upgrade all packages
ECHO [INFO] Upgrading all packages to latest versions...
uv sync --upgrade

IF ERRORLEVEL 1 (
    ECHO [WARNING] uv sync failed, trying alternative method...
    uv pip install --upgrade filetype loguru rich tqdm xxhash
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to upgrade packages
        PAUSE>NUL & EXIT /B
    ) ELSE (
        ECHO [SUCCESS] Packages upgraded successfully
    )
) ELSE (
    ECHO [SUCCESS] Packages upgraded successfully
)

:: Show new versions
ECHO.
ECHO [INFO] New package versions:
uv run python -c "import filetype, loguru, rich, tqdm, xxhash; print(f'filetype: {filetype.__version__}'); print(f'loguru: {loguru.__version__}'); print(f'rich: {rich.__version__}'); print(f'tqdm: {tqdm.__version__}'); print(f'xxhash: {xxhash.__version__}')"

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
