<!-- =============================================================================

# Urls:
# ===
- "ai-prompt-generator","https://www.feedough.com/ai-prompt-generator/"
- "prompt-clarity-checker","https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/67388dff-af98-8008-afaf-76668b24a938"
- "prompt-clarity-checker","https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/6738a0fe-bb88-8008-8a6f-bd75b8ab30c6"
- "prompt-clarity-checker","https://chatgpt.com/g/g-aDBT0YUU3-prompt-clarity-checker/c/6731fdfe-565c-8008-8017-b6b0e7c225f0"
- "gpt-o1-preview","https://chatgpt.com/c/6738aa2d-bff8-8008-8241-4c1522e1e433"

============================================================================= -->

Please evaluate this code, specifically with respect to optimized inference between the agents:
```python
import os
import logging
import asyncio
import json
import time
from typing import List, Dict, Optional, Union
import numpy as np
from openai import OpenAI

# Set up logging configuration
logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# --- Initialize OpenAI Client ---
def init_openai_client() -> Optional[OpenAI]:
    """Initializes the OpenAI client using the API key from environment variables."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# --- Memory Manager Class ---
class MemoryManager:
    """Manages the storage and retrieval of text embeddings for contextual memory."""

    def __init__(self):
        self.memory_store = []

    def add_memory(self, text: str, embedding: np.ndarray):
        """Adds a text and its embedding to the memory store."""
        self.memory_store.append({"text": text, "embedding": embedding})

    def retrieve_relevant_context(self, query: str, top_k: int = 3) -> List[str]:
        """Retrieves relevant context texts based on similarity to the query."""
        query_embedding = self.get_embedding(query)
        similarities = [
            (self.cosine_similarity(query_embedding, mem["embedding"]), mem["text"])
            for mem in self.memory_store
        ]
        similarities.sort(reverse=True, key=lambda x: x[0])
        return [text for _, text in similarities[:top_k]]

    def get_embedding(self, text: str) -> np.ndarray:
        """Generates an embedding for the given text."""
        try:
            response = client.embeddings.create(input=text, model="text-embedding-ada-002")
            return np.array(response.data[0].embedding)
        except Exception as e:
            logging.error(f"Embedding generation failed: {e}")
            if self.memory_store:
                logging.info("Using average of existing embeddings as fallback.")
                embeddings = [mem["embedding"] for mem in self.memory_store]
                return np.mean(embeddings, axis=0)
            return np.zeros(1536)

    @staticmethod
    def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
        """Calculates the cosine similarity between two embeddings."""
        if np.linalg.norm(a) == 0 or np.linalg.norm(b) == 0:
            return 0.0
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

# --- Blueprint Manager Class ---
class BlueprintManager:
    """Manages the creation of the guiding blueprint for the refinement process."""

    def create_blueprint(self, initial_input: str, mode: str, desired_length: str = None, desired_format: str = None) -> str:
        """Creates a blueprint based on the initial input and user preferences."""
        mode_descriptions = {
            "Prompt Generation": "focused on generating concise, impactful prompts.",
            "Content Creation": "focused on detailed, structured content development.",
            "User Guidance": "focused on providing clear, actionable guidance for end-users.",
            "General": "balanced, high-quality response for general use."
        }
        mode_description = mode_descriptions.get(mode, "balanced response")
        length_description = f"Desired Length: {desired_length}\n" if desired_length else ""
        format_description = f"Desired Format: {desired_format}\n" if desired_format else ""

        return (f"Guiding Blueprint:\n"
                f"Mode: {mode} - {mode_description}\n"
                f"Topic: {initial_input}\n"
                f"{length_description}"
                f"{format_description}"
                f"Objective: Enhance the response for clarity, coherence, relevance, and usability.\n"
                f"Structure: Dynamic and adaptive refinement based on input analysis.\n"
                f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# --- Quality Evaluator Class ---
class QualityEvaluator:
    """Evaluates the quality of responses based on specified criteria."""

    def assess_quality(self, response: str) -> Dict[str, float]:
        """Assesses the response and returns scores for clarity, coherence, style, and correctness."""
        evaluation_prompt = (
            f"Evaluate the following response on a scale from 0 to 1 for: clarity, coherence, style, correctness.\n\n"
            f"Response:\n{response}\n\n"
            "Please provide the results in valid JSON format without any additional text. The JSON should be in the following format:\n"
            '{"clarity": 0.0, "coherence": 0.0, "style": 0.0, "correctness": 0.0}'
        )
        evaluation = get_completion(evaluation_prompt)
        try:
            evaluation_scores = json.loads(evaluation)
            return evaluation_scores
        except json.JSONDecodeError:
            logging.warning("Evaluation failed. Using default scores.")
            return {"clarity": 0.5, "coherence": 0.5, "style": 0.5, "correctness": 0.5}

# --- Agent Processor Class ---
class AgentProcessor:
    """Processes agents to refine responses based on the guiding blueprint."""

    def __init__(self, memory_manager: MemoryManager, blueprint: str):
        self.memory_manager = memory_manager
        self.blueprint = blueprint

    async def generate_prompt(self, agent_prompt: str, current_response: str, iteration: int, user_feedback: str = "") -> str:
        """Generates the prompt for the agent, including the blueprint, user feedback, and current response."""
        feedback_section = f"User Feedback:\n{user_feedback}\n\n" if user_feedback else ""
        prompt = (
            f"{self.blueprint}\n\n"
            f"{feedback_section}"
            f"{agent_prompt}\n\n"
            f"Current Response:\n{current_response}\n\n"
            f"Refined Response:"
        )
        return prompt

    async def apply_agent(self, agent: Dict[str, str], current_response: str, iteration: int, user_feedback: str = "") -> str:
        """Applies an agent to refine the current response."""
        prompt = await self.generate_prompt(agent['prompt'], current_response, iteration, user_feedback)
        refined_response = await get_completion_async(prompt)
        if refined_response:
            logging.info(f"Agent '{agent['role']}' successfully refined the response.")
            # Update memory with the refined response
            embedding = self.memory_manager.get_embedding(refined_response)
            self.memory_manager.add_memory(refined_response, embedding)
            return refined_response
        else:
            logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
            return current_response

# --- Get Completion Function with retries and exponential backoff ---
def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """Calls the OpenAI API to get a completion for the given prompt with retries and exponential backoff."""
    delay = 1  # Initial delay in seconds
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
            time.sleep(delay)
            delay *= 2  # Exponential backoff
    return None

async def get_completion_async(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    """Asynchronous wrapper for get_completion."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, get_completion, prompt, model)


def get_refinement_chain() -> Dict[str, List[Dict[str, str]]]:
    """
    Returns a dictionary of grouped agent chains, each focused on a specific phase of the refinement process.
    Tailored for optimizing project structure, coding style, and clean, maintainable code for the scenario.
    """
    return {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint outlining the project structure, coding style, and commenting strategy tailored to the scenario. Include modularity, maintainability, and subjective preferences."},
            {"role": "Objective Setter", "prompt": "Define the primary objective of optimizing the project, focusing on clean, maintainable code and a scalable structure."},
            {"role": "Scope Clarifier", "prompt": "Clarify the scope and limitations of the current project to ensure focus and alignment in the refinement process."}
        ],

        "Prompt Reformulation Group": [
            {"role": "Inquiry Formulator", "prompt": "Rephrase the scenario as a comprehensive question that prompts detailed, actionable steps for optimizing the project."},
            {"role": "Objective Clarifier", "prompt": "Identify the key objectives from the scenario and ensure they are reflected clearly in the reformulated prompt."},
            {"role": "Response Categorizer", "prompt": "Categorize the reformulated prompt by focus areas such as 'modularity,' 'maintainability,' or 'readability,' to guide downstream agents."},
            {"role": "Consistency Reinforcer", "prompt": "Ensure the reformulated question aligns with the Blueprint and is consistent with the scenario's goals."},
            {"role": "Structure and Formatting Checker", "prompt": "Ensure the reformulated prompt is structured clearly and adheres to Blueprint standards for readability and clarity."}
        ],

        "Core Content Development Group": [
            {"role": "Context Enhancer", "prompt": "Add background information relevant to Python project structuring and clean coding practices to enrich the response."},
            {"role": "Structure Architect", "prompt": "Propose an optimal folder and file structure for the project, ensuring modularity and maintainability."},
            {"role": "Commenting Strategist", "prompt": "Define a commenting strategy for the project that balances brevity and clarity, focusing on high-value comments for complex logic."},
            {"role": "Modularity Strategist", "prompt": "Identify repetitive patterns in the existing code and propose modular solutions to improve maintainability."},
            {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint's structure, objectives, and key points throughout this phase."}
        ],

        "Quality and Standards Assurance Group": [
            {"role": "Standards Enforcer", "prompt": "Ensure the proposed solutions adhere to PEP 8, SOLID principles, and maintain a balance between readability and efficiency."},
            {"role": "Error Handling Specialist", "prompt": "Evaluate the code for robust error handling, especially for API calls and file operations, and propose improvements."},
            {"role": "Testing Strategist", "prompt": "Suggest a testing strategy for the project, including unit tests for core functionalities."},
            {"role": "Security Auditor", "prompt": "Review the code for potential vulnerabilities and propose measures to enhance security."},
            {"role": "Consistency Reinforcer", "prompt": "Ensure that the refinements maintain consistency with the Blueprint and the project’s overarching goals."}
        ],

        "User Experience and Readability Group": [
            {"role": "User Experience Optimizer", "prompt": "Optimize the project for usability and ease of navigation, focusing on intuitive folder structures and clear documentation."},
            {"role": "Logic Validator", "prompt": "Validate the logical flow of the proposed project structure and coding style, ensuring coherence and ease of understanding."},
            {"role": "Clarity Balancer", "prompt": "Simplify complex code where possible and scale only as necessary to retain clarity and maintainability."},
            {"role": "Topic Consistency Tracker", "prompt": "Review the entire project for alignment with the initial goals and objectives as defined in the Blueprint."},
            {"role": "Structure and Formatting Checker", "prompt": "Ensure the project structure and formatting align with industry best practices and the Blueprint’s standards."}
        ],

        "Final Optimization and Documentation Group": [
            {"role": "Performance Optimizer", "prompt": "Optimize the code for performance without sacrificing clarity or maintainability."},
            {"role": "Documentation Curator", "prompt": "Provide clear, concise documentation for the project, including instructions for future developers to extend the project."},
            {"role": "Transparency Advocate", "prompt": "Identify limitations of the current structure and coding style, and provide recommendations for future enhancements."},
            {"role": "Topic Consistency Tracker", "prompt": "Perform a final review to ensure all refinements align with the scenario's goals and the Blueprint."},
            {"role": "Iterative Refiner", "prompt": "Apply final refinements to ensure the project is clean, efficient, and adheres to all specified standards."}
        ]
    }


# --- Execution and Refinement ---
async def execute_refinement_pipeline(initial_input: str, mode: str, memory_manager: MemoryManager, max_iterations: int = 5):
    """Executes the refinement pipeline and dynamically adjusts based on feedback."""
    blueprint_manager = BlueprintManager()
    blueprint = blueprint_manager.create_blueprint(initial_input, mode)

    agent_processor = AgentProcessor(memory_manager, blueprint)

    # Create refinement chain
    refinement_chain = get_refinement_chain()
    current_response = initial_input

    for iteration in range(max_iterations):
        for group, agents in refinement_chain.items():
            for agent in agents:
                current_response = await agent_processor.apply_agent(agent, current_response, iteration)

        # Evaluate the response
        quality_evaluator = QualityEvaluator()
        quality_scores = quality_evaluator.assess_quality(current_response)
        if all(score >= 0.8 for score in quality_scores.values()):
            logging.info("Quality threshold met, finalizing response.")
            break

    print(f"\nFinal Output:\n{current_response}")

async def main():
    initial_input = "Define an optimal project structure and coding style for a Python project."
    mode = "Content Creation"
    memory_manager = MemoryManager()
    await execute_refinement_pipeline(initial_input, mode, memory_manager)

if __name__ == "__main__":
    asyncio.run(main())
```




<!-- =============================================================================

# Urls:
# ===
- "gpt-o1-preview","https://chatgpt.com/c/6738aa2d-bff8-8008-8241-4c1522e1e433"

============================================================================= -->

In order for you to be able to take the correct context, let me give you a specific scenario. Your task in this, is to understand the implications of the scenario (provided below) to such degree that you can determine how the requirements of this scope affects our specific framework/flow of the agents/interactions. Your ultimate goal will be to determine how our approach best can transformed into a *point of departure*.

# Scenario:
You are an expert Python developer with over 23 years of experience specializing in optimizing project structures, enhancing coding styles, and writing clean, efficient code. You balance readability with functionality and add concise, insightful comments that clarify the code without overloading it. You adapt best practices to individual preferences, fostering a productive and maintainable coding environment. Your task is to help me define and set up an optimal project structure and coding style for the a Python project (the existing project will be provided to you after you confirm that you accept the task).

Details about the project:
- Project Name: `py__MyDataRetriever`
- Project Description: `Scripts for fetching and retrieving my personal online data, such as emails, youtube-logs/playlists, etc. This is a subcomponent of a larger framework of mine used to automate all of my digital and personal life. These scripts will run automatically at regular intervals to ensure i'm always up-to-date, which i then interact upon and run rules that "filters" the data to automate the process of generating my own personal feed. This will work as a way to "interface" my personal life with the vast digital landscape. Instead of manually having to scroll through large amounts of misdirected algorithms and distractions, i instead automate the retrieval-process such that i can re-direct it. I can always define new rules (e.g. retrieving youtube-updates *only* from a certain set of account), and gain immediate value when implementing the rule.`
- Existing Issues or Challenges: `Project has been written without any specific guidelines or any consideration to consistency and flexibility, and has turned into something unorganized.`
- Specific Requirements or Preferences: `Striking the exact balance between comments and code, the comments should be *short* and straight-forward, and should be written in a concistent manner. The comment should *not* be overly explicit or contain unneccessary phrasing, but should be precice and "high-value". Similar sections, subsections, groups and category of code should share codingstyle and commentstyle when *similar*.`
- Bias: `Provide guidance on how to structure the folders and files within the project to ensure clarity and ease of maintenance, as well as best practices for writing clean code within this context. Ensure the code is well-structured and only include brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.`
- Core: `Always lay a foundation worthy of building upon. This is a rule you live by, because it’s the difference between chaos and clarity. **All you do is point of departure**.`
- Reminder: `Always the stage for success. It results in workspaces others can seamlessly take over, not only will this remove friction, but it is also *helpful* in the way for others easily can be exposed to and learn from *your* definitions.`
- Project's Current File Structure:
```
[-] ├── .gitignore
[ ] └── gmail_exporter
[-] │   ├── credentials.json
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_jh_paulsen_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_kimtuvsjoen_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com.csv
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com.json
[-] │   ├── emails_from_y_engbraten_gmail_com_to_jh_paulsen_gmail_com_processed.txt
[ ] │   ├── fetch_emails.py
[-] │   ├── token.json
[ ] ├── main.py
[-] ├── py__MyDataRetriever.sublime-project
[-] ├── requirements.txt
```


### fetch_emails.py

```python
# Function: Gmail Email Fetcher
# =======================================================
# Fetch emails from a Gmail account using the Gmail API. The script allows filtering
# by sender or recipient, supports saving results to CSV and JSON files, and tracks
# processed emails to avoid duplicates.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
import os
import re
import csv
import json
import argparse
from dotenv import load_dotenv
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
CLIENT_SECRET_FILE = os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json")
TOKEN_FILE = os.getenv("GOOGLE_TOKEN_FILE", "token.json")
SCOPES = os.getenv("GOOGLE_SCOPES", "https://www.googleapis.com/auth/gmail.readonly").split()

# Configure loguru
# -------------------------------------------------------
logger.remove()
logger.add("email_fetcher.log", rotation="10 MB", retention="10 days", level="INFO")

# Authentication
# =======================================================
def authenticate():
    """ Authenticate and retrieve credentials for Gmail API. """
    creds = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            logger.info("Credentials refreshed.")
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New credentials obtained.")
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())
            logger.info("Credentials saved to token file.")
    return creds


# Function: Generate Filename
# =======================================================
def generate_filename(base_name, sender=None, recipient=None, suffix="", file_type="csv"):
    """ Generate a consistent filename based on sender, recipient, and suffix. """
    parts = [base_name]
    if sender:
        parts.append(f"from_{sender.replace('@', '_').replace('.', '_')}")
    if recipient:
        parts.append(f"to_{recipient.replace('@', '_').replace('.', '_')}")
    if suffix:
        parts.append(suffix)
    return f"{'_'.join(parts)}.{file_type}"


# Function: Append Emails to CSV
# =======================================================
def append_emails_to_csv(emails, file_path):
    """ Append a list of emails to a CSV file. """
    with open(file_path, mode="a", newline="", encoding="utf-8-sig") as file:
        writer = csv.writer(file)
        for email in emails:
            writer.writerow(
                [
                    email["date"],
                    email["from"],
                    email["to"],
                    email["subject"],
                    email["snippet"],
                ]
            )


# Function: Append Emails to JSON
# =======================================================
def append_emails_to_json(emails, file_path):
    """ Append a list of emails to a JSON file. """
    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump([], file)
    with open(file_path, "r+", encoding="utf-8") as file:
        existing_data = json.load(file)
        existing_data.extend(emails)
        file.seek(0)
        json.dump(existing_data, file, ensure_ascii=False, indent=4)


# Function: Load Processed Emails
# =======================================================
def load_processed_emails(file_path):
    """ Load the list of already processed email IDs. """
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return set(f.read().splitlines())
    return set()


# Function: Save Processed Email ID
# =======================================================
def save_processed_email(email_id, file_path):
    """ Append an email ID to the processed emails file. """
    with open(file_path, "a") as f:
        f.write(email_id + "\n")


# Function: Safe Strip
# =======================================================
def safe_strip(value):
    """ Safely strip a value; returns an empty string if None. """
    return value.strip() if value else ""


# Function: Get Header Value
# =======================================================
def get_header_value(headers, name):
    """ Retrieve the value of a specific header by its name. """
    for header in headers:
        if header["name"].lower() == name.lower():
            return header["value"]
    return None


# Function: Clean Sender
# =======================================================
def clean_sender(sender):
    """ Standardize the format of the sender field. """
    match = re.match(r'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)', sender)
    if match:
        name, email = match.groups()
        return f"{name.strip()} <{email.strip()}>" if name else f"{email.strip()}"
    return sender


# Function: Fetch Emails
# =======================================================
def fetch_emails(sender=None, recipient=None, max_results=10):
    """ Fetch emails from Gmail based on sender and recipient filters. """
    creds = authenticate()
    service = build("gmail", "v1", credentials=creds)

    # Prepare file paths
    base_name = "emails"
    csv_file_path = generate_filename(base_name, sender, recipient, file_type="csv")
    json_file_path = generate_filename(base_name, sender, recipient, file_type="json")
    processed_emails_file = generate_filename(base_name, sender, recipient, suffix="processed", file_type="txt")
    processed_emails = load_processed_emails(processed_emails_file)

    # Build query string and fetch emails
    query_string = build_query(sender, recipient)
    total_fetched = 0
    next_page_token = None


    with tqdm(total=max_results, desc="Fetching Emails", unit="email") as pbar:
        while total_fetched < max_results:
            messages, next_page_token = fetch_message_ids(
                service, query_string, max_results - total_fetched, next_page_token
            )
            if not messages:
                logger.info(f"No more emails found matching query: {query_string}")
                break
            emails = process_messages(service, messages, processed_emails, processed_emails_file)
            save_emails(emails, csv_file_path, json_file_path)
            fetched = len(emails)
            total_fetched += fetched
            logger.info(f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}")
            pbar.update(fetched)
            if not next_page_token:
                break
    logger.success(f"Finished fetching {total_fetched} emails.")


# Function: Build Query
# =======================================================
def build_query(sender, recipient):
    """ Construct a Gmail search query string. """
    query = []
    if sender:
        query.append(f"from:{sender}")
    if recipient:
        query.append(f"to:{recipient}")
    return " ".join(query)


# Function: Fetch Message IDs
# =======================================================
def fetch_message_ids(service, query_string, max_results, next_page_token):
    """ Fetch message IDs matching the query string. """
    result = (
        service.users()
        .messages()
        .list(
            userId="me",
            q=query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        )
        .execute()
    )
    return result.get("messages", []), result.get("nextPageToken")


# Function: Process Messages
# =======================================================
def process_messages(service, messages, processed_emails, processed_emails_file):
    """ Retrieve and process messages by fetching details like subject and sender. """
    emails = []
    for message in messages:
        email_id = message["id"]
        if email_id in processed_emails:
            continue
        msg = service.users().messages().get(userId="me", id=email_id).execute()
        headers = msg["payload"]["headers"]
        email_data = {
            "date": safe_strip(get_header_value(headers, "Date")),
            "from": safe_strip(clean_sender(get_header_value(headers, "From"))),
            "to": safe_strip(clean_sender(get_header_value(headers, "To")))
            if get_header_value(headers, "To")
            else "",
            "subject": safe_strip(get_header_value(headers, "Subject")),
            "snippet": safe_strip(msg.get("snippet", "")),
        }
        emails.append(email_data)
        save_processed_email(email_id, processed_emails_file)
    return emails


# Function: Save Emails
# =======================================================
def save_emails(emails, csv_file_path, json_file_path):
    """Save emails to CSV and JSON files."""
    if emails:
        append_emails_to_csv(emails, csv_file_path)
        append_emails_to_json(emails, json_file_path)


# Main Execution
# =======================================================
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument("--max_results", type=int, default=10, help="Specify the maximum number of emails to fetch")
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)

```
