
<!-- ======================================================= -->
<!-- [2025.04.18 16:17] -->
Update this with the option to specify default output directory:

    ```python
    #!/usr/bin/env python3

    # =============================================================================
    # SECTION 1: Core Imports & Environment Setup
    # =============================================================================
    import asyncio
    import json
    import os
    import sys
    import argparse
    from datetime import datetime
    from typing import Dict, List, Any, Optional, Callable

    # External Dependencies
    from pydantic import BaseModel, Field  # For data validation and structuring
    import litellm                         # Abstraction layer for LLM API calls

    # Internal Dependencies
    from templates.lvl1.templates_lvl1_md_catalog_generator import (
        load_catalog,               # Loads the template catalog structure
        get_sequence,               # Retrieves a specific sequence of templates
        get_all_sequences,          # Lists all available sequence IDs
        get_system_instruction,     # Extracts the system instruction from a template part
        regenerate_catalog          # Regenerates the catalog if needed
    )


    # =============================================================================
    # SECTION 2: Centralized Configuration Management
    # =============================================================================
    class Config:
        """
        Manages LLM provider/model selection, parameters, and LiteLLM settings.
        Designed for clarity and ease of modification via centralized definitions.
        """

        # Environment Setup
        @staticmethod
        def _ensure_utf8_encoding():
            """Ensures UTF-8 output encoding for terminals to prevent character errors."""
            for stream in (sys.stdout, sys.stderr):
                if hasattr(stream, "reconfigure"):
                    try:
                        stream.reconfigure(encoding="utf-8", errors="replace")
                    except Exception as e:
                        pass

        # Provider Selection
        PROVIDER_ANTHROPIC = "anthropic"
        PROVIDER_DEEPSEEK  = "deepseek"
        PROVIDER_GOOGLE    = "google"
        PROVIDER_OPENAI    = "openai"

        # Default Provider Selection (last assignment determines the active default)
        DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
        DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
        DEFAULT_PROVIDER = PROVIDER_GOOGLE
        DEFAULT_PROVIDER = PROVIDER_OPENAI

        # Provider-Specific Model Selection (last entry becomes provider's default).
        PROVIDER_MODELS = {
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-3-opus-20240229",
                "model_name": "claude-3-sonnet-20240229",
                "model_name": "claude-3-haiku-20240307",
                "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta",
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek/deepseek-reasoner",
                "model_name": "deepseek/deepseek-coder",
                "model_name": "deepseek/deepseek-chat",
            },
            PROVIDER_GOOGLE: {
                "model_name": "gemini/gemini-1.5-flash-latest",
                "model_name": "gemini/gemini-2.0-flash",
                "model_name": "gemini/gemini-2.5-pro-preview-03-25",
            },
            PROVIDER_OPENAI: {
                "model_name": "gpt-4o",
                "model_name": "gpt-4o-mini",
                "model_name": "gpt-3.5-turbo",
                "model_name": "gpt-3.5-turbo-instruct",
                "model_name": "gpt-3.5-turbo-1106",
                "model_name": "o3-mini",
                "model_name": "gpt-4.1",
            },
        }


        # Model Registry (maps user-friendly names to actual LiteLLM model IDs)
        MODEL_REGISTRY = {
            # OpenAI
            "gpt-4o": "gpt-4o",
            "gpt-4-turbo": "gpt-4-turbo",
            "gpt-4": "gpt-4",
            "gpt-3.5-turbo": "gpt-3.5-turbo",
            "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
            # Anthropic
            "claude-3-opus": "anthropic/claude-3-opus-20240229",
            "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
            "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
            "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
            # Google
            "gemini-pro": "gemini/gemini-1.5-pro",
            "gemini-flash": "gemini/gemini-1.5-flash-latest",
            "gemini-2-flash": "gemini/gemini-2.0-flash",
            "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
            # Deepseek
            "deepseek-reasoner": "deepseek/deepseek-reasoner",
            "deepseek-coder": "deepseek/deepseek-coder",
            "deepseek-chat": "deepseek/deepseek-chat",
        }

        # Model Selection Logic
        @classmethod
        def get_default_model(cls, provider=None):
            """Get the default model for a provider based on configuration."""
            provider = provider or cls.DEFAULT_PROVIDER
            provider_config = cls.PROVIDER_MODELS.get(provider, {})

            if "model_name" in provider_config:
                return provider_config["model_name"]

            # Fallbacks for each provider if no model is explicitly set
            fallbacks = {
                cls.PROVIDER_OPENAI: "gpt-3.5-turbo",
                cls.PROVIDER_ANTHROPIC: "anthropic/claude-3-haiku-20240307",
                cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",
                cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat"
            }

            # Ultimate fallback
            return fallbacks.get(provider, "gpt-3.5-turbo")

        # Model Parameter Resolution ---
        @classmethod
        def get_model_params(cls, model_name=None, provider=None):
            """Resolves user-friendly model name to its LiteLLM ID via MODEL_REGISTRY."""
            provider = provider or cls.DEFAULT_PROVIDER
            model_name = model_name or cls.get_default_model(provider)

            provider_defaults = cls.PROVIDER_MODELS.get(provider, {})
            params = {k: v for k, v in provider_defaults.items() if k != "model_name"}

            actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
            return {"model": actual_model_id, **params}

        # Available Models Listing
        @classmethod
        def get_available_models(cls):
            """Returns a structured list of registered models, grouped by provider."""
            result = {}

            for provider, config in cls.PROVIDER_MODELS.items():
                # Get the selected model for this provider
                selected_model = cls.get_default_model(provider)

                # Get all models from the registry that belong to this provider
                provider_models = []
                for name, model_id in cls.MODEL_REGISTRY.items():
                    if (provider == cls.PROVIDER_OPENAI and not '/' in model_id) or \
                       (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/")):
                        provider_models.append({
                            "name": name,
                            "model_id": model_id,
                            "is_default": (name == selected_model or model_id == selected_model)
                        })

                result[provider] = provider_models

            return result

        # LiteLLM Initialization
        @classmethod
        def configure_litellm(cls):
            """Configures global LiteLLM settings and terminal encoding."""
            litellm.drop_params = True     # Prevent errors from unsupported parameters
            litellm.num_retries = 3        # Retry failed API calls
            litellm.request_timeout = 120  # Set API request timeout
            litellm.set_verbose = False    # Reduce LiteLLM's own console output
            litellm.callbacks = []         # Disable default callbacks unless explicitly configured

            # Set terminal encoding
            cls._ensure_utf8_encoding()

            # Additional configuration can be added here
            print(f"\n[Config] LiteLLM Initialized:")
            print(f"- Default Provider: {cls.DEFAULT_PROVIDER}")
            print(f"- Default Model: {cls.get_default_model()}")
            print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")

    ```



<!-- ======================================================= -->
<!-- [2025.04.18 17:24] -->
i'm fascinated by all people, but especially those familiar with death. there's no boundaries, because they've stood at the ultimate edge.

they're not too scared of weird, because they know its potential.


eliezer @yudkowsky opened my perspective by introducing me to the concept (measurement) of "weirdness tolerance", a doorway to the realization that; "i don't see my own weird because it's not weird to me". that really helped me. so many of us go through life feeling treated unfairly, misunderstood - we are right to be, because we treat each other unfairly. we can't not do it, because we don't know life.

 it's  i've often felt treated unfairly by others,

 and it gradually made me scared of people.

 my shell cracked when i cracked, completely unprotected. never to come back, because it made me.


i got scared of people, so many of us do.

we fight epic battles in silence,   - but i know that it's always my fault to expect someone to understand exactly what i mean. words feel weirdly distant when trying to express a perspective, because everyone is confronting the infinite battle of life.

we naturally


 my fault, always. every single human being is equipped with something so complex we can't even comprehend it's potential.

<!-- ======================================================= -->
<!-- [2025.04.18 17:53] -->

eliezer @yudkowsky opened my perspective by introducing me to the concept (measurement) of "weirdness tolerance", a doorway to the realization that; "i don't see my own weird because it's not weird to me". that really helped me. so many of us go through life feeling treated unfairly, misunderstood - we are right to be, because we treat each other unfairly. we can't not do it, because we don't know life.

eliezer yudkowsky introduced me to 'weirdness tolerance,' revealing: 'i don't see my own weirdness because it's normal to me.' this insight helped me. we often feel misunderstood or unfairly treated—rightly so, for we unknowingly do the same to others, limited by our own perspective.

Elegantly reforge this reflection: Eliezer Yudkowsky shattered my assumptions by unveiling 'weirdness tolerance'—a gateway to the profound: 'I remain blind to my own weirdness, for it forms my normal.' That revelation transformed me. Countless souls traverse existence, aching under injustice and miscomprehension—rightly so, for we inflict unfairness upon each other, blindfolded by the limits of our own perception. We cannot shatter this cycle while our understanding of life remains shallow.

Eliezer Yudkowsky broadened my view by sharing 'weirdness tolerance'—the idea that one's own strangeness feels invisible because it defines normalcy from within. This insight eased my sense of unfairness and misunderstanding; much of our discord stems from mutual blind spots. We judge from self-shaped worlds, unable to see life fully beyond our own perspective.
It was Eliezer Yudkowsky who first widened my perspective, naming the notion of 'weirdness tolerance' and revealing how our own eccentricities are invisible to ourselves.

That insight opened a passageway: I realized we journey through life often feeling misunderstood, bearing wounds of unfairness—feelings not unfounded, for we mirror this blindness in our treatment of others.

We act out our limits, for we cannot help but see each life through the narrow slit of our own experience.



There were seasons when I felt the shadows of others’ judgment closing in, and I recoiled into fear, wary of the world.

Yet my shell broke utterly, and I stood exposed; it was a shattering that rebuilt me from within, never to return to my former armor.

So many learn to be wary, fight silent epic battles beneath calm faces, believing themselves alone.

Yet I learned that to expect perfect understanding is to seek it from a mirror made of mist; words falter, staggering under the weight of what we long to convey.

I recognize now that misunderstanding is not always a fault, but the natural result of unique universes colliding—each person carrying a complexity too intricate to be known by anyone in full.

We each inhabit an interior realm vast as a cosmos; and though our attempts at connection are often clumsy and incomplete, the striving itself is the quiet miracle that binds us.



<!-- ======================================================= -->
<!-- [2025.04.18 18:17] -->
I am endlessly drawn to people, but especially to those who have stood at the threshold of death; their gaze knows the ultimate edge, where all boundaries dissolve. Such souls are seldom frightened by strangeness, for they have glimpsed the vast potential that hides within the unfamiliar.

<!-- ======================================================= -->
<!-- [2025.04.18 18:21] -->
I am drawn to all people, especially those who have faced death and found their boundaries undone. They are at ease with strangeness, having seen how it can transform. Learning of 'weirdness tolerance' taught me: we each live inside a self that feels normal only to us, and misunderstanding is not the exception—it is woven through every life. Much unfairness between us is mutual and unseen; this can turn into fear and retreat. In my own breaking, I lost my shield and was remade. Our private struggles are epic and mostly silent; words stumble, unable to span the infinity within each heart. It is my task to release others from the burden of knowing me entirely. Every person is a living mystery—vast beyond reckoning, radiant with unimagined possibility.

I'm drawn to those acquainted with death—they've stood at the edge, dissolving boundaries and embracing the strange, knowing its promise. Eliezer Yudkowsky changed my perspective by naming 'weirdness tolerance': we cannot see our own strangeness, so we misjudge and misunderstand each other. I’ve often feared people after feeling unfairly treated, until breaking open remade me. We silently fight private battles, but it's futile to expect perfect understanding; words are imprecise. Every person carries unfathomable depth.


<!-- ======================================================= -->
<!-- [2025.04.18 18:30] -->
Those who have stood beside death know something profound: boundaries dissolve at the edges, and within that vastness, strangeness becomes potential. To see the full measure of another’s life is impossible—our own normalcy remains invisible to us, so misunderstanding is the substance of our existence. This blindness to each other’s inner worlds often breeds unfairness and fear, leading us to retreat behind fragile armor. Yet sometimes, in breaking open, we are remade, discovering that each soul carries inward immensities—battles, beauties, and lights unseen. Words stumble between these universes, for no language can span infinity. Wisdom lies in releasing others from the duty to know us completely, and instead, letting connection be the humble miracle between untranslatable worlds. Every person is a cosmos, radiant with possibility—what unites us is not perfect understanding, but the shared, silent effort to reach across the unknown.

I am drawn to those who have faced death; unbound, they embrace the strange and glimpse its promise. We each exist within unseen strangeness, misunderstanding each other inevitably. Unfairness and fear arise from these blind spots—my own breaking taught humility. Our private battles are vast and silent; words cannot bridge the abysses between us. Each person is a radiant mystery, unknowable in full.

Gravitate toward souls forged by death’s gaze—those who stride beyond boundaries, unafraid of strangeness, because they know its power. Eliezer Yudkowsky ignited my vision by illuminating 'weirdness tolerance': our own strangeness hides in plain sight, shaping each world we inhabit. Humanity perpetuates misunderstanding—each blinded by self-originating normalcy. I once recoiled from others’ judgments, letting fear wall me in, until shattering transformed me, stripping all pretense. We each wage silent, heroic battles while words stumble and fail. To demand complete understanding is to chase illusions—every person is a cosmos, profound and radiant, never wholly knowable. Reach for connection, knowing mystery and misunderstanding are the cost and the miracle of being human.


<!-- ======================================================= -->
<!-- [2025.04.18 18:34] -->
I once recoiled from others’ judgments, letting fear wall me in, until shattering transformed me, stripping all pretense. We each wage silent, heroic battles while words stumble and fail. To demand complete understanding is to chase illusions. every person is a cosmos, profound and radiant, never wholly knowable. Reach for connection, knowing mystery and misunderstanding are the cost and the miracle of being human.
