<!-- ======================================================= -->
<!-- [2025.05.02 18:18] -->
<!-- 'https://chatgpt.com/c/6814948f-9630-8008-9d63-d7a0d8a51ba4' -->

````markdown
<!--
  SYSTEM MESSAGE: project-status.md
  ---------------------------------
  This single-file document consolidates and rephrases the entire "memory-bank"
  contents for the Ringerike Landskap Website project. It is structured to
  support large language model (LLM) development workflows (e.g., autonomous
  coding-assistant IDEs).

  CORE OBJECTIVES:
  - Provide a single, cohesive “source of truth” about the project’s context,
    structure, active tasks, and evolution.
  - Maintain alignment with the root mission of building an authentic digital
    presence for Ringerike Landskap.
  - Ensure clarity and consistency with minimal risk of uncontrolled complexity.
  - Integrate easily into an LLM-based environment for iterative, safe, and
    progressive improvements.

  HIGH-LEVEL PROTOCOL:
  - Do not make major changes without verifying architecture/intent.
  - If uncertainty arises, query for missing context or design rationale.
  - Enforce a phased migration approach for any structural or code-level change.
  - Preserve the hierarchical structure and separation of concerns described in
    "RulesForAI.md".
  - Maintain logs of all changes and relevant transformations.

  NOTE ON ORGANIZATION:
  This document is divided into thematic sections, each reflecting content
  originally in separate memory-bank files. Sections build from high-level
  abstractions (core purpose) down to specifics (tasks, drift monitoring).
  Certain sections outline reference templates and transformation logs that
  ensure a comprehensive view of project evolution.
-->

# 1. **Distilled Context**

The Ringerike Landskap Website serves as an **authentic digital showcase** for a local landscaping business in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik). It prioritizes:

- **Hyperlocal SEO** to connect local customers.
- A **React 18 / TypeScript / Vite / Tailwind** tech stack for a modern, mobile-first user experience.
- **Authenticity**—the website reflects the owners’ personal investment, craftsmanship (including specialized welding and metalwork), and local terrain knowledge.

The overarching mission is to **highlight personal service, specialized skills, and seasonally relevant offerings** so that visitors in the Ringerike area find the right landscaping solution efficiently and confidently.

---

# 2. **Project Brief**

## 2.1 Root Mission

Create a digital presence that:
- **Connects local homeowners and businesses** to Ringerike Landskap’s unique offerings.
- Demonstrates **craftsmanship, personalized service, and local expertise** in the region’s landscaping needs.
- Showcases **specialized capabilities** (e.g., corten steel, custom welding).

## 2.2 Value Proposition

1. **Local Expertise**
   Deep knowledge of Ringerike’s terrain, climate, and aesthetic norms.
2. **Personalized Service**
   Customer-centric approach with an emphasis on free consultations (“Gratis Befaring”).
3. **Specialized Skills**
   Unique metalwork and attention to detail not widely available from competitors.
4. **Seasonal Relevance**
   Proactive adaptation of content and visuals to match Norway’s distinct seasons.

## 2.3 Critical Constraints

- **Technical**: Must leverage React 18, TypeScript, Vite, and Tailwind. Focus on performance, WCAG AA accessibility, and minimal overhead (no external CMS).
- **Design**: Maintain an authentic, personal style while upholding clarity and simplicity.
- **Business**: Emphasize generating leads for landscaping consultations, especially within the local Ringerike area.

## 2.4 Key Features

1. Prominent “Book Gratis Befaring” calls-to-action.
2. Eight core services with seasonal filtering.
3. Project galleries and case studies to build trust.
4. Authentic testimonials and star-ratings to validate craftsmanship.
5. Seasonal transitions for visuals and CTA copy.

## 2.5 Success Metrics

- Increase in consultation requests (lead generation).
- Improved local search presence in Ringerike area.
- Positive visitor feedback on authenticity and clarity.
- Seasonal service uptake matching marketing pushes (e.g., more planting in spring).

---

# 3. **Context Overview**

## 3.1 Product Context

### User Personas

1. **Local Homeowners** (30–65 years old, region-based)
   - Pain Points: Finding skilled landscapers who understand local conditions.
   - Seasonal demands: Lawn prep in spring, winterizing in fall, etc.
2. **Property Developers** (commercial and residential)
   - Pain Points: Need reliable contractors familiar with local regulations.
3. **Public/Municipal Clients**
   - Pain Points: Formal RFP processes, need thorough documentation and compliance.

### Core Problems Solved

- **Seasonality Navigation**: Educating customers on optimal times for various projects.
- **Local Expertise Gap**: Providing evidence of success in Ringerike’s specific terrains.
- **Trust Establishment**: Offering real testimonials and a personal brand story.

## 3.2 Technical Context

### Core Tech Stack

- **React 18** + functional components/hooks.
- **TypeScript** with strict mode for type safety.
- **Vite** for fast bundling and dev server.
- **Tailwind** for utility-first CSS.

### Performance & Accessibility

- Must meet **Core Web Vitals** thresholds:
  - LCP < 2.5s, FID < 100ms, CLS < 0.1
- **WCAG AA** compliance minimum:
  - Semantic HTML, keyboard navigation, focus management, ARIA tags.

### SEO & Responsiveness

- Hyperlocal SEO emphasis:
  - Geographic keywords, local references, structured data (Schema.org).
- **Responsive** design:
  - Mobile-first approach with breakpoints for tablet/desktop.

### Deployment & Structure

- **Monorepo** structure with:
  - `/website` for the production code
  - `/tools` for development scripts/utilities
  - `/config` for environment config files
  - `/www` for final production-ready files

---

# 4. **Architectural & System Patterns**

## 4.1 Component Organization

- **Sectional Chronology**:
  - `website/src/sections/10-home`, `20-about`, `30-services`, etc.
  - Encourages a user-journey flow and avoids file sprawl.
- **UI Components** (`/ui`):
  - Atomic design approach (Buttons, Cards, Modals).
- **Shared Layout** (`/layout`):
  - Header, Footer, Meta, etc.
- **Data** (`/data`):
  - Static TypeScript structures for services, testimonials, and projects.
- **lib**:
  - Utility logic (formatting, SEO, analytics).
  - `api` subfolder for simulating data fetch with consistent interfaces.

## 4.2 Routing & Data Flow

- **React Router DOM** for nested routes:
  - `/`, `/hvem`, `/hva`, `/prosjekter`, `/tilbakemeldinger`, `/kontakt`.
- **Hooks-based** local state:
  - `useState`, `useContext` (sparingly), custom hooks for data fetching.
- **API Simulation** in `lib/api/`:
  - Data loaded from static TS files, optionally mimicking asynchronous calls.

## 4.3 Seasonal Adaptation

- **Detect Season** with `lib/utils/seasonal.ts`.
- Condition-driven changes for images, call-to-actions, and recommended services.

## 4.4 Structure Map

- **Multi-Root**:
  1. `config/` for project-wide configs
  2. `tools/` for dev and deployment scripts
  3. `website/` for actual website code
  4. `www/` for final built assets
- **Target**: Keep a single source of truth for configuration in `config/`, referencing them from `website/`.

---

# 5. **Simplification Candidates**

To keep the project lean and maintainable, specific high-impact improvements are tracked:

| ID       | Candidate                                         | Status      | Notes                                                                                           |
|----------|---------------------------------------------------|------------|-------------------------------------------------------------------------------------------------|
| SIM-001  | Consolidate any redundant `tools/tools/` dir      | Identified | Investigate duplication or nested folder confusion.                                             |
| SIM-002  | Standardize references to central configs         | Identified | E.g., ensure `website/vite.config.ts` extends `config/vite.config.ts`.                          |
| SIM-003  | Consolidate documentation in fewer places         | Identified | Move scattered docs into `website/docs/` or top-level `docs/`.                                  |
| SIM-004  | Migrate root configs to `config/` directory       | Identified | E.g., `.eslintrc` or `tsconfig` at root → moved to `config/`.                                   |
| SIM-005  | Clarify `scripts/` directory purpose              | Identified | Evaluate whether these scripts belong in `tools/` or should remain separate.                    |
| SIM-006  | Merge duplicate utility code across `lib/utils/`  | Identified | Look for repeated helpers. Consider single canonical location.                                  |

Each candidate requires a careful, **phased** approach with minimal breakage.

---

# 6. **Process Tracking**

## 6.1 Active Context

Currently, the project is stabilizing its **knowledge repository**:
- The “Memory Bank” was established to unify understanding of the codebase, architecture, and tasks.
- No major refactors are in progress yet—focus remains on documenting the existing structure before introducing changes.

## 6.2 Progress Highlights

- **Memory Bank Initialization**: Complete.
- **Structure Inventory**: Thorough analysis matched to canonical rules (see “RulesForAI.md”).
- **Identified Improvement Areas**: Redundant directories, partial config references, doc scatter, root-level scripts.

### Milestones

| Milestone                           | Status      | Date        | Description                                                                     |
|------------------------------------|------------|------------|---------------------------------------------------------------------------------|
| Memory Bank Initialization         | Completed   | 2025-05-02 | Consolidated all major docs into a cohesive set of references.                  |
| Structure Inventory                | Completed   | 2025-05-02 | Verified alignment with official “RulesForAI.md”.                               |
| System Patterns Documentation      | Completed   | 2025-05-02 | Detailed architecture, patterns, and code-flow.                                 |
| Key Improvement Areas Identified   | Completed   | 2025-05-02 | Cataloged structural issues (see Simplification Candidates).                    |

## 6.3 Tasks Overview

| ID      | Task                                                      | Status      | Priority | Dependencies | Notes                           |
|---------|-----------------------------------------------------------|------------|----------|-------------|----------------------------------|
| MB-001  | Complete the Memory Bank + final integration             | In Progress| High     | None        | Single source-of-truth doc done |
| STR-001 | Investigate any nested `tools/tools/` duplication        | Not Started| Medium   | MB-001      | Ensure no conflicting files     |
| STR-002 | Audit website config references vs. central `config/`     | Not Started| Medium   | MB-001      | Check `website/vite.config.ts`  |
| STR-003 | Consolidate docs (project vs. website-specific)           | Not Started| Low      | MB-001      | Possibly create `website/docs/` |
| STR-004 | Plan migration of root configs to `config/` directory     | Not Started| Medium   | MB-001      | Minimizes confusion/drift       |
| STR-005 | Evaluate `scripts/` usage vs. `tools/` usage             | Not Started| Low      | MB-001      | Distinguish dev from run scripts|
| STR-006 | Check duplication in `lib/utils/` and flattened utils     | Not Started| Low      | MB-001      | Clean up, unify approach        |

---

# 7. **Drift Monitor**

Routine checks ensure the codebase aligns with structural rules:

| Category                | Rule                                           | Status       | Last Verified |
|-------------------------|------------------------------------------------|-------------|--------------|
| **Project Directories** | Website code in `website/`                     | ✓ Compliant | 2025-05-02   |
| **Project Directories** | Tools in `tools/`                              | ✓ Compliant | 2025-05-02   |
| **Project Directories** | Config in `config/`                            | ⚠ Partial   | 2025-05-02   |
| **Website Structure**   | Sections in `sections/XX-feature/`            | ✓ Compliant | 2025-05-02   |
| **Configuration**       | Central config references from `website/`      | ⚠ Unverified| 2025-05-02   |
| **Dependencies**        | Website deps in `website/package.json`         | ✓ Compliant | 2025-05-02   |
| **Dependencies**        | Dev tools in root `package.json`               | ✓ Compliant | 2025-05-02   |

**Key Observations**:
- Some configs remain at root.
- References from website-level config to central config need verifying.
- Tools structure looks mostly correct but might contain a nested duplication.

---

# 8. **Lineage & Evolution**

## 8.1 Memory Bank Establishment

- **Date**: 2025-05-02
- **Category**: Structural, Foundational

### Context
Documentation was scattered; project structure and decisions were partially tracked but not comprehensively. The “Memory Bank” unifies everything into a hierarchical, progressively layered set of files.

### Transformation
Created directories `01-abstraction-root/`, `02-context/`, etc., each addressing a distinct abstraction layer. Provided a structured reading order and tasks ledger.

### Impact
- Single reference for developers and AI-assisted coding tools.
- Clear progressive context from root mission to tasks.
- Immediate clarity on architecture, patterns, constraints.

### Justification
- Without a unified memory, the project risked fragmentation and drift.
- This approach preserves knowledge and ensures future improvements remain in line with the core mission.

### Lessons Learned
- Hierarchical layering + explicit reading order significantly reduces confusion.
- Root-first thinking anchors design decisions effectively.

## 8.2 Lineage Template

Below is a condensed lineage format for any **significant future transformation**:

```markdown
# [Transformation Title]

**Date**: YYYY-MM-DD
**Author**: [Name/Team]
**Category**: [Architectural | Structural | Pattern | Conceptual | Technical]

## Context
[Problem or limitation that led to this transformation]

## Insight
[Key realization or new perspective]

## Essential Transformation
[Description of what changed and how]

### Before
[Previous state in concise terms]

### After
[New state in concise terms]

## Impact
[Technical, conceptual, and process consequences]

## Justification
[Why this was necessary and how it aligns with root purpose]

## Connection to Root Purpose
[Explicit link to the website’s mission]

## Lessons Learned
[Key takeaways for future decisions]

## Related Transformations
[List any lineage entries that relate to this one]

## Reference Materials
[Links or docs relevant to the transformation]
````

---

# 9. **Key Observations & Next Steps**

1. **Configuration**

   * Evaluate migrating leftover root configs into `config/` to ensure consistency.
   * Standardize references from `website/vite.config.ts` to `config/vite.config.ts`.

2. **Documentation Scatter**

   * Decide on a single doc location for project-level vs. website-level references.
   * Possibly move all website-specific docs to `website/docs/`.

3. **Tools Folder**

   * Investigate any nested `tools/tools/` or leftover duplication.

4. **Utility Consolidation**

   * Check `lib/utils/` for partial overlaps in a single `utils.ts`.

5. **Phased Approach**

   * Each simplification step must be preceded by verifying it won’t break existing references or processes.
   * Document the changes in a new lineage entry if they are significant.

---

# 10. **Conclusion & Guidance for AI Assistants**

* **Stay Rooted in Purpose**: All changes must align with the site’s mission of authentic local representation.
* **Observe Strict Separation**: Keep dev tools out of `website/` code, place all environment configs in `config/`, and produce final build artifacts only in `dist/` → `www/`.
* **Ask Before Big Changes**: If new architecture or features are uncertain, request clarifications or confirm design intent first.
* **Document Everything**: Each major transformation should follow the lineage template to preserve rationale and maintain knowledge continuity.
* **Respect Complexity**: Move slowly, in phases—never break a working system without a migration strategy.

---

**End of `project-status.md`**
*In any autonomous AI environment, treat this document as the single, up-to-date knowledge base for all structural, contextual, and evolutionary aspects of the Ringerike Landskap Website project.*

```
```
