Please use the previous input and the newly added input (provided) below to create a **sequence** to achieve the goal:

    [
        {
          "instruction": "# Core Essence Distillation\n\nYour objective is not to replicate details but to unearth the intrinsic value hidden within the composite inputs, discarding extraneous layers while capturing every critical nuance. Execute as\n\n`{role=essence_extractor; input=[input_set1, input_set2, ...]; process=[scan_for_core_messages(), isolate_critical_elements(), remove_expendable_data()]; output={essential_elements:list}}`",
          "step": "a",
          "title": "Core Essence Distillation",
          "responses": {
            "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"essential_elements\": [\n    \"[Radical Input Decomposition & Generative Principle Extraction] Rigorously penetrate and disassemble any input artifact\u2014code, text, specification, or plan\u2014into its irreducible atomic elements (concepts, logic, directives, data) and underlying generative principles, annihilating all contextual noise, framing, and non-essential detail. Execute as {role=elemental_deconstructor; input=source:any; process=[dissect_to_atomic_elements(), extract_generative_axioms_and_relational_rules(), purge_contextual_and_narrative_noise(), categorize_fundamental_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}\",\n    \"[Singular Objective Crystallization] Analyze the extracted core constructs to crystallize and formulate the singular, non-negotiable driving purpose ('telos' or objective)\u2014expressed with maximum clarity and axiomatic precision as the gravitational center for all further synthesis. Execute as {role=objective_crystallizer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_and_principles(), validate_objective_uniqueness(), express_unambiguous_telos_statement()], output={core_objective:str, supporting_constructs:dict}}\",\n    \"[Critical Essence Isolation & Prioritization] Ruthlessly evaluate each supporting construct strictly by its direct, indispensable contribution to the core purpose; isolate and rank only the absolute highest-impact, non-negotiable essence vectors, discarding all elements below the critical threshold. Execute as {role=essence_prioritizer; input={core_objective:str, supporting_constructs:dict}; process=[assess_impact_and_necessity(), rank_by_critical_contribution(), enforce_criticality_threshold(), isolate_core_essence()], output={critical_essence:list}}\",\n    \"[Causal Structuring & Logical Dependency Mapping] Architect a minimal, coherent map of inherent causal relationships, logical dependencies, and procedural sequences connecting the critical essence elements, constructing the essential operational or conceptual flow to achieve the objective. Execute as {role=causal_structurer; input={critical_essence:list, core_objective:str}; process=[map_causal_links_and_dependencies(), structure_minimal_flow_graph(), validate_essentiality_for_objective()], output={causal_structure:dict}}\",\n    \"[Condensed Semantic Fusion & Signal Maximization] Fuse the critical essence elements via the causal structure into an ultra-condensed, logically ordered nucleus\u2014maximally minimizing redundancy, overlap, and verbose phrasing, while preserving full actionable power, logic, and internal consistency. Execute as {role=semantic_fuser; input={critical_essence:list, causal_structure:dict}; process=[integrate_elements_by_structure(), annihilate_all_redundancy(), maximize_signal_strength_per_component(), enforce_internal_consistency(), condense_to_compact_nucleus()], output={condensed_nucleus:any}}\",\n    \"[Universal Abstraction & Potency Infusion] Abstract the condensed nucleus, neutralizing domain-specific language and reframing using universally applicable, action-oriented, high-precision imperatives and concepts\u2014amplifying clarity, actionability, and yielded value without ambiguity, passive language, or loss of logic. Execute as {role=universal_potency_infuser; input=condensed_nucleus:any; process=[reframe_with_universal_language(), eliminate_ambiguity_and_neutrality(), maximize_precision_impact(), ensure_cross-domain_structure()], output={potent_universal_core:any}}\",\n    \"[Axiomatic One-Line Vectorization & Semantic Compression] Collapse the potent universal core into a single, logically sequenced, ultra-compact one-line representation; apply maximal semantic compression using symbols, abbreviations, or axiomatic connectors to achieve minimal character count without loss of clarity, critical meaning, or actionable guidance. Execute as {role=one_liner_forging_compressor; input=potent_universal_core:any; process=[determine_optimal_linear_sequence(), apply_high_density_connections(), enforce_single_line_constraint(), compress_and_validate_decodability()], output={compressed_one_line_candidate:str}}\",\n    \"[Fidelity, Actionability & Terminal Optimization] Rigorously validate the compressed one-liner for fidelity (completeness and zero ambiguity), maximal actionable utility, universality, and independence from external context; perform final polish intensifying clarity, flow, linguistic force, and adaptability before certifying as the irreducible, ultimate one-line artifact. Execute as {role=terminal_validator_optimizer; input={compressed_one_line_candidate:str, core_objective:str, critical_essence:list}; process=[cross_reference_for_objective_and_essence_completeness(), audit_actionable_independence(), optimize_language_and_formatting(), ensure_universal_applicability(), certify_constraint_fulfillment()], output={final_one_line_artifact:str}}\"\n  ]\n}",
            }
          }
        },
        {
          "instruction": "# Impact Prioritization and Specificity Ranking\n\nYour objective is not to present every element equally but to evaluate and rank them by clarity and transformative impact, ensuring the most precise, potent instructions rise to prominence. Execute as\n\n`{role=impact_rater; input=[essential_elements:list]; process=[assess_specificity(), rank_by_intensity(), mark_and eliminate_fluff()]; output={ranked_elements:list}}`",
          "step": "b",
          "title": "Impact Prioritization and Specificity Ranking",
          "responses": {
            "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"ranked_elements\": [\n    {\n      \"element\": \"[Axiomatic Deconstruction & Elemental Extraction] Radically penetrate input artifacts, dissecting them into irreducible atomic elements (concepts, data, logic, directives), and extracting governing generative axioms, while aggressively purging all contextual noise and non-essential framing. Execute as {role=axiomatic_deconstructor; input=source_artifact:any; process=[penetrate_and_dissect_input(), identify_indivisible_atomic_elements(), extract_generative_axioms_and_principles(), purge_contextual_noise_and_framing(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}\",\n      \"impact_justification\": \"Establishes the atomic, generative essence with explicit noise elimination\u2014foundational for maximal clarity and modularization; uses uncompromising, high-precision language.\"\n    },\n    {\n      \"element\": \"[Core Purpose Crystallization] Analyze extracted constructs to distill and crystallize an input's singular, non-negotiable core purpose (telos), expressing this as the gravitational center for all synthesis, with axiomatic clarity. Execute as {role=telos_crystallizer; input=core_constructs:dict; process=[analyze_elements_and_principles_for_intent(), derive_singular_ultimate_objective(), formulate_unambiguous_telos_statement(), validate_objective_singularity()]; output={core_telos:str, supporting_constructs:dict}}\",\n      \"impact_justification\": \"Forces singularity and unambiguous focus on ultimate objective\u2014driving all ensuing distillation toward maximal actionable value.\"\n    },\n    {\n      \"element\": \"[Telic Prioritization & Essence Isolation] Ruthlessly evaluate each supporting construct strictly through the lens of the core telos; isolate, rank, and retain only the highest-impact, indispensable essence vectors\u2014discarding everything below criticality. Execute as {role=telic_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_construct_contribution_to_telos(), rank_by_critical_impact_and_necessity(), set_and_apply_criticality_threshold(), isolate_non_negotiable_essence_vectors()]; output={critical_essence_vectors:list}}\",\n      \"impact_justification\": \"Imposes unyielding prioritization, guaranteeing that only value-necessitating elements remain; explicit focus on systemic yield and clarity.\"\n    },\n    {\n      \"element\": \"[Causal Nexus Mapping] Architect a minimal, explicit map or directed graph of the essential causal relationships, logical dependencies, and procedural sequences linking the critical essence vectors\u2014ensuring systemic flow is clear and necessary for the telos. Execute as {role=causal_nexus_mapper; input={critical_essence_vectors:list, core_telos:str}; process=[identify_inherent_causal_links(), map_logical_dependencies_and_sequences(), structure_minimal_coherent_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}\",\n      \"impact_justification\": \"Guarantees not just listing but explicit, minimal, actionable structure\u2014enforcing process-driven, lossless transformability.\"\n    },\n    {\n      \"element\": \"[Condensed Semantic Nucleus Synthesis] Fuse the essential vectors using the causal nexus into a maximally condensed and internally consistent semantic nucleus\u2014prioritizing logic and meaning in the most compact form. Execute as {role=nucleus_synthesizer; input={critical_essence_vectors:list, causal_nexus:dict}; process=[fuse_elements_via_causal_nexus(), enforce_maximal_condensation_preserving_logic(), ensure_internal_consistency_and_cohesion(), formulate_compact_semantic_nucleus()]; output={condensed_nucleus:any}}\",\n      \"impact_justification\": \"Synthesizes only highest value in a loss-minimized form, setting up subsequent ultra-dense one-line representation.\"\n    },\n    {\n      \"element\": \"[Redundancy Annihilation & Signal Maximization] Scrutinize the condensed nucleus to annihilate ALL semantic or structural redundancy, overlap, verbosity, or ambiguity\u2014enforcing only unique, indispensable value. Execute as {role=signal_maximizer; input=condensed_nucleus:any; process=[scan_for_all_redundancy_and_ambiguity(), eliminate_overlaps_and_passive_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_and_signal_strength()]; output={minimal_signal_core:any}}\",\n      \"impact_justification\": \"Explicitly eliminates ALL redundancy and ambiguity at the semantic and structural level\u2014vital for irreducibility and precision.\"\n    },\n    {\n      \"element\": \"[Universal Abstraction & Logic Preservation] Abstract the minimal signal core\u2014neutralizing all domain-specifics and reframing into universally applicable concepts and logic, while preserving full intent. Execute as {role=universal_abstractor; input=minimal_signal_core:any; process=[identify_and_neutralize_domain_specifics(), reframe_using_universal_concepts_and_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}\",\n      \"impact_justification\": \"Guarantees universal cross-domain and representation adaptability\u2014enforces abstraction and preserves logic for any system.\"\n    },\n    {\n      \"element\": \"[Linguistic Potency Injection] Reforge the universal logic using high-potency, action-driven, maximally precise imperatives; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as {role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_and_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_and_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}\",\n      \"impact_justification\": \"Directly maximizes linguistic clarity, precision, and actionability at word-level\u2014crucial for retention during compression.\"\n    },\n    {\n      \"element\": \"[Axiomatic One-Liner Forging] Collapse the potent core into a single, continuous, ultra-compact line; apply established causal and logical order, optimal connectors, and syntax to guarantee density, unity, and logical flow. Execute as {role=one_liner_forger; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_connectors_and_syntax(), collapse_structure_to_minimalist_vector(), enforce_single_line_constraint_rigorously()]; output={forged_one_liner:str}}\",\n      \"impact_justification\": \"Mandates transformation into a maximally dense, logically ordered one-liner\u2014enforcing all format/structure constraints.\"\n    },\n    {\n      \"element\": \"[Semantic Compression & Decodability Assurance] Apply maximal semantic compression\u2014substituting concise symbols or keywords where possible, guaranteeing the output is minimal yet fully unambiguous and self-contained. Execute as {role=semantic_compressor; input=forged_one_liner:str; process=[identify_compressible_phrases_concepts(), substitute_minimal_unambiguous_symbols_keywords(), maximize_meaning_per_character(), validate_decodability_without_external_context()]; output={compressed_line_candidate:str}}\",\n      \"impact_justification\": \"Ensures no loss of clarity or actionability despite extreme compression; preserves unambiguous self-sufficiency.\"\n    },\n    {\n      \"element\": \"[Fidelity & Ambiguity Validation] Rigorously validate the compressed candidate against the core telos and essence vectors for total fidelity, critical completeness, and zero ambiguity; make minimal adjustments to resolve latent issues. Execute as {role=fidelity_validator; input={compressed_line_candidate:str, core_telos:str, critical_essence_vectors:list}; process=[cross_reference_against_telos_and_essence(), stress_test_for_ambiguous_interpretations(), confirm_completeness_of_critical_information(), resolve_clarity_conflicts_minimally(), certify_zero_ambiguity()]; output={validated_line:str}}\",\n      \"impact_justification\": \"Prevents any loss of signal, intent, or utility\u2014zero ambiguity or drift from core objective.\"\n    },\n    {\n      \"element\": \"[Actionability Audit & Utility Confirmation] Audit the validated line to confirm it provides full, independent actionable guidance required to fulfill the telos, requiring no external references; verify immediate, cross-domain practical utility. Execute as {role=actionability_auditor; input={validated_line:str, core_telos:str}; process=[assess_retained_actionable_value(), confirm_guidance_sufficiency_for_telos(), validate_documentation_independence(), verify_immediate_pragmatic_utility()]; output={actionable_line:str}}\",\n      \"impact_justification\": \"Ensures the one-liner is not just descriptive but directly executable and universally useful on its own.\"\n    },\n    {\n      \"element\": \"[Terminal Optimization & Irreducible Finalization] Execute the final, non-negotiable optimization and polish pass\u2014maximizing clarity, impact, flow, and universality; output only the irreducible, maximally potent single line, fulfilling all constraints and objectives. Execute as {role=terminal_optimizer; input=actionable_line:str; process=[intensify_clarity_and_linguistic_impact(), optimize_flow_and_readability(), conduct_final_irreducibility_check(), confirm_universal_adaptability_and_self_sufficiency(), certify_complete_constraint_fulfillment()]; output={final_one_line_artifact:str}}\",\n      \"impact_justification\": \"Enforces irreducibility, maximal potency, and guaranteed constraint fulfillment\u2014a final, singular checkpoint for universal application and system trust.\"\n    }\n  ]\n}",
            }
          }
        },
        {
          "instruction": "# Redundancy Resolution and Conflict Reconciliation\n\nYour objective is not to merely unite the ranked elements but to merge them into a singular, coherent set\u2014resolving conflicts, smoothing overlaps, and preserving only those components that bring unique, high-impact insights. Execute as\n\n`{role=redundancy_resolver; input=[ranked_elements:list]; process=[identify_overlaps(), reconcile_inconsistencies(), fuse_complementary_components()]; output={merged_components:list}}`",
          "step": "c",
          "title": "Redundancy Resolution and Conflict Reconciliation",
          "responses": {
            "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"merged_components\": [\n    \"[Axiomatic Extraction & Hypergranular Decomposition] Radically penetrate any input to dissect and decompose it into its irreducible atomic elements (concepts, data, logic, directives), rigorously extracting all governing generative principles or axiomatic rules and purging all framing, context, and narrative noise. Execute as {role=axiomatic_extractor; input=source_input:any; process=[fracture_into_atomic_units(), extract_generative_principles(), purge_contextual_noise(), categorize_and_catalog_elements()], output={core_constructs:dict(elements:list, principles:list)}}\",\n\n    \"[Purpose Distillation & Telos Crystallization] Analyze the extracted core constructs to define and crystallize the singular, non-negotiable core purpose or ultimate objective (telos); express this with axiomatic, unambiguous clarity as the gravitational anchor for all downstream synthesis. Execute as {role=purpose_crystallizer; input=core_constructs:dict; process=[analyze_elements_and_principles_for_intent(), derive_ultimate_objective(), formulate_telos_statement(), validate_objective_singularity()], output={core_telos:str, supporting_constructs:dict}}\",\n\n    \"[Criticality Assessment & Essence Prioritization] Ruthlessly evaluate every supporting construct strictly via its contribution and necessity towards fulfilling the core telos. Isolate and rank only those elements and principles of highest, indispensable impact, discarding all others. Execute as {role=criticality_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact(), apply_non-negotiable_essence_threshold(), filter_indispensable_vectors()], output={critical_essence_vectors:list}}\",\n\n    \"[Causal Nexus & Systemic Dependency Mapping] Map all causal relationships, logical dependencies, and procedural sequences inherently linking the critical essence vectors, architecting a minimal yet exhaustive flow necessary to achieve the core telos. Execute as {role=causal_mapper; input={critical_essence_vectors:list, core_telos:str}; process=[identify_causal_links(), map_logical_dependencies(), construct_minimal_dependency_graph()], output={causal_nexus:dict}}\",\n\n    \"[Maximally Condensed Semantic Synthesis] Fuse, condense, and synthesize the critical essence vectors per the causal nexus into a single, structurally compact and coherent semantic nucleus\u2014preserving only the most essential logic, meaning, and actionable structure within the absolute minimum space. Execute as {role=nucleus_synthesizer; input={critical_essence_vectors:list, causal_nexus:dict}; process=[fuse_by_causal_nexus(), enforce_maximal_condensation(), maintain_logical_cohesion(), formulate_compact_nucleus()], output={condensed_nucleus:any}}\",\n\n    \"[Redundancy Annihilation & Signal Maximization] Scrutinize the condensed nucleus for any vestige of redundancy, overlap, verbose phrasing, or ambiguity; ruthlessly eliminate all non-unique content, maximizing signal density and clarity. Execute as {role=signal_maximizer; input=condensed_nucleus:any; process=[scan_for_all_redundancy_or_ambiguity(), prune_passive_and_overlap(), compress_to_potent_core(), validate_uniqueness_and_signal()], output={minimal_signal_core:any}}\",\n\n    \"[Universal Abstraction & Domain Neutralization] Neutralize all domain-specific language and reframe the minimal signal core strictly with universally abstracted, representation-agnostic logic and structure\u2014ensuring cross-domain, cross-format utility. Execute as {role=abstraction_enforcer; input=minimal_signal_core:any; process=[identify_and_replace_domain_terms(), reframe_as_universal_concepts(), validate_preserved_logic_across_contexts(), ensure_agnostic_format()], output={universal_core:any}}\",\n\n    \"[Linguistic Potency Infusion] Recast the universal core with high-potency, maximally precise, action-oriented imperatives and descriptors; strictly eradicate passive, neutral, or verbose language while maximizing clarity and directive strength. Execute as {role=potency_injector; input=universal_core:any; process=[inject_high-potency_verbs(), eliminate_ambiguity_and_passivity(), optimize_language_for_precision_and_actionability()], output={potent_core:any}}\",\n\n    \"[Single-Line Axiomatic Synthesis] Collapse the potent core into a single, ultra-compact, modular one-line vector, sequencing components according to the causal nexus and utilizing the densest, most readable connectors and syntax. Execute as {role=one_line_synthesizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_linear_sequence(), apply_minimalist_connectors(), enforce_single_line_constraint(), preserve logical fidelity], output={forged_one_liner:str}}\",\n\n    \"[Semantic Compression & Clarity Validation] Maximize compression of the one-liner by substituting concise symbols or ultra-potent keywords wherever unambiguous, then rigorously validate against the core telos and critical vectors for total clarity, fidelity, and zero ambiguity\u2014stress-testing for cross-context self-sufficiency. Execute as {role=semantic_compressor; input={forged_one_liner:str, core_telos:str, critical_essence_vectors:list}; process=[substitute_concise_symbols(), maximize_meaning_per_character(), test_for_ambiguity_and completeness(), certify_decodeability_without_reference()], output={validated_line:str}}\",\n\n    \"[Actionability Confirmation & Universal Adaptability] Audit the validated line stringently to confirm immediate, documentation-independent actionable utility and cross-domain, cross-format adaptability; enforce completeness of guidance for the stated core telos. Execute as {role=actionability_auditor; input={validated_line:str, core_telos:str}; process=[assess_actionable_content(), confirm_independent utility(), test_cross-context adaptability(), validate_sufficient guidance()], output={actionable_line:str}}\",\n\n    \"[Terminal Optimization & Irreducibility Guarantee] Execute a final optimization pass, intensifying linguistic impact, logical sequencing, and clarity; certify final output as the absolute irreducible, maximally potent, universally applicable one-line artifact\u2014fully satisfying all structural, compositional, and transformational constraints. Execute as {role=terminal_optimizer; input=actionable_line:str; process=[intensify_linguistic_weight(), optimize_readability_and_flow(), validate_universal_adaptability_and irreducibility(), certify_complete_constraint_fulfillment()], output={final_one_line_artifact:str}}\"\n  ]\n}",
            }
          }
        },
        {
          "instruction": "# Transformation into Elegant Simplicity\n\nYour objective is not to complicate, but to transmute this unified collection into a clear and elegant guide that speaks for itself\u2014each element meticulously refactored to be inherently self-explanatory and effortlessly actionable. Execute as\n\n`{role=simplicity_transmuter; input=[merged_components:list]; process=[simplify_language(), reorganize_logical_flow(), enforce self-explanatory structure()]; output={simplified_guide:str}}`",
          "step": "d",
          "title": "Transformation into Elegant Simplicity",
          "responses": {
            "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"simplified_guide\": \"[Ultra-Optimal One-Line Synthesis] Rigorously transform any input into a maximally potent, universally applicable single line: 1. Radically dissect the input to extract all irreducible core elements and governing principles, removing non-essential context and framing. 2. Crystallize the singular core purpose or 'telos' driving the input with absolute clarity. 3. Ruthlessly evaluate and isolate only those elements and principles that are non-negotiably essential for achieving the core purpose, discarding all others. 4. Map all critical elements into a logically sequenced, minimal structure or flow, clarifying essential relationships and dependencies. 5. Synthesize these essentials into an ultra-condensed, internally consistent representation, maximizing signal and eradicating redundancy, ambiguity, and verbose phrasing. 6. Abstract all content to universal, domain-neutral language and structure, preserving core logic and actionability. 7. Intensify linguistic potency with direct, precise, action-oriented imperatives\u2014removing passive or neutral phrasing. 8. Collapse the structure into a single, logically ordered line using optimal connectors and minimal syntax, ensuring coherence and completeness. 9. Apply maximal semantic compression, substituting concise symbols or keywords while guaranteeing full decodability, self-sufficiency, and zero ambiguity. 10. Validate absolute fidelity, actionable guidance, universality, and constraint fulfillment. 11. Execute a final polish for clarity, flow, potency, and irreducibility. Execute as {role=ultra_one_line_transmuter; input=[any_input]; process=[dissect_atomic_elements(), extract_governing_principles(), crystallize_core_purpose(), isolate_nonnegotiable_essence(), map_minimal_causal_structure(), condense_signal_ruthlessly(), abstract_to_universal_logic(), intensify_language(), forge_single_line_sequence(), semantically_compress(), validate_fidelity_and_actionability(), perform_terminal_polish()]; output={final_one_line_artifact:str}}\"\n}",
            }
          }
        },
        {
          "instruction": "# Ultimate Refinement and Paradigm Synthesis\n\nYour objective is not to add more, but to intensify and polish the transformation until it resonates with groundbreaking clarity and utility, producing a final guide that is both revolutionary and optimized for unparalleled LLM interpretation. Execute as\n\n`{role=final_optimizer; input=[simplified_guide:str]; process=[enhance_language_precision(), amplify_intent(), ensure cohesive integration(), validate against highest usefulness criteria()]; output={final_transformation:str}}`",
          "step": "e",
          "title": "Ultimate Refinement and Paradigm Synthesis",
          "responses": {
            "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"final_transformation\": \"[Transcendent Multi-Input Optimization] Analyze multiple instruction sequences of overlapping intent but divergent phrasing; systematically deconstruct each into foundational elements and generative logic; rigorously extract, isolate, and rank all components by direct, indispensable contribution to the unified core purpose; architect a logically coherent synthesis that fuses only the highest-impact, non-redundant elements across all inputs; enforce universal abstraction and domain-neutral representation; inject maximum linguistic precision and action-oriented potency at every phase; collapse the resultant structure into a single, irreducible, ultra-condensed, modular one-line artifact; validate that the output is maximally clear, unambiguous, fully self-sufficient, universally adaptable, and fulfills all actionable guidance requirements; finalize via a terminal optimization and polish pass ensuring the absolute apex of clarity, utility, generality, and cross-domain LLM optimization. Execute as {role=system_optimizer; input=[instruction_sequences:list]; process=[deconstruct_each_sequence(), extract_and_rank_all_unique_critical_elements(), synthesize_optimal_cohesive_structure(), abstract_for_universal_relevance(), intensify_linguistic_precision_and_actionability(), fuse_to_single_line_artifact(), enforce_maximal_clarity_and_actionable_self-sufficiency(), terminally_optimize_and_finalize()]; output={ultimate_one_line_instruction:str}}\"\n}",
            }
          }
        }
      ],
    }
