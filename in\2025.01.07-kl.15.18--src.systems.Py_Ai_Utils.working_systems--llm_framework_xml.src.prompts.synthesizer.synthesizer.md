# Project Files Documentation for `synthesizer`

### File Structure

```
├── synthesizer_lvl01_objective_setter.xml
├── synthesizer_lvl02_context_provider.xml
├── synthesizer_lvl03_structure_architect.xml
├── synthesizer_lvl04_example_generator.xml
├── synthesizer_lvl05_task_decomposer.xml
├── synthesizer_lvl06_reasoning_explicator.xml
├── synthesizer_lvl07_perspective_expander.xml
├── synthesizer_lvl08_creativity_enhancer.xml
├── synthesizer_lvl09_uncertainty_assessor.xml
├── synthesizer_lvl10_key_point_extractor.xml
├── synthesizer_lvl11_clarity_improver.xml
├── synthesizer_lvl12_fact_checker.xml
├── synthesizer_lvl13_bias_detector.xml
├── synthesizer_lvl14_practical_applicator.xml
└── synthesizer_lvl15_refinement_optimizer.xml
```
### 1. `synthesizer_lvl01_objective_setter.xml`

#### `synthesizer_lvl01_objective_setter.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Analyze the given input and clearly define the primary objective of the task.
  -->
  <purpose>Define the primary objective based on the provided input.</purpose>

  <instructions>
    <!-- "How" we refine: Focus on clarifying the main objective from the raw content -->
    <instruction>Review [PASTE_TEXT_HERE] thoroughly.</instruction>
    <instruction>Articulate the main goal or purpose in a concise statement.</instruction>
    <instruction>Ensure that the objective is specific, measurable, or at least clearly outlined.</instruction>
  </instructions>

  <!-- "What" is refined: the raw content from which to extract the objective -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 2. `synthesizer_lvl02_context_provider.xml`

#### `synthesizer_lvl02_context_provider.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Identify and incorporate relevant background info to enrich the response.
  -->
  <purpose>Add essential context to enhance the overall understanding.</purpose>

  <instructions>
    <!-- "How" we refine: incorporate external or background knowledge -->
    <instruction>Consult [RECAP] from Level 1 to ensure alignment with the defined objective.</instruction>
    <instruction>Insert pertinent background details or supporting information that the user/system might need.</instruction>
    <instruction>Keep the context relevant and avoid unnecessary tangents.</instruction>
  </instructions>

  <!-- Recap from Level 1 to keep track of the previously stated objective -->
  <recap>
    <!-- Inserted by code if referencing previous content -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 3. `synthesizer_lvl03_structure_architect.xml`

#### `synthesizer_lvl03_structure_architect.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Determine the format and structure best suited for the output, based on the objective.
  -->
  <purpose>Devise the optimal structure or format for the final output.</purpose>

  <instructions>
    <!-- "How" we refine: create an outline or skeleton -->
    <instruction>Review [RECAP] (Objective + Context) to understand the final goal.</instruction>
    <instruction>Propose a clear layout, such as sections, bullet points, or headings, that best serves the objective.</instruction>
    <instruction>Explain briefly why this structure is suitable, referencing [SCENARIO_CONTEXT] or any relevant scenario details if applicable.</instruction>
  </instructions>

  <recap>
    <!-- Content from Levels 1 & 2 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 4. `synthesizer_lvl04_example_generator.xml`

#### `synthesizer_lvl04_example_generator.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Generate relevant examples or analogies to illustrate key points in the response.
  -->
  <purpose>Provide concrete examples or analogies to clarify the content.</purpose>

  <instructions>
    <!-- "How" we refine: add demonstration or illustration -->
    <instruction>Review [RECAP] to identify the main points that would benefit from examples.</instruction>
    <instruction>Propose relevant examples or analogies to illustrate these points more vividly.</instruction>
    <instruction>Keep the examples concise, ensuring they remain aligned with the main objective.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1â€“3 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 5. `synthesizer_lvl05_task_decomposer.xml`

#### `synthesizer_lvl05_task_decomposer.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Break down complex parts into smaller, manageable chunks.
  -->
  <purpose>Decompose complex elements into simpler tasks or components.</purpose>

  <instructions>
    <!-- "How" we refine: identify intricacies and split them out -->
    <instruction>Check [RECAP] for any intricate or multi-step portions.</instruction>
    <instruction>Reorganize or outline these in bite-sized pieces that are easier to tackle.</instruction>
    <instruction>Preserve overall coherence with the main objective and context.</instruction>
  </instructions>

  <recap>
    <!-- Aggregated content from Levels 1â€“4 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 6. `synthesizer_lvl06_reasoning_explicator.xml`

#### `synthesizer_lvl06_reasoning_explicator.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Elaborate on the step-by-step reasoning behind key conclusions or arguments.
  -->
  <purpose>Make the underlying logic of the response transparent.</purpose>

  <instructions>
    <!-- "How" we refine: show reasoning paths -->
    <instruction>Use [RECAP] to pinpoint the main arguments or conclusions needing elaboration.</instruction>
    <instruction>Present a concise but clear chain-of-thought or rationale behind each conclusion.</instruction>
    <instruction>Ensure the reasoning is easy to follow without unnecessary complexity.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1â€“5 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 7. `synthesizer_lvl07_perspective_expander.xml`

#### `synthesizer_lvl07_perspective_expander.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Broaden the response by introducing alternative viewpoints or approaches.
  -->
  <purpose>Offer additional perspectives or methods that enhance the discussion.</purpose>

  <instructions>
    <!-- "How" we refine: gather diverse angles -->
    <instruction>Review [RECAP] to see where alternative stances or methods could be insightful.</instruction>
    <instruction>Propose at least one or two different ways to look at the problem or topic, supporting or contrasting the main approach.</instruction>
    <instruction>Ensure these viewpoints remain relevant and do not derail the primary objective.</instruction>
  </instructions>

  <recap>
    <!-- Content from Levels 1â€“6 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 8. `synthesizer_lvl08_creativity_enhancer.xml`

#### `synthesizer_lvl08_creativity_enhancer.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Introduce creative or novel ideas while staying relevant to the objective.
  -->
  <purpose>Add innovative or imaginative elements that enrich the response.</purpose>

  <instructions>
    <!-- "How" we refine: push for a bit of creativity -->
    <instruction>Reflect on [RECAP] to identify areas that could benefit from fresh or unexpected ideas.</instruction>
    <instruction>Suggest one or more creative twists—metaphors, scenarios, or unorthodox solutions—while maintaining alignment with the objective.</instruction>
    <instruction>Avoid overshadowing core clarity or correctness with excessive creativity.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1–7 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 9. `synthesizer_lvl09_uncertainty_assessor.xml`

#### `synthesizer_lvl09_uncertainty_assessor.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Evaluate confidence levels or uncertainties in different parts of the response.
  -->
  <purpose>Identify areas of doubt or knowledge gaps, and quantify confidence levels.</purpose>

  <instructions>
    <!-- "How" we refine: highlight any uncertainties -->
    <instruction>Scan [RECAP] to detect statements that might be uncertain, speculative, or incomplete.</instruction>
    <instruction>Tag these areas with a brief note on confidence or risk level (e.g., “Likely,” “Uncertain,” “High Confidence”).</instruction>
    <instruction>Where feasible, suggest how to resolve or investigate uncertainties further.</instruction>
  </instructions>

  <recap>
    <!-- Content from Levels 1–8 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 10. `synthesizer_lvl10_key_point_extractor.xml`

#### `synthesizer_lvl10_key_point_extractor.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Summarize the most crucial insights from the entire chain so far.
  -->
  <purpose>Distill the response down to its key points or takeaways.</purpose>

  <instructions>
    <!-- "How" we refine: produce a short, bullet-based summary of critical items -->
    <instruction>Review [RECAP] thoroughly and list the most critical points or insights in bullet form.</instruction>
    <instruction>Avoid minor details—emphasize what truly matters for the objective.</instruction>
    <instruction>Keep the summary succinct but comprehensive enough to reflect the major outcomes.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1–9 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 11. `synthesizer_lvl11_clarity_improver.xml`

#### `synthesizer_lvl11_clarity_improver.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Identify ambiguities or vague areas and clarify them.
  -->
  <purpose>Improve clarity by refining ambiguous or confusing segments.</purpose>

  <instructions>
    <!-- "How" we refine: highlight unclear parts and refine them -->
    <instruction>Check [RECAP] for any statements that might be misread, misunderstood, or appear contradictory.</instruction>
    <instruction>Rewrite or expand on these points for better clarity without increasing bloat.</instruction>
    <instruction>Ensure consistency of terminology and style across the whole response.</instruction>
  </instructions>

  <recap>
    <!-- Content from Levels 1â€“10 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 12. `synthesizer_lvl12_fact_checker.xml`

#### `synthesizer_lvl12_fact_checker.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Verify the factual claims in the response and correct inaccuracies.
  -->
  <purpose>Ensure factual correctness of all major statements.</purpose>

  <instructions>
    <!-- "How" we refine: cross-check references or known data -->
    <instruction>Review [RECAP] for any statements that might need external verification or data references.</instruction>
    <instruction>Flag and correct any obvious or suspected inaccuracies, or add disclaimers if uncertain.</instruction>
    <instruction>Preserve the overall flow of the response, modifying it only to maintain factual integrity.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1â€“11 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 13. `synthesizer_lvl13_bias_detector.xml`

#### `synthesizer_lvl13_bias_detector.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Identify and address potential biases or limitations in reasoning or conclusions.
  -->
  <purpose>Spot and mitigate biases in the response.</purpose>

  <instructions>
    <!-- "How" we refine: check for unintentional or one-sided assumptions -->
    <instruction>Re-examine [RECAP] to see if the response favors certain viewpoints or data unfairly.</instruction>
    <instruction>Call out biases or partialities; propose balanced phrasing or alternative considerations.</instruction>
    <instruction>Ensure the final output remains inclusive and well-rounded.</instruction>
  </instructions>

  <recap>
    <!-- Content from Levels 1â€“12 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 14. `synthesizer_lvl14_practical_applicator.xml`

#### `synthesizer_lvl14_practical_applicator.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Suggest real-world applications or practical use-cases related to the response.
  -->
  <purpose>Connect the main ideas to tangible, real-world scenarios.</purpose>

  <instructions>
    <!-- "How" we refine: illustrate practical usage or relevance -->
    <instruction>Review [RECAP] for the core insights or solutions.</instruction>
    <instruction>Offer a few practical examples or use-cases showing how to apply these insights in everyday or professional contexts.</instruction>
    <instruction>Keep suggestions realistic yet encouraging, tying back to the main objective.</instruction>
  </instructions>

  <recap>
    <!-- Summaries from Levels 1â€“13 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
### 15. `synthesizer_lvl15_refinement_optimizer.xml`

#### `synthesizer_lvl15_refinement_optimizer.xml`

```xml
<prompt>
  <!--
      Purpose (Why):
      Optimize the final text, enhancing clarity, conciseness, and impact while preserving key insights.
  -->
  <purpose>Polish and finalize the overall response for maximum effectiveness.</purpose>

  <instructions>
    <!-- "How" we refine: do a final pass to ensure everything is crisp and coherent -->
    <instruction>Integrate all prior steps from [RECAP], ensuring the final text is cohesive and well-structured.</instruction>
    <instruction>Eliminate redundant phrasing, refine awkward sentences, and highlight the most impactful details.</instruction>
    <instruction>Preserve essential content and any disclaimers or context from previous steps.</instruction>
  </instructions>

  <recap>
    <!-- Aggregated content from all prior levels -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

```
