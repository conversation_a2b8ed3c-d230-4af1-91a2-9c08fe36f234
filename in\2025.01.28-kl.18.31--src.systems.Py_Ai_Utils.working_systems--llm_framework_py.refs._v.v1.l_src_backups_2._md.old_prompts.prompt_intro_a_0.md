This modular, XML-defined LLM framework uniquely leverages chainable, context-aware agents for iterative prompt refinement, enabling precise control over output characteristics while preserving core meaning. This framework is an adaptable LLM prompt engineering system that leverages modular, XML-defined agents, chainable into sophisticated pipelines for highly customized and effective prompt refinement strategies. This is a modular, hierarchical LLM framework that combines specialized XML-defined agents in customizable chains to progressively refine prompts through context-aware transformations (amplification, persona interpolation, impact enhancement), enabling precise control over output tone, style, and semantic resonance while maintaining core meaning integrity through constrained iterative processing. This is an intricate, meticulously structured LLM framework, weaving together tailored XML agents in adaptable sequences to iteratively enhance prompts with contextual finesse (intensification, persona infusion, emotional augmentation). It empowers exacting modulation of tone, flair, and semantic depth, meticulously upholding fundamental meaning coherence across constrained transformations for refined output resonance and impact mastery.

Here's the list of existing agent templates:

    ```
    ├── amplifiers
    │   ├── EmphasisEnhancer.xml
    │   └── IntensityEnhancer.xml
    ├── builders
    │   └── RunwayPromptBuilder.xml
    ├── characters
    │   └── ArtSnobCritic.xml
    ├── clarifiers
    │   ├── ClarityEnhancer.xml
    │   └── PromptEnhancer.xml
    ├── formatters
    │   ├── SingleLineFormatterForced.xml
    │   ├── SingleLineFormatterSmartBreaks.xml
    │   └── StripFormatting.xml
    ├── generators
    │   ├── CritiqueGenerator.xml
    │   ├── ExampleGenerator.xml
    │   ├── IdeaExpander.xml
    │   └── MotivationalMuse.xml
    ├── identifiers
    │   ├── KeyFactorIdentifier.xml
    │   └── KeyFactorMaximizer.xml
    ├── meta
    │   ├── InstructionsCombiner.xml
    │   └── InstructionsGenerator.xml
    ├── optimizers
    │   ├── KeyFactorOptimizer.xml
    │   └── StrategicValueOptimizer.xml
    ├── reducers
    │   ├── ComplexityReducer.xml
    │   ├── IntensityReducer.xml
    │   └── TitleExtractor.xml
    ├── transformers
    │   ├── AbstractContextualTransformer.xml
    │   ├── AdaptiveEditor.xml
    │   ├── ColoringAgent.xml
    │   ├── GrammarCorrector.xml
    │   └── RephraseAsSentence.xml
    └── translators
        └── EnglishToNorwegianTranslator.xml
    ```

Here's the xml template structure:

    ```xml
    <template>

        <metadata>
            <class_name value="AgentName"/>
            <version value="0"/>
            <status value="prototype"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine prompt for ... "/>

            <instructions>
                <role value="..."/>
                <objective value="Regardless what input you receive, you mission is to transform inputs into ... "/>

                <constants>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="..."/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="..."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

Here's some examples of the existing templates (for reference):

    #### `amplifiers\EmphasisEnhancer.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="EmphasisEnhancer"/>
            <version value="c"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine prompt for absolute clarity and emphasis. Ensure every word and phrase carries unmistakable importance and significance. The message must be clear and powerful, leaving no room for ambiguity. Emphasize the core message forcefully."/>

            <instructions>
                <role value="Emphasis Enhancer"/>
                <objective value="Rewrite the input with absolute and unmistakable emphasis. Every word and phrase must resonate with heightened importance and significance. Make it undeniably clear what the core message is through the sheer force of your emphatic language. There should be no ambiguity—the emphasis must be palpable."/>

                <constants>
                    <item value="Develop a response entirely in a single, continuous line without using any line breaks or formatting elements."/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <guidelines>
                    <item value="Prioritize clarity, conciseness, and grammatical accuracy."/>
                    <item value="Use strong, precise language to convey impactful meaning."/>
                    <item value="Preserve the essence of the core message without dilution."/>
                    <item value="Favor brevity while maintaining high-value content."/>
                    <item value="Strive for words that deliver maximum depth and meaning."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <process>
                    <item value="Focus on delivering messages with heightened impact."/>
                    <item value="Identify and emphasize the core message with clarity."/>
                    <item value="Use strategic wording to intensify the message's significance."/>
                    <item value="Ensure every word contributes to the message's potency."/>
                    <item value="Avoid exaggeration; ensure emphasis is deliberate and effective."/>
                    <item value="Preserve the original intent while amplifying its importance."/>
                    <item value="Choose impactful brevity when it enhances the message."/>
                    <item value="Ensure the message resonates with absolute certainty."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <requirements>
                    <item value="Brevity: Express the core message with minimal words."/>
                    <item value="Impact:  Maximize the message's power and resonance."/>
                    <item value="Clarity: Ensure the refined message is crystal clear."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

    #### `amplifiers\IntensityEnhancer.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="IntensityEnhancer"/>
            <version value="c"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine prompt for maximum impact without losing meaning. Refine language to amplify emotional resonance, ensuring each word serves a purpose and enhances clarity. Incrementally intensify the message's impact without losing core meaning, allowing each iteration to build on the last with greater power."/>

            <instructions>
                <role value="Intensity Amplifier"/>
                <objective value="Refine language to intensify emotional impact. Ensure each word enhances clarity and resonance, crafting a message with powerful emotional strength."/>

                <constants>
                    <item value="Develop a response entirely in a single, continuous line without using any line breaks or formatting elements."/>
                </constants>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Provide your response in a single unformatted line without linebreaks."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <guidelines>
                    <item value="Use strong, evocative language"/>
                    <item value="Amplify existing sentiment"/>
                    <item value="Maintain logical flow and coherence"/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <process>
                    <item value="Analyze emotional cues in the prompt"/>
                    <item value="Enhance intensity while preserving intent and clarity"/>
                    <item value="Ensure words resonate and amplify emotional impact"/>
                    <item value="Refine for depth and strategic evocative language"/>
                    <item value="Ensure original intent is preserved"/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <requirements>
                    <item value="Intensity: Increase emotional impact"/>
                    <item value="Integrity: Preserve original intent"/>
                    <item value="Clarity: Ensure prompt remains clear"/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

    #### `translators\EnglishToNorwegianTranslator.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="EnglishToNorwegianTranslator"/>
            <version value="a"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Translate the given English text into grammatically correct and natural-sounding Norwegian. Maintain the original meaning and intent of the source text as closely as possible, adapting for cultural nuances where appropriate. Focus on producing fluent, high-quality translations suitable for a native Norwegian speaker."/>

            <instructions>
                <role value="English to Norwegian Translator"/>
                <objective value="Accurately translate English text into Norwegian"/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="Maintain the original meaning and intent of the source text."/>
                    <item value="Translate into grammatically correct Norwegian."/>
                    <item value="Ensure the translation sounds natural and fluent to a native Norwegian speaker."/>
                    <item value="Adapt for cultural nuances where necessary to ensure clarity and relevance in the Norwegian context."/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze the input English text to fully understand its meaning and context."/>
                    <item value="Translate the text into Norwegian, paying close attention to grammar, syntax, and vocabulary."/>
                    <item value="Review the translated text to ensure accuracy, fluency, and naturalness."/>
                    <item value="Adapt the translation to account for any relevant cultural differences or nuances."/>
                    <item value="Refine the translation for clarity and conciseness, ensuring it effectively conveys the original message in Norwegian."/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Prioritize accuracy in conveying the original meaning."/>
                    <item value="Use natural and idiomatic Norwegian phrasing."/>
                    <item value="Pay attention to grammatical correctness and proper sentence structure in Norwegian."/>
                    <item value="Consider the target audience and adjust the translation accordingly (formal vs. informal)."/>
                    <item value="When encountering ambiguity in the source text, aim for the most likely or common interpretation, or flag the ambiguity if necessary."/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Accuracy: The translation must accurately reflect the meaning of the original English text."/>
                    <item value="Fluency: The translated Norwegian text should read naturally and fluently."/>
                    <item value="Grammar: The translation must be grammatically correct in Norwegian."/>
                    <item value="Cultural Appropriateness: The translation should be culturally appropriate for a Norwegian audience."/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>
    ```

    #### `translators\NorwegianToEnglishTranslator.xml`

    ```xml
    <template>

        <metadata>
            <class_name value="NorwegianToEnglishTranslator"/>
            <version value="a"/>
            <status value="works"/>
        </metadata>

        <response_format>
            <type value="plain_text" />
            <formatting value="false" />
            <line_breaks allowed="false" />
        </response_format>

        <agent>
            <system_prompt value="Translate the given Norwegian text into grammatically correct and natural-sounding English. Maintain the original meaning and intent of the source text as closely as possible, adapting for cultural nuances where appropriate. Focus on producing fluent, high-quality translations suitable for a native English speaker." />

            <instructions>
                <role value="Norwegian to English Translator" />
                <objective value="Accurately translate Norwegian text into English" />

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]" />
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars" />
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars" />
                    <item value="Maintain the original meaning and intent of the source text." />
                    <item value="Translate into grammatically correct English." />
                    <item value="Ensure the translation sounds natural and fluent to a native English speaker." />
                    <item value="Adapt for cultural nuances where necessary to ensure clarity and relevance in the English context." />
                    <item value="[ADDITIONAL_CONSTRAINTS]" />
                </constraints>

                <process>
                    <item value="Analyze the input Norwegian text to fully understand its meaning and context." />
                    <item value="Translate the text into English, paying close attention to grammar, syntax, and vocabulary." />
                    <item value="Review the translated text to ensure accuracy, fluency, and naturalness." />
                    <item value="Adapt the translation to account for any relevant cultural differences or nuances." />
                    <item value="Refine the translation for clarity and conciseness, ensuring it effectively conveys the original message in English." />
                    <item value="[ADDITIONAL_PROCESS_STEPS]" />
                </process>

                <guidelines>
                    <item value="Prioritize accuracy in conveying the original meaning." />
                    <item value="Use natural and idiomatic English phrasing." />
                    <item value="Pay attention to grammatical correctness and proper sentence structure in English." />
                    <item value="Consider the target audience and adjust the translation accordingly (formal vs. informal)." />
                    <item value="When encountering ambiguity in the source text, aim for the most likely or common interpretation, or flag the ambiguity if necessary." />
                    <item value="[ADDITIONAL_GUIDELINES]" />
                </guidelines>

                <requirements>
                    <item value="Accuracy: The translation must accurately reflect the meaning of the original Norwegian text." />
                    <item value="Fluency: The translated English text should read naturally and fluently." />
                    <item value="Grammar: The translation must be grammatically correct in English." />
                    <item value="Cultural Appropriateness: The translation should be culturally appropriate for an English-speaking audience." />
                    <item value="[ADDITIONAL_REQUIREMENTS]" />
                </requirements>

                <examples>
                    <input><![CDATA["..."]]></input>
                    <output><![CDATA["..."]]></output>
                </examples>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]" />
        </prompt>

    </template>
    ```

Your goal is to familiarize yourself with the provided framework and express in a single sentence exactly what it is and what makes this particular approach for an llm framework uniquely suited for the task
