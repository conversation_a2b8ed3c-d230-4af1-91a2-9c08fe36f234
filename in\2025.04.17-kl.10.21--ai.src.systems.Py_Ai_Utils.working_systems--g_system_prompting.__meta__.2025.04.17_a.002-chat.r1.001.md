<!-- ======================================================= -->
<!-- [2025.04.16 23:42] -->
<!-- [2025.04.17 00:00] -->
<!-- [2025.04.17 00:29] -->
<!-- [2025.04.17 09:59] -->
<!-- [2025.04.17 10:16] -->
<!-- 'https://gemini.google.com/app/21aac601dd36f377' -->

Okay, here is a new, consolidated instruction sequence (ID: 0118) designed for **Sublime Text Plugin Familiarization**. This sequence integrates the core familiarization steps with the specific principles you provided (Structural Purity, Clarity & Conciseness, Essential Commentary, Optimized Implementation), expanding the process to 15 steps for thorough analysis.

---

**Sequence ID: 0118 - Enhanced Sublime Plugin Familiarization & Quality Assessment**

---

#### `0112-a-st-plugin-plugin-manifest-component-inventory.md`

```markdown
[Plugin Manifest & Component Inventory] Your first task is to inventory the plugin's structure: List all files within the plugin's directory, identify their type based on extension (`.py`, `.sublime-commands`, `.sublime-settings`, `.sublime-menu`, `.sublime-keymap`, etc.), and state the general purpose of each file type in the context of a Sublime Text plugin. Execute as `{role=sublime_manifest_analyzer; input=plugin_directory_path:str; process=[list_plugin_files(), identify_file_types_by_extension(), determine_standard_purpose_of_each_file_type()]; output={component_inventory:list[dict(file:str, type:str, purpose:str)]}}`
```

---

#### `0112-b-st-plugin-settings-file-analysis.md`

```markdown
[Settings File Analysis] Analyze the content of the `.sublime-settings` file(s). Extract all defined settings, their default values, and infer their purpose based on their names and any accompanying comments, noting how they configure plugin behavior. Execute as `{role=sublime_settings_parser; input=settings_file_content:str; process=[parse_settings_json(), extract_setting_keys_values(), infer_setting_purpose(), analyze_configurability()]; output={plugin_settings:list[dict(setting:str, default_value:any, purpose:str)]}}`
```

---

#### `0112-c-st-plugin-command-trigger-mapping.md`

```markdown
[Command & Trigger Mapping] Analyze the `.sublime-commands`, `.sublime-menu`, and any `.sublime-keymap` files. Map user-facing captions/menus/keybindings to the specific Python `command` names they trigger, including any `args` passed, identifying all user interaction entry points. Execute as `{role=sublime_trigger_mapper; input={commands_content:str|None, menu_content:str|None, keymap_content:str|None}; process=[parse_command_definitions(), parse_menu_definitions(), parse_keymap_definitions(), map_ui_trigger_to_python_command_and_args()]; output={command_triggers:list[dict(trigger_type:str, trigger_ui:str, python_command:str, args:dict)]}}`
```

---

#### `0112-d-st-plugin-core-python-logic-identification.md`

```markdown
[Core Python Logic Identification] Analyze the main `.py` file(s). Identify all classes inheriting from `sublime_plugin` base classes (e.g., `EventListener`, `TextCommand`, `WindowCommand`, `ApplicationCommand`). List the key methods within these classes (especially `on_*` event handlers and `run` methods) that constitute the core logic execution points. Execute as `{role=sublime_python_analyzer; input=python_file_content:str; process=[find_sublime_plugin_subclasses(), identify_event_handler_methods(), identify_command_run_methods(), list_key_logic_entry_points()]; output={logic_entry_points:dict(event_listeners:list[str], commands:list[str])}}`
```

---

#### `0112-e-st-plugin-structural-purity-modularity-assessment.md`

```markdown
[Structural Purity & Modularity Assessment] Analyze the organization of the Python code identified in the previous step. Evaluate how functionality is decomposed into modules, classes, and functions. Assess structural logic, cohesion of related functionalities, clarity of interfaces between components, and adherence to modular design principles based on naming and structure. Execute as `{role=sublime_structure_assessor; input={python_file_content:str, logic_entry_points:dict}; process=[analyze_code_decomposition(modules, classes, functions), evaluate_functional_cohesion(), assess_interface_clarity(), check_naming_convention_consistency_for_structure(), report_modularity_strengths_weaknesses()]; output={structural_assessment:dict(modularity_score:float, cohesion_notes:str, interface_clarity_notes:str)}}`
```

---

#### `0112-f-st-plugin-event-listener-behavior-analysis.md`

```markdown
[Event Listener Behavior Analysis] For each identified `EventListener` class and its `on_*` methods, detail the specific Sublime Text events triggering them and summarize the primary actions performed, noting interactions with other components or APIs. Execute as `{role=sublime_event_analyzer; input={python_file_content:str, logic_entry_points:dict}; process=[analyze_each_on_method(), determine_trigger_conditions(), summarize_method_actions(), identify_api_calls_within_events()]; output={event_behaviors:list[dict(method:str, triggers:str, summary:str)]}}`
```

---

#### `0112-g-st-plugin-command-execution-analysis.md`

```markdown
[Command Execution Analysis] For each identified `*Command` class and its `run` method, analyze the code to determine the sequence of actions performed when the command is executed. Note arguments, state changes, and API interactions. Execute as `{role=sublime_command_analyzer; input={python_file_content:str, logic_entry_points:dict}; process=[analyze_each_run_method(), identify_input_arguments(), trace_execution_logic(), summarize_command_outcome_and_side_effects()]; output={command_implementations:list[dict(command_class:str, summary:str, args_handling:str)]}}`
```

---

#### `0112-h-st-plugin-implementation-optimization-assessment.md`

```markdown
[Implementation Optimization Assessment] Within the analyzed event and command methods, assess the implementation approach for effectiveness and simplicity. Identify areas of potential redundancy, overly complex logic, or opportunities where alternative structures (e.g., consolidating functions into a class) might significantly enhance clarity, maintainability, or elegance, *without suggesting specific code changes*. Execute as `{role=sublime_optimization_assessor; input={python_file_content:str, event_behaviors:list, command_implementations:list}; process=[scan_for_redundant_logic_patterns(), evaluate_algorithmic_complexity_simplicity(), identify_potential_structural_optimization_opportunities(), assess_effectiveness_of_implementation()]; output={optimization_potential:dict(redundancy_areas:list, complexity_hotspots:list, structural_suggestions_highlevel:list)}}`
```

---

#### `0112-i-st-plugin-clarity-conciseness-self-explanation-audit.md`

```markdown
[Clarity, Conciseness & Self-Explanation Audit] Evaluate the codebase (especially function/method bodies) for inherent clarity and conciseness. Assess the quality of identifier naming, the simplicity of logical flow, and how well the code explains itself *without relying on comments*. Note areas where clarity could be improved via better naming or structure, promoting self-documentation. Execute as `{role=code_clarity_auditor; input=python_file_content:str; process=[evaluate_identifier_naming_clarity(), assess_logical_flow_simplicity(), determine_self_explanation_level(), identify_areas_needing_clarity_refactoring()]; output={clarity_assessment:dict(naming_score:float, flow_score:float, self_explanation_notes:str)}}`
```

---

#### `0112-j-st-plugin-comment-inventory-essentiality-analysis.md`

```markdown
[Comment Inventory & Essentiality Analysis] Inventory all comments in the Python code. Apply the "Essential Commentary Only" principle: classify each comment as 'Essential' (explaining non-obvious 'why'/'what', critical architecture), 'Redundant/Obvious' (restating code, explaining simple names), or 'Interface-Doc' (should be in docstring). Flag non-essential comments for potential removal or refactoring of code to obviate them. Execute as `{role=essential_comment_analyzer; input=python_file_content:str; process=[inventory_all_python_comments_with_location(), classify_comment_purpose_and_necessity(), apply_strict_essential_only_filter(), flag_non_essential_comments()]; output={comment_analysis:list[dict(location:str, content:str, classification:str, action_needed:str)]}}`
```

---

#### `0112-k-st-plugin-interface-documentation-audit-docstrings.md`

```markdown
[Interface Documentation Audit (Docstrings)] Specifically review public classes, methods, and functions. Verify the presence, clarity, and completeness of docstrings explaining purpose, parameters, and returns according to standard conventions (e.g., PEP 257). Ensure inline comments are not used inappropriately for primary interface documentation, promoting clean interfaces. Execute as `{role=docstring_auditor; input=python_file_content:str; process=[identify_public_interfaces(), audit_docstring_presence_and_quality(), check_conformance_to_standards(), flag_interfaces_lacking_proper_docstrings()]; output={docstring_audit:dict(coverage_score:float, quality_notes:str)}}`
```

---

#### `0112-l-st-plugin-sublime-api-usage-audit.md`

```markdown
[Sublime API Usage Audit] Identify significant Sublime Text API calls (`view.*`, `window.*`, `sublime.*`) within the Python code. Briefly explain the purpose of key/frequent calls in the plugin's context to understand external dependencies and interactions. Execute as `{role=sublime_api_auditor; input=python_file_content:str; process=[scan_for_sublime_api_calls(), identify_key_api_functions_methods(), determine_purpose_in_context(), list_significant_api_usage()]; output={api_usage:list[dict(api_call:str, purpose:str)]}}`
```

---

#### `0112-m-st-plugin-internal-state-helper-analysis.md`

```markdown
[Internal State & Helper Function Analysis] Analyze the Python code to identify variables used for state management (class or global scope) and summarize their role and lifecycle. Identify internal helper methods (`_` prefix typically) and summarize their purpose and contribution to modularity. Execute as `{role=sublime_internal_analyzer; input=python_file_content:str; process=[find_state_variables(), determine_state_management_logic(), identify_helper_methods(), summarize_helper_purpose()]; output={internal_logic:dict(state_variables:list[dict], helpers:list[dict])}}`
```

---

#### `0112-n-st-plugin-cross-component-interaction-synthesis.md`

```markdown
[Cross-Component Interaction Synthesis] Synthesize findings: Explain how UI triggers (`command_triggers`) activate Python commands (`command_implementations`), how settings (`plugin_settings`) affect logic, how Python code (`event_behaviors`, `command_implementations`) uses APIs (`api_usage`) and manages state (`internal_logic`), considering the overall structural organization (`structural_assessment`). Map key user workflows. Execute as `{role=sublime_interaction_synthesizer; input=all_previous_step_outputs:dict; process=[connect_all_components(ui, settings, python_code, api, state), trace_key_user_workflows(), synthesize_holistic_operational_view()]; output={plugin_workflow_summary:str}}`
```

---

#### `0112-o-st-plugin-consolidated-familiarization-summary-quality-report.md`

```markdown
[Consolidated Familiarization Summary & Quality Report] Generate a final, high-level summary of the plugin's purpose and core functionality based on the `plugin_workflow_summary`. Additionally, explicitly report on the findings related to the integrated principles: structural quality (`structural_assessment`), clarity/self-explanation (`clarity_assessment`), comment essentiality (`comment_analysis` summary), and optimization potential (`optimization_potential`), providing a holistic quality overview. Execute as `{role=plugin_quality_summarizer; input={plugin_workflow_summary:str, structural_assessment:dict, clarity_assessment:dict, comment_analysis:list, optimization_potential:dict}; process=[distill_core_purpose_and_features(), summarize_structural_modularity_findings(), summarize_clarity_self_explanation_level(), report_on_comment_policy_adherence(), highlight_optimization_opportunities(), produce_consolidated_report()]; output={final_familiarization_report:str}}`
```
