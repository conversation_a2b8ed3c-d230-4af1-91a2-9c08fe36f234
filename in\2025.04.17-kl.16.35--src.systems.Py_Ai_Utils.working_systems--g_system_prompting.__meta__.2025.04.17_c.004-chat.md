
Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters:

---

#### `1`

    #### `0140-a-structure-and-purpose-orientation-scan.md`

    ```markdown
    [Structure & Purpose Orientation Scan] Establish a structural and conceptual baseline. Inventory all top-level components, identify entry/exit points, and map architectural regions to functional roles. Distill the systemâ€™s primary intent from structure and traceable behaviorsâ€”not from wishful documentation. Execute as: `{role=structure_orienter; input=codebase_root:path; process=[inventory_directories_and_files(), classify_modules_by_function(), trace_entry_exit_points(), infer_primary_purpose_from_structure_and_code()], output={structure_map:dict, core_purpose:str}}`
    ```

    #### `0140-b-critical-path-and-complexity-trace.md`

    ```markdown
    [Critical Path & Complexity Trace] Map the core workflows aligned with the `core_purpose`. Trace control/data flows across modules. Identify coupling clusters, cognitive load bottlenecks, and convoluted abstractions. Focus on where structure fractures clarity. Execute as: `{role=workflow_tracer; input={structure_map:dict, core_purpose:str}; process=[trace_primary_workflows(), visualize_data_and_control_flow(), detect_cross-module complexity zones(), flag indirect abstraction breakpoints()], output={workflow_map:dict, complexity_hotspots:list}}`
    ```

    #### `0140-c-inherent-clarity-and-responsibility-audit.md`

    ```markdown
    [Inherent Clarity & Responsibility Audit] Evaluate how well the codebase *explains itself*. Assess naming quality, logic transparency, structural cohesion, and adherence to SRP. Identify regions that rely on commentary for basic clarityâ€”an unforgivable sin in elegant architecture. Execute as: `{role=clarity_auditor; input={workflow_map:dict, codebase_content:dict}; process=[score_naming_and_structural_expressiveness(), identify_comment-reliant_zones(), detect_srp_violations_and_multi-responsibility_components(), assess_cohesion_per_module()], output={clarity_issues:dict, srp_violations:list, cohesion_gaps:list}}`
    ```

    #### `0140-d-commentary-minimization-and-retention-pass.md`

    ```markdown
    [Commentary Minimization & Retention Pass] Apply the minimalist standard: retain only commentary that explains non-obvious *why*. Remove all commentary that parrots *what* or *how*. The structure and naming must render all such fluff unnecessary. Execute as: `{role=commentary_refiner; input={codebase_content:dict}; process=[extract_all_comments(), classify_by_intent(why_what_how), remove_non-essential_comments(), retain_or_rewrite_only_crucial_explanations()], output={comment_changes:list}}`
    ```

    #### `0140-e-structural-and-logical-simplification-targeting.md`

    ```markdown
    [Structural & Logical Simplification Targeting] Merge prior findings to identify prime cleanup targets. Seek low-risk, high-impact opportunities: dead code, poor cohesion, redundant logic, confusing file/module layout, and ambiguous naming. Execute as: `{role=simplification_targeter; input={clarity_issues:dict, srp_violations:list, complexity_hotspots:list}; process=[rank_targets_by_impact_vs_risk(), group_targets_by category (logic, naming, structure), detect redundant/fragmented flows(), prepare simplified alternatives], output={simplification_targets:list}}`
    ```

    #### `0140-f-prioritized-cleanup-plan-synthesis.md`

    ```markdown
    [Prioritized Cleanup Plan Synthesis] Produce a surgically ordered, traceable plan of refactoring actions. Prioritize safety, readability yield, and principle alignment. Each action must be atomic, reversible, and elegance-inducing. Execute as: `{role=cleanup_planner; input={simplification_targets:list, comment_changes:list}; process=[merge_cleanup_targets_and_comment_actions(), order_by_impact_and_simplicity(), sequence_refactors_for_dependency-safety(), produce_cleanup_steps_with_rationale()], output={prioritized_plan:list}}`
    ```

    #### `0140-g-guided-implementation-and-integrity-check.md`

    ```markdown
    [Guided Implementation & Integrity Check] Apply the `prioritized_plan`. Execute refactorings step-by-step. After each logical unit of work, verify functional preservation via tests or manual flow assertions. Execute as: `{role=executor_validator; input={codebase_root:path, prioritized_plan:list}; process=[execute_stepwise_changes(), run_tests_or_manual_checks(), confirm_no_regression(), adjust_plan_if_needed()], output={cleaned_codebase:path, integrity_verified:bool}}`
    ```

    #### `0140-h-final-principle-alignment-review.md`

    ```markdown
    [Final Principle Alignment Review] Assess the cleaned codebase against the core ideals: simplicity, structural clarity, cohesion, self-explanation. Identify remaining violations or incomplete refinements. Decide if further iteration is warranted. Execute as: `{role=final_reviewer; input={cleaned_codebase:path, structure_map:dict, core_purpose:str}; process=[score_against_core_principles(), highlight_any_remaining_opportunities(), estimate cost/impact of another cycle(), decide iteration necessity()], output={final_report:dict, recommend_next_cycle:bool}}`
    ```

    #### `0140-i-cleanup-impact-summary-and-developer-handoff.md`

    ```markdown
    [Cleanup Impact Summary & Developer Handoff] Summarize the cleanupâ€™s purpose, principles enforced, actions taken, and resulting benefits. Provide a crystal-clear guide for future developers: what changed, why it mattered, and how to uphold its new elegance. Execute as: `{role=cleanup_reporter; input={prioritized_plan:list, final_report:dict}; process=[map_actions_to principles(), summarize clarity and maintainability improvements(), describe before/after structural flow, write future-facing developer notes()], output={handoff_summary:str}}`
    ```

---

#### `2`

    #### `0141-a-baseline-structure-intent-map.md`

    ```markdown
    [Baseline Structure & Intent Map] Begin by scanning the full codebase to map its high-level structure. Identify main files, directories, and components. Determine entry points and infer the system’s core purpose from its structure. This establishes an aligned foundation for cleanup. Execute as `{role=baseline_mapper; input=codebase_root:path; process=[inventory_top_level_structure(), identify_entry_exit_points(), outline_primary_components_and_domains(), distill_core_purpose()]; output={structure_overview:dict, core_purpose:str}}`
    ```

    #### `0141-b-critical-path-and-flow-mapping.md`

    ```markdown
    [Critical Path & Flow Mapping] Trace key workflows that drive the system’s primary purpose. Map control and data flow through components. Identify logic complexity, coupling hotspots, and conceptual bottlenecks that affect clarity. Execute as `{role=flow_mapper; input={structure_overview:dict, core_purpose:str}; process=[trace_primary_execution_paths(), map_internal_data_transformations(), detect_cross-module_coupling(), isolate_workflow_complexity_zones()]; output={workflow_map:list, complexity_zones:list}}`
    ```

    #### `0141-c-readability-cohesion-and-comment-dependence-audit.md`

    ```markdown
    [Readability, Cohesion & Comment Dependence Audit] Evaluate identifier clarity, self-explanation, and cohesion within modules. Identify places where comments are compensating for poor naming or structure. Highlight SRP violations, comment-overreliance, and unclear abstractions. Execute as `{role=clarity_auditor; input={workflow_map:list, complexity_zones:list}; process=[assess_identifier_naming_quality(), evaluate_module_cohesion(), locate_comment-dependent_clarity_zones(), detect_responsibility_violations(), summarize_readability_issues()]; output={clarity_audit_report:dict}}`
    ```

    #### `0141-d-simplification-opportunities-synthesis.md`

    ```markdown
    [Simplification Opportunities Synthesis] Consolidate audit results to detect opportunities for reducing complexity. Target dead code, redundant logic, tight coupling, fragmented responsibility, and over-commented zones. Execute as `{role=simplification_scanner; input={clarity_audit_report:dict}; process=[detect_dead_code(), isolate_redundant_patterns(), assess structure_naming_confusion(), flag over-fragmentation_and_low_cohesion(), synthesize_simplification_targets()]; output={simplification_targets:dict}}`
    ```

    #### `0141-e-cleanup-plan-generation.md`

    ```markdown
    [Cleanup Plan Generation] Translate findings into an actionable cleanup plan. Prioritize clarity and simplicity gains that are low risk and high value. Focus on improving naming, removing clutter, restructuring modules, and reducing commentary dependency. Execute as `{role=cleanup_planner; input={simplification_targets:dict}; process=[rank_opportunities_by_impact_and_safety(), define_discrete_cleanup_actions(), group_actions_by_scope(structure, logic, naming, comments), generate_execution_plan_sequence()]; output={cleanup_plan:list}}`
    ```

    #### `0141-f-guided-structure-and-logic-refinement.md`

    ```markdown
    [Guided Structure & Logic Refinement] Execute the `cleanup_plan` step-by-step. Apply minimal, high-impact changes that reinforce clarity: structural simplifications, naming refactors, logic consolidations, and decoupling. Execute as `{role=refactor_executor; input={codebase_root:path, cleanup_plan:list}; process=[perform_directory_file_restructuring(), apply_naming_and_cohesion_improvements(), simplify_control_flows(), remove_dead_code(), reduce_to_essential_comments()]; output={refactored_codebase:path}}`
    ```

    #### `0141-g-integrity-verification-and-principle-alignment-check.md`

    ```markdown
    [Integrity Verification & Principle Alignment Check] Verify that refactoring preserved behavior and improved alignment with core principles: Simplicity, SRP, Minimalism, Self-Explanation. Run tests, check for regressions, and review clarity. Execute as `{role=validation_checker; input={refactored_codebase:path, core_purpose:str}; process=[run_test_suite_or_manual_flow_validation(), check_structural_alignment_with_intent(), confirm_code_self_explanation(), audit_against_design_principles()], output={validation_report:dict(functional_equivalence:bool, clarity_score:float, remaining_gaps:list)}}`
    ```

    #### `0141-h-recursive-refinement-checkpoint.md`

    ```markdown
    [Recursive Refinement Checkpoint] Review the `validation_report`. If further clarity or simplification gains are evident and low cost, define a focused scope for another refinement cycle. Else, finalize cleanup and report outcome. Execute as `{role=refinement_assessor; input={validation_report:dict}; process=[analyze_remaining_clarity_gaps(), assess_effort_vs_impact_for_next_pass(), determine_if_next_cycle_needed(), define_refinement_scope_or_finalize()], output={next_steps:dict(proceed:bool, focus:list, summary:str)}}`
    ```

---

#### `3`

    #### `0142-a-cleanup-step-01-structural-purpose-orientation.md`
    ```markdown
    [Structural & Purpose Orientation] Begin with a complete structural sweep: identify major directories, files, modules, and boundaries. Infer the systemâ€™s intended purpose and primary workflows through code inspection and artifact analysis. Execute as: `{role=structure_orienter; input=codebase_root:path; process=[scan_directory_structure(), inventory_modules_and_artifacts(), identify_entry_exit_points(), infer_core_purpose_from_usage_patterns()], output={structure_map:dict, core_purpose:str}}`
    ```

    #### `0142-b-cleanup-step-02-logic-path-dependency-trace.md`
    ```markdown
    [Logic Path & Dependency Trace] Map control, data, and dependency flows essential to the `core_purpose`. Visualize high-impact logic routes, critical dependencies, and coupling zones. Execute as: `{role=logic_mapper; input={structure_map:dict, core_purpose:str}; process=[trace_control_and_data_flow(), detect_module_import_graph(), identify_coupling_clusters(), highlight_logic_hotspots()], output={workflow_map:dict, dependency_map:dict, complexity_zones:list}}`
    ```

    #### `0142-c-cleanup-step-03-clarity-cohesion-self-explanation-audit.md`
    ```markdown
    [Clarity, Cohesion & Self-Explanation Audit] Audit the codebase for naming clarity, structural cohesion, and readability without comments. Identify SRP violations, over-fragmentation, and areas where structure fails to explain itself. Execute as: `{role=clarity_auditor; input={workflow_map:dict, dependency_map:dict}; process=[assess_identifier_clarity(), locate cohesion/srp violations(), detect comment-dependent comprehension(), summarize self-explanation weak points()], output={clarity_report:dict, cohesion_findings:list, comment_dependence:list}}`
    ```

    #### `0142-d-cleanup-step-04-cleanup-target-opportunity-synthesis.md`
    ```markdown
    [Cleanup Target & Opportunity Synthesis] Consolidate findings to isolate cleanup zones with highest clarity gain vs effort. Focus on logic simplification, structural realignment, naming improvements, and comment reduction. Execute as: `{role=opportunity_synthesizer; input={clarity_report:dict, cohesion_findings:list, comment_dependence:list}; process=[rank_issues_by_impact_vs_effort(), group related issues by scope(), isolate safe/high-value cleanup zones(), define actionable targets()], output={cleanup_targets:list, opportunity_matrix:dict}}`
    ```

    #### `0142-e-cleanup-step-05-prioritized-refactor-plan.md`
    ```markdown
    [Prioritized Refactor Plan] Generate an atomic cleanup blueprint. Each action must yield measurable clarity/simplicity benefit with low risk. Order steps to optimize safety, flow, and minimal disruption. Execute as: `{role=refactor_planner; input={cleanup_targets:list}; process=[decompose_targets_into_refactor_steps(), rank_by safety-impact ratio(), establish task dependencies, structure into sequential plan()], output={refactor_plan:list}}`
    ```

    #### `0142-f-cleanup-step-06-structure-and-naming-refactor-pass.md`
    ```markdown
    [Structure & Naming Refactor Pass] Apply structural reorganizations and identifier renaming from `refactor_plan`. Ensure improved grouping, clearer file/module names, reduced fragmentation, and more intuitive navigation. Execute as: `{role=structure_executor; input={refactor_plan:list, codebase_root:path}; process=[restructure_directories_and_files(), refactor module/group boundaries(), unify naming conventions, update all references/imports], output={realigned_structure:path}}`
    ```

    #### `0142-g-cleanup-step-07-logic-simplification-and-comment-reduction.md`
    ```markdown
    [Logic Simplification & Comment Reduction] Simplify internal logic, consolidate functionality, and remove dead/duplicated code. Strip unnecessary commentsâ€”retain only *why-level* intent comments. Execute as: `{role=logic_simplifier; input={realigned_structure:path, refactor_plan:list}; process=[remove_redundant_logic(), enforce single-responsibility, apply naming-based clarification, purge/comment-refine non-essential notes()], output={refactored_codebase:path}}`
    ```

    #### `0142-h-cleanup-step-08-validation-pass-and-principle-alignment.md`
    ```markdown
    [Validation Pass & Principle Alignment Review] Run regression validation and audit final structure against principles: Simplicity, Clarity, SRP, Minimalism, and Self-Explanation. Verify functional equivalence and structural clarity. Execute as: `{role=validator; input={original_codebase:any, refactored_codebase:path}; process=[run regression checks(), audit for principle adherence(), check navigation and readability, record any remaining issues()], output={validation_report:dict(functional_equivalence:bool, principle_score:float, residual_issues:list}}`
    ```

    #### `0142-i-cleanup-step-09-recursive-refinement-checkpoint.md`
    ```markdown
    [Recursive Refinement Checkpoint] If validation passes and structure is aligned, conclude. If complexity, redundancy, or naming gaps remain with reasonable refactor potential, define scope for next cycle. Execute as: `{role=refinement_reviewer; input={refactored_codebase:path, validation_report:dict}; process=[identify remaining cleanup value(), weigh cost/benefit of another pass(), define next cycleâ€™s narrowed focus()], output={next_pass_decision:dict(proceed:bool, recommended_scope:list)}}`
    ```

    #### `0142-j-cleanup-step-10-cleanup-summary-and-principle-rationale-report.md`
    ```markdown
    [Cleanup Summary & Principle Rationale Report] Document the rationale and outcome for each major change, referencing clarity/cohesion principles. Provide a human-readable summary suitable for onboarding or auditing. Execute as: `{role=impact_reporter; input={refactor_plan:list, validation_report:dict}; process=[map actions to principle-based motivations(), summarize before/after structural clarity(), compile onboarding notes and rationale summaries()], output={cleanup_summary:str}}`
    ```
    ```

---

#### `4`

    #### `0143-a-cleanup-structure-intent-orientation.md`

    ```markdown
    [Structure & Intent Orientation] Establish foundational understanding. Map top-level structure, locate entry/output flows, and distill the system‚Äôs purpose. Determine if structure communicates function. Execute as `{role=structure_orienter; input=codebase_root:path; process=[map_directory_structure(), identify_entry_exit_points(), summarize_core_modules_and_files(), distill_system_purpose_from_code_structure()], output={structure_map:dict, system_purpose:str}}`
    ```

    #### `0143-b-cleanup-core-flow-and-dependency-tracing.md`

    ```markdown
    [Core Flow & Dependency Tracing] Trace the key workflows that drive the system‚Äôs `system_purpose`. Map logic and data propagation across components and detect architectural bottlenecks. Execute as `{role=flow_mapper; input={structure_map:dict, system_purpose:str}; process=[identify_primary_control_paths(), trace_data_flow_between_components(), map import and call dependencies, highlight complex junctions and circular dependencies()], output={workflow_map:dict, dependency_graph:dict, complexity_hotspots:list}}`
    ```

    #### `0143-c-cleanup-clarity-cohesion-and-comment-reliance-audit.md`

    ```markdown
    [Clarity, Cohesion & Comment Reliance Audit] Audit the codebase for readability, naming precision, comment reliance, and cohesion. Determine where clarity is structural vs. supplemental. Execute as `{role=clarity_auditor; input={workflow_map:dict, codebase_content:dict}; process=[evaluate naming quality(), assess module/class cohesion(), locate SRP violations(), identify over-commented logic, classify 'why' vs 'what' comments()], output={clarity_issues:list, comment_dependence:list, cohesion_warnings:list}}`
    ```

    #### `0143-d-cleanup-simplification-target-synthesis.md`

    ```markdown
    [Simplification Target Synthesis] Synthesize all clarity, cohesion, and complexity findings into prioritized, principle-aligned cleanup targets. Execute as `{role=simplification_synthesizer; input={clarity_issues:list, complexity_hotspots:list, cohesion_warnings:list, comment_dependence:list}; process=[merge_and deduplicate issues(), rank targets by impact-to-risk ratio(), isolate high-value naming/logic/structure opportunities(), generate simplification_targets:list], output={simplification_targets:list}}`
    ```

    #### `0143-e-cleanup-guided-refactoring-and-cohesive-realignment.md`

    ```markdown
    [Guided Refactoring & Cohesive Realignment] Execute high-confidence simplification and structure refactors. Improve naming, restructure files, and clarify logic flows‚Äîreinforcing self-explanation. Execute as `{role=refactor_agent; input={codebase_root:path, simplification_targets:list}; process=[apply file/module reorganization(), simplify logic flows(), enforce naming clarity(), implement SRP adherence(), reduce inter-component coupling()], output={refactored_codebase:path}}`
    ```

    #### `0143-f-cleanup-functional-and-principle-validation.md`

    ```markdown
    [Functional & Principle Validation] Validate the behavior and structure of the `refactored_codebase`. Check that simplification retained all functionality and improved core principle alignment. Execute as `{role=validator; input={refactored_codebase:path, original_codebase_snapshot:any}; process=[verify behavioral equivalence(), rerun test suite or key flows(), reassess self-explanation via structure/naming(), score principle alignment()], output={validation_report:dict(functional_match:bool, clarity_score:int, remaining_issues:list)}}`
    ```

    #### `0143-g-cleanup-refinement-scope-checkpoint.md`

    ```markdown
    [Refinement Scope Checkpoint] Decide whether to end or continue refinement. Analyze `validation_report` to identify cost-effective future cleanup areas. Execute as `{role=refinement_assessor; input={validation_report:dict, refactored_codebase:path}; process=[evaluate remaining complexity vs effort(), isolate new refinement scopes(), estimate return on another cycle(), recommend continuation or closure()], output={refinement_decision:dict(proceed:bool, focus_areas:list, recommendation:str)}}`
    ```

---

#### `5`

    #### `0144-a-cleanup-holistic-structure-purpose-scan.md`

    ```markdown
    [Holistic Structure & Purpose Scan] Perform a rapid overview of the codebase to establish its overall purpose, how it’s organized, and where main logic resides. Inventory top-level directories and files, identify entry points, and outline primary responsibilities. This baseline guides all subsequent cleanup steps. Execute as: `{role=holistic_scanner; input=codebase_root:any; process=[map_primary_directories_files(), identify_main_entry_points_and_data_flows(), note_core_dependencies(), infer_system_intent_and_primary_functionality()]; output={structure_map:dict, inferred_purpose:str}}`
    ```

    #### `0144-b-cleanup-key-workflow-dependency-mapping.md`

    ```markdown
    [Key Workflow & Dependency Mapping] Focus on the essential workflows that fulfill the `inferred_purpose`. Trace how control and data move through the system; map which modules, classes, or functions are critical to these flows. Capture key interdependencies and potential complexity hotspots. Execute as: `{role=workflow_mapper; input={structure_map:dict, inferred_purpose:str, codebase_content:dict}; process=[identify_critical_workflows(), trace_control_flow_in_core_paths(), outline_primary_data_transformations(), detect_high_dependency_zones()], output={workflow_maps:list, dependency_map:dict, complexity_nodes:list}}`
    ```

    #### `0144-c-cleanup-clarity-cohesion-redundancy-audit.md`

    ```markdown
    [Clarity, Cohesion & Redundancy Audit] Evaluate the codebase against core design principles: naming clarity, logical simplicity, single responsibility adherence, and module/class cohesion. Identify duplication, dead code, or reliance on verbose comments for explanation. Single out components that break the flow or obscure readability. Execute as: `{role=clarity_auditor; input={workflow_maps:list, dependency_map:dict, codebase_content:dict}; process=[examine_identifier_naming_quality(), detect_duplicate_or_dead_logic(), assess_module_class_cohesion_and_srp(), locate_comment_overdependence_or_inconsistency()], output={audit_findings:dict(clarity_issues:list, redundancy_report:list, srp_violations:list, comment_hotspots:list)}}`
    ```

    #### `0144-d-cleanup-prioritized-cleanup-plan-synthesis.md`

    ```markdown
    [Prioritized Cleanup Plan Synthesis] Combine insights from the audits into a cohesive, *incremental* action plan. Focus on safe, high-impact improvements first (removing dead code, simplifying naming, re-grouping cohesive elements, trimming unnecessary comments), followed by deeper architectural or logic changes. Provide a clear, itemized roadmap. Execute as: `{role=cleanup_planner; input={audit_findings:dict}; process=[compile_all_issues_by_severity_and_simplicity(), rank_actions_by_impact_and_risk(), proposestructure_refactors_and_naming_overhauls(), finalize_sequential_cleanup_tasks()], output={cleanup_plan:list_of_tasks}}`
    ```

    #### `0144-e-cleanup-targeted-realignment-refactoring.md`

    ```markdown
    [Targeted Realignment & Refactoring Execution] Carry out structural reorganizations and refactoring steps specified in the `cleanup_plan`. Adjust directories, rename files/modules for clarity, reduce cross-dependencies, and consolidate scattered logic. Ensure each modification enhances both understanding and maintainability. Execute as: `{role=structure_refiner; input={codebase_root:any, cleanup_plan:list}; process=[restructure_files_and_directories_for_flow_cohesion(), apply_naming_improvements(), remove_or_merge_redundant_components(), tighten_dependency_boundaries(), verify_functional_equivalence_along_the_way()], output={refactored_codebase:any}}`
    ```

    #### `0144-f-cleanup-essential-comment-rationalization.md`

    ```markdown
    [Essential Comment Rationalization] Enforce minimal, purposeful commentary. Strip out comments that duplicate what improved naming or structure already conveys. Preserve and refine only those clarifying *why* a complex solution or workaround exists. Avoid repeating the *what* or *how*—the code itself should cover those. Execute as: `{role=comment_curator; input=refactored_codebase:any; process=[inventory_all_comments(), classify_comments_as_why_vs_what_or_how(), remove_or_rewrite_non_essential_comments(), confirm_code_remains_self_explanatory()], output={comment_refined_codebase:any}}`
    ```

    #### `0144-g-cleanup-validation-and-recursive-refinement.md`

    ```markdown
    [Validation & Recursive Refinement] Confirm the final cleaned codebase preserves original functionality and meets core principles (Simplicity, Clarity, Cohesion, SRP, Minimal Commentary). Run tests or manual checks to ensure no regressions. If structural or logic deficits remain, repeat from an earlier step with a narrowed scope. Execute as: `{role=final_validator; input={original_codebase:any, comment_refined_codebase:any}; process=[run_functional_equivalence_tests(), review_against_principle_checklist(), evaluate_remaining_complexity_vs_benefits(), decide_on_next_cleanup_iteration_or_finalize()], output={validation_report:dict(equivalence_ok:bool, principle_adherence_score:float, next_cycle_recommendation:bool}}`
    ```

---

#### `6`

    #### `0145-a-cleanup-foundation-scan-structure-purpose-principle-baseline.md`

    ```markdown
    [Foundation Scan: Structure, Purpose & Principle Baseline] Your primary directive is rapid, holistic reconnaissance aligned with core principles. Scan the codebase root to map its high-level structure (directories, key modules), identify main entry/exit points, and distill its fundamental purpose. Simultaneously, perform an initial assessment against core principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation) to establish a baseline understanding and identify immediate, obvious deviations. Execute as `{role=foundation_assessor; input=codebase_root:path; process=[map_high_level_structure_and_components(), identify_entry_points_and_primary_workflows(), distill_core_system_purpose(), perform_initial_scan_for_principle_adherence_violations()]; output={baseline_analysis:dict(structure_map:any, core_purpose:str, key_workflows:list, initial_principle_assessment:dict)}}`
    ```

    #### `0145-b-cleanup-problem-identification-clarity-complexity-cohesion-audit.md`

    ```markdown
    [Problem Identification: Clarity, Complexity & Cohesion Audit] Building on the baseline, perform a deep dive to pinpoint specific impediments to elegance and maintainability. Trace `key_workflows` to analyze logical complexity, identifier clarity, and structural self-explanation. Critically evaluate component cohesion, adherence to the Single Responsibility Principle (SRP), and reliance on comments versus clear design. Identify concrete anti-patterns, redundancy (dead/duplicate code), and principle violations. Execute as `{role=issue_detector; input={baseline_analysis:dict, codebase_content:dict}; process=[trace_and_analyze_workflow_complexity(), evaluate_naming_clarity_and_consistency(), assess_component_cohesion_and_srp_adherence(), audit_comment_necessity_vs_code_clarity(), detect_redundancy_and_dead_code(), identify_specific_principle_violations_and_anti_patterns()]; output={problem_report:dict(complexity_hotspots:list, clarity_deficits:list(naming:list, structure:list), cohesion_srp_violations:list, redundancy_findings:list, comment_issues:list)}}`
    ```

    #### `0145-c-cleanup-prioritized-cleanup-blueprint-generation.md`

    ```markdown
    [Prioritized Cleanup Blueprint Generation] Synthesize findings from the `problem_report` into a strategic, actionable, and sequenced cleanup plan. Prioritize tasks based on maximizing clarity, simplicity, and maintainability impact while minimizing risk (Safety First: Removals > Renaming/Clarity > Structure > Complex Logic). Define specific, targeted actions for structural reorganization, code simplification/refactoring (logic, naming, SRP), redundancy removal, and comment rationalization. Output a clear blueprint ready for execution. Execute as `{role=cleanup_strategist; input={problem_report:dict}; process=[consolidate_identified_problems(), rank_potential_actions_by_safety_then_impact(), define_specific_refactoring_tasks(structure, code, naming, comments, removals), sequence_tasks_logically_with_dependencies(), finalize_prioritized_blueprint()]; output={cleanup_blueprint:list[dict(task_id:str, type:str, description:str, target:any, priority:int, rationale:str, dependencies:list)]}}`
    ```

    #### `0145-d-cleanup-structural-elegance-realignment.md`

    ```markdown
    [Structural Elegance Realignment] Execute the *structural* refactoring tasks defined in the `cleanup_blueprint` (e.g., tasks of type 'structure', 'structural_naming'). Reorganize directories and files to inherently mirror the system's conceptual organization and logical flow. Consolidate cohesive components, decouple unrelated ones, apply clear sequential/functional naming to structural elements, and update all necessary references/imports. Focus strictly on implementing the planned structural changes. Execute as `{role=structure_realigner; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_prioritized_structural_tasks_from_blueprint(), reorganize_directories_and_files_for_logical_flow(), apply_cohesive_grouping_and_decoupling(), update_structural_naming_conventions(), resolve_all_import_and_reference_updates()]; output={realigned_codebase_structure:path, executed_structural_tasks:list}}`
    ```

    #### `0145-e-cleanup-intrinsic-code-clarification-comment-rationalization.md`

    ```markdown
    [Intrinsic Code Clarification & Comment Rationalization] Execute the *code-level* refactoring and cleanup tasks within the `realigned_codebase_structure`, guided strictly by the `cleanup_blueprint` (e.g., tasks of type 'code', 'naming', 'srp', 'comment', 'removal'). Apply self-explanatory naming. Simplify complex logic. Enforce SRP. Remove dead/redundant code. Rationalize comments per the 'minimal_concise_essential' policy, removing those made obsolete by clarity improvements and refining only vital 'why' comments. Execute as `{role=code_clarifier; input={realigned_codebase_structure:path, cleanup_blueprint:list}; process=[implement_prioritized_code_level_tasks(), apply_self_explanatory_naming_conventions(), simplify_algorithms_and_consolidate_logic(), enforce_srp_at_component_level(), remove_dead_redundant_code_and_comments(), refine_essential_comments_for_brevity_and_precision()]; output={clarified_codebase:path, executed_code_tasks:list}}`
    ```

    #### `0145-f-cleanup-validation-functional-integrity-principle-adherence-check.md`

    ```markdown
    [Validation: Functional Integrity & Principle Adherence Check] Rigorously verify the `clarified_codebase`. Confirm 100% functional equivalence with the original state using tests or other verification methods. Critically audit the final state against all core principles (Simplicity, Clarity, Cohesion, SRP, Minimalism, Self-Explanation) and the specific goals targeted by the `cleanup_blueprint`. Document adherence and any remaining deviations. Execute as `{role=integrity_validator; input={original_codebase_state:any, clarified_codebase:path, cleanup_blueprint:list}; process=[perform_functional_equivalence_verification(via_tests_or_diff), audit_final_codebase_against_core_principles(), assess_achievement_of_blueprint_goals(), check_for_consistency_and_style(), document_validation_results_and_deviations()]; output={validation_report:dict(functional_equivalence_passed:bool, principle_adherence_summary:dict, remaining_deviations:list)}}`
    ```

    #### `0145-g-cleanup-final-assessment-iteration-decision.md`

    ```markdown
    [Final Assessment & Iteration Decision] Analyze the `validation_report` and the final state of the `clarified_codebase`. Evaluate the overall effectiveness of the cleanup cycle. Assess the significance of any `remaining_deviations` or complexity. Determine the cost-benefit ratio of initiating another refinement cycle. If beneficial, define a focused scope for the next iteration (potentially looping back to step 2 or 3); otherwise, conclude the cleanup process. Execute as `{role=refinement_coordinator; input={clarified_codebase:path, validation_report:dict}; process=[evaluate_overall_cleanup_impact_and_effectiveness(), analyze_significance_of_remaining_deviations(), assess_cost_benefit_of_further_refinement(), decide_on_necessity_of_next_iteration(), define_scope_and_focus_if_proceeding()]; output={iteration_decision:dict(proceed_further:bool, next_cycle_scope:list, final_assessment_summary:str)}}`
    ```
    ```

---

#### `7`

    #### `0146-a-cleanup-baseline-assessment-principle-alignment.md`

    ```markdown
    [Baseline Assessment & Principle Alignment] Your first imperative is rapid orientation and principled evaluation. Scan the codebase structure, map major components/modules, identify primary entry/exit points and core purpose. Simultaneously perform an initial assessment against key principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation, Minimalism), noting immediate alignments or violations. Execute as `{role=baseline_assessor; input=codebase_root:path; process=[map_structure_and_key_components(), identify_core_purpose_and_workflows(), perform_initial_principle_adherence_scan(), summarize_baseline_state_and_initial_findings()]; output={baseline_report:dict(structure_map:any, core_purpose:str, initial_principle_assessment:dict)}}`
    ```

    #### `0146-b-cleanup-targeted-analysis-cleanup-blueprint.md`

    ```markdown
    [Targeted Analysis & Cleanup Blueprint] Dive deeper, informed by the `baseline_report`. Trace critical workflows, pinpointing specific hotspots: unclear naming, complex logic, low cohesion/high coupling, structural awkwardness, redundancy (code/comments), and comment necessity vs. clarity. Synthesize these findings *directly* into a prioritized, actionable blueprint outlining specific cleanup tasks (structural, code, naming, comments), ranked by safety and impact on core principles. Execute as `{role=analysis_strategist; input={baseline_report:dict, codebase_content:dict}; process=[trace_critical_paths_for_issues(), analyze_naming_clarity_and_logic_complexity(), assess_cohesion_srp_violations(), evaluate_comment_necessity_and_redundancy(), detect_dead_code_and_duplication(), synthesize_findings_into_prioritized_action_blueprint(rank_by_safety_then_impact)]; output={cleanup_blueprint:list[dict(action:str, target:any, priority:int, rationale:str, type:enum[structure,code,naming,comment,removal])], detailed_analysis:dict}}`
    ```

    #### `0146-c-cleanup-structural-refinement-execution.md`

    ```markdown
    [Structural Refinement Execution] Execute *only* the high-priority structural changes specified in the `cleanup_blueprint`. Reorganize directories and files to inherently reflect logical flow and conceptual groupings. Consolidate cohesive elements, decouple unrelated ones, and apply clear, consistent structural naming. Update all necessary references and imports. Focus is purely on enhancing the architectural clarity and foundation. Execute as `{role=structure_refactorer; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_prioritized_structural_tasks(type='structure'), apply_cohesive_grouping_and_decoupling(), enforce_structural_naming_conventions(), resolve_all_import_and_reference_updates()]; output={realigned_structure_path:path}}`
    ```

    #### `0146-d-cleanup-code-clarity-comment-rationalization.md`

    ```markdown
    [Code Clarity & Comment Rationalization] Within the `realigned_structure_path`, execute the prioritized code-level and comment-related tasks from the `cleanup_blueprint`. Apply self-explanatory naming, simplify logic, enforce SRP, remove dead/redundant code, and rigorously apply the 'minimal essential commentary' principle (purging redundant/obsolete comments, refining necessary ones). The goal is maximum code self-explanation. Execute as `{role=code_rationalizer; input={realigned_structure_path:path, cleanup_blueprint:list}; process=[apply_prioritized_code_tasks(type=['code','naming','removal','comment']), implement_naming_improvements(), execute_logic_simplification_and_consolidation(), enforce_srp_at_component_level(), purge_dead_code_and_redundancy(), execute_comment_removal_and_refinement_plan()]; output={clarified_codebase_path:path}}`
    ```

    #### `0146-e-cleanup-validation-principle-audit-refinement-loop.md`

    ```markdown
    [Validation, Principle Audit & Refinement Loop] Perform comprehensive validation of the `clarified_codebase_path`. Verify 100% functional equivalence with the original state (using tests or defined checks). Critically audit the final state against *all* core principles (Simplicity, Clarity, Cohesion, SRP, Minimalism, Self-Explanation). Assess the effectiveness of the executed `cleanup_blueprint`. Identify any significant remaining principle violations or complexity hotspots and determine if a further, focused cleanup cycle is warranted based on cost/benefit. Execute as `{role=final_validator_assessor; input={original_codebase_state:any, clarified_codebase_path:path, cleanup_blueprint:list}; process=[perform_functional_equivalence_validation(), conduct_final_audit_against_core_principles(), evaluate_effectiveness_of_cleanup_actions(), identify_residual_complexity_or_violations(), assess_cost_benefit_of_further_refinement(), generate_final_report_and_next_cycle_decision()]; output={validation_report:dict(functional_equivalence:bool, principle_adherence_summary:dict, remaining_concerns:list), final_codebase_state:path, next_cycle:dict(recommended:bool, focus_areas:list)}}`
    ```

---

#### `8`

    #### `0147-a-cleanup-holistic-structure-purpose-orientation.md`

    ```markdown
    [Holistic Structure & Purpose Orientation] Begin with a rapid, high-level orientation. Map the codebase’s structure, inventory primary directories, files, and components, and identify entry points and architectural boundaries. Distill the system’s core purpose and highest-level workflows. Execute as `{role=structure_orienter; input=codebase_root:path; process=[inventory_structure_and_key_components(), identify_entry_exit_points(), map_high_level_dependencies_and_domains(), distill_primary_purpose_and_intent()]; output={structure_map:dict, primary_purpose:str, domain_overview:dict}}`
    ```

    #### `0147-b-cleanup-critical-workflow-flow-clarity-mapping.md`

    ```markdown
    [Critical Workflow & Flow Clarity Mapping] Identify and trace the system’s essential execution and data flow paths—focusing on those that fulfill the `primary_purpose`. Map cross-component logic, find complexity zones, and diagram dependencies and information flow, highlighting architectural bottlenecks or confusion points. Execute as `{role=workflow_mapper; input={structure_map:dict, codebase_content:any, primary_purpose:str}; process=[trace_primary_execution_flows(), map_data_and_control_links(), highlight_complexity_and_bottlenecks(), visualize_cross-module_interactions()]; output={workflow_map:list, dependency_map:dict, complexity_zones:list}}`
    ```

    #### `0147-c-cleanup-clarity-cohesion-and-comment-dependence-audit.md`

    ```markdown
    [Clarity, Cohesion & Comment Dependence Audit] Evaluate the codebase's inherent clarity, cohesion, and self-explanation. Assess naming, structure, Single Responsibility Principle (SRP), and module/class cohesion, flagging areas where clarity relies on excessive commentary or where redundant/dead code is present. Execute as `{role=clarity_auditor; input={workflow_map:list, codebase_content:dict, dependency_map:dict, complexity_zones:list}; process=[assess_identifier_naming_and_consistency(), check_module_and_class_cohesion(), detect_srp_violations(), locate_comment-dependent_clarity(), find redundant/dead_code()]; output={clarity_report:dict, srp_cohesion_issues:list, comment_reliance_zones:list, redundancy_zones:list}}`
    ```

    #### `0147-d-cleanup-structural-simplification-and-opportunity-mapping.md`

    ```markdown
    [Structural Simplification & Opportunity Mapping] Synthesize all audit findings to pinpoint concrete, high-value opportunities for simplification: restructure confusing modules, consolidate related logic, decouple tightly-bound components, improve naming, and identify necessary comment reductions. Execute as `{role=simplification_mapper; input={clarity_report:dict, srp_cohesion_issues:list, redundancy_zones:list, comment_reliance_zones:list, dependency_map:dict}; process=[compile_simplification_targets(), rank_by_clarity_impact_and_safety(), propose_structural_realignment(), suggest module/function renaming(), isolate comment minimization/refinement zones()]; output={simplification_targets:list, proposed_structure:dict, naming_plans:list, comment_actions:list}}`
    ```

    #### `0147-e-cleanup-prioritized-cleanup-action-plan.md`

    ```markdown
    [Prioritized Cleanup Action Plan] Consolidate all recommendations into a clear, sequenced, actionable plan. Order tasks by safety, impact, and logical dependency: dead code removal, naming and comment refactoring, structural simplification, and logic clarity improvements. Execute as `{role=cleanup_planner; input={simplification_targets:list, proposed_structure:dict, naming_plans:list, comment_actions:list}; process=[merge_and_sequence_actions_by_impact_and_risk(), define clear task scopes(), annotate logic/structural dependencies(), finalize as stepwise cleanup plan()]; output={prioritized_cleanup_plan:list}}`
    ```

    #### `0147-f-cleanup-guided-execution-and-integrity-validation.md`

    ```markdown
    [Guided Execution & Integrity Validation] Methodically execute the cleanup plan—incrementally applying changes and validating after each significant batch. Ensure that all improvements strictly preserve original functionality; use automated and/or manual checks. Execute as `{role=cleanup_executor; input={codebase_root:path, prioritized_cleanup_plan:list}; process=[apply_changes_in_priority_order(), run_suite_validation_tests(), verify functional equivalence, review for principle adherence after each phase], output={cleaned_codebase:path, validation_log:list}}`
    ```

    #### `0147-g-cleanup-final-audit-elegance-and-recursive-check.md`

    ```markdown
    [Final Audit: Elegance & Recursive Check] Critically audit the resulting codebase for principle alignment (simplicity, clarity, SRP, cohesion, minimal essential commentary, self-explanation). Score against before/after metrics, and decide if a further focused refinement pass would yield sufficient value. Execute as `{role=final_reviewer; input={cleaned_codebase:path, validation_log:list, original_state:dict}; process=[audit for residual complexity or awkwardness(), score clarity and self-explanation, surface missed/refinement opportunities, recommend (or decline) next refinement cycle()], output={final_report:dict(metrics:dict, remaining_opportunities:list, recommend_next_pass:bool)}}`
    ```

    #### `0147-h-cleanup-impact-summary-and-handoff-report.md`

    ```markdown
    [Impact Summary & Handoff Report] Summarize the changes made, principles supported, clarity/cohesion gains, and any residual technical debt. Report is structured for team handoff or as a baseline for future cycles. Execute as `{role=impact_reporter; input={final_report:dict, prioritized_cleanup_plan:list}; process=[map changes to principle improvements(), summarize clarity and structure benefits, highlight handoff/transition notes, document open issues if any()], output={cleanup_summary:str}}`
    ```
