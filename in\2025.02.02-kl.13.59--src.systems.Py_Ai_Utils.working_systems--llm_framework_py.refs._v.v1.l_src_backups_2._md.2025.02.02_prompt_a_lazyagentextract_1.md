Please extract the exact instructions and implement them perfectly into this base:

    ```python
    import os
    import sys
    from pathlib import Path
    from loguru import logger
    import glob
    import yaml
    import re
    from dotenv import load_dotenv
    from openai import OpenAI
    import logging
    from datetime import datetime
    from typing import List, Dict, Optional
    from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

    class Config:
        """
        Global settings.
        """
        PROVIDER_OPENAI = "openai"
        PROVIDER_DEEPSEEK = "deepseek"
        PROVIDER_ANTHROPIC = "anthropic"

        AVAILABLE_MODELS = {
            PROVIDER_OPENAI: {
                "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
                "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
                "gpt-4": "Latest GPT-4 stable release",
                "gpt-4-0125-preview": "Preview GPT-4 Turbo",
                "gpt-4-0613": "June 2023 GPT-4 snapshot",
                "gpt-4-1106-preview": "Preview GPT-4 Turbo",
                "gpt-4-turbo": "Latest GPT-4 Turbo release",
                "gpt-4-turbo-2024-04-09": "Current GPT-4 Turbo with vision",
                "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
                "gpt-4o": "Base GPT-4o model",
                "gpt-4o-mini": "Lightweight GPT-4o variant",
            },
            PROVIDER_DEEPSEEK: {
                "deepseek-chat": "DeepSeek Chat model",
                "deepseek-reasoner": "Specialized reasoning model",
            },
            PROVIDER_ANTHROPIC: {
                "claude-2": "Base Claude 2 model",
                "claude-2.0": "Enhanced Claude 2.0",
                "claude-2.1": "Latest Claude 2.1 release",
                "claude-3-opus-20240229": "Claude 3 Opus",
                "claude-3-sonnet-20240229": "Claude 3 Sonnet",
                "claude-3-haiku-20240307": "Claude 3 Haiku",
            },
        }

        DEFAULT_MODEL_PARAMS = {
            PROVIDER_OPENAI: {
                "model_name": "gpt-4-turbo-preview",
                "model_name": "gpt-4-turbo",
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_DEEPSEEK: {
                "model_name": "deepseek-reasoner",
                "model_name": "deepseek-coder",
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 800,
            },
            PROVIDER_ANTHROPIC: {
                "model_name": "claude-2.1",
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.7,
                "max_tokens": 800,
            },
        }

        API_KEY_ENV_VARS = {
            PROVIDER_OPENAI: "OPENAI_API_KEY",
            PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
            PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        }

        BASE_URLS = {
            PROVIDER_DEEPSEEK: "https://api.deepseek.com",
        }

        DEFAULT_PROVIDER = PROVIDER_OPENAI

        def __init__(self):
            load_dotenv()
            self.configure_utf8_encoding()
            self.provider = self.DEFAULT_PROVIDER.lower()
            self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
            self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
            self.setup_logger()

        def configure_utf8_encoding(self):
            """
            Ensure UTF-8 encoding for standard output and error streams.
            """
            if hasattr(sys.stdout, "reconfigure"):
                sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            if hasattr(sys.stderr, "reconfigure"):
                sys.stderr.reconfigure(encoding="utf-8", errors="replace")
    ```


Not only is your goal to provide executable code with the possibility to easily change model through `DEFAULT_PROVIDER`, but also to implement the extracted instructions in a way that yeld the best result. For reference, I've provided an example of input vs output below:

    - initial prompt: """i'm looking to create a user interface for my python utility, but since there are so many alternatives i'm looking for the cleanest and most suitable one for my needs. i want to avoid gui alternatives that require a complex set of required components and those that require a 'server-setup' as i need it to run locally. it also needs to be self-contained such that the python utility works without the gui, while still having the option to use gui. i like the idea of using simple webinterface, but if i choose to use webinterface it needs to be a solution that is *easy* to set up when initializing the utility on new computers. i don't know if solutions such as flask adhere to my requirements or if there are even cleaner and more suitable solutions. given my requirement of being able to easily initialize the utility on new computers and have the gui work independently with the utility (such that it can optionally be used without gui). i'm looking for solutions that are specifically designed for my use-case scenario."""

    - response output: """
            Seeking a simple, local GUI for a Python utility that runs independently and is easy to initialize on new computers.

            Context Layers:

            Level 1: Need a clean, self-contained GUI for a Python utility.
            Level 2: Avoid complex GUIs with numerous dependencies and server setups.
            Level 2: Require a GUI that operates independently but allows the utility to function without it.
            Level 2: Prefer a simple web interface that's easy to set up on new machines.
            Level 3: Uncertain if frameworks like Flask meet these criteria.
            Level 3: Need solutions specifically designed for easy deployment and optional GUI use.
        """



























    # Initialize the client with DeepSeek API key and base URL
    client = OpenAI(
        api_key=os.environ.get("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com"
    )

    # Function to interact with DeepSeek API
    def chat_with_deepseek(prompt):
        # Define the system instructions for prompt optimization
        system_instructions = """
        You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines:
        - Deliver concise, precise results for quick understanding
        - Focus on brevity while preserving the core message
        - Use clear, unambiguous language
        - Present information logically
        - The enhanced prompt MUST NOT exceed twice the length of the original prompt
        - Generate a brief title (max 50 chars) that captures the main purpose/topic
        - Structure the response in a nested dialogue format to facilitate context-rich, layered conversations
        - Support complex reasoning through hierarchical organization of ideas

        You MUST return a JSON response containing:
        {
          "title": "Short descriptive title",
          "enhanced_prompt": "Optimized version of the original prompt",
          "context_layers": [
            {
              "level": 1,
              "context": "Primary context layer"
            },
            {
              "level": 2,
              "context": "Secondary contextual details"
            },
            {
              "level": 3,
              "context": "Tertiary supporting information"
            }
          ]
        }
        """

        response = client.chat.completions.create(
            model="deepseek-chat",  # Use the reasoning-focused model
            messages=[
                {"role": "system", "content": system_instructions},
                {"role": "user", "content": prompt}
            ],
            stream=False
        )
        return response.choices[0].message.content

    # Example usage
    user_input = "i'm looking to create a user interface for my python utility, but since there are so many alternatives i'm looking for the cleanest and most suitable one for my needs. i want to avoid gui alternatives that require a complex set of required components and those that require a 'server-setup' as i need it to run locally. it also needs to be self-contained such that the python utility works without the gui, while still having the option to use gui. i like the idea of using simple webinterface, but if i choose to use webinterface it needs to be a solution that is *easy* to set up when initializing the utility on new computers. i don't know if solutions such as flask adhere to my requirements or if there are even cleaner and more suitable solutions. given my requirement of being able to easily initialize the utility on new computers and have the gui work independently with the utility (such that it can optionally be used without gui). i'm looking for solutions that are specifically designed for my use-case scenario."

    result = chat_with_deepseek(user_input)
    print(result)

    ```

Remember to reanalyze relevant sections of the source code initially provided. I've included an example of such section below:

    ```python
        # Prompt Enhancement API route
        @app.route("/api/enhance_prompt", methods=["POST"])
        def enhance_prompt():
            try:
                original_prompt = request.json.get("prompt")

                if not original_prompt:
                    return jsonify({"error": "No prompt provided"}), 400

                settings = ChatbotSettings.get_settings()

                # Calculate maximum allowed length for enhanced prompt
                max_length = len(original_prompt) * 20

                enhancement_instructions = """
                You are a prompt optimization expert. Your task is to enhance the given prompt following these guidelines:
                - Deliver concise, precise results for quick understanding
                - Focus on brevity while preserving the core message
                - Use clear, unambiguous language
                - Present information logically
                - The enhanced prompt MUST NOT exceed twice the length of the original prompt
                - Generate a brief title (max 50 chars) that captures the main purpose/topic
                - Structure the response in a nested dialogue format to facilitate context-rich, layered conversations
                - Support complex reasoning through hierarchical organization of ideas

                Original prompt length: """ + str(len(original_prompt)) + """ characters
                Maximum allowed length: """ + str(max_length) + """ characters
                """

                response = llm(
                    prompt=f"{enhancement_instructions}\n\nOriginal Prompt: {original_prompt}\n\nProvide both a title and enhanced prompt structured in a nested dialogue format:",
                    response_schema={
                        "type": "object",
                        "properties": {
                            "title": {"type": "string", "maxLength": 50},
                            "enhanced_prompt": {"type": "string"},
                            "context_layers": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "level": {"type": "integer"},
                                        "context": {"type": "string"}
                                    }
                                }
                            }
                        },
                        "required": ["title", "enhanced_prompt", "context_layers"]
                    },
                    image_url=None,
                    model=settings.model_selection,
                    temperature=settings.temperature
                )

                title = response["title"][:50]  # Ensure title length limit
                enhanced_prompt = response["enhanced_prompt"]
                context_layers = response["context_layers"]

                # Format the enhanced prompt with nested context
                formatted_prompt = enhanced_prompt + "\n\nContext Layers:\n"
                for layer in context_layers:
                    formatted_prompt += f"\nLevel {layer['level']}: {layer['context']}"

                # Ensure the enhanced prompt doesn't exceed maximum length
                if len(formatted_prompt) > max_length:
                    formatted_prompt = formatted_prompt[:max_length]
                    app.logger.warning(f"Enhanced prompt was truncated to meet length requirement")

                return jsonify({
                    "title": title,
                    "enhanced_prompt": formatted_prompt,
                    "original_length": len(original_prompt),
                    "enhanced_length": len(formatted_prompt),
                    "context_layers": context_layers
                })
            except Exception as e:
                app.logger.error(f"Error enhancing prompt: {str(e)}")
                return jsonify({"error": "An error occurred while enhancing the prompt"}), 500
    ```
