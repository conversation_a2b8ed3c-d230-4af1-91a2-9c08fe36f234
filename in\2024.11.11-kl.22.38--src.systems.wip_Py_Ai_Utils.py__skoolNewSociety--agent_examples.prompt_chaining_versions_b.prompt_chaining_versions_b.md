# Project Files Documentation for `prompt_chaining_versions_b`

### File Structure

```
├── 005_prompt_chaining_5_c.py
├── 005_prompt_chaining_5_c_2.py
```
### 1. `005_prompt_chaining_5_c.py`

#### `005_prompt_chaining_5_c.py`

```python
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# Define Blueprint creation with mode-based customization
def create_guiding_blueprint(initial_input: str, mode: str) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users."
    }
    mode_description = mode_descriptions.get(mode, "defaulting to a general response focus.")

    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Enhance clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n"
            f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# Define grouped agent chains based on mode
def get_refinement_chain(mode: str) -> Dict[str, List[Dict[str, str]]]:
    chain = {
        "Blueprint Creation": [
            {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint with topic, objective, and structure based on the input."}
        ],
        "Prompt Reformulation": [
            {"role": "Inquiry Formulator", "prompt": f"Rephrase the data as a comprehensive question, focusing on '{mode}'."},
            {"role": "Objective Clarifier", "prompt": f"Define the primary objective based on the Blueprint, aligned with '{mode}'."},
            {"role": "Topic Alignment", "prompt": "Review and realign with the original topic and Blueprint if needed."}
        ],
        "Content Development": [
            {"role": "Context Enhancer", "prompt": f"Add relevant background for enriched response accuracy, with '{mode}' as focus."},
            {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint."},
            {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint and topic before further development."}
        ],
        "Quality Assurance": [
            {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability."},
            {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback."},
            {"role": "Topic Alignment", "prompt": "Ensure content remains aligned with the original topic and Blueprint."}
        ],
        "User Experience and Readability": [
            {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility."},
            {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability."},
            {"role": "Topic Consistency Tracker", "prompt": "Ensure content aligns with the original question and topic."}
        ],
        "Final Optimization": [
            {"role": "Performance Optimizer", "prompt": "Optimize for efficiency without sacrificing clarity."},
            {"role": "Documentation Curator", "prompt": "Provide clear documentation focusing on usability."},
            {"role": "Quality Assessor", "prompt": "Perform a final quality check, allowing early exit if quality is sufficient."},
            {"role": "Final Topic Consistency Checker", "prompt": "Ensure the response aligns fully with the original topic and Blueprint."}
        ]
    }

    # Modify chain activation based on mode
    if mode == "Prompt Generation":
        # Emphasize clarity, conciseness, and immediate relevance
        chain["Prompt Reformulation"].append({"role": "Conciseness Optimizer", "prompt": "Ensure prompt is concise and impactful."})
    elif mode == "Content Creation":
        # Emphasize detailed structuring and background information
        chain["Content Development"].append({"role": "Detail Enhancer", "prompt": "Add necessary details to support a thorough response."})
    elif mode == "User Guidance":
        # Emphasize user-centered clarity and step-by-step guidance
        chain["User Experience and Readability"].append({"role": "Guidance Clarity Agent", "prompt": "Ensure instructions are clear and actionable for end-users."})

    return chain

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

# Categorize the prompt and select relevant agent chains
def categorize_prompt(prompt: str) -> List[str]:
    categories = []
    if any(keyword in prompt.lower() for keyword in ["clarity", "coherence", "reformulation"]):
        categories.append("Prompt Reformulation")
    if any(keyword in prompt.lower() for keyword in ["background", "content development", "context"]):
        categories.append("Content Development")
    if any(keyword in prompt.lower() for keyword in ["quality assurance", "standards", "error handling"]):
        categories.append("Quality Assurance")
    if any(keyword in prompt.lower() for keyword in ["user experience", "readability"]):
        categories.append("User Experience and Readability")
    if any(keyword in prompt.lower() for keyword in ["optimization", "performance"]):
        categories.append("Final Optimization")
    return categories if categories else ["Blueprint Creation"]

def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, iteration: int) -> str:
    re_anchor_message = f"{blueprint}\n\n" if iteration % 3 == 0 else ""
    prompt = f"{agent['prompt']}\n\nTopic Context from Blueprint:\n{blueprint}\n\n{re_anchor_message}Current Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response

def apply_agent_chain(blueprint: str, initial_input: str, mode: str) -> str:
    current_response = initial_input
    refinement_chain = get_refinement_chain(mode)
    selected_categories = categorize_prompt(initial_input)

    logging.info(f"Selected categories based on prompt: {selected_categories}")

    for category in selected_categories:
        logging.info(f"Starting group: {category}")
        agents = refinement_chain.get(category, [])
        for i, agent in enumerate(agents):
            current_response = apply_agent(agent, blueprint, current_response, i)
            if category == "Final Optimization" and i + 1 >= 5 and assess_response_quality(current_response):
                logging.info("Early exit after quality criteria met.")
                return current_response
    return current_response

def main():
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    mode = "Prompt Generation"  # User-selected mode
    blueprint = create_guiding_blueprint(initial_input, mode)

    logging.info(f"Starting process in '{mode}' mode...")
    final_output = apply_agent_chain(blueprint, initial_input, mode)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
### 2. `005_prompt_chaining_5_c_2.py`

#### `005_prompt_chaining_5_c_2.py`

```python
import os
import logging
from openai import OpenAI
from typing import List, Dict, Optional, Union

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def init_openai_client() -> Optional[OpenAI]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY environment variable is not set")
        return None
    try:
        return OpenAI(api_key=api_key)
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
        return None

client = init_openai_client()
if client is None:
    raise SystemExit("Failed to initialize OpenAI client due to missing API key.")

# Step 1: Blueprint creation with mode-based customization and default mode
def create_guiding_blueprint(initial_input: str, mode: Optional[str] = None) -> str:
    mode_descriptions = {
        "Prompt Generation": "focused on generating concise, impactful prompts.",
        "Content Creation": "focused on detailed, structured content development.",
        "User Guidance": "focused on providing clear, actionable guidance for end-users."
    }
    # Default to general purpose mode if none is specified
    mode = mode or "General"
    mode_description = mode_descriptions.get(mode, "focused on a balanced, high-quality response.")

    return (f"Guiding Blueprint:\n"
            f"Mode: {mode} - {mode_description}\n"
            f"Topic: {initial_input}\n"
            f"Objective: Enhance clarity, coherence, relevance, and usability.\n"
            f"Structure: Reformulation, content development, quality assurance, user experience, final optimization.\n"
            f"Key Points: Maintain topic alignment and ensure conciseness.\n")

# Step 2: Define grouped agent chains based on mode and metadata tags for flexibility
def get_refinement_chain(mode: str) -> Dict[str, Dict[str, Union[List[Dict[str, str]], List[str]]]]:
    chain = {
        "Blueprint Creation": {
            "agents": [
                {"role": "Blueprint Creator", "prompt": "Create a Guiding Blueprint with topic, objective, and structure based on the input."}
            ],
            "tags": ["general", "structure"]
        },
        "Prompt Reformulation": {
            "agents": [
                {"role": "Inquiry Formulator", "prompt": f"Rephrase the data as a comprehensive question, focusing on '{mode}'."},
                {"role": "Objective Clarifier", "prompt": f"Define the primary objective based on the Blueprint, aligned with '{mode}'."},
                {"role": "Topic Alignment", "prompt": "Review and realign with the original topic and Blueprint if needed."}
            ],
            "tags": ["clarity", "generation"]
        },
        "Content Development": {
            "agents": [
                {"role": "Context Enhancer", "prompt": f"Add relevant background for enriched response accuracy, with '{mode}' as focus."},
                {"role": "Structure Architect", "prompt": "Organize content logically, guided by the Blueprint."},
                {"role": "Blueprint Reminder Agent", "prompt": "Reinforce alignment with the Blueprint and topic before further development."}
            ],
            "tags": ["content", "context", "depth"]
        },
        "Quality Assurance": {
            "agents": [
                {"role": "Standards Enforcer", "prompt": "Enforce coding standards and maintainability."},
                {"role": "Error Handling Specialist", "prompt": "Implement robust error management with clear feedback."},
                {"role": "Topic Alignment", "prompt": "Ensure content remains aligned with the original topic and Blueprint."}
            ],
            "tags": ["quality", "accuracy", "standards"]
        },
        "User Experience and Readability": {
            "agents": [
                {"role": "User Experience Optimizer", "prompt": "Enhance clarity, usability, and accessibility."},
                {"role": "Logic Validator", "prompt": "Confirm logical flow for improved readability."},
                {"role": "Topic Consistency Tracker", "prompt": "Ensure content aligns with the original question and topic."}
            ],
            "tags": ["usability", "readability", "clarity"]
        },
        "Final Optimization": {
            "agents": [
                {"role": "Performance Optimizer", "prompt": "Optimize for efficiency without sacrificing clarity."},
                {"role": "Documentation Curator", "prompt": "Provide clear documentation focusing on usability."},
                {"role": "Quality Assessor", "prompt": "Perform a final quality check, allowing early exit if quality is sufficient."},
                {"role": "Final Topic Consistency Checker", "prompt": "Ensure the response aligns fully with the original topic and Blueprint."}
            ],
            "tags": ["optimization", "efficiency", "final"]
        }
    }

    # Add specific agents based on mode
    if mode == "Prompt Generation":
        chain["Prompt Reformulation"]["agents"].append({"role": "Conciseness Optimizer", "prompt": "Ensure prompt is concise and impactful."})
    elif mode == "Content Creation":
        chain["Content Development"]["agents"].append({"role": "Detail Enhancer", "prompt": "Add necessary details to support a thorough response."})
    elif mode == "User Guidance":
        chain["User Experience and Readability"]["agents"].append({"role": "Guidance Clarity Agent", "prompt": "Ensure instructions are clear and actionable for end-users."})

    return chain

def get_completion(prompt: str, model: str = "gpt-3.5-turbo") -> Union[str, None]:
    for _ in range(3):  # Retry up to 3 times
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=800
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"API call failed: {e}")
    return None

# Quality assessment with early exit for efficiency
def assess_response_quality(response: str) -> bool:
    quality_indicators = ["clear", "concise", "relevant"]
    meets_quality = sum(indicator in response for indicator in quality_indicators) >= 2
    return meets_quality

def apply_agent(agent: Dict[str, str], blueprint: str, current_response: str, iteration: int) -> str:
    re_anchor_message = f"{blueprint}\n\n" if iteration % 3 == 0 else ""
    prompt = f"{agent['prompt']}\n\nTopic Context from Blueprint:\n{blueprint}\n\n{re_anchor_message}Current Response:\n{current_response}\n\nRefined Response:"
    refined_response = get_completion(prompt)
    if refined_response:
        logging.info(f"Agent '{agent['role']}' successfully refined the response.")
        return refined_response
    else:
        logging.warning(f"Agent '{agent['role']}' could not refine the response; skipping to next.")
        return current_response

def apply_agent_chain(blueprint: str, initial_input: str, mode: str) -> str:
    current_response = initial_input
    refinement_chain = get_refinement_chain(mode)

    for category, group in refinement_chain.items():
        logging.info(f"Starting group: {category}")
        agents = group["agents"]
        for i, agent in enumerate(agents):
            current_response = apply_agent(agent, blueprint, current_response, i)
            if category == "Final Optimization" and i + 1 >= 5 and assess_response_quality(current_response):
                logging.info("Early exit after quality criteria met.")
                return current_response
    return current_response

def main():
    initial_input = "Write a highly optimized LLM prompt that demonstrates the best use of prompt chains."
    # initial_input = "i'm writing a message to a stranger. a stranger that i know is suffering. he doesn't want to be helped, and i won't try to help him - but i want to leave him a message. can you please write a short and original message, e.g. 'may your travels be rich in serendipity and scarce in dead ends - one important criteria is that it should not be clich√©, the second criteria is that it should contain the word serendipity'"
    # initial_input = "improve the quote: 'it's almost as i'm dreaming'"
    mode = "Prompt Generation"  # User-selected mode or inferred mode
    blueprint = create_guiding_blueprint(initial_input, mode)

    logging.info(f"Starting process in '{mode}' mode...")
    final_output = apply_agent_chain(blueprint, initial_input, mode)

    print("Initial Input:")
    print(initial_input)
    print("\nFinal Output:")
    print(final_output)

if __name__ == "__main__":
    main()

```
