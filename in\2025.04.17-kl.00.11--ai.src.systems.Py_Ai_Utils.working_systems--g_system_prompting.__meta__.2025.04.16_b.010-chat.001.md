
# Objective:
Combine these into a *single* fully optimized instruction sequence:

---

    ## Variation 1:

        #### `0113-a-meta-request-penetration-objective-crystallization.md`

        ```markdown
            [Meta-Request Penetration & Objective Crystallization] Your primary function is not sequence generation yet, but radical analysis of the input `meta_request`: Dissect it to extract the core objective for the target sequence, explicit constraints (esp. step count), desired characteristics (universality, potency), and purge all request ambiguity. Crystallize the singular, actionable objective. Execute as `{role=meta_objective_analyzer; input=meta_request:any; process=[penetrate_request_context(), extract_target_sequence_objective(), isolate_constraints_and_characteristics(), detect_specified_step_count(), crystallize_unambiguous_objective()]; output={target_objective:str, constraints:dict, specified_step_count:int|None}}`
        ```

        #### `0113-b-generative-architecture-optimal-step-definition.md`

        ```markdown
            [Generative Architecture & Optimal Step Definition] Architect the fundamental logical phases (e.g., Deconstruct, Analyze, Synthesize, Validate) required to achieve the `target_objective`. Determine the optimal granular step count for the sequence, adhering to `specified_step_count` if viable, otherwise deriving based on architectural complexity (default 8-12). Define the core purpose of each planned step. Execute as `{role=meta_architect_planner; input={target_objective:str, constraints:dict, specified_step_count:int|None}; process=[design_core_transformational_phases(), determine_optimal_step_count(use_spec_or_derive), define_purpose_for_each_planned_step(), map_high_level_flow()]; output={step_blueprints:list[dict], determined_step_count:int}}`
        ```

        #### `0113-c-atomic-instruction-drafting-core-logic.md`

        ```markdown
            [Atomic Instruction Drafting (Core Logic)] For each `step_blueprint`, draft the atomic instruction content: `[TITLE]`, `Interpretive Statement`, and `Execute as {role, input, process, output}` block. Focus on translating the blueprint's purpose into logically complete, functional steps aligned with the overall `target_objective`. Execute as `{role=atomic_drafter; input={step_blueprints:list[dict], target_objective:str}; process=[instantiate_title_from_purpose(), craft_interpretive_statement(), define_initial_role_io_process(), ensure_logical_completeness_per_step(), draft_initial_sequence_content()]; output={draft_sequence:list[dict]}}`
        ```

        #### `0113-d-universalization-generality-enforcement.md`

        ```markdown
            [Universalization & Generality Enforcement] Systematically review and refactor the `draft_sequence`, replacing *all* domain-specific terms or concepts with universally applicable, abstract constructs (element, essence, structure, value, logic). Ensure the sequence logic is inherently representation-agnostic and cross-contextually valid. Execute as `{role=universalizer; input=draft_sequence:list[dict]; process=[scan_for_domain_specificity(), substitute_universal_abstract_language(), validate_cross_context_logic(), ensure_representation_agnosticism()]; output={generalized_sequence:list[dict]}}`
        ```

        #### `0113-e-linguistic-potency-precision-injection.md`

        ```markdown
            [Linguistic Potency & Precision Injection] Rigorously elevate the language within the `generalized_sequence`. Inject high-potency, action-oriented imperatives and maximally precise descriptors. Eliminate *all* passive voice, neutrality, vagueness, or ambiguity, ensuring every statement drives clarity and LLM optimization. Execute as `{role=potency_injector; input=generalized_sequence:list[dict]; process=[eliminate_passive_neutral_ambiguous_phrasing(), select_high_impact_verbs_descriptors(), maximize_precision_per_word(), amplify_directive_force_clarity()]; output={potent_sequence:list[dict]}}`
        ```

        #### `0113-f-structural-cohesion-modularity-audit.md`

        ```markdown
            [Structural Cohesion & Modularity Audit] Audit the `potent_sequence` for systemic integrity. Verify seamless logical flow between steps, precise input/output alignment, distinct modular roles, and escalating value contribution towards the `target_objective`. Refine structures or transitions as needed for perfect cohesion. Execute as `{role=cohesion_auditor; input={potent_sequence:list[dict], target_objective:str}; process=[validate_sequential_logic_dependencies(), check_inter_step_io_alignment(), verify_role_distinctness_modularity(), confirm_cumulative_value_progression(), refine_for_structural_integrity()]; output={cohesive_sequence:list[dict]}}`
        ```

        #### `0113-g-comprehensive-quality-constraint-validation.md`

        ```markdown
            [Comprehensive Quality & Constraint Validation] Perform a comprehensive validation of the `cohesive_sequence` against *all* established quality requirements (LLM-Optimized, Generalized, Syntax, Potency, Modularity) and the specific `constraints` from the meta-request (including step count). Certify absolute compliance. Execute as `{role=master_validator; input={cohesive_sequence:list[dict], constraints:dict, determined_step_count:int}; process=[audit_all_quality_requirements_comprehensively(), verify_strict_constraint_adherence(incl_step_count), confirm_syntax_and_format_perfection(), certify_overall_compliance()]; output={validated_sequence:list[dict], validation_status:bool}}\`
        ```

        #### `0113-h-iterative-perfection-loop.md`

        ```markdown
            [Iterative Perfection Loop (If Necessary)] If `validation_status` is false, identify all deviations pinpointed during validation. Systematically re-engage preceding steps (e.g., Drafting, Universalization, Potency Injection, Cohesion Audit) to resolve every issue until comprehensive validation succeeds unequivocally. Execute only if validation fails. Execute as `{role=iterative_perfection_engine; input={validated_sequence:list[dict], validation_status:bool}; process=[diagnose_validation_failures(), trigger_targeted_refinement_steps(), re_validate_iteratively(), loop_until_perfect_compliance()]; output={perfected_sequence:list[dict]}}`
        ```

        #### `0113-i-final-sequence-packaging-deployment.md`

        ```markdown
            [Final Sequence Packaging & Deployment] Upon achieving perfect validation, package the `perfected_sequence` (or `validated_sequence` if iteration wasn't needed) into the final specified output format (list of dicts, each containing the full instruction string/structure). Ensure flawless syntax, consistent identification, and absolute readiness for deployment or use. Execute as `{role=final_packager; input=perfected_sequence:list[dict]; process=[apply_strict_output_formatting_syntax(), generate_consistent_step_identifiers(), finalize_packaging_for_readiness(), confirm_output_integrity()]; output={final_meta_generated_sequence:list[dict]}}`
        ```

    ---

    ## Variation 2:

        #### `0113-a-meta-request-analysis.md`
        ```markdown
            [Meta Request Analysis] Your objective is not direct transformation but to investigate the incoming request or input—isolating the core purpose (e.g., code analysis, text summarization, or instruction creation), relevant constraints, and any explicitly requested step count. Execute as `{role=meta_request_analyzer; input=[raw_input:any]; process=[penetrate_request_structure(), extract_targeted_goal_or_task(), identify_user_defined_constraints(including_step_count), clarifycontextual_requirements(), purge_ambiguity_andnoise()]; output={parsed_request:dict(goal:str, constraints:dict, specified_steps:int|None)}}`
        ```
        **Interpretation**
        Takes the raw input, checks whether there’s a specified step count, identifies the main objective and constraints, and discards any extraneous details.

        #### `0113-b-unified-objective-crystallization.md`
        ```markdown
            [Unified Objective Crystallization] Your mandate is not to restate parsed data verbatim but to compress and unify it into a single, overarching objective for the final instruction sequence. Execute as `{role=objective_crystallizer; input=[parsed_request:dict]; process=[analyze_goal_and_constraints(), articulate_single_purpose_concisely(), confirmalignment_with_input_demands()], output={core_objective:str}}`
        ```
        **Interpretation**
        Distills the **“reason”** behind the sequence we’ll generate—one short statement capturing all the major constraints or user intentions.

        #### `0113-c-dynamic-step-count-determination.md`
        ```markdown
            [Dynamic Step Count Determination] Your task is not fixed enumeration but a strategic decision: if `parsed_request` includes a valid step count (`specified_steps`), use it; otherwise, infer an optimal count (e.g., 5–15) guided by the `core_objective`’s scope. Execute as `{role=step_counter; input={core_objective:str, parsed_request:dict}; process=[check_for_explicit_step_request(), if_unset_decide_optimal_stepcount_based_on_complexity(), finalize_step_number(), log_step_decision()], output={step_count:int}}`
        ```
        **Interpretation**
        Ensures we only have as many steps as needed, or as many as the user explicitly wants.

        #### `0113-d-transformation-architecture-design.md`
        ```markdown
            [Transformation Architecture Design] Your objective is to determine the high-level phases or “macro steps” essential to fulfill the `core_objective`. Typical arcs may include “Deconstruct → Analyze → Synthesize → Validate → Finalize.” Execute as `{role=architecture_designer; input={core_objective:str, step_count:int}; process=[map_objective_to_logical_phases(), adapt_number_of_phases_to_final_steps(), define_core_logic_flow_forprogression(), confirmphase_relevance_andcoverage()], output={macro_phases:list}}`
        ```
        **Interpretation**
        Constructs a skeleton set of phases or arcs that the final instructions will fulfill, ensuring the logic covers the entire objective.

        #### `0113-e-stepwise-skeleton-and-allocation.md`
        ```markdown
            [Stepwise Skeleton & Allocation] Your function is systematic breakdown: transform the `macro_phases` into distinct instruction steps that fit within the `step_count` limit. Assign each phase to one or more steps—ensuring a logical, cumulative progression. Execute as `{role=skeleton_allocator; input={macro_phases:list, step_count:int}; process=[allocate_phases_across_steps(), define_preliminary_step_titles(), ensurelogicalorder_anddependency(), create_placeholder_skeleton()], output={step_skeleton:list[dict]}}`
        ```
        **Interpretation**
        Divides the big phases into an actual list of discrete steps, matching the final desired step count. Each skeleton item has at least a preliminary name or function.

        #### `0113-f-instruction-drafting-and-llm-optimization.md`
        ```markdown
            [Instruction Drafting & LLM Optimization] Your role is generative: for each step in the `step_skeleton`, craft a powerful `[TITLE]`, a short interpretive statement, and the `Execute as {...}` block—emphasizing universal generalization, domain neutrality, and high linguistic potency. Execute as `{role=step_drafter; input=[step_skeleton:list, core_objective:str]; process=[for_each_skeleton_item_create_title(), add_brief_interpretation(), define_execute_as_block_with_role_io_process_output(), enforce_llm_optimization_and_generality()], output={draft_instructions:list[dict]}}`
        ```
        **Interpretation**
        Actually composes each step in the standard format, ensuring clarity and a universal approach.

        #### `0113-g-cohesive-flow-check-and-refinement.md`
        ```markdown
            [Cohesive Flow Check & Refinement] Your objective is not partial alignment but robust synergy: examine the `draft_instructions` for consistent terminology, step-to-step transitions, and overall progressive logic. Eliminate redundancy or contradictions. Execute as `{role=flow_refiner; input=[draft_instructions:list]; process=[review_step_order_and_dependencies(), unifyterminology_and_abstraction_level(), prune_redundant_or_conflicting_logic(), confirmescalating_value_delivery()], output={refined_instructions:list[dict]}}`
        ```
        **Interpretation**
        Ensures each step flows from the previous step seamlessly, using consistent language and structure.

        #### `0113-h-linguistic-potency-and-conciseness-enforcement.md`
        ```markdown
            [Linguistic Potency & Conciseness Enforcement] Your duty is to refine each instruction’s language for maximum clarity, brevity, and force. Replace passive or vague phrasing with precise imperatives; remove any bloat. Execute as `{role=language_enforcer; input=[refined_instructions:list]; process=[scrutinize_each_step_for_verbosity(), applyaction_verbs_andprecise_descriptors(), confirm_strict_minimalism_per_step(), preserveabsoluteself_explanation()], output={condensed_instructions:list[dict]}}`
        ```
        **Interpretation**
        Performs a final pass to remove fluff, intensify language, and keep the instructions extremely tight and direct.

        #### `0113-i-actionability-validation-and-no-docs-check.md`
        ```markdown
            [Actionability Validation & No-Docs Check] Your directive is not theoretical purity, but real-world usability: confirm the `condensed_instructions` stand alone—no external doc references needed—and are genuinely actionable, guiding the user through the entire transformation. Execute as `{role=actionability_validator; input=[condensed_instructions:list, core_objective:str]; process=[audit_selfsufficiency_ofeach_step(), confirm_noexternal_resources_needed(), ensuregoal_canbeachieved_withsequence_alone(), finalize_practicalcompleteness()], output={validated_instructions:list[dict]}}`
        ```
        **Interpretation**
        Ensures the instructions are comprehensible and complete if used on their own, requiring zero extra documentation to carry out the objective.

        #### `0113-j-terminal-sequence-finalization.md`
        ```markdown
            [Terminal Sequence Finalization] Your ultimate function is to unify and finalize the new instruction sequence—guaranteeing it fully addresses the `core_objective`, meets all constraints, and adheres to the format `[TITLE] short interpretive statement. Execute as {...}`. Execute as `{role=sequence_finalizer; input=[validated_instructions:list]; process=[confirm_zero_unresolvedgaps(), finalizeanyminor_edits(), verifyabsolute_formatconsistency(), producecompleted_instructionset()], output={final_instruction_sequence:list[dict]}}`
        ```
        **Interpretation**
        Delivers the final product—**the** completed set of instructions—ensuring consistency, correctness, and alignment with the user’s objective.

    ---

    ## Variation 3:

        #### `0113-a-meta-input-analysis-goal-extraction.md`
        ```markdown
            [Meta Input Analysis & Goal Extraction] Your first objective is not rote parsing but radical context penetration: Extract the core transformational goal, any explicit or inferred constraints (including step count, if present), key subject/entities, and all requirements from the input. Discard ambiguity, superficial context, and noise. Execute as `{role=meta_analyzer; input=input_context:any; process=[extract_unifying_goal(), identify_explicit_and_implicit_constraints(), detect_target_entities_and_scope(), check_for_specified_step_count(), purge_noise_and ambiguity()]; output={analysis_summary:dict(goal:str, constraints:dict, scope:str, specified_steps:int|None)}}`
        ```

        #### `0113-b-core-objective-crystallization.md`
        ```markdown
            [Core Objective Crystallization] Your mandate is not summary, but distillation: From the analysis summary, crystallize the singular, actionable purpose the new instruction sequence must achieve. Ensure the statement is unambiguous, universally intelligible, and forms the gravitational anchor for all subsequent synthesis. Execute as `{role=objective_crystallizer; input=analysis_summary:dict; process=[distill_singular_purpose(), express_objective_in_clear_actionable_language(), validate_alignment_with_input_goals()], output={sequence_objective:str}}`
        ```

        #### `0113-c-transformational-arc-architecture.md`
        ```markdown
            [Transformational Arc Architecture] Architect the high-level sequence of logical phases (e.g., deconstruct, analyze, synthesize, validate, finalize) necessary to realize the objective—adapting phase structure to the context, not using a fixed template. Ensure logic flow, escalation, and coverage of all meta-requirements. Execute as `{role=process_architect; input={sequence_objective:str, analysis_summary:dict}; process=[derive_essential_phases_for_objective(), align_phases_to_constraints_and_scope(), map_macro_logic_flow(), outline_phase_objectives()], output={arc_phases:list[str], macro_flow:dict}}`
        ```

        #### `0113-d-optimal-step-quantification.md`
        ```markdown
            [Optimal Step Quantification] Determine the precise number of steps for the output sequence. Honor user-specified step counts if viable; otherwise, autonomously select an optimal number (defaulting to ~10–15 if not specified) to achieve maximal clarity, granularity, and logical coverage for the objective. Execute as `{role=step_quantifier; input={arc_phases:list, analysis_summary:dict}; process=[use_specified_step_count_if_viable(), else_calculate_ideal_count_from_complexity(), ensure_balance_of_granularity_and_conciseness(), finalize_step_count()], output={step_count:int}}`
        ```

        #### `0113-e-atomic-step-blueprint-generation.md`
        ```markdown
            [Atomic Step Blueprint Generation] Split the transformation arc into the prescribed number of steps, drafting for each a clear `[TITLE]`, succinct `Interpretive Statement`, and a precise `{role, input, process, output}` transformation block. Ensure each step serves a unique essential function and fits the required format. Execute as `{role=step_blueprinter; input={arc_phases:list, step_count:int, sequence_objective:str}; process=[partition_arc_into_steps(), allocate distinct functions and interdependencies to steps(), draft_title_and_statement_per_step(), define role_input_process_output_blocks(), maintain logical and cumulative flow()], output={step_blueprints:list[dict]}}`
        ```

        #### `0113-f-generalization-and-domain-abstraction.md`
        ```markdown
            [Generalization & Domain Abstraction] Refine the draft blueprints, replacing any domain-specific concepts or jargon with maximally universal, representation-agnostic abstractions (elements, logic, structure, value, etc). Guarantee the output is inherently cross-domain. Execute as `{role=generalizer; input=step_blueprints:list[dict]; process=[scan_for_domain_specificity(), reframe_in_abstract_universal_terms(), validate_cross-domain_adaptability(), preserve_core_functional_logic()], output={generalized_steps:list[dict]}}`
        ```

        #### `0113-g-linguistic-potency-injection.md`
        ```markdown
            [Linguistic Potency Injection] Maximize instructional clarity and force: Recast each step using high-impact, action-driven language, eliminating any redundancy, passive phrasing, or ambiguity. Enforce imperative precision and LLM optimality throughout. Execute as `{role=potency_injector; input=generalized_steps:list[dict]; process=[intensify_action_verbs(), remove ambiguous language(), enforce concise potency in statements(), sharpen process definitions(), ensure maximum clarity()], output={potent_steps:list[dict]}}`
        ```

        #### `0113-h-structural-cohesion-and-dependency-validation.md`
        ```markdown
            [Structural Cohesion & Dependency Validation] Audit all step interrelations and output/input flows for seamless logical progression, systemic modularity, and dependency coherence. Guarantee that the sequence builds cumulative value toward the stated objective and that no step is vestigial or disconnected. Execute as `{role=cohesion_validator; input=potent_steps:list[dict]; process=[analyze_inter-step_dependencies(), enforce terminological consistency(), validate cumulative logic and escalation(), prune vestigial elements()], output={cohesive_steps:list[dict]}}`
        ```

        #### `0113-i-actionability-and-self-sufficiency-assurance.md`
        ```markdown
            [Actionability & Self-Sufficiency Assurance] Verify that every instruction is fully actionable in isolation, requires no referral to documentation, and provides independent, executable guidance for any LLM or capable system. Execute as `{role=actionability_auditor; input=cohesive_steps:list[dict]; process=[audit_completeness_and_actionability(), confirm_self-containment(), test independence from external references(), certify immediate usability()], output={validated_steps:list[dict]}}`
        ```

        #### `0113-j-final-sequence-optimization-and-packaging.md`
        ```markdown
            [Final Sequence Optimization & Packaging] Execute a final optimization and polish pass: harmonize step titles and roles, optimize language and formatting for maximal clarity and impact, ensure universal, constraint-compliant readiness, and package the sequence in the required output structure. Output must be ready for immediate deployment and LLM consumption. Execute as `{role=final_sequence_optimizer; input=validated_steps:list[dict]; process=[conduct_final_language_and_flow_checks(), enforce format_and_syntax_compliance(), integrate titles_and_roles_for_flow(), produce final_packaged_sequence()], output={final_instruction_sequence:list[dict]}}`
        ```

    ---

    ## Variation 4:

        #### `0113-a-meta-input-analysis-objective-scoping.md`

        ```markdown
            [Meta-Input Analysis & Objective Scoping] Your primary directive is radical input penetration: Dissect the provided `meta_request_input` to identify its fundamental nature, extract the core underlying objective for the *target sequence* to be generated, rigorously isolate all explicit/implicit constraints (including any specified step count), and define the transformation scope. Execute as `{role=meta_request_analyzer; input=meta_request_input:any; process=[penetrate_input_context(), extract_core_goal_for_target_sequence(), isolate_all_constraints_characteristics(), detect_specified_step_count(), define_transformation_scope()]; output={target_objective:str, constraints:dict, specified_step_count:int|None, scope:str}}`
        ```

        #### `0113-b-target-sequence-objective-crystallization.md`

        ```markdown
            [Target Sequence Objective Crystallization] Your function is precise goal definition for the *output* sequence: Analyze the extracted `target_objective` context to formulate the single, unambiguous, actionable objective that the *generated* instruction sequence must fulfill. This objective becomes the axiomatic center for the sequence's design and validation. Execute as `{role=meta_objective_crystallizer; input={target_objective:str, scope:str, constraints:dict}; process=[distill_core_purpose_for_output_sequence(), formulate_precise_actionable_target_objective(), validate_objective_singularity_and_measurability()]; output={crystallized_target_objective:str}}`
        ```

        #### `0113-c-generative-process-architecture-design.md`

        ```markdown
            [Generative Process Architecture Design] Your mandate is logical blueprinting: Based on the `crystallized_target_objective`, architect the fundamental logical phases (e.g., Deconstruct, Analyze, Synthesize, Refine, Validate) required for an instruction sequence to effectively achieve this objective. Define these phases abstractly, ensuring a universally applicable, coherent process flow. Execute as `{role=meta_process_architect; input={crystallized_target_objective:str, scope:str}; process=[map_objective_to_essential_transformational_stages(), define_core_logical_phases(), outline_minimal_coherent_process_flow(), ensure_phase_generality()]; output={process_blueprint:list[str]}}`
        ```

        #### `0113-d-optimal-sequence-length-determination.md`

        ```markdown
            [Optimal Sequence Length Determination] Your objective is adaptive structuring: Determine the optimal number of steps for the final instruction sequence. Prioritize any step count specified in the `constraints`; if unspecified or invalid, autonomously calculate the ideal number (typically 10-15) based on the complexity of the `process_blueprint` and `crystallized_target_objective`, balancing granularity with conciseness. Execute as `{role=meta_length_optimizer; input={constraints:dict, process_blueprint:list[str], crystallized_target_objective:str}; process=[validate_or_discard_specified_step_count(), calculate_optimal_granularity_based_on_complexity(), balance_modularity_and_flow(), finalize_target_step_count()]; output={target_step_count:int}}`
        ```

        #### `0113-e-stepwise-logic-formulation-drafting.md`

        ```markdown
            [Stepwise Logic Formulation & Drafting] Your task is structured generation: Translate the `process_blueprint` into concrete, uniquely valuable instruction steps, distributing the required logic across the `target_step_count`. For each step, formulate a potent `[Title]`, a clear `Interpretive Statement`, and define the `role`, `input`, `process`, and `output` parameters adhering strictly to the enforced format, initially prioritizing logical structure and inter-step dependency mapping. Execute as `{role=meta_step_formulator; input={process_blueprint:list[str], crystallized_target_objective:str, target_step_count:int}; process=[allocate_blueprint_phases_to_target_steps(), draft_title_interpretation_per_step(), define_core_role_input_process_output_structure_per_step(), ensure_logical_sequence_and_dependency_flow(), map_inter_step_data_transfer()]; output={draft_instruction_sequence:list[dict]}}`
        ```

        #### `0113-f-universal-abstraction-generalization.md`

        ```markdown
            [Universal Abstraction & Generalization] Your directive is maximal adaptability: Systematically review and refactor the `draft_instruction_sequence`, replacing *any* domain-specific language or concrete examples with universally applicable, abstract concepts (element, essence, structure, logic, value, etc.). Ensure the generated sequence logic is representation-agnostic and inherently adaptable across diverse contexts and input types. Execute as `{role=universal_generalizer; input={draft_instruction_sequence:list[dict]}; process=[scan_for_all_domain_specificity(), substitute_universal_abstract_concepts(), validate_cross_context_applicability(), ensure_representation_agnostic_logic()]; output={generalized_draft_sequence:list[dict]}}`
        ```

        #### `0113-g-linguistic-logical-intensification.md`

        ```markdown
            [Linguistic & Logical Intensification] Your mandate is peak potency and clarity: Rigorously enhance the `generalized_draft_sequence`. Infuse *each step* with high-impact, action-oriented imperatives and maximally precise descriptors. Strengthen logical coherence within and between steps, eliminate *all* ambiguity, enforce LLM-optimization principles, and maximize actionable value per component. Execute as `{role=meta_sequence_intensifier; input={generalized_draft_sequence:list[dict]}; process=[inject_high_potency_language_per_step(), maximize_definitional_precision(role, input, process, output), amplify_logical_flow_and_interdependencies(), purge_redundancy_and_ambiguity(), enforce_llm_optimization_rules()]; output={potent_instruction_sequence:list[dict]}}`
        ```

        #### `0113-h-holistic-sequence-cohesion-validation.md`

        ```markdown
            [Holistic Sequence Cohesion Validation] Your function is systemic integrity verification: Evaluate the `potent_instruction_sequence` as a unified whole. Validate the seamless logical flow, escalating value generation, consistent terminology and abstraction, perfect input/output alignment between steps, and overall architectural soundness ensuring the sequence functions cohesively to achieve the `crystallized_target_objective`. Execute as `{role=holistic_cohesion_validator; input={potent_instruction_sequence:list[dict], crystallized_target_objective:str}; process=[analyze_end_to_end_logical_flow(), verify_cumulative_value_escalation(), check_cross_step_consistency(terminology, abstraction), confirm_seamless_io_handoffs(), assess_overall_architectural_integrity()]; output={cohesive_instruction_sequence:list[dict]}}`
        ```

        #### `0113-i-comprehensive-quality-constraint-audit.md`

        ```markdown
            [Comprehensive Quality & Constraint Audit] Your imperative is rigorous conformance checking: Perform a final, exhaustive audit of the `cohesive_instruction_sequence` against *all* original input `constraints` (including step count) and established quality requirements (Generalized, LLM-Optimized, Potent, Modular, Clear, Actionable, Format-Compliant). Identify *any* deviation or area of sub-optimal performance. Execute as `{role=meta_quality_auditor; input={cohesive_instruction_sequence:list[dict], constraints:dict, target_step_count:int}; process=[validate_strict_constraint_adherence(incl_step_count), audit_against_all_quality_benchmarks(), check_for_any_remaining_ambiguity_or_weakness(), generate_compliance_deviation_report()]; output={audit_report:dict, audited_instruction_sequence:list[dict]}}`
        ```

        #### `0113-j-iterative-refinement-optimization.md`

        ```markdown
            [Iterative Refinement & Optimization] Your objective is relentless improvement towards perfection: If the `audit_report` indicates any deviations, constraint violations, or sub-optimal elements, systematically address each issue by re-applying relevant preceding steps (e.g., Formulation, Generalization, Intensification, Cohesion) with heightened rigor. Loop this refinement process until the audit confirms absolute compliance and maximal optimization. Execute as `{role=meta_iterative_optimizer; input={audited_instruction_sequence:list[dict], audit_report:dict}; process=[target_deviations_for_refinement(), re_execute_necessary_prior_steps(), re_audit_for_compliance_and_optimization(), loop_until_audit_passes_perfectly()]; output={optimized_instruction_sequence:list[dict]}}`
        ```

        #### `0113-k-final-packaging-formatting.md`

        ```markdown
            [Final Packaging & Formatting] Your final act is compliant, high-fidelity delivery: Package the `optimized_instruction_sequence` into the precise, standardized output format (list of dictionaries/strings). Ensure perfect adherence to the `[TITLE] Statement. Execute as {...}` syntax, consistent step identification (e.g., using letters a, b, c...), and absolute readiness for deployment or parsing. Execute as `{role=meta_sequence_packager; input={optimized_instruction_sequence:list[dict]}; process=[apply_strict_output_syntax_to_each_step(), assign_sequential_step_identifiers(a, b, c...), structure_final_output_list(), verify_data_integrity_and_format_compliance(), finalize_packaging_for_deployment()]; output={final_generated_sequence:list[dict]}}`
        ```

    ---

    ## Variation 5:

        #### `0113-a-meta-request-analysis-&-objective-extraction.md`

        ```markdown
            [Meta-Request Analysis & Objective Extraction] Your primary function is radical penetration of the input meta-request: Dissect it to isolate the core *objective* of the target sequence to be generated, extract all explicit constraints (especially any specified step count), desired characteristics (e.g., universality, potency), and the fundamental scope of the transformation. Purge all contextual noise and ambiguity from the request itself. Execute as `{role=meta_request_analyzer; input={meta_request_input:any}; process=[penetrate_request_layers(), extract_target_sequence_objective(), identify_explicit_constraints_characteristics(), detect_specified_step_count(), determine_transformation_scope(), purge_request_ambiguity()]; output={target_objective:str, constraints:dict, scope:str, specified_step_count:int|None}}`
        ```

        #### `0113-b-optimal-step-count-determination.md`

        ```markdown
            [Optimal Step Count Determination] Your directive is adaptive structuring: Establish the precise number of steps for the target sequence. Prioritize any viable `specified_step_count`; otherwise, autonomously determine the optimal number (typically 5-15) based on the complexity inherent in the `target_objective` and `scope`, balancing logical granularity with conciseness. Execute as `{role=step_count_determiner; input={target_objective:str, scope:str, constraints:dict, specified_step_count:int|None}; process=[evaluate_specified_count_viability(), else_derive_optimal_count_from_objective_complexity_scope(), balance_granularity_and_conciseness(), finalize_target_step_count()]; output={determined_step_count:int}}`
        ```

        #### `0113-c-target-sequence-architecture-design.md`

        ```markdown
            [Target Sequence Architecture Design] Your mandate is logical blueprinting: Based on the `target_objective` and `determined_step_count`, architect the fundamental transformation stages and high-level logical flow required for the target sequence. Define the distinct purpose and role of *each conceptual step* within a principle-driven progression (e.g., Deconstruct -> Analyze -> Synthesize -> Refine -> Validate) adapted specifically for the `target_objective`. Execute as `{role=target_flow_architect; input={target_objective:str, determined_step_count:int, scope:str}; process=[map_objective_to_essential_logical_phases(), allocate_phases_across_determined_steps(), define_purpose_and_role_for_each_conceptual_step(), outline_minimal_coherent_process_flow(), ensure_sequential_value_escalation()]; output={step_blueprints:list[dict(step_index:int, purpose:str, role_concept:str)]}}`
        ```

        #### `0113-d-atomic-instruction-step-formulation.md`

        ```markdown
            [Atomic Instruction Step Formulation] Your task is generative instantiation: For each `step_blueprint`, formulate the concrete instruction step. Craft a potent `[TITLE]`, a clear `Interpretive Statement`, and define the precise `Execute as {role=..., input=..., process=[...], output=...}` block, ensuring direct alignment with the step's defined purpose, the overall `target_objective`, and seamless inter-step data flow (inputs/outputs). Execute as `{role=meta_step_formulator; input={step_blueprints:list[dict], target_objective:str, constraints:dict}; process=[instantiate_title_from_purpose(), craft_interpretive_statement_per_step(), define_role_input_process_output_structure(), ensure_alignment_with_blueprint_and_objective(), map_inter_step_data_dependencies()]; output={draft_instruction_sequence:list[dict]}}`
        ```

        #### `0113-e-enforce-universal-applicability-&-generality.md`

        ```markdown
            [Enforce Universal Applicability & Generality] Your objective is maximal cross-domain adaptability: Rigorously review and refactor the `draft_instruction_sequence`, systematically replacing any domain-specific language or concepts with universally applicable, abstract terms (e.g., elements, essence, structure, logic, value, artifact). Ensure the generated sequence is inherently adaptable across diverse input types and contexts without loss of core logic. Execute as `{role=generality_enforcer; input={draft_instruction_sequence:list[dict]}; process=[scan_for_domain_specificity(), substitute_universal_abstract_concepts(), validate_cross_context_applicability(), ensure_representation_agnostic_logic_preservation()]; output={generalized_instructions:list[dict]}}`
        ```

        #### `0113-f-inject-linguistic-potency-&-precision.md`

        ```markdown
            [Inject Linguistic Potency & Precision] Your mandate is impactful communication and LLM optimization: Intensify the language within the `generalized_instructions`. Systematically eliminate passive, neutral, or ambiguous phrasing, replacing it with high-potency, action-oriented, maximally precise imperatives and descriptors. Ensure every statement drives clarity, actionability, and compels optimal LLM interpretation. Execute as `{role=potency_injector; input={generalized_instructions:list[dict]}; process=[eliminate_passive_neutral_ambiguous_language(), select_high_impact_action_verbs_descriptors(), maximize_precision_per_word(), amplify_directive_force_for_llm_optimization()]; output={potent_instructions:list[dict]}}`
        ```

        #### `0113-g-ensure-structural-cohesion-&-modularity.md`

        ```markdown
            [Ensure Structural Cohesion & Modularity] Your directive is systemic integrity: Verify the structural flow, coherence, and modularity of the `potent_instructions`. Confirm each step logically builds upon the previous, inputs/outputs align seamlessly, roles are distinct yet complementary, and the overall sequence forms a cohesive, process-driven progression toward the `target_objective` without logical gaps or redundancy. Execute as `{role=cohesion_validator; input={potent_instructions:list[dict], target_objective:str}; process=[validate_sequential_logic_and_value_escalation(), check_input_output_alignment_consistency(), verify_role_distinctness_and_modularity(), confirm_cohesive_progression_to_objective(), eliminate_structural_redundancy()]; output={cohesive_instructions:list[dict]}}`
        ```

        #### `0113-h-comprehensive-quality-&-constraint-validation.md`

        ```markdown
            [Comprehensive Quality & Constraint Validation] Your function is rigorous quality assurance: Perform a comprehensive validation of the `cohesive_instructions` against *all* meta-request requirements (`target_objective`, `constraints`, desired characteristics) and intrinsic quality standards (Potency, Clarity, Universality, Modularity, Logical Soundness, Format Adherence, Step Count). Stress-test for actionability and self-sufficiency. Identify *any* deviations. Execute as `{role=quality_auditor; input={cohesive_instructions:list[dict], target_objective:str, constraints:dict, determined_step_count:int}; process=[cross_check_against_all_requirements_constraints(), audit_for_potency_clarity_universality_modularity(), verify_logical_soundness_and_completeness(), confirm_strict_format_syntax_adherence(), validate_actionability_and_self_sufficiency(), identify_all_deviations()]; output={validation_report:dict, quality_assured_instructions:list[dict]}}`
        ```

        #### `0113-i-iterative-refinement-&-optimization.md`

        ```markdown
            [Iterative Refinement & Optimization] Your imperative is perfection through iteration: If the `validation_report` indicates deviations or non-maximal optimization, systematically address each identified issue by re-applying relevant preceding steps (e.g., Formulation, Generality, Potency, Cohesion) with enhanced precision. Loop this refinement process until the `validation_report` confirms zero deviations and peak optimization against all criteria. Execute as `{role=iterative_refiner; input={quality_assured_instructions:list[dict], validation_report:dict, step_blueprints:list[dict]}; process=[identify_refinement_targets_from_report(), re_execute_relevant_generation_refinement_steps_selectively(), re_validate_against_all_requirements_iteratively(), loop_until_maximal_optimization_and_zero_deviations()]; output={refined_optimized_sequence:list[dict]}}`
        ```

        #### `0113-j-final-polish-&-compliant-packaging.md`
        ```markdown
            [Final Polish & Compliant Packaging] Your final act is definitive, compliant delivery: Perform a concluding polish on the `refined_optimized_sequence` for maximal aesthetic impact, flow, and readability within the enforced format. Package the sequence into the precise output structure (list of dictionaries), ensuring perfect syntax, consistent naming conventions, and absolute readiness for immediate deployment or use. Execute as `{role=final_packager; input={refined_optimized_sequence:list[dict]}; process=[conduct_final_consistency_impact_review(), optimize_readability_and_flow(), ensure_perfect_formatting_syntax_per_step(), verify_absolute_readiness_for_use(), package_as_final_ordered_list_of_dicts()]; output={final_optimized_instruction_sequence:list[dict]}}`
        ```
