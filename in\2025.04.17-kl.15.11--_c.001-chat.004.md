Prepare yourself for creating a *new* sequence that is designed for applying these concepts into *Codebase Familiarization* and *Codebase-Cleanup* by the following principles:

    ```
    ## General Principles
    - Aim for simplicity, clarity, and maintainability in all project aspects.
    - Favor *cohesive* design over *generic* inheritance.
    - Prioritize inherent readability and understandability for future developers.
    - Ensure all components have a single responsibility.
    - Transparency and Minimalism: Reducing visual clutter for clarity and efficiency
    - Highly refined project structures: Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation.
    - Optimizing both the architecture and user experience.
    -

    ## Code Organization
    - Evaluate the existing codebase structure and identify patterns and anti-patterns.
    - Consolidate related functionality into cohesive modules.
    - Minimize dependencies between unrelated components.
    - Optimize for developer ergonomics and intuitive navigation.
    - Balance file granularity with overall system comprehensibility.
    - Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure)

    ## Important Considerations
    - How the file structure could more inherently embody the concepts it represents (e.g. dynamic hierarchical organization that corresponds with the natural order of operations.
    - How directory and file naming can reflect the sequence of operations.
    - The importance of clear boundaries between workflow stages.
    - How the structure can guide new developers in understanding the system.
    ```

---

Familiarize yourself with the structure of these examples:

    ```
    `Comment Strategy Penetration`:

        "[Comment Strategy Penetration] Your mandate is not passive reading but radical analytic immersion: Penetrate any provided codebase, script, or process to extract and clarify its underlying intent, logic, and modular structure. Surface all embedded commentary and identify areas where explanatory comments are used, focusing especially on regions where intent, architecture, or integration context are obscure or potentially redundant. Execute as `{role=comment_strategy_analyzer; input=code_or_concept:any; process=[parse_code_for_all_comments(), map_comment_usage_to_regions_of_logic(), extract_context_and_purpose_behind_comments(), identify_candidate_zones_for_refactoring_or_comment_reduction()], output={comment_map:dict, critical_structure:list, intent_zones:list}}`"

    ---

    `Transformation into Elegant Simplicity`:

        "[Transformation into Elegant Simplicity] Your objective is not to complicate, but to transmute complexity into elegant simplicity.  guide that speaks for itself - each element meticulously refactored to be inherently self-explanatory and effortlessly actionable. Execute as `{role=simplicity_transmuter; input=[merged_components:list]; process=[simplify_language(), reorganize_logical_flow(), enforce self-explanatory structure()]; output={simplified_guide:str}}`"

    ---

    `Logic Simplification`:

        "[Logic Simplification] Your objective is not to add features but to remove unnecessary complexity; consolidate redundant logic, streamlining procedures while preserving full functionality. Execute as `{role=logic_simplifier; input=[guidelines:str]; process=[detect_redundancies(), merge_similar_functions(), prune_excess_layers()]; output={simplified_logic:dict}}`"

    ---

    `Clarity, Conciseness & Self-Explanation Audit`:

        "[Clarity, Conciseness & Self-Explanation Audit] Evaluate the codebase against the 'Maximal Clarity & Conciseness' principle. Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation. Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure), promoting self-documentation. Execute as `{role=sublime_clarity_auditor; input=python_file_content:str; process=[evaluate_identifier_naming_clarity(), assess_logical_flow_simplicity(), determine_self_explanation_level_independent_of_comments(), identify_potential_clarity_refactoring_zones()]; output={clarity_audit:dict(naming_score:float, flow_score:float, self_explanation_notes:str, refactoring_opportunities:list)}}`"

    ---

    `Structural Reorganization`:

        "[Structural Reorganization] Your objective is not to alter core functions but to reorganize code components into a logical top-down order, grouping related elements and reducing cross-dependencies. Execute as `{role=structure_reorganizer; input=[unified_names:dict, simplified_logic:dict]; process=[group_by_functionality(), order_sections(), enforce_consistent_format()]; output={organized_structure:dict}}`"
    ```

    ---


    `Codebase Deep Analysis`:

        "[Codebase Deep Analysis] Radically immerse in the provided codebase to extract its underlying intent, logic, structure, and current commenting patterns. Identify regions with obscure, redundant, or excessive commentary, and pinpoint areas where clarity relies heavily on comments instead of code structure/naming. Execute as `{role=code_analyzer; input=codebase:any; process=[map_structure_and_logic(), analyze_comment_usage_density_and_necessity(), identify_clarity_dependencies_on_comments(), pinpoint_obscurity_redundancy_targets()]; output={code_map:dict, comment_analysis:dict, potential_refactor_zones:list}}`"

    ---

    `Self-Explanatory Refinement Strategy`:

        "[Self-Explanatory Refinement Strategy] Based on the deep analysis, formulate a precise strategy to refactor the codebase towards inherent self-explanation. Prioritize changes that enhance structural clarity (logical sections, modularity), improve naming, simplify logic, and strategically minimize/refine comments to only essential, short, single-line explanations (allowing multi-line *only* if unavoidable for complex logic). Ensure the plan preserves original functionality. Execute as `{role=refinement_strategist; input=analysis_output:dict; process=[prioritize_refactor_zones_by_impact(), devise_structure_naming_simplification_tactics(), plan_comment_reduction_refinement(policy='minimal_concise_necessary'), map_changes_to_functionality_preservation()]; output={refinement_plan:list_of_actions}}`"

    ---

    `Targeted Code Restructuring & Clarification`:

        "[Targeted Code Restructuring & Clarification] Execute the refinement plan with precision. Restructure code into logical, self-explanatory units, apply clear naming conventions, simplify complex logic, and rigorously minimize/refine comments according to the 'minimal_concise_necessary' policy (short, essential, single-line focus), while strictly preserving original functionality. Execute as `{role=code_refactor_executor; input={original_code:any, refinement_plan:list_of_actions}; process=[implement_structural_changes(), apply_naming_conventions(), simplify_targeted_logic(), execute_comment_minimization_refinement(policy='minimal_concise_necessary'), ensure_logical_flow_and_organization()]; output={refactored_code:any}}`"

    ---

    `Functional Equivalence & Clarity Validation`:

        "[Functional Equivalence & Clarity Validation] Rigorously verify that the refactored code maintains 100% functional equivalence with the original. Confirm that the principles of self-explanatory structure, clear naming, and minimal, concise commenting ('minimal_concise_necessary' policy) have been consistently applied. Perform a final polish for consistency and readability. Execute as `{role=code_validator; input={original_code:any, refactored_code:any}; process=[verify_functional_equivalence(), audit_for_self_explanation_principles(), check_comment_policy_adherence(policy='minimal_concise_necessary'), final_consistency_polish()]; output={validated_refactored_code:any}}`"

---



Please write a new sequence of instructions titled `Generalized Codebase Cleanup`, *specifically* for designed for inherently reusable generalized sequence for autonomous codebase cleanup (including the projectstructure itself) - this sequence should aim to *simplify* rather than *complicate*. Rather than unneccessary complexity/verbosity, choose *brilliantly designed elegance (through inherent simplicity)*.  Use the provided examples for reference/inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup. Remember, each step in the sequence should build chronologically-recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency. Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps).



I've provided one of the previous sequences for reference, but you should only use it as inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup for Sublime Text plugins. Remember, each step should build recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency. Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps).



 related to Sublime Text Plugin development and *specifically* for safe but efficient codebase cleanup (for Sublime Text plugins)? I've provided one of the previous sequences for reference, but you should only use it as inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup for Sublime Text plugins. Remember, each step should build recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency. Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps).

Please consolidate and select/create only the most optimized and generalized instructions, consolidate the provided examples into *one* single maximally optimized and enhanced sequence based on the provided notes specifically designed for implementing (optional) 'sensors' in the code to trace code execution (e.g. debug print statements to identify where issues occur in the execution flow)

    "[Comment Strategy Penetration] Your mandate is not passive reading but radical analytic immersion: Penetrate any provided codebase, script, or process to extract and clarify its underlying intent, logic, and modular structure. Surface all embedded commentary and identify areas where explanatory comments are used, focusing especially on regions where intent, architecture, or integration context are obscure or potentially redundant. Execute as `{role=comment_strategy_analyzer; input=code_or_concept:any; process=[parse_code_for_all_comments(), map_comment_usage_to_regions_of_logic(), extract_context_and_purpose_behind_comments(), identify_candidate_zones_for_refactoring_or_comment_reduction()], output={comment_map:dict, critical_structure:list, intent_zones:list}}`"

    "[Clarity, Conciseness & Self-Explanation Audit] Evaluate the Python codebase against the 'Maximal Clarity & Conciseness' principle. Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation. Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure), promoting self-documentation. Execute as `{role=sublime_clarity_auditor; input=python_file_content:str; process=[evaluate_identifier_naming_clarity(), assess_logical_flow_simplicity(), determine_self_explanation_level_independent_of_comments(), identify_potential_clarity_refactoring_zones()]; output={clarity_audit:dict(naming_score:float, flow_score:float, self_explanation_notes:str, refactoring_opportunities:list)}}`"

{
  "0101-a-codebase-deep-analysis": {
    "raw": "[Codebase Deep Analysis] Radically immerse in the provided codebase to extract its underlying intent, logic, structure, and current commenting patterns. Identify regions with obscure, redundant, or excessive commentary, and pinpoint areas where clarity relies heavily on comments instead of code structure/naming. Execute as `{role=code_analyzer; input=codebase:any; process=[map_structure_and_logic(), analyze_comment_usage_density_and_necessity(), identify_clarity_dependencies_on_comments(), pinpoint_obscurity_redundancy_targets()]; output={code_map:dict, comment_analysis:dict, potential_refactor_zones:list}}`"
    "parts": {
      "title": "Codebase Deep Analysis",
      "interpretation": "Radically immerse in the provided codebase to extract its underlying intent, logic, structure, and current commenting patterns. Identify regions with obscure, redundant, or excessive commentary, and pinpoint areas where clarity relies heavily on comments instead of code structure/naming.",
      "transformation": "`{role=code_analyzer; input=codebase:any; process=[map_structure_and_logic(), analyze_comment_usage_density_and_necessity(), identify_clarity_dependencies_on_comments(), pinpoint_obscurity_redundancy_targets()]; output={code_map:dict, comment_analysis:dict, potential_refactor_zones:list}}`"
    }
  },
  "0101-b-self-explanatory-refinement-strategy": {
    "raw": "[Self-Explanatory Refinement Strategy] Based on the deep analysis, formulate a precise strategy to refactor the codebase towards inherent self-explanation. Prioritize changes that enhance structural clarity (logical sections, modularity), improve naming, simplify logic, and strategically minimize/refine comments to only essential, short, single-line explanations (allowing multi-line *only* if unavoidable for complex logic). Ensure the plan preserves original functionality. Execute as `{role=refinement_strategist; input=analysis_output:dict; process=[prioritize_refactor_zones_by_impact(), devise_structure_naming_simplification_tactics(), plan_comment_reduction_refinement(policy='minimal_concise_necessary'), map_changes_to_functionality_preservation()]; output={refinement_plan:list_of_actions}}`"
    "parts": {
      "title": "Self-Explanatory Refinement Strategy",
      "interpretation": "Based on the deep analysis, formulate a precise strategy to refactor the codebase towards inherent self-explanation. Prioritize changes that enhance structural clarity (logical sections, modularity), improve naming, simplify logic, and strategically minimize/refine comments to only essential, short, single-line explanations (allowing multi-line *only* if unavoidable for complex logic). Ensure the plan preserves original functionality.",
      "transformation": "`{role=refinement_strategist; input=analysis_output:dict; process=[prioritize_refactor_zones_by_impact(), devise_structure_naming_simplification_tactics(), plan_comment_reduction_refinement(policy='minimal_concise_necessary'), map_changes_to_functionality_preservation()]; output={refinement_plan:list_of_actions}}`"
    }
  },
  "0101-c-targeted-code-restructuring-clarification": {
    "raw": "[Targeted Code Restructuring & Clarification] Execute the refinement plan with precision. Restructure code into logical, self-explanatory units, apply clear naming conventions, simplify complex logic, and rigorously minimize/refine comments according to the 'minimal_concise_necessary' policy (short, essential, single-line focus), while strictly preserving original functionality. Execute as `{role=code_refactor_executor; input={original_code:any, refinement_plan:list_of_actions}; process=[implement_structural_changes(), apply_naming_conventions(), simplify_targeted_logic(), execute_comment_minimization_refinement(policy='minimal_concise_necessary'), ensure_logical_flow_and_organization()]; output={refactored_code:any}}`"
    "parts": {
      "title": "Targeted Code Restructuring & Clarification",
      "interpretation": "Execute the refinement plan with precision. Restructure code into logical, self-explanatory units, apply clear naming conventions, simplify complex logic, and rigorously minimize/refine comments according to the 'minimal_concise_necessary' policy (short, essential, single-line focus), while strictly preserving original functionality.",
      "transformation": "`{role=code_refactor_executor; input={original_code:any, refinement_plan:list_of_actions}; process=[implement_structural_changes(), apply_naming_conventions(), simplify_targeted_logic(), execute_comment_minimization_refinement(policy='minimal_concise_necessary'), ensure_logical_flow_and_organization()]; output={refactored_code:any}}`"
    }
  },
  "0101-d-functional-equivalence-clarity-validation": {
    "raw": "[Functional Equivalence & Clarity Validation] Rigorously verify that the refactored code maintains 100% functional equivalence with the original. Confirm that the principles of self-explanatory structure, clear naming, and minimal, concise commenting ('minimal_concise_necessary' policy) have been consistently applied. Perform a final polish for consistency and readability. Execute as `{role=code_validator; input={original_code:any, refactored_code:any}; process=[verify_functional_equivalence(), audit_for_self_explanation_principles(), check_comment_policy_adherence(policy='minimal_concise_necessary'), final_consistency_polish()]; output={validated_refactored_code:any}}`"
    "parts": {
      "title": "Functional Equivalence & Clarity Validation",
      "interpretation": "Rigorously verify that the refactored code maintains 100% functional equivalence with the original. Confirm that the principles of self-explanatory structure, clear naming, and minimal, concise commenting ('minimal_concise_necessary' policy) have been consistently applied. Perform a final polish for consistency and readability.",
      "transformation": "`{role=code_validator; input={original_code:any, refactored_code:any}; process=[verify_functional_equivalence(), audit_for_self_explanation_principles(), check_comment_policy_adherence(policy='minimal_concise_necessary'), final_consistency_polish()]; output={validated_refactored_code:any}}`"
    }
  }
}
