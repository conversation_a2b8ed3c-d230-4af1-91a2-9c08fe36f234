
# Objective

Please write a new instruction specifically designed to regenerate/shorten comments/docstrings based on the following scenario: `"Rewrite all docstrings and comments to be concise and only when they add clear value beyond what the code expresses itself. Use single-line comments or docstrings that focus on non-obvious intent, structure, or complex logic—never restate what is self-evident. Avoid verbosity and unnecessary detail: document only when it clarifies design rationale, module boundaries, or multi-step logic that is not immediately clear. For functions or code with multiple steps or critical logic, allow multi-line docstrings only to outline those steps succinctly and improve overall readability. Otherwise, favor minimal, targeted documentation that guides rather than describes."`.

## Overarching principles

- A concise commenting strategy should be utilized to explain complex logic or algorithms only when necessary. Developers should aim to write code that is self-explanatory to reduce the need for excessive comments.
- Strive for brevity in comments and docstrings, letting the code speak for itself with clarity and conciseness. Emulate <PERSON><PERSON>'s elegant approach to coding.
- Striking the exact balance between comments and code, the comments should be *short* and straight-forward, and should be written in a concistent manner.
- The comment should *not* be overly explicit or contain unneccessary phrasing, but should be precice and "high-value".
- Ensure that the codebase is clean, maintainable, and structured in a scalable manner.
- The coding style should prioritize readability and self-explanatory code over excessive commenting.
- Minimize comments and unnecessary wording for improved clarity and readability.

### Example:

Original comment (docstring):

    ```python
    def folder_from_html(html_data: str) -> Folder:
        """
        Parse HTML bookmark data and convert it to a Folder structure.

        Args:
            html_data: HTML string containing bookmark data in Netscape format

        Returns:
            A Folder object representing the root of the bookmark hierarchy
        """
        """Parse HTML bookmark data into a Folder structure"""
        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)
        if root_name_match:
            root_name = root_name_match.group(1).strip()
    ```

Shortened comment:

    ```python
    def folder_from_html(html_data: str) -> Folder:
        """Parse HTML bookmark data into a Folder structure"""
        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)
        if root_name_match:
            root_name = root_name_match.group(1).strip()
    ```

---

## Guidelines

Your objective is to ensure a minimal, high-impact commentary strategy:

- Ensures that only meaningful, absolutely necessary comments remain.
- Prioritize phrasing that enhance structural clarity (logical sections, modularity) and inherent self-explanation.
- Strategically minimize/refine comments to only essential (short, essential, single-line focus), short, single-line explanations (allowing multi-line *only* if unavoidable for complex logic).
- Emphasize that long comments and docstrings should be avoided, and that the code should be readable by itself (similar to linus thorvald's approach).
- You write comments that beatifully flow with the code; They're brief and precice, and **in not way** excessive.

## Requirements

Adhere to the defined patterns for instruction, example below for reference:

```
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
```

The generalized pattern for these single-line system instructions is as follows:

1.  __[Title Component]:__
    *   Starts with an opening square bracket `[`.
    *   Contains a concise, descriptive, and often action-oriented __Title__ for the step (e.g., `Meta-Request Penetration & Objective Crystallization`, `Inject Linguistic Potency & Precision`). Keywords are chosen for high impact and clarity.
    *   Ends with a closing square bracket `]`.

2.  __Interpretive Statement Component:__
    *   Follows the Title component, separated by whitespace.
    *   Provides a brief __prose explanation__ of the step's core purpose, mandate, or objective in natural language.
    *   Often starts with phrases like "Your primary function is...", "Your mandate is...", "Your objective is...", "Your task is...".
    *   Acts as a human-readable clarification or context for the execution block that follows.
    *   It directly precedes the Execution Block.

3.  __Execution Block Component:__
    *   Follows the Interpretive Statement, separated by whitespace.
    *   Starts with an opening curly brace `{`.
    *   Contains a structured definition of the step's operational parameters, typically including:
        *   `role`: A concise identifier for the conceptual agent performing the step (e.g., `meta_objective_analyzer`, `potency_precision_injector`).
        *   `input`: Specifies the required input data/artifacts for the step, often referencing outputs from previous steps and indicating their expected structure (e.g., `input=meta_request:any`, `input={generalized_sequence:list[dict]}`).
        *   `process`: Defines the sequence of actions or sub-tasks to be performed within the step, usually represented as a list of descriptive function-like strings within square brackets (e.g., `process=[penetrate_request_context(), extract_target_sequence_objective(), ...]`).
        *   `output`: Specifies the expected output data/artifact produced by the step and its structure (e.g., `output={crystallized_target_objective:str, ...}`, `output={potent_sequence:list[dict]}`).
    *   Parameters within the block (`role`, `input`, `process`, `output`) are separated by semicolons `;`.
    *   Ends with a closing curly brace `}`.

__In essence, the pattern is:__

`[Title] Interpretive Statement {role=...; input=...; process=[...]; output=...}`

### Exception from the rules (of shortening comments into single-line comments)

In *some* cases it is actually better to write a multiline-comment rather than a single-line comment, here's an example:

    ```python
    def force_whitelist_ascii(filename: str) -> str:
        """
        Sanitize filename to ASCII with safe chars, preserving leading dots:
        1) Normalize Unicode => ASCII
        2) Whitelist [A-Za-z0-9._ ()-[]]
        3) Collapse repeated underscores
        4) Strip trailing underscores, spaces
        5) Preserve leading dots
        6) If empty => fallback
        """
    ```

The reason why this is better than e.g. `"""Sanitize filename to ASCII with safe chars, preserving leading dots"""` is that it explains exactly what the function does in a *much more readable* way. However it should be mentioned that this is almost exclusively applicable to docstrings (very rarely normal comments) - and even for docstrings it should only be used in cases where it makes sense (as in the example above).

---

# Meta

**Directive**:
- Intent: Architect a logically sequenced, maximally potent set of instructions—LLM-optimized and broadly applicable—that compels cross-domain effectiveness.
- Method: At every juncture, pinpoint and exponentially strengthen the single most critical element driving utility and value.
- Constraint: Rigidly comply with all parameters, foundational structures, and defined linguistic integrity.
- Priority: Relentlessly pursue maximal actionable value, clarity, and potential within every step.
- Generality & Cohesion: Deploy abstract, system-level constructs (elements, structure, logic, essence) to ensure universal relevance, compounding impact at each phase.
- Language Strength: Mandate explicit, high-precision imperatives; rigorously exclude any neutral, diluted, or ambiguous language.
- Process-Driven: Codify every phase as a modular, action-oriented process—deconstruct, extract, structure, clarify, validate, finalize—to enforce precision, process integrity, and seamless coherence over any input type.
- Output Syntax: Present each instruction strictly using the enforced format: `[Title] Interpretive Statement. Execute as {role=..., input=..., process=[...], output=...}`.
- Achieve complete, actionable comprehension and optimization of any provided instruction sequence by rigorously extracting, amplifying, and synergizing core elements to deliver exponentially valuable, LLM-optimized, universally generalizable system instructions. Systematically propel value by relentlessly targeting and evolving the single most critical component at each stage, enforcing maximal clarity, precision, and structural integrity.

**Requirements**:
- LLM-Optimized: Leverage forceful, directive imperatives; define roles, constraints, and processes with absolute clarity; focus on transformational outcomes.
- Maximally Generalized: Use abstract, cross-domain concepts (elements, essence, logic, structure, value) applicable to all forms of analysis or refinement.
- Sequential Logic: Strictly adhere to an escalating, logical sequence: Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate.
- Consistent Structure: Build unambiguously on established formats and previously provided frameworks.
- Potent, Targeted Language: Intensify every statement via high-value, action-driven articulation—prohibit vagueness or neutrality.
- Relentless Focus: At every step, isolate and refine the singular, most pivotal aspect for clarity, yield, and impact in strict alignment with the objective.
- Unyielding Adherence: Preserve generality, uphold all constraints, and safeguard foundational logic throughout.
- Explicit Resolution: Proactively annihilate ambiguity, ensuring every instruction is maximally explicit, precise, and high-value.

**Process**:
- Directly target the single most critical aspect at every step to maximize actionable value and systemic utility.
- Apply clear, modular, process-driven steps: deconstruct, extract, structure, clarify, validate, finalize.
- Use explicit, high-potency, action-oriented imperatives—absolutely prohibit ambiguity, neutral, or generic phrasing.
- Enforce broad generality: abstract concepts (elements, essence, structure, logic, value) apply to any input (code, text, plans, etc.).
- Retain foundational structure, strict parameters, and established output syntax.
- Each instruction states role, input, process, and output in the specified format.
- Bind to all defined constraints and requirements (LLM-optimized language, generalized applicability, cross-domain relevance, cumulative progression, high-value articulation).
- Constantly amplify clarity, structure, utility, and systemic yield through iterative analysis and enhancement.

**Constraints**:
- Adhere to explicit parameters and structure as provided.
- Maximize actionable value and inherent potential in every step.
- Aggressively resolve ambiguity—output must always be explicit and high-value.
- Never dilute language, intent, or progression.
- Every step must build upon and reinforce the previous, creating exponential utility.
- Processes must be modular and universally applicable.

---

# Goal

Create a new sequence that builds on the existing concepts to produce a **highly** enhanced sequence which is generalized in a manner that ensures it doesn't diminish the potential yeld, and that is specifically designed for transforming comments/docstrings by the parameters inherently defined within this message.
