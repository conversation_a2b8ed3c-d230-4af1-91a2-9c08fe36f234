#!/usr/bin/env python3
"""
Example usage of py_similaritymapper for analyzing text file similarities.

This script demonstrates how to use the SimilarityMapper to find duplicate
and near-duplicate files in your text collection.
"""

from pathlib import Path
from src.similarity_mapper import SimilarityMapper


def analyze_files():
    """Run similarity analysis on files in the 'in' directory."""
    
    # Check if input directory exists
    input_dir = Path("in")
    if not input_dir.exists():
        print(f"Input directory '{input_dir}' not found!")
        print("Please ensure you have text files in the 'in' directory.")
        return
    
    # Create output directory
    output_dir = Path("out")
    output_dir.mkdir(exist_ok=True)
    
    print("=== py_similaritymapper Example ===")
    print(f"Analyzing files in: {input_dir}")
    print(f"Results will be saved to: {output_dir}")
    print()
    
    # Create mapper instance
    mapper = SimilarityMapper(input_dir=input_dir, output_dir=output_dir)
    
    # Run analysis with different thresholds and methods
    print("1. Running analysis with Jac<PERSON> similarity (80% threshold)...")
    report_jaccard = mapper.run_analysis(
        similarity_threshold=80.0, 
        method="jaccard"
    )
    
    print("\n" + "="*50 + "\n")
    
    # Reset for second analysis
    mapper = SimilarityMapper(input_dir=input_dir, output_dir=output_dir)
    
    print("2. Running analysis with TextDistance algorithm (85% threshold)...")
    report_textdist = mapper.run_analysis(
        similarity_threshold=85.0, 
        method="textdistance"
    )
    
    print("\n=== Analysis Complete ===")
    print(f"Jaccard report: {report_jaccard}")
    print(f"TextDistance report: {report_textdist}")
    print("\nCheck the JSON files for detailed results!")


def quick_demo():
    """Quick demonstration with a few sample files."""
    
    input_dir = Path("in")
    
    # Count files
    text_extensions = {'.md', '.txt', '.py', '.json', '.yaml', '.yml'}
    files = [f for f in input_dir.rglob('*') 
             if f.is_file() and f.suffix.lower() in text_extensions]
    
    print(f"Found {len(files)} text files in '{input_dir}'")
    
    if len(files) == 0:
        print("No text files found! Add some .md, .txt, or .py files to the 'in' directory.")
        return
    
    # Show first few files
    print("\nSample files:")
    for i, file_path in enumerate(files[:5]):
        rel_path = file_path.relative_to(input_dir)
        size_kb = file_path.stat().st_size / 1024
        print(f"  {i+1}. {rel_path} ({size_kb:.1f} KB)")
    
    if len(files) > 5:
        print(f"  ... and {len(files) - 5} more files")
    
    print(f"\nTo analyze these files, run:")
    print(f"python example_usage.py")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        quick_demo()
    else:
        analyze_files()
