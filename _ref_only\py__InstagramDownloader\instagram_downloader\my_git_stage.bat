:: =============================================================================
:: my_git_stage_individual.bat
:: Stages each modified file individually to prevent one error from
:: stopping the entire process.
:: =============================================================================

@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

:: Set working directory to script's location or provided path
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: =============================================================================
:: Git: Verify we are in a Git repository
:: =============================================================================
git rev-parse --is-inside-work-tree > nul 2>&1
IF ERRORLEVEL 1 (
    ECHO Error: Not a git repository.
    ECHO Please run this script from within a Git-managed directory.
    GOTO ExitScript
)

:: =============================================================================
:: Git: Get a list of all modified files and attempt to stage them one-by-one
:: =============================================================================
ECHO.
ECHO Scanning for modified files to stage...
ECHO.

SET "success_count=0"
SET "fail_count=0"

:: This FOR loop is the core of the solution.
:: 'git ls-files -m' gets all modified files.
:: The loop then processes each file ('%%F') one at a time.
FOR /F "delims=" %%F IN ('git ls-files -m') DO (
    ECHO -----------------------------------------------------------------------------
    ECHO Processing: "%%F"

    :: We run "git add" for each file individually.
    :: If a file causes an error (like "filename too long"), only this one
    :: command will fail, and the loop will continue to the next file.
    git add "%%F"

    :: Check the result of the "git add" command for the individual file.
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to stage: "%%F"
        ECHO         This file will be skipped. The script will continue.
        SET /A fail_count+=1
    ) ELSE (
        ECHO [SUCCESS] Staged: "%%F"
        SET /A success_count+=1
    )
)
ECHO -----------------------------------------------------------------------------

:: =============================================================================
:: Report: Show a summary of what was staged and what failed.
:: =============================================================================
ECHO.
ECHO Staging process complete.
ECHO.
ECHO Successfully staged: !success_count! file(s).
IF !fail_count! GTR 0 (
    ECHO Failed to stage:   !fail_count! file(s) (due to errors like 'filename too long').
)
ECHO.

:: Check if any files were staged at all.
git diff --staged --quiet --exit-code
IF NOT ERRORLEVEL 1 (
    ECHO No new files were staged.
)

:: =============================================================================
:: Exit
:: =============================================================================
:ExitScript
ECHO. & ECHO Window will close in 5 seconds... & PING 127.0.0.1 -n 5 > NUL
ENDLOCAL
PAUSE
EXIT /B