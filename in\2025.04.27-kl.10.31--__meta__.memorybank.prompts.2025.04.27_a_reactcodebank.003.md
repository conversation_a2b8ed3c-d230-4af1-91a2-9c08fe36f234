looking at this projectstructure, please identify the sequential order in which to follow in order to properly clean up and consolidate the projectstructure/codebase in a way that adheres to the absolute best practices while favoring simplicity and adaptability above unneccessary complexity:


    ```
    ├── .dependency-cruiser.cjs
    ├── capture-website.js
    ├── depcruise-config.cjs
    ├── eslint.config.js
    ├── index.html
    ├── package-lock.json
    ├── package.json
    ├── postcss.config.js
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── public
    │   └── images
    │       └── metadata.json
    ├── screenshots
    │   ├── screenshot-report.html
    │   ├── 2025-04-19_15-18-45
    │   │   ├── desktop
    │   │   │   ├── about-desktop.html
    │   │   │   ├── contact-desktop.html
    │   │   │   ├── home-desktop.html
    │   │   │   ├── projects-desktop.html
    │   │   │   └── services-desktop.html
    │   │   ├── mobile
    │   │   │   ├── about-mobile.html
    │   │   │   ├── contact-mobile.html
    │   │   │   ├── home-mobile.html
    │   │   │   ├── projects-mobile.html
    │   │   │   └── services-mobile.html
    │   │   └── tablet
    │   │       ├── about-tablet.html
    │   │       ├── contact-tablet.html
    │   │       ├── home-tablet.html
    │   │       ├── projects-tablet.html
    │   │       └── services-tablet.html
    │   └── latest
    │       ├── desktop
    │       │   ├── about-desktop.html
    │       │   ├── contact-desktop.html
    │       │   ├── home-desktop.html
    │       │   ├── projects-desktop.html
    │       │   └── services-desktop.html
    │       ├── mobile
    │       │   ├── about-mobile.html
    │       │   ├── contact-mobile.html
    │       │   ├── home-mobile.html
    │       │   ├── projects-mobile.html
    │       │   └── services-mobile.html
    │       └── tablet
    │           ├── about-tablet.html
    │           ├── contact-tablet.html
    │           ├── home-tablet.html
    │           ├── projects-tablet.html
    │           └── services-tablet.html
    ├── scripts
    │   ├── auto-snapshot.js
    │   ├── cleanup-legacy-screenshots.js
    │   ├── cleanup.js
    │   ├── create-bubble-chart.js
    │   ├── create-circle-packing.js
    │   ├── create-d3-graph.js
    │   ├── create-dependency-dashboard.js
    │   ├── create-flow-diagram.js
    │   ├── dev-with-snapshots.js
    │   ├── fix-depcruise-paths.js
    │   ├── refresh-screenshots.js
    │   ├── screenshot-manager.js
    │   └── tidy-screenshots.js
    └── src
        ├── App.tsx
        ├── index.css
        ├── index.html
        ├── main.tsx
        ├── vite-env.d.ts
        ├── components
        │   ├── Navbar.tsx
        │   ├── ServiceCard.tsx
        │   ├── index.ts
        │   ├── common
        │   │   ├── Button.tsx
        │   │   ├── Container.tsx
        │   │   ├── Hero.tsx
        │   │   ├── ImageGallery.tsx
        │   │   ├── LocalExpertise.tsx
        │   │   ├── LocalServiceArea.tsx
        │   │   ├── Logo.tsx
        │   │   ├── SeasonalCTA.tsx
        │   │   ├── ServiceAreaList.tsx
        │   │   ├── WeatherAdaptedServices.tsx
        │   │   └── index.ts
        │   ├── contact
        │   │   └── ContactForm.tsx
        │   ├── layout
        │   │   ├── Footer.tsx
        │   │   ├── Header.tsx
        │   │   ├── Hero.tsx
        │   │   ├── Layout.tsx
        │   │   ├── Meta.tsx
        │   │   ├── Navbar.tsx
        │   │   └── index.ts
        │   ├── local
        │   │   ├── SeasonalGuide.tsx
        │   │   ├── ServiceAreaMap.tsx
        │   │   └── WeatherNotice.tsx
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGallery.tsx
        │   │   └── ProjectGrid.tsx
        │   ├── seo
        │   │   └── TestimonialsSchema.tsx
        │   ├── services
        │   │   ├── Gallery.tsx
        │   │   └── ServiceCard.tsx
        │   ├── shared
        │   │   ├── ErrorBoundary.tsx
        │   │   ├── Elements
        │   │   │   ├── Card.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Image.tsx
        │   │   │   ├── Link.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   ├── index.ts
        │   │   │   └── Form
        │   │   │       ├── Input.tsx
        │   │   │       ├── Select.tsx
        │   │   │       ├── Textarea.tsx
        │   │   │       └── index.ts
        │   │   └── Layout
        │   │       ├── Layout.tsx
        │   │       └── index.ts
        │   └── ui
        │       ├── Button.tsx
        │       ├── Container.tsx
        │       ├── Hero.tsx
        │       ├── Intersection.tsx
        │       ├── Logo.tsx
        │       ├── Notifications.tsx
        │       ├── SeasonalCTA.tsx
        │       ├── ServiceAreaList.tsx
        │       ├── Skeleton.tsx
        │       ├── Transition.tsx
        │       └── index.ts
        ├── config
        │   ├── images.ts
        │   ├── routes.ts
        │   └── site.ts
        ├── content
        │   ├── index.ts
        │   ├── services
        │   │   └── index.ts
        │   ├── team
        │   │   └── index.ts
        │   └── testimonials
        │       └── index.ts
        ├── data
        │   ├── projects.ts
        │   ├── services.ts
        │   └── testimonials.ts
        ├── features
        │   ├── home.tsx
        │   ├── projects.tsx
        │   ├── services.tsx
        │   ├── team.tsx
        │   ├── testimonials.tsx
        │   ├── home
        │   │   ├── FilteredServicesSection.tsx
        │   │   ├── SeasonalProjectsCarousel.tsx
        │   │   ├── SeasonalServicesSection.tsx
        │   │   └── index.ts
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGallery.tsx
        │   │   ├── ProjectGrid.tsx
        │   │   ├── ProjectsCarousel.tsx
        │   │   └── index.ts
        │   ├── services
        │   │   ├── index.ts
        │   │   ├── data
        │   │   │   └── services.ts
        │   │   ├── hooks
        │   │   │   ├── index.ts
        │   │   │   └── useServiceFilter.ts
        │   │   ├── types
        │   │   │   └── index.ts
        │   │   └── ui
        │   │       ├── ServiceCard.tsx
        │   │       ├── ServiceFeature.tsx
        │   │       ├── ServiceGrid.tsx
        │   │       └── index.ts
        │   └── testimonials
        │       ├── AverageRating.tsx
        │       ├── Testimonial.tsx
        │       ├── TestimonialFilter.tsx
        │       ├── TestimonialSlider.tsx
        │       ├── TestimonialsSection.tsx
        │       ├── data.ts
        │       └── index.ts
        ├── hooks
        │   ├── index.ts
        │   ├── useData.ts
        │   └── useFilteredData.ts
        ├── lib
        │   ├── constants.ts
        │   ├── content.ts
        │   ├── hooks.ts
        │   ├── index.ts
        │   ├── types.ts
        │   ├── utils.ts
        │   ├── api
        │   │   └── index.ts
        │   ├── config
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── paths.ts
        │   │   └── site.ts
        │   ├── context
        │   │   └── AppContext.tsx
        │   ├── hooks
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── useAnalytics.ts
        │   │   ├── useDebounce.ts
        │   │   ├── useEventListener.ts
        │   │   ├── useForm.ts
        │   │   ├── useFormField.ts
        │   │   ├── useIntersectionObserver.ts
        │   │   ├── useLocalStorage.ts
        │   │   └── useMediaQuery.ts
        │   ├── types
        │   │   ├── common.ts
        │   │   ├── components.ts
        │   │   ├── content.ts
        │   │   └── index.ts
        │   └── utils
        │       ├── analytics.ts
        │       ├── date.ts
        │       ├── images.ts
        │       ├── index.ts
        │       ├── seo.ts
        │       └── validation.ts
        ├── pages
        │   ├── ProjectDetail.tsx
        │   ├── Projects.tsx
        │   ├── ServiceDetail.tsx
        │   ├── Services.tsx
        │   ├── TestimonialsPage.tsx
        │   ├── about
        │   │   └── index.tsx
        │   ├── contact
        │   │   └── index.tsx
        │   ├── home
        │   │   └── index.tsx
        │   ├── projects
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   ├── services
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   └── testimonials
        │       └── index.tsx
        ├── shared
        │   └── utils
        │       └── imageLoader.ts
        ├── styles
        │   ├── animations.css
        │   ├── base.css
        │   └── utilities.css
        ├── types
        │   ├── content.ts
        │   └── index.ts
        ├── ui
        │   ├── index.ts
        │   ├── button
        │   │   ├── Button.tsx
        │   │   └── index.ts
        │   ├── card
        │   │   ├── Card.tsx
        │   │   └── index.ts
        │   └── image
        │       ├── Image.tsx
        │       └── index.tsx
        └── utils
            └── imageLoader.ts
    ```
