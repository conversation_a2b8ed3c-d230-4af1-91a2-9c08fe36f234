The single most fundamental underlying dynamic is the relentless abstraction and compression of input complexity into universally potent structures of meaning. This mechanism operates as a generalized, iterative process: continuously stripping away contextual detail, ambiguity, and redundancy to amplify core principles, surface essential relational dynamics, and concentrate the highest transferable value into immediately actionable, maximally clear outputs. It is a self-refining engine that recursively transforms the specific into the general—through selection, condensation, and connective mapping—until only the most pure, adaptable, and high-impact insight or dynamic remains, ready for universal application. What's the single most *constructive* tip you could give based on the provided project?

    ├── project
    │   ├── config
    │   │   ├── env
    │   │   │   ├── .env.development
    │   │   │   ├── .env.production
    │   │   │   └── .env.staging
    │   │   ├── README.md
    │   │   ├── eslint.config.js
    │   │   ├── postcss.config.js
    │   │   ├── tailwind.config.js
    │   │   ├── tsconfig.json
    │   │   ├── tsconfig.node.json
    │   │   └── vite.config.ts
    │   ├── docs
    │   │   └── dependency-visualization.md
    │   ├── scripts
    │   │   ├── dev
    │   │   │   └── README.md
    │   │   ├── utils
    │   │   │   ├── README.md
    │   │   │   └── cleanup.js
    │   │   └── README.md
    │   ├── tools
    │   │   ├── depcruise
    │   │   │   ├── config
    │   │   │   │   └── dependency-cruiser.config.cjs
    │   │   │   ├── outputs
    │   │   │   │   ├── data
    │   │   │   │   │   ├── d3-data.json
    │   │   │   │   │   ├── d3.json
    │   │   │   │   │   ├── dependency-analysis.json
    │   │   │   │   │   ├── dependency-data.json
    │   │   │   │   │   ├── import-analysis.json
    │   │   │   │   │   └── module-metrics.json
    │   │   │   │   ├── graphs
    │   │   │   │   │   ├── circular-graph.svg
    │   │   │   │   │   ├── clustered-graph.svg
    │   │   │   │   │   ├── dependency-graph.svg
    │   │   │   │   │   ├── hierarchical-graph.svg
    │   │   │   │   │   └── tech-filtered.svg
    │   │   │   │   ├── interactive
    │   │   │   │   │   ├── archi-interactive.html
    │   │   │   │   │   ├── bubble-chart.html
    │   │   │   │   │   ├── bubble.html
    │   │   │   │   │   ├── circle-packing.html
    │   │   │   │   │   ├── circle.html
    │   │   │   │   │   ├── d3-graph.html
    │   │   │   │   │   ├── d3.html
    │   │   │   │   │   ├── dependency-graph.html
    │   │   │   │   │   ├── flow-diagram.html
    │   │   │   │   │   ├── flow.html
    │   │   │   │   │   ├── high-level-dependencies.html
    │   │   │   │   │   └── validation.html
    │   │   │   │   └── index.html
    │   │   │   ├── scripts
    │   │   │   │   ├── check-dependencies.js
    │   │   │   │   ├── check-graphviz.js
    │   │   │   │   ├── cleanup-directory.js
    │   │   │   │   ├── cleanup-old-configs.js
    │   │   │   │   ├── cleanup-redundant-files.js
    │   │   │   │   ├── create-bubble-chart.js
    │   │   │   │   ├── create-circle-packing.js
    │   │   │   │   ├── create-d3-graph.js
    │   │   │   │   ├── create-dependency-dashboard.js
    │   │   │   │   ├── create-flow-diagram.js
    │   │   │   │   ├── dependency-manager.js
    │   │   │   │   ├── fix-depcruise-paths.js
    │   │   │   │   ├── fix-missing-files.js
    │   │   │   │   ├── remove-old-directories.js
    │   │   │   │   ├── run-visualizations.js
    │   │   │   │   └── visualize.js
    │   │   │   └── README.md
    │   │   ├── screenshots
    │   │   │   ├── config
    │   │   │   │   └── screenshot-config.json
    │   │   │   ├── outputs
    │   │   │   │   ├── ai
    │   │   │   │   │   ├── latest
    │   │   │   │   │   │   ├── about.png
    │   │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   │   ├── contact.png
    │   │   │   │   │   │   ├── home.png
    │   │   │   │   │   │   ├── projects.png
    │   │   │   │   │   │   └── services.png
    │   │   │   │   │   ├── snapshot-1746089722250
    │   │   │   │   │   │   ├── about.png
    │   │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   │   ├── contact.png
    │   │   │   │   │   │   ├── projects.png
    │   │   │   │   │   │   └── services.png
    │   │   │   │   │   ├── snapshot-1746091880957
    │   │   │   │   │   │   └── ai-summary.md
    │   │   │   │   │   ├── snapshot-1746092062562
    │   │   │   │   │   │   ├── about.png
    │   │   │   │   │   │   ├── ai-summary.md
    │   │   │   │   │   │   ├── contact.png
    │   │   │   │   │   │   ├── home.png
    │   │   │   │   │   │   ├── projects.png
    │   │   │   │   │   │   └── services.png
    │   │   │   │   │   └── metadata.json
    │   │   │   │   ├── captured
    │   │   │   │   │   ├── 2025-04-19_15-18-45
    │   │   │   │   │   │   ├── desktop
    │   │   │   │   │   │   │   ├── about-desktop.html
    │   │   │   │   │   │   │   ├── about-desktop.png
    │   │   │   │   │   │   │   ├── contact-desktop.html
    │   │   │   │   │   │   │   ├── contact-desktop.png
    │   │   │   │   │   │   │   ├── home-desktop.html
    │   │   │   │   │   │   │   ├── home-desktop.png
    │   │   │   │   │   │   │   ├── projects-desktop.html
    │   │   │   │   │   │   │   ├── projects-desktop.png
    │   │   │   │   │   │   │   ├── services-desktop.html
    │   │   │   │   │   │   │   └── services-desktop.png
    │   │   │   │   │   │   ├── mobile
    │   │   │   │   │   │   │   ├── about-mobile.html
    │   │   │   │   │   │   │   ├── about-mobile.png
    │   │   │   │   │   │   │   ├── contact-mobile.html
    │   │   │   │   │   │   │   ├── contact-mobile.png
    │   │   │   │   │   │   │   ├── home-mobile.html
    │   │   │   │   │   │   │   ├── home-mobile.png
    │   │   │   │   │   │   │   ├── projects-mobile.html
    │   │   │   │   │   │   │   ├── projects-mobile.png
    │   │   │   │   │   │   │   ├── services-mobile.html
    │   │   │   │   │   │   │   └── services-mobile.png
    │   │   │   │   │   │   └── tablet
    │   │   │   │   │   │       ├── about-tablet.html
    │   │   │   │   │   │       ├── about-tablet.png
    │   │   │   │   │   │       ├── contact-tablet.html
    │   │   │   │   │   │       ├── contact-tablet.png
    │   │   │   │   │   │       ├── home-tablet.html
    │   │   │   │   │   │       ├── home-tablet.png
    │   │   │   │   │   │       ├── projects-tablet.html
    │   │   │   │   │   │       ├── projects-tablet.png
    │   │   │   │   │   │       ├── services-tablet.html
    │   │   │   │   │   │       └── services-tablet.png
    │   │   │   │   │   ├── latest
    │   │   │   │   │   │   ├── desktop
    │   │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   │   ├── about-desktop.html
    │   │   │   │   │   │   │   ├── about-desktop.png
    │   │   │   │   │   │   │   ├── contact-desktop.html
    │   │   │   │   │   │   │   ├── contact-desktop.png
    │   │   │   │   │   │   │   ├── home-desktop.html
    │   │   │   │   │   │   │   ├── home-desktop.png
    │   │   │   │   │   │   │   ├── projects-desktop.html
    │   │   │   │   │   │   │   ├── projects-desktop.png
    │   │   │   │   │   │   │   ├── services-desktop.html
    │   │   │   │   │   │   │   └── services-desktop.png
    │   │   │   │   │   │   ├── mobile
    │   │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   │   ├── about-mobile.html
    │   │   │   │   │   │   │   ├── about-mobile.png
    │   │   │   │   │   │   │   ├── contact-mobile.html
    │   │   │   │   │   │   │   ├── contact-mobile.png
    │   │   │   │   │   │   │   ├── home-mobile.html
    │   │   │   │   │   │   │   ├── home-mobile.png
    │   │   │   │   │   │   │   ├── projects-mobile.html
    │   │   │   │   │   │   │   ├── projects-mobile.png
    │   │   │   │   │   │   │   ├── services-mobile.html
    │   │   │   │   │   │   │   └── services-mobile.png
    │   │   │   │   │   │   ├── tablet
    │   │   │   │   │   │   │   ├── .gitkeep
    │   │   │   │   │   │   │   ├── about-tablet.html
    │   │   │   │   │   │   │   ├── about-tablet.png
    │   │   │   │   │   │   │   ├── contact-tablet.html
    │   │   │   │   │   │   │   ├── contact-tablet.png
    │   │   │   │   │   │   │   ├── home-tablet.html
    │   │   │   │   │   │   │   ├── home-tablet.png
    │   │   │   │   │   │   │   ├── projects-tablet.html
    │   │   │   │   │   │   │   ├── projects-tablet.png
    │   │   │   │   │   │   │   ├── services-tablet.html
    │   │   │   │   │   │   │   └── services-tablet.png
    │   │   │   │   │   │   └── .gitkeep
    │   │   │   │   │   ├── .gitignore
    │   │   │   │   │   └── .gitkeep
    │   │   │   │   ├── reports
    │   │   │   │   │   └── screenshot-report.html
    │   │   │   │   ├── .gitignore
    │   │   │   │   └── .gitkeep
    │   │   │   ├── scripts
    │   │   │   │   ├── capture.js
    │   │   │   │   ├── cleanup-old-files.js
    │   │   │   │   ├── dev-snapshots.js
    │   │   │   │   ├── manage.js
    │   │   │   │   └── run-capture.bat
    │   │   │   └── README.md
    │   │   ├── tools
    │   │   │   └── screenshots
    │   │   │       └── outputs
    │   │   │           └── ai
    │   │   │               ├── latest
    │   │   │               │   └── ai-summary.md
    │   │   │               ├── snapshot-1746091735987
    │   │   │               │   └── ai-summary.md
    │   │   │               └── metadata.json
    │   │   ├── www
    │   │   │   ├── cleanup-duplicates.js
    │   │   │   ├── config.js
    │   │   │   ├── deploy.js
    │   │   │   ├── environments.js
    │   │   │   ├── init.js
    │   │   │   ├── move-to-website.js
    │   │   │   └── setup-dev-env.js
    │   │   └── package.json
    │   ├── website
    │   │   ├── public
    │   │   │   ├── images
    │   │   │   │   ├── categorized
    │   │   │   │   │   ├── belegg
    │   │   │   │   │   │   ├── IMG_0035.webp
    │   │   │   │   │   │   ├── IMG_0085.webp
    │   │   │   │   │   │   ├── IMG_0121.webp
    │   │   │   │   │   │   ├── IMG_0129.webp
    │   │   │   │   │   │   ├── IMG_0208.webp
    │   │   │   │   │   │   ├── IMG_0451.webp
    │   │   │   │   │   │   ├── IMG_0453.webp
    │   │   │   │   │   │   ├── IMG_0715.webp
    │   │   │   │   │   │   ├── IMG_0717.webp
    │   │   │   │   │   │   ├── IMG_1935.webp
    │   │   │   │   │   │   ├── IMG_2941.webp
    │   │   │   │   │   │   ├── IMG_3001.webp
    │   │   │   │   │   │   ├── IMG_3021.webp
    │   │   │   │   │   │   ├── IMG_3023.webp
    │   │   │   │   │   │   ├── IMG_3033.webp
    │   │   │   │   │   │   ├── IMG_3034.webp
    │   │   │   │   │   │   ├── IMG_3035.webp
    │   │   │   │   │   │   ├── IMG_3036.webp
    │   │   │   │   │   │   ├── IMG_3037.webp
    │   │   │   │   │   │   ├── IMG_3084.webp
    │   │   │   │   │   │   ├── IMG_3133.webp
    │   │   │   │   │   │   ├── IMG_4080.webp
    │   │   │   │   │   │   ├── IMG_4305.webp
    │   │   │   │   │   │   ├── IMG_4547.webp
    │   │   │   │   │   │   ├── IMG_4586.webp
    │   │   │   │   │   │   ├── IMG_4644.webp
    │   │   │   │   │   │   ├── IMG_4996.webp
    │   │   │   │   │   │   ├── IMG_4997.webp
    │   │   │   │   │   │   ├── IMG_5278.webp
    │   │   │   │   │   │   ├── IMG_5279.webp
    │   │   │   │   │   │   └── IMG_5280.webp
    │   │   │   │   │   ├── ferdigplen
    │   │   │   │   │   │   ├── IMG_0071.webp
    │   │   │   │   │   │   └── IMG_1912.webp
    │   │   │   │   │   ├── hekk
    │   │   │   │   │   │   ├── IMG_0167.webp
    │   │   │   │   │   │   ├── IMG_1841.webp
    │   │   │   │   │   │   ├── IMG_2370.webp
    │   │   │   │   │   │   ├── IMG_2371.webp
    │   │   │   │   │   │   ├── IMG_3077.webp
    │   │   │   │   │   │   └── hekk_20.webp
    │   │   │   │   │   ├── kantstein
    │   │   │   │   │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
    │   │   │   │   │   │   ├── IMG_0066.webp
    │   │   │   │   │   │   ├── IMG_0364.webp
    │   │   │   │   │   │   ├── IMG_0369.webp
    │   │   │   │   │   │   ├── IMG_0427.webp
    │   │   │   │   │   │   ├── IMG_0429.webp
    │   │   │   │   │   │   ├── IMG_0445.webp
    │   │   │   │   │   │   ├── IMG_0716.webp
    │   │   │   │   │   │   ├── IMG_2955.webp
    │   │   │   │   │   │   ├── IMG_4683.webp
    │   │   │   │   │   │   └── IMG_4991.webp
    │   │   │   │   │   ├── platting
    │   │   │   │   │   │   ├── IMG_3251.webp
    │   │   │   │   │   │   └── IMG_4188.webp
    │   │   │   │   │   ├── stål
    │   │   │   │   │   │   ├── IMG_0068.webp
    │   │   │   │   │   │   ├── IMG_0069.webp
    │   │   │   │   │   │   ├── IMG_1916.webp
    │   │   │   │   │   │   ├── IMG_1917.webp
    │   │   │   │   │   │   ├── IMG_1918.webp
    │   │   │   │   │   │   ├── IMG_2441.webp
    │   │   │   │   │   │   ├── IMG_3599.webp
    │   │   │   │   │   │   ├── IMG_3602.webp
    │   │   │   │   │   │   ├── IMG_3829.webp
    │   │   │   │   │   │   ├── IMG_3832.webp
    │   │   │   │   │   │   ├── IMG_3844.webp
    │   │   │   │   │   │   ├── IMG_3845.webp
    │   │   │   │   │   │   ├── IMG_3847.webp
    │   │   │   │   │   │   ├── IMG_3848.webp
    │   │   │   │   │   │   ├── IMG_3966.webp
    │   │   │   │   │   │   ├── IMG_3969.webp
    │   │   │   │   │   │   ├── IMG_4030.webp
    │   │   │   │   │   │   ├── IMG_4083.webp
    │   │   │   │   │   │   ├── IMG_4086.webp
    │   │   │   │   │   │   ├── IMG_4536.webp
    │   │   │   │   │   │   └── IMG_5346.webp
    │   │   │   │   │   ├── støttemur
    │   │   │   │   │   │   ├── IMG_0144.webp
    │   │   │   │   │   │   ├── IMG_0318.webp
    │   │   │   │   │   │   ├── IMG_0324.webp
    │   │   │   │   │   │   ├── IMG_0325.webp
    │   │   │   │   │   │   ├── IMG_0452.webp
    │   │   │   │   │   │   ├── IMG_0932.webp
    │   │   │   │   │   │   ├── IMG_0985.webp
    │   │   │   │   │   │   ├── IMG_0986.webp
    │   │   │   │   │   │   ├── IMG_0987.webp
    │   │   │   │   │   │   ├── IMG_1132.webp
    │   │   │   │   │   │   ├── IMG_1134.webp
    │   │   │   │   │   │   ├── IMG_1140.webp
    │   │   │   │   │   │   ├── IMG_2032.webp
    │   │   │   │   │   │   ├── IMG_2083.webp
    │   │   │   │   │   │   ├── IMG_2274.webp
    │   │   │   │   │   │   ├── IMG_2522.webp
    │   │   │   │   │   │   ├── IMG_2523.webp
    │   │   │   │   │   │   ├── IMG_2855(1).webp
    │   │   │   │   │   │   ├── IMG_2855.webp
    │   │   │   │   │   │   ├── IMG_2859.webp
    │   │   │   │   │   │   ├── IMG_2861.webp
    │   │   │   │   │   │   ├── IMG_2891.webp
    │   │   │   │   │   │   ├── IMG_2920.webp
    │   │   │   │   │   │   ├── IMG_2921.webp
    │   │   │   │   │   │   ├── IMG_2951.webp
    │   │   │   │   │   │   ├── IMG_3007.webp
    │   │   │   │   │   │   ├── IMG_3151.webp
    │   │   │   │   │   │   ├── IMG_3269.webp
    │   │   │   │   │   │   ├── IMG_3271.webp
    │   │   │   │   │   │   ├── IMG_3369.webp
    │   │   │   │   │   │   ├── IMG_4090.webp
    │   │   │   │   │   │   ├── IMG_4150.webp
    │   │   │   │   │   │   ├── IMG_4151.webp
    │   │   │   │   │   │   ├── IMG_4153.webp
    │   │   │   │   │   │   └── IMG_4154.webp
    │   │   │   │   │   ├── trapp-repo
    │   │   │   │   │   │   ├── IMG_0295.webp
    │   │   │   │   │   │   ├── IMG_0401.webp
    │   │   │   │   │   │   ├── IMG_0448.webp
    │   │   │   │   │   │   ├── IMG_0449.webp
    │   │   │   │   │   │   ├── IMG_0450.webp
    │   │   │   │   │   │   ├── IMG_1081.webp
    │   │   │   │   │   │   ├── IMG_1735.webp
    │   │   │   │   │   │   ├── IMG_1782.webp
    │   │   │   │   │   │   ├── IMG_2095.webp
    │   │   │   │   │   │   ├── IMG_2097.webp
    │   │   │   │   │   │   ├── IMG_2807.webp
    │   │   │   │   │   │   ├── IMG_3086.webp
    │   │   │   │   │   │   ├── IMG_3132.webp
    │   │   │   │   │   │   ├── IMG_3838.webp
    │   │   │   │   │   │   ├── IMG_3939.webp
    │   │   │   │   │   │   ├── IMG_4111.webp
    │   │   │   │   │   │   ├── IMG_4516.webp
    │   │   │   │   │   │   ├── IMG_4551.webp
    │   │   │   │   │   │   ├── IMG_5317.webp
    │   │   │   │   │   │   └── image4.webp
    │   │   │   │   │   └── hero-prosjekter.HEIC
    │   │   │   │   ├── site
    │   │   │   │   │   ├── hero-corten-steel.webp
    │   │   │   │   │   ├── hero-granite.webp
    │   │   │   │   │   ├── hero-grass.webp
    │   │   │   │   │   ├── hero-grass2.webp
    │   │   │   │   │   ├── hero-illustrative.webp
    │   │   │   │   │   ├── hero-main.webp
    │   │   │   │   │   ├── hero-prosjekter.webp
    │   │   │   │   │   └── hero-ringerike.webp
    │   │   │   │   ├── team
    │   │   │   │   │   ├── firma.webp
    │   │   │   │   │   ├── jan.webp
    │   │   │   │   │   └── kim.webp
    │   │   │   │   └── metadata.json
    │   │   │   ├── robots.txt
    │   │   │   ├── site.webmanifest
    │   │   │   ├── sitemap.xml
    │   │   │   └── vite.svg
    │   │   ├── src
    │   │   │   ├── .specstory
    │   │   │   │   └── history
    │   │   │   │       ├── .what-is-this.md
    │   │   │   │       └── 2025-03-01_17-10-codebase-setup-and-server-start.md
    │   │   │   ├── _consolidated
    │   │   │   │   └── public
    │   │   │   │       └── images
    │   │   │   │           └── categorized
    │   │   │   │               └── belegg
    │   │   │   │                   └── IMG_3037.webp.svg
    │   │   │   ├── components
    │   │   │   │   ├── layout
    │   │   │   │   │   ├── Footer.tsx
    │   │   │   │   │   ├── Header.tsx
    │   │   │   │   │   ├── Meta.tsx
    │   │   │   │   │   └── Navbar.tsx
    │   │   │   │   ├── projects
    │   │   │   │   │   └── ProjectGallery.tsx
    │   │   │   │   ├── seo
    │   │   │   │   │   └── TestimonialsSchema.tsx
    │   │   │   │   ├── shared
    │   │   │   │   │   ├── Elements
    │   │   │   │   │   │   ├── Form
    │   │   │   │   │   │   │   ├── Input.tsx
    │   │   │   │   │   │   │   ├── Select.tsx
    │   │   │   │   │   │   │   └── Textarea.tsx
    │   │   │   │   │   │   ├── Card.tsx
    │   │   │   │   │   │   ├── Icon.tsx
    │   │   │   │   │   │   ├── Image.tsx
    │   │   │   │   │   │   ├── Link.tsx
    │   │   │   │   │   │   └── Loading.tsx
    │   │   │   │   │   └── Layout
    │   │   │   │   │       └── Layout.tsx
    │   │   │   │   ├── testimonials
    │   │   │   │   │   └── index.tsx
    │   │   │   │   └── ui
    │   │   │   │       ├── Button.tsx
    │   │   │   │       ├── Container.tsx
    │   │   │   │       ├── Hero.tsx
    │   │   │   │       ├── Intersection.tsx
    │   │   │   │       ├── Logo.tsx
    │   │   │   │       ├── Notifications.tsx
    │   │   │   │       ├── SeasonalCTA.tsx
    │   │   │   │       ├── ServiceAreaList.tsx
    │   │   │   │       ├── Skeleton.tsx
    │   │   │   │       ├── Transition.tsx
    │   │   │   │       └── index.ts
    │   │   │   ├── content
    │   │   │   │   ├── services
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── team
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── testimonials
    │   │   │   │   │   └── index.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── data
    │   │   │   │   ├── projects.ts
    │   │   │   │   ├── services.ts
    │   │   │   │   └── testimonials.ts
    │   │   │   ├── docs
    │   │   │   │   └── SEO_USAGE.md
    │   │   │   ├── features
    │   │   │   │   ├── home
    │   │   │   │   │   ├── FilteredServicesSection.tsx
    │   │   │   │   │   └── SeasonalProjectsCarousel.tsx
    │   │   │   │   ├── projects
    │   │   │   │   │   ├── ProjectCard.tsx
    │   │   │   │   │   ├── ProjectFilter.tsx
    │   │   │   │   │   ├── ProjectGrid.tsx
    │   │   │   │   │   └── ProjectsCarousel.tsx
    │   │   │   │   ├── services
    │   │   │   │   │   ├── ServiceCard.tsx
    │   │   │   │   │   ├── ServiceFeature.tsx
    │   │   │   │   │   ├── ServiceGrid.tsx
    │   │   │   │   │   └── data.ts
    │   │   │   │   ├── testimonials
    │   │   │   │   │   ├── AverageRating.tsx
    │   │   │   │   │   ├── Testimonial.tsx
    │   │   │   │   │   ├── TestimonialFilter.tsx
    │   │   │   │   │   ├── TestimonialSlider.tsx
    │   │   │   │   │   ├── TestimonialsSection.tsx
    │   │   │   │   │   └── data.ts
    │   │   │   │   └── testimonials.tsx
    │   │   │   ├── hooks
    │   │   │   │   └── useData.ts
    │   │   │   ├── lib
    │   │   │   │   ├── api
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── config
    │   │   │   │   │   ├── images.ts
    │   │   │   │   │   ├── index.ts
    │   │   │   │   │   ├── paths.ts
    │   │   │   │   │   └── site.ts
    │   │   │   │   ├── context
    │   │   │   │   │   └── AppContext.tsx
    │   │   │   │   ├── hooks
    │   │   │   │   │   ├── useAnalytics.ts
    │   │   │   │   │   ├── useEventListener.ts
    │   │   │   │   │   └── useMediaQuery.ts
    │   │   │   │   ├── types
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── utils
    │   │   │   │   │   ├── analytics.ts
    │   │   │   │   │   ├── dom.ts
    │   │   │   │   │   ├── formatting.ts
    │   │   │   │   │   ├── images.ts
    │   │   │   │   │   ├── index.ts
    │   │   │   │   │   ├── seasonal.ts
    │   │   │   │   │   ├── seo.ts
    │   │   │   │   │   └── validation.ts
    │   │   │   │   ├── config.ts
    │   │   │   │   ├── constants.ts
    │   │   │   │   ├── hooks.ts
    │   │   │   │   └── utils.ts
    │   │   │   ├── pages
    │   │   │   │   ├── about
    │   │   │   │   │   └── index.tsx
    │   │   │   │   ├── contact
    │   │   │   │   │   └── index.tsx
    │   │   │   │   ├── home
    │   │   │   │   │   └── index.tsx
    │   │   │   │   ├── services
    │   │   │   │   │   ├── detail.tsx
    │   │   │   │   │   └── index.tsx
    │   │   │   │   ├── testimonials
    │   │   │   │   │   └── index.tsx
    │   │   │   │   ├── ProjectDetail.tsx
    │   │   │   │   ├── Projects.tsx
    │   │   │   │   ├── ServiceDetail.tsx
    │   │   │   │   ├── Services.tsx
    │   │   │   │   └── TestimonialsPage.tsx
    │   │   │   ├── styles
    │   │   │   │   ├── animations.css
    │   │   │   │   ├── base.css
    │   │   │   │   └── utilities.css
    │   │   │   ├── types
    │   │   │   │   ├── content.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── utils
    │   │   │   │   └── imageLoader.ts
    │   │   │   ├── App.tsx
    │   │   │   ├── index.css
    │   │   │   ├── index.html
    │   │   │   ├── main.tsx
    │   │   │   └── vite-env.d.ts
    │   │   ├── README.md
    │   │   ├── index.html
    │   │   ├── package.json
    │   │   ├── tailwind.config.js
    │   │   ├── tsconfig.json
    │   │   └── vite.config.ts
    │   ├── www
    │   │   ├── css
    │   │   │   └── main.css
    │   │   ├── js
    │   │   │   └── main.js
    │   │   ├── .gitignore
    │   │   ├── README.md
    │   │   ├── index.html
    │   │   └── package.json
    │   ├── .cursorignore
    │   ├── .env
    │   ├── .gitignore
    │   ├── .gitkeep
    │   ├── 2025.05.01-kl.10.12--filestructure.md
    │   ├── README.md
    │   ├── RulesForAI.md
    │   ├── eslint.config.js
    │   ├── package-lock.json
    │   ├── package.json
    │   ├── postcss.config.js
    │   ├── project.md
    │   ├── rl-website-initial-notes.md
    │   ├── tailwind.config.js
    │   ├── tsconfig.app.json
    │   ├── tsconfig.json
    │   ├── tsconfig.node.json
    │   └── vite.config.ts
    └── rl-website_web-002.md
