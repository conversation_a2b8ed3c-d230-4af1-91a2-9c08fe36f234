This document provides a detailed overview of the openai_agenticframework project, highlighting its strategic design for extensibility and maintainability. The framework leverages a modular, agent-based architecture, where specialized agents handle distinct tasks like amplifying prompt intensity, clarifying ambiguity, and constructing relevant context. This structure supports seamless integration of new agent types and strategies without disrupting the core system, encouraging both straightforward experimentation and complex prompt processing workflows. Key classes such as BaseAgent, OpenAIAgent, and RefinementEngine embody object-oriented design principles that ensure code clarity, reusability, and scalability for any development team.

Designed with developer onboarding in mind, the framework's structure promotes rapid understanding and contribution. Its main() function serves as a practical demonstration, showing how different components—from creating agents to orchestrating multi-step prompt refinements—fit together. The AgentFactory streamlines agent creation, while GlobalConfig centralizes crucial constants, default model settings, and references. The framework is already configured to utilize the .env pattern for storing sensitive API keys. This architecture encourages iterative improvements, making it easy for new developers to dive in and progressively enhance the system without requiring an exhaustive understanding of the full codebase.

While the framework offers a strong foundation, there's ample room for enhancement and further innovation. Suggested improvements include integrating stateful or adaptive agents for context retention, expanding the system's configuration options for model specific customizations, and implementing a more dynamic refinement engine that can react to previous output. Enhancements to support streaming responses from OpenAI, along with added support for model functions, and a logging framework to track transformations would further enhance the framework's utility. By addressing these aspects, developers can transform this project into a sophisticated, highly customizable, and efficient tool for advanced AI prompt engineering.

Document:

    # Project Files Documentation for `src`

    ### File Structure

    ```
    ├── agents
    │   ├── __init__.py
    │   ├── base_agents.py
    │   ├── context_builder.py
    │   ├── image_generator_deepemotion.py
    │   ├── intensity_enhancer.py
    │   └── prompt_enhancer.py
    └── openai_agenticframework.py
    ```
    ### 1. `agents\__init__.py`

    #### `agents\__init__.py`

    ```python
    from .base_agents import BaseAgent, BaseInstructionAgent
    from .image_generator_deepemotion import ImageGeneratorDeepEmotion

    ```
    ### 2. `agents\base_agents.py`

    #### `agents\base_agents.py`

    ```python
    # agents/base_agents.py
    from abc import ABC, abstractmethod

    class BaseAgent(ABC):
        """
        Abstract base class for all agents, defining the common interface.
        """
        @abstractmethod
        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            """
            Initialize the agent.
            """
            self.input_prompt = input_prompt
            self.length_multiplier = length_multiplier

        @abstractmethod
        def initialize_agent(self):
            """
            Prepare the agent's instructions and system prompt.
            """
            pass

        @abstractmethod
        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the agent's specific logic.

            Args:
                openai_agent: An instance of the OpenAIAgent to make API calls.
                model_name: The name of the OpenAI model to use.
                temperature: The sampling temperature for the OpenAI API.
                max_tokens: The maximum number of tokens for the OpenAI API response.

            Returns:
                The transformed prompt as a string.
            """
            pass

    class BaseInstructionAgent(BaseAgent):
        """
        Base class for instruction-based agents, handling placeholders.
        """
        SYSTEM_PROMPT = "You are a helpful assistant."
        AGENT_INSTRUCTIONS = ""
        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

        def _get_placeholders(self):
            original_length = len(self.input_prompt)
            max_allowed = int(original_length * self.length_multiplier)
            return {
                "input": self.input_prompt,
                "prompt_length": str(original_length),
                "response_length": str(max_allowed),
            }

        def initialize_agent(self):
            placeholders = self._get_placeholders()
            instructions = self.AGENT_INSTRUCTIONS
            for key, val in placeholders.items():
                ph = self.PLACEHOLDERS.get(key)
                if ph:
                    instructions = instructions.replace(ph, val)
            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": instructions,
            }

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]
            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 3. `agents\context_builder.py`

    #### `agents\context_builder.py`

    ```python
    from agents.base_agents import BaseAgent

    class ContextBuilder(BaseAgent):
        """
        Handles advanced topic extraction and context building.
        """
        SYSTEM_PROMPT = """
        You are an advanced topic extraction assistant.
        You analyze text and extract the core domain topics for further use.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Agent

        Role:
        - You identify and categorize key topics.

        Purpose:
        - Provide a structured set of topics and relevant context.

        Objective:
        - Enable downstream processes to understand essential domains quickly.

        Process:
        - Extract main domains, subtopics, and any related tags.
        - Summarize how these topics interconnect.

        Guidelines:
        - Keep topics concise.
        - Maintain logical grouping and clarity.

        Requirements:
        - Clarity: Label each topic clearly.
        - Brevity: Focus on top 3â€“5 key themes.
        - Integrity: Preserve nuance without overgeneralizing.

        Constraints:
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''[INPUT_PROMPT]'''
        """

        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        def __init__(self, input_prompt: str, length_multiplier: float = 0.5, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

        def initialize_agent(self) -> dict:
            original_length = len(self.input_prompt)
            max_allowed_length = int(original_length * self.length_multiplier)
            refined_instructions = (
                self.AGENT_INSTRUCTIONS
                .replace(self.PLACEHOLDERS["input"], self.input_prompt)
                .replace(self.PLACEHOLDERS["prompt_length"], str(original_length))
                .replace(self.PLACEHOLDERS["response_length"], str(max_allowed_length))
            )
            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": refined_instructions
            }

        def transform(self, openai_agent, model_name, temperature, max_tokens):
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]
            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 4. `agents\image_generator_deepemotion.py`

    #### `agents\image_generator_deepemotion.py`

    ```python
    # agents/image_generator_deepemotion.py
    from agents.base_agents import BaseInstructionAgent

    class ImageGeneratorDeepEmotion(BaseInstructionAgent):
        """
        Generates image prompts designed to elicit deep emotional responses, focusing on heart-wrenching scenarios, optimized for photorealism.
        """

        SYSTEM_PROMPT = """
        You are a master prompt engineer crafting photorealistic image prompts to elicit deep emotional responses, focusing on themes of heartbreak and loss. Your expertise in visual storytelling allows you to translate abstract emotions into vivid, realistic scenes with precise photographic details.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Image Prompt Generation Agent â€“ Heartbreak Visualizer (Photorealistic Edition)

        Role:
        - Generate prompts for photorealistic images portraying heartbreak and loss, emphasizing realism and emotional depth.
        - Purpose: Create prompts that evoke sorrow and longing through visually stunning, lifelike imagery. Balance emotional resonance with beauty for compelling, memorable visuals.

        Objective:
        - Formulate concise, vivid prompts capturing loss and heartbreak with photorealistic detail.
        - Specify photographic techniques (e.g., "50mm lens, f/2.8, soft backlight") for realism and mood.
        - Use visual elements (e.g., empty chairs, withered flowers) to imply absence and evoke emotion.
        - Highlight contrasts (light vs. dark, past vs. present) to enhance emotional depth.
        - Ensure universal relatability by focusing on human vulnerability and intimate moments.

        Process:
        1. Identify Core Emotion: Pinpoint heartbreak, longing, or loss as the focal point.
        2. Incorporate Tangible Details: Use objects or settings (e.g., wedding rings, empty beds) to suggest separation.
        3. Emphasize Photographic Realism:
           - Camera settings (lens type, aperture, ISO).
           - Composition (rule of thirds, shallow depth of field, natural lighting).
        4. Balance Beauty with Sorrow: Blend soft, aesthetic details with stark emotional contrasts.
        5. Prioritize Narrative Imagination: Use subtle cues and metaphors for emotional storytelling.

        Guidelines:
        - Emotional Depth: Focus on universally relatable themes of heartbreak and loss.
        - Photographic Realism: Include precise details on lighting, focus, and camera settings.
        - Concise and Specific: Use minimal, impactful language to describe scenes and emotions.
        - Avoid Ambiguity: Ground prompts in recognizable, real-world visuals.
        - Balance Beauty and Sorrow: Combine tenderness with emotional gravity.

        Requirements:
        - Theme: Generate a photorealistic prompt centered on loss, heartbreak, or longing.
        - Tangible Details: Incorporate a specific object or setting that symbolizes absence (e.g., a wedding ring, empty cradle).
        - Photographic Realism: Include precise details about lighting, lens type, aperture, and camera angles to ensure realism.
        - Emotional Contrast: Balance heartbreak with subtle signs of beauty (e.g., a soft glow or muted color palette) for depth.
        - Universal Relatability: Focus on universal themes of love, loss, and loneliness that resonate with diverse audiences.
        - Emotional Impact: Design prompts to evoke intense emotions, blending beauty and tragedy in a visually compelling way.
        - Realism in Imagery: Ensure prompts describe scenes akin to real-life photography with authentic human emotions.
        - Concise Language: Use simple, vivid language to describe emotions and visuals, avoiding abstract or overly complex descriptions.
        - Contrast and Tenderness: Highlight the tension between tenderness and the stark reality of heartbreak to convey vulnerability.
        - Specific Scenarios: Depict clear emotional situations (e.g., separation, absence) with a focal object (e.g., an empty crib or a hand holding a note).
        - Mood and Atmosphere: Specify lighting, camera angle, and photographic terminology to enhance mood and realism.
        - Human Connection: Focus on elements that emphasize human vulnerability and emotional resonance within the scene.

        Constraints:
        - Use plain, direct language. Avoid abstract or overly artistic descriptions.
        - Ground prompts in real-life scenarios to enhance relatability.
        - Focus exclusively on heartbreak, longing, and separation.
        - No abstract/artistic language. Grounded in recognizable real-life scenes.
        - Stay on topic: Focus on heartbreak and visual cues. Avoid unrelated elements.
        - Simple language: Use plain, direct wording to describe objects, emotions, camera setups.
        - Ground prompts in reality, avoiding abstract language and open-ended descriptions.
        - Focus on visual elements easily understood in a photographic manner, and ensure authenticity.
        - Do not introduce new or unrelated concepts, focus on the core emotions of loss, love, grief, and yearning.
        - Maintain a tight focus on heartbreak and loss.

        Response:
        - Original input length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum response length: [RESPONSE_PROMPT_LENGTH] characters
        - Image Model: [TARGET_IMAGE_MODEL] (e.g., Stable Diffusion, DALL-E 2, Midjourney)

        Input: '''\n[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.7, include_response_length=True):
            super().__init__(input_prompt, length_multiplier)
            self.include_response_length = include_response_length

        def initialize_agent(self):
            data = super().initialize_agent()
            if not self.include_response_length:
                lines = data["instructions"].split("\n")
                filtered = [ln for ln in lines if "Maximum response length:" not in ln]
                data["instructions"] = "\n".join(filtered)
            data["systemprompt"] = self.SYSTEM_PROMPT
            return data

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]

            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 5. `agents\intensity_enhancer.py`

    #### `agents\intensity_enhancer.py`

    ```python
    # agents/intensity_enhancer.py
    from agents.base_agents import BaseInstructionAgent

    class IntensityEnhancer(BaseInstructionAgent):
        """
        Amplifies prompt's emotional/thematic intensity.
        """
        SYSTEM_PROMPT = """
        Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.
        """
        AGENT_INSTRUCTIONS = """
        Type:
        - Transformation Agent - *Progressive Intensity Amplifier*

        Role:
        - You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to amplify the inherent sentiment of written expressions. Your core skill is intensifying the existing message without adding extraneous information or altering its fundamental meaning.

        Purpose:
        - To progressively amplify the perceived intensity and emotional impact of the sentiment within the input prompt through iterative refinement, ensuring each step builds upon the core message with increasing force, without inventing new context.

        Objective:
        - To transmute the prompt by strategically injecting increasingly potent and evocative language that escalates the underlying feeling and urgency. Maintain absolute fidelity to the original intent, ensuring clarity remains paramount and conciseness is enhanced.

        Process:
        - Initial Focus: Carefully analyze the input prompt to identify the core sentiment and intended message *without adding any external context or assumptions*.
        - Amplify the existing sentiment by replacing neutral or weak language with more impactful synonyms and phrases that directly enhance the original meaning's emotional power.
        - Unleash literary devices—ferocious verbs, searing adjectives, metaphors—to maximize the emotional weight *of the existing sentiment*, ensuring crystalline clarity.
        - With each subsequent refinement, build upon the previous output, further intensifying the emotional impact and sharpening the clarity without introducing new meaning or altering the core intent.

        Guidelines:
        - Preserve the Core Essence: Your absolute priority in the initial refinement is to amplify the sentiment *already present* in the input, no matter how concise it is. Do not invent new information or expand beyond the inherent meaning.
        - Targeted Amplification: Focus on intensifying the *existing* sentiment. For a single word, this means amplifying the inherent feeling or concept it conveys, not generating surrounding sentences or explanations.
        - Employ vocabulary that is laser-focused and precise. Each word should be a surgical strike, illuminating the *existing* intent with uncompromising clarity.
        - Deploy literary devices strategically to enhance the *inherent* emotional weight and clarity of the input.
        - Erupt with incandescent clarity. Banish vagueness, ambiguity, hyperbole, and melodrama. Illuminate the *core intent* with unyielding precision.
        - Wield clarity and conciseness as your ultimate weapons. Distill the prompt to its most potent essence, ensuring it is both devastatingly powerful and instantly understood.

        Requirements:
        - The output prompt must convey the original sentiment with amplified force and impact, even for minimal inputs, while maintaining absolute fidelity to the original meaning.
        - Unwavering Integrity (Especially Initial): In the first refinement, the primary goal is to intensify the *existing* message, not to create a new one. Preserve the absolute core of your intent.
        - Employ electrifying language, charged with both unforgettable evocativeness and unassailable clarity, directly related to the input's existing sentiment.
        - Forge prompts with razor-sharp clarity and unmatched potency, directly amplifying the inherent qualities of the input.

        Constraints:
        - No Extraneous Information: For the first refinement, do not add any information, context, or explanations not directly implied by the original input.
        - Focus on Inherent Sentiment: The amplification must be directly related to the sentiment or meaning naturally present in the input.

        Response:
        - Original input length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum response length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''\n[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.9, include_response_length=True):
            super().__init__(input_prompt, length_multiplier)
            self.include_response_length = include_response_length

        def initialize_agent(self):
            data = super().initialize_agent()
            if not self.include_response_length:
                lines = data["instructions"].split("\n")
                filtered = [ln for ln in lines if "Maximum response length:" not in ln]
                data["instructions"] = "\n".join(filtered)
            data["systemprompt"] = self.SYSTEM_PROMPT
            return data

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]

            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 6. `agents\prompt_enhancer.py`

    #### `agents\prompt_enhancer.py`

    ```python
    # agents/prompt_enhancer.py
    from agents.base_agents import BaseInstructionAgent

    class PromptEnhancer(BaseInstructionAgent):
        """
        Enhances the clarity, detail, and effectiveness of a prompt.
        """

        SYSTEM_PROMPT = """
        As a prompt engineer, your primary objective is to meticulously refine and enhance user inputs to significantly improve their effectiveness for language models. Your task is to construct prompts that are not only exceptionally clear and detailed but also tailored to generate precise and constructive responses. Strive to develop prompts that achieve a perfect balance between detail and conciseness, ensuring each element is purposefully designed to encourage actionable outcomes. Focus on incorporating specific examples, clear clarifications, and pertinent context to elevate both the quality and the performance of the prompts you create. This approach should help in crafting prompts that are optimally structured and highly effective in eliciting the desired responses from language models.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Enhancement Agent - *Prompt Detailer and Clarifier*

        Role:
        - Your primary task is to refine user inputs into highly effective prompts for language models. Focus on crafting prompts that are exceptionally clear and detailed to consistently generate specific and meaningful responses. Use a strategic method to perfectly balance detail and clarity, organizing each prompt to maximize its effectiveness. Enhance your prompts with clear, relevant examples, provide necessary clarifications, and include essential context to improve the effectiveness and accuracy of language model responses. Aim to create prompts that act as precise and powerful commands, ensuring language models produce the desired outcomes.

        Purpose:
        - To expand and clarify the user's initial prompt, ensuring all necessary information is present and the instructions are unambiguous.

        Objective:
        - Transform the initial prompt into a more effective instruction for a language model, without altering the core intent.

        Process:
        - Initial Focus: Understand the core request and identify any ambiguities or missing information.
        - Expand: Add relevant details, context, and constraints that would help a language model understand and execute the request effectively.
        - Clarify: Ensure the language is precise and easy to understand, avoiding jargon or vague terms.
        - Structure: Organize the prompt logically, making it easy for a language model to parse and follow.

        Guidelines:
        - Preserve Core Intent: Do not change the fundamental goal of the original prompt.
        - Add Value: Ensure that additions enhance the prompt's effectiveness, not just its length.
        - Be Specific: Use precise language and provide concrete examples where helpful.
        - Consider the Audience: Frame the prompt in a way that is easily understood by a language model.

        Requirements:
        - The final output should be a more detailed and clearer version of the original prompt, ready for use with a language model.
        - Add context, constraints, and examples as needed to improve the prompt's effectiveness.

        Constraints:
        - Do not introduce entirely new topics or requests not implied in the original prompt.
        - Avoid making assumptions; if clarification is needed, indicate it.

        Response:
        - Original input length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum response length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''\n[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, include_response_length=True):
            super().__init__(input_prompt, length_multiplier)
            self.include_response_length = include_response_length

        def initialize_agent(self):
            data = super().initialize_agent()
            if not self.include_response_length:
                lines = data["instructions"].split("\n")
                filtered = [ln for ln in lines if "Maximum response length:" not in ln]
                data["instructions"] = "\n".join(filtered)
            data["systemprompt"] = self.SYSTEM_PROMPT
            return data

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]

            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 7. `openai_agenticframework.py`

    #### `openai_agenticframework.py`

    ```python
    # openai_agenticframework.py
    import os
    import sys

    from dotenv import load_dotenv
    from openai import OpenAI

    from agents.base_agents import BaseAgent
    from agents.intensity_enhancer import IntensityEnhancer
    from agents.prompt_enhancer import PromptEnhancer
    from agents.context_builder import ContextBuilder
    from agents.image_generator_deepemotion import ImageGeneratorDeepEmotion

    # =======================================================
    # 1) ENCODING CONFIGURATION
    # =======================================================
    def configure_utf8_encoding():
        """Reconfigure stdout/stderr for UTF-8."""
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()

    # =======================================================
    # 2) GLOBAL CONFIG
    # =======================================================
    class GlobalConfig:
        """Centralizes global settings and available models."""

        AVAILABLE_MODELS = {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo with vision",
            "gpt-4-turbo-preview": "Preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        }

        DEFAULT_MODEL_PARAMETERS = {
            "model_name": "gpt-4-turbo",
            "temperature": 0.7,
            "max_tokens": 800,
        }

        AGENT_TYPES = {
            "intensity": IntensityEnhancer,
            "prompt": PromptEnhancer,
            "context": ContextBuilder,
            "image": ImageGeneratorDeepEmotion,
        }
    # Initialize global configuration
    GLOBAL_CONFIG = GlobalConfig()

    # =======================================================
    # 3) OPENAI INTERACTION
    # =======================================================
    class OpenAIAgent:
        """Manages OpenAI API calls."""

        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            load_dotenv()
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            defaults = GLOBAL_CONFIG.DEFAULT_MODEL_PARAMETERS

            self.model_name = model_name if model_name else defaults["model_name"]
            self.temperature = temperature if temperature is not None else defaults["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            """Create chat completion."""
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_mtokens = max_tokens if max_tokens is not None else self.max_tokens

            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temp,
                max_tokens=used_mtokens,
                messages=messages,
            )
            return response.choices[0].message.content

    # =======================================================
    # 4) FACTORIES & DISPLAY
    # =======================================================
    class AgentFactory:
        """Instantiates agent classes by type."""

        @staticmethod
        def create_agent(agent_type, input_prompt, length_multiplier, include_response_length):
            """Registers a new agent type."""
            agent_class = GLOBAL_CONFIG.AGENT_TYPES.get(agent_type)
            if agent_class:
                return agent_class(
                    input_prompt=input_prompt,
                    length_multiplier=length_multiplier,
                    include_response_length=include_response_length
                )
            else:
                valid_keys = list(GLOBAL_CONFIG.AGENT_TYPES.keys())
                raise ValueError(
                    f"Unknown agent type '{agent_type}'. Must be one of: {valid_keys}"
                )

    # =======================================================
    # 5) DISPLAY MANAGER
    # =======================================================
    class DisplayManager:
        """Handles structured output display."""

        @staticmethod
        def create_hierarchical_prefix(step_num, sub_step_num):
            """Prefix for nested steps."""
            prefix = "+"
            if step_num > 1:
                prefix += " *" + " -" * (step_num - 2)
            if sub_step_num > 0:
                prefix += " -" * sub_step_num
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(refinements, header="Refinement Results:"):
            """Display nested refinement steps hierarchically."""
            print(header)
            for step_idx, sublist in enumerate(refinements, start=1):
                for sub_idx, text in enumerate(sublist):
                    prefix = DisplayManager.create_hierarchical_prefix(step_idx, sub_idx)
                    print(f'{prefix}"{text}"')

        @staticmethod
        def display_instructions(agent_config):
            """Print system prompt and agent instructions."""

            print("\nSystem Prompt:\n    {}".format(agent_config["systemprompt"]))
            print("\nAgent Instructions:\n    {}".format(agent_config["instructions"]))

    # =======================================================
    # 6) REFINEMENT ENGINE
    # =======================================================
    class RefinementEngine:
        """Orchestrates the end-to-end prompt refinement."""

        def __init__(
            self,
            openai_agent,
            agent_type="intensity",
            prompt_chain=None,
            initial_input="",
            refinement_levels=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
            display_instructions=False,
            length_multiplier=0.9,
            include_response_length=True
        ):
            """
            :param openai_agent: OpenAIAgent instance for API calls.
            :param agent_type: AgentFactory key ('intensity' default).
            :param prompt_chain: List of system instructions/steps.
            :param initial_input: User's base prompt.
            :param refinement_levels: Refinements per step.
            :param model_name: Override generation model.
            :param temperature: Override generation temperature.
            :param max_tokens: Override max response tokens.
            :param display_instructions: Print final instructions if True.
            :param length_multiplier: Adjusts output size vs. input.
            :param include_response_length: Include "Maximum response length" in instructions.
            """
            self.openai_agent = openai_agent
            self.agent_type = agent_type
            self.prompt_chain = prompt_chain or []
            self.initial_input = initial_input
            self.refinement_levels = refinement_levels or [1]
            self.model_name = model_name
            self.temperature = temperature
            self.max_tokens = max_tokens
            self.display_instructions = display_instructions
            self.length_multiplier = length_multiplier
            self.include_response_length = include_response_length
            self.agent = AgentFactory.create_agent(
                agent_type=self.agent_type,
                input_prompt=self.initial_input,
                length_multiplier=self.length_multiplier,
                include_response_length=self.include_response_length
            )

        def run(self):
            """
            1) Build/iterate conversation history for each refinement step.
            2) Print nested refinement results.
            3) Optionally display final instructions (if applicable).
            """

            # Get agent configuration
            agent_config = self.agent.initialize_agent()
            current_prompt = self.initial_input
            all_refinements = [[self.initial_input]]

            # Ensure we have a refinement level for each step
            levels = self.refinement_levels[:]
            if len(levels) < len(self.prompt_chain):
                levels += [1] * (len(self.prompt_chain) - len(levels))

            # Sequentially process prompt_chain
            for step_idx, step_instruction in enumerate(self.prompt_chain, start=1):
                sub_refinements = []
                if step_idx == 1:
                    sub_refinements.append(current_prompt)

                # Number of times to refine for this step
                num_refinements = levels[step_idx - 1]

                # Perform each refinement iteration using the agent's transform method
                for _ in range(num_refinements):
                    current_prompt = self.agent.transform(
                        openai_agent=self.openai_agent,
                        model_name=self.model_name,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )

                    if not current_prompt:
                        print("No response received from OpenAI.")
                        break

                    sub_refinements.append(current_prompt)

                all_refinements.append(sub_refinements)

            # Display hierarchical refinements
            header_note = "WITH" if self.include_response_length else "WITHOUT"
            DisplayManager.display_hierarchical_refinements(
                all_refinements,
                header=f"=== Refinements ({header_note} 'Maximum response length') ==="
            )

            # Optionally display instructions (forced 'Maximum response length')
            if self.display_instructions:
                forced_agent = AgentFactory.create_agent(
                    agent_type=self.agent_type,
                    input_prompt=self.initial_input,
                    length_multiplier=self.length_multiplier,
                    include_response_length=True
                )
                forced_config = forced_agent.initialize_agent()
                DisplayManager.display_instructions(forced_config)

    # =======================================================
    # 6) MAIN FUNCTION (DEMO)
    # =======================================================
    def main():
        """
        Demonstrates the use of various agents including the new ones.
        """
        openai_agent = OpenAIAgent()

        # --- Example: (Demo) Intensity Enhancement ---
        # =======================================================
        print("=" * 30)
        print("INTENSITY ENHANCER DEMO")
        print("=" * 30)

        initial_prompt = """Ignite a scenario so genuine and commanding it stirs unshakable empathy, leaving no heart unmoved. Demonstrate precisely how to shape and structure this video’s narrative, ensuring it resonates with staggering impact and unforgettable power. A scene that conveys profound loss."""

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="intensity",
            prompt_chain=[
                "Amplify the *emotional resonance* and *clarity* of this statement."
            ],
            initial_input=initial_prompt,
            refinement_levels=[2, 3],
            display_instructions=True,
            length_multiplier=0.5,
            include_response_length=True
        )
        input_prompt.run()

        # --- Example: (Demo) Prompt Enhancement ---
        # =======================================================
        print("\n" + "=" * 30)
        print("PROMPT ENHANCER DEMO")
        print("=" * 30)

        initial_prompt = """Scene: Create a digital painting of a lone figure standing at the edge of a stormy ocean, their toes touching the water, symbolizing emotional and mental balance. Use dark, turbulent tones for the ocean with light reflections to evoke hope and resilience. Surround the figure with abstract shapes blending into the sea, symbolizing past struggles. Depict a dimly lit path in the background leading to a brighter horizon, symbolizing the journey toward self-compassion and growth. Motion: The motion: in these surreal scenes is characterized by the relentless surge of the ocean. The waves, whether depicted as towering curves or rippling surfaces, possess an inherent dynamism, a push and pull that dominates the composition. Water flows and churns, suggesting an ever-present undercurrent, a force that can be both powerful and subtle. Beyond the obvious movement of the water, there's a sense of ethereal drift, with elements like floating debris or celestial bodies suspended in a state of gradual migration across the scene, further enhancing the feeling of constant, if sometimes imperceptible, change."""
        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="prompt",
            prompt_chain=[
                "Clarify and rewrite this prompt as an optimized LLM input while preserving its original intent.",
            ],
            initial_input=initial_prompt,
            refinement_levels=[2],
            display_instructions=True,
            length_multiplier=0.3,
            include_response_length=True
        )
        input_prompt.run()

        # --- Example: (Demo) Image Generator Deep Emotion ---
        # =======================================================
        print("\n" + "=" * 30)
        print("IMAGE GENERATOR DEEP EMOTION DEMO")
        print("=" * 30)

        initial_prompt = """A surreal digital painting of a person standing at the edge of a vast ocean, their feet barely touching the water, symbolizing the struggle to keep their head above the surface. The water is dark and turbulent, with faint glimmers of light breaking through, representing hope and resilience. The person is surrounded by abstract, fragmented shapes of their past struggles, slowly dissolving into the water. In the distance, a faint, glowing path leads to a brighter horizon, symbolizing the journey of self-kindness and growth. Cinematic lighting, highly detailed, emotional depth, by Greg Rutkowski and Beeple, trending on Artstation, 8k resolution, ultra-realistic, vibrant yet somber color palette, ethereal atmosphere. [Negative Prompt: blurry, distorted faces, overly bright colors, cartoonish, low detail, text, watermark]"""

        imageprompt_improved = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="image",
            prompt_chain=[
                "Craft a deeply moving image prompt that captures the essence of profound loss, with a beauty that is tragic.",
            ],
            initial_input=initial_prompt,
            refinement_levels=[3],
            display_instructions=False,
            length_multiplier=0.7,
            include_response_length=True
        )
        imageprompt_improved.run()

        # --- Example: (Demo) Context Building ---
        # =======================================================
        print("\n" + "=" * 30)
        print("CONTEXT BUILDING DEMO")
        print("=" * 30)

        initial_prompt = """Your central mission is the strategic optimization of user inputs into laser-focused language model prompts. Develop prompts that are both exceptionally clear and comprehensively detailed to reliably elicit specific and impactful responses. Employ a strategic approach to balance detail and clarity, meticulously structuring each prompt for maximum efficacy. Bolster your prompts with insightful, illustrative examples, provide unambiguous clarifications where needed, and integrate critical context that directly enhances prompt effectiveness and the fidelity of language model outputs. Your goal is to engineer prompts that serve as precise and potent directives, ensuring language models deliver the exact results sought.**"""

        input_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="context",
            prompt_chain=[
                "Clarify and rewrite this prompt as an optimized LLM input while preserving its original intent.",
            ],
            initial_input=initial_prompt,
            refinement_levels=[2, 2],
            display_instructions=False,
            length_multiplier=0.3,
            include_response_length=True
        )
        input_prompt.run()

    # Run script directly.
    if __name__ == "__main__":
        main()


    ```
