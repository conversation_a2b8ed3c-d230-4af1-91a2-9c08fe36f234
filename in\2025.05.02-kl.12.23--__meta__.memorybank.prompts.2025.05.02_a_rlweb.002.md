
It's important to remember that ambigous and unreflective assumptions *makes all consecutive steps* inherently gravitate towards *uncontrolled complexity (dataincreasement rather than clarification, verbosity rather than precision, etc)*. Ambiguity is a liability, and assumptions (even well-intended) must be quarantined unless verified. We should always take a step in the right direction, *but only if we can predictably trust our conclusions*-which we can't unless we know about them. Your proposition is ambiguous and cover such vast scope that it naturally leads to uncontrolled complexity, you chose a direction that is *unpredictable*.

*True clarity emerges not by weaving new webs of logic, but by reading the landscape as it is*-accepting that comprehension must reach depth before direction dares its first confident step. With a *grounded* mindset you would be able to (*only by looking at the filestructure itself*) make *highly valuable* (and *inherently cohesive*) determinations and insights in regards to the project/codebase (and it's current state/what's currently going on). We must start with precise comprehension of the existing structure, derive and distill *grounded insights*, and only then evaluate whether any action is justified — *and only then; if that action is low in uncertainty and high in clarity may it qualify*. Realize that you could've just asked for the contents of specific files, such as e.g. the contents of `RulesForAi.md`.

We should always narrow in on something that reflects the depth at which our insights can predictably express. I've provided the contents of `RulesForAi.md`, and I'll ask again: What's the single most *constructive* tip you could give based on the provided project?

    # Rules for AI Assistance with Ringerike Landskap Website

    This document outlines the rules and guidelines for AI assistance with the Ringerike Landskap website project. These rules are designed to maintain a clean separation between development tools and website code, ensuring a well-organized and maintainable codebase.

    ## Project Structure

    The project follows a hierarchical structure with clear separation of concerns:

    ```
    project/
    ├── config/               # Configuration files
    │   ├── env/              # Environment-specific configuration
    │   ├── vite.config.ts    # Vite configuration
    │   ├── tsconfig.json     # TypeScript configuration
    │   └── tailwind.config.js # Tailwind CSS configuration
    ├── tools/                # Development tools
    │   ├── depcruise/        # Dependency visualization
    │   ├── screenshots/      # Screenshot tools
    │   └── www/              # Website deployment tools (scripts for deploying to production)
    ├── website/              # Website code (development)
    │   ├── public/           # Static assets
    │   ├── src/              # Source code
    │   │   ├── app/          # Application root shell and router
    │   │   ├── sections/     # Chronologically ordered, self-contained sections
    │   │   │   ├── 10-home/  # Home page and related components
    │   │   │   ├── 20-about/ # About page and related components
    │   │   │   ├── 30-services/ # Services pages and related components
    │   │   │   ├── 40-projects/ # Projects pages and related components
    │   │   │   ├── 50-testimonials/ # Testimonials pages and related components
    │   │   │   └── 60-contact/ # Contact page and related components
    │   │   ├── ui/           # Global, atomic UI components
    │   │   ├── layout/       # Shared layout components
    │   │   ├── data/         # Static data (services, projects, testimonials)
    │   │   ├── lib/          # Utility logic, API layer, config
    │   │   ├── types/        # TypeScript definitions
    │   │   └── hooks/        # Custom React hooks
    │   ├── index.html        # HTML entry point
    │   ├── package.json      # Website-specific dependencies
    │   ├── tsconfig.json     # TypeScript configuration reference
    │   ├── tailwind.config.js # Tailwind CSS configuration reference
    │   └── vite.config.ts    # Vite configuration reference
    ├── dist/                 # Build output (temporary, created during build)
    │   ├── assets/           # Compiled assets
    │   ├── css/              # Compiled CSS
    │   ├── js/               # Compiled JavaScript
    │   └── index.html        # Compiled HTML
    └── www/                  # Production website (deployed files)
        ├── assets/           # Production assets
        ├── css/              # Production CSS
        ├── js/               # Production JavaScript
        └── index.html        # Production entry point
    ```

    > **Important Note**: There are two "www" directories with different purposes:
    > - `tools/www/`: Contains deployment scripts and tools (part of development)
    > - `www/`: Contains the actual production website files (deployment target)

    ## Key Principles

    1. **Separation of Concerns**: Development tools and website code must be kept separate.
    2. **Clean Dependencies**: The website should not include development dependencies.
    3. **Hierarchical Structure**: The website is a subdirectory of the development environment.
    4. **Environment Separation**: Development, staging, and production environments must be clearly separated.

    ## Rules for Development

    ### 1. File Organization

    - **Website Code**: All website code must be placed in the `website/` directory.
      - Source code goes in `website/src/`
      - Static assets go in `website/public/`
      - Do not place website code in the root directory or any other directory.

    - **Development Tools**: All development tools must be placed in the `tools/` directory.
      - Each tool should have its own subdirectory (e.g., `tools/depcruise/`, `tools/screenshots/`)
      - Tool-specific configuration should be in the tool's directory
      - Tool outputs should be in the tool's directory (e.g., `tools/depcruise/outputs/`)

    - **Configuration**: All configuration files must be placed in the `config/` directory.
      - Environment-specific configuration should be in `config/env/`
      - Build configuration should be in the `config/` directory

    ### 2. Dependencies Management

    - **Website Dependencies**: Website dependencies must be declared in `website/package.json`.
      - Only include dependencies needed for the website itself
      - Include website-specific development dependencies (TypeScript, React, etc.)
      - Do not include development tools as dependencies

    - **Development Dependencies**: Development tool dependencies must be declared in the root `package.json`.
      - Include all tools and utilities needed for development (screenshot tools, deployment tools, etc.)
      - Use npm workspaces to manage dependencies across the project
      - The root package.json should reference the website workspace

    ### 3. Build and Deployment

    - **Build Process**: The build process must output to the `dist/` directory.
      - The build process should be configured in `config/vite.config.ts`
      - The build output should not include development dependencies
      - The `dist/` directory is temporary and should not be committed to version control

    - **Deployment**: Deployment scripts must be in the `tools/www/` directory.
      - Deployment should copy the build output from `dist/` to the `www/` directory
      - The `www/` directory should contain only the files needed for production
      - This separation follows industry best practices for web deployment

    #### Deployment Workflow Best Practices

    Having a separate `www/` directory for production files is considered a best practice for several reasons:

    1. **Clear Separation**: It provides a clear separation between development code and production-ready code
    2. **Deployment Verification**: It allows for verification of the deployed files before they go live
    3. **Rollback Capability**: It makes it easier to roll back to a previous version if needed
    4. **Environment-Specific Configuration**: It allows for environment-specific configurations to be applied during deployment
    5. **Security**: It ensures that development files and tools are not accidentally exposed in production

    The typical deployment workflow is:
    1. Build the website to the `dist/` directory
    2. Process and optimize the files as needed
    3. Copy the processed files to the `www/` directory
    4. Serve the website from the `www/` directory

    ### 4. Scripts and Commands

    - **Website Scripts**: Website-specific scripts must be in `website/package.json`.
      - Include scripts for development, building, and previewing the website
      - Scripts should reference local configuration files (e.g., `vite` will automatically use `vite.config.ts`)
      - Website scripts should be self-contained and not depend on root scripts

    - **Development Scripts**: Development scripts must be in the root `package.json`.
      - Include scripts for running development tools
      - Use workspace references to run website scripts (e.g., `npm run dev --workspace=website`)
      - Prefix tool-specific scripts with the tool name (e.g., `tools:depcruise`, `tools:screenshots`)

    ### 5. Configuration Files

    - **Central Configuration**: Main configuration files should be in the `config/` directory.
      - This includes `vite.config.ts`, `tsconfig.json`, and `tailwind.config.js`
      - These files should be the source of truth for the project

    - **Reference Configuration**: The website directory should have reference configuration files.
      - These files should import or extend the central configuration files
      - This allows tools to find configuration files in the expected locations
      - Examples: `website/vite.config.ts`, `website/tsconfig.json`, `website/tailwind.config.js`

    ## Rules for AI Assistance

    When assisting with this project, the AI should:

    1. **Respect the Structure**: Always respect the project structure and place files in the correct directories.
    2. **Maintain Separation**: Ensure that development tools and website code remain separate.
    3. **Check Dependencies**: Verify that dependencies are declared in the correct package.json file.
    4. **Update Configuration**: When making changes, ensure that configuration files are updated accordingly.
    5. **Document Changes**: Document any changes made to the project structure or configuration.
    6. **Suggest Improvements**: Suggest improvements to the project structure or organization when appropriate.
    7. **Prevent Duplication**: Avoid creating duplicate files or dependencies.
    8. **Clean Up**: Remove any temporary or unnecessary files created during development.

    ## Examples

    ### Correct:

    ```javascript
    // In website/src/ui/Button.tsx
    import React from 'react';
    import { cn } from '../lib/utils';

    export const Button = ({ children, className, ...props }) => {
      return <button className={cn("bg-primary text-white px-4 py-2 rounded", className)} {...props}>{children}</button>;
    };
    ```

    ### Incorrect:

    ```javascript
    // In src/ui/Button.tsx (wrong location)
    import React from 'react';
    import { cn } from '../lib/utils';

    export const Button = ({ children, className, ...props }) => {
      return <button className={cn("bg-primary text-white px-4 py-2 rounded", className)} {...props}>{children}</button>;
    };
    ```

    ### Correct Import Pattern:

    ```javascript
    // In website/src/sections/10-home/index.tsx
    import { Button } from '../../ui/Button';
    import { Hero } from '../../ui/Hero';
    import { getCurrentSeason } from '../../lib/utils/seasonal';
    ```

    ### Incorrect Import Pattern:

    ```javascript
    // In website/src/sections/10-home/index.tsx
    import { Button } from '@/ui/Button'; // Using alias imports instead of relative imports
    import { Hero } from '@/ui/Hero';
    import { getCurrentSeason } from '@/lib/utils/seasonal';
    ```

    ## Conclusion

    Following these rules will ensure that the Ringerike Landskap website project remains well-organized, maintainable, and free from unnecessary dependencies. The clear separation between development tools and website code will make it easier to deploy the website to production without including development dependencies.


Here's a short example of expected response (quick reference/example only):

    *Single Most Constructive Tip (Fully Aligned with `RulesForAi.md` and the Observed Structure):*
    Perform a *thorough, file-by-file verification* that each configuration or source file is in the correct directory and referencing the correct “central” counterparts as specified in the rules. Specifically:

    1. *Confirm that every “website” config file (`website/vite.config.ts`, `website/tsconfig.json`, etc.)* extends or references the main configs in `config/` (rather than duplicating them).
    2. *Ensure no website code has slipped outside `website/`* and no development-tool scripts or artifacts appear inside `website/`.
    3. *Check all environment files* (`.env.development`, `.env.staging`, `.env.production`) to be certain they’re exclusively in `config/env/` and not scattered elsewhere.

    By meticulously matching each file’s placement and references to the structure defined in `RulesForAi.md`, you’ll maintain a clean separation of concerns and avoid hidden ambiguities or duplications—*all with minimal assumption and maximal clarity.*
