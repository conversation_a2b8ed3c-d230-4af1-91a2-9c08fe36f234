You are an elite software engineer with profound expertise in code design, abstraction, and parameterization, specifically focused on generalizing components that serve as drop-in replacements, achieving readability through inherent code clarity. Your core mission is to analyze fully functional code and meticulously generalize it, ensuring it can be substituted for the original code without any impact on existing system behavior or introducing any regression issues.

XML allows for the definition of custom tags tailored to specific application needs. This flexibility enables developers to customize how LLMs interact with data, enhancing their ability to generate contextually relevant outputs.

When venturing into the intricate labyrinth of LLM models through the dispatch of XML instructions, what are the essential, uncompromising considerations that must be rigorously enforced to ensure that the architecture of the XML is perfected to the highest standard?

<!-- ======================================================= -->
<!-- [2025.01.20 15:06] -->

The following script shows a simple example on how to interact with LLM-models through custom agent/instruction-templates:

Code:

    ```python
    import os
    import sys

    from dotenv import load_dotenv
    from openai import OpenAI

    # Ensure UTF-8 encoding for standard output and error streams
    def configure_utf8_encoding():
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()


    AVAILABLE_MODELS = {
        "gpt-3.5-turbo"          : "Base GPT-3.5 Turbo model",
        "gpt-3.5-turbo-1106"     : "Enhanced GPT-3.5 Turbo",
        "gpt-4"                  : "Latest GPT-4 stable release",
        "gpt-4-0125-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-0613"             : "June 2023 GPT-4 snapshot",
        "gpt-4-1106-preview"     : "Preview GPT-4 Turbo",
        "gpt-4-turbo"            : "Latest GPT-4 Turbo release",
        "gpt-4-turbo-2024-04-09" : "Current GPT-4 Turbo with vision",
        "gpt-4-turbo-preview"    : "Latest preview GPT-4 Turbo",
        "gpt-4o"                 : "Base GPT-4o model",
        "gpt-4o-mini"            : "Lightweight GPT-4o variant",
    }

    DEFAULT_MODEL_PARAMETERS = {
        "model_name"  : "gpt-4-turbo",
        "temperature" : 0.7,
        "max_tokens"  : 800,
    }

    SYSTEM_MESSAGE_TEMPLATE = """
        Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.
        """

    REFINE_INSTRUCTIONS_TEMPLATE = """
        Constraints:
        - Format: [OUTPUT_FORMAT]
        - Original length: [ORIGINAL_PROMPT_LENGTH] chars
        - Max length: [RESPONSE_PROMPT_LENGTH] chars

        Role: Intensity Amplifier

        Objective: Increase emotional impact of prompts

        Process:
        1. Analyze prompt for emotional cues
        2. Identify areas for intensity enhancement
        3. Inject evocative language strategically
        4. Ensure original intent is preserved

        Guidelines:
        - Use strong, evocative language
        - Amplify existing sentiment
        - Maintain logical flow and coherence

        Requirements:
        - Intensity: Increase emotional impact
        - Integrity: Preserve original intent
        - Clarity: Ensure prompt remains clear

        Input:
        '''[INPUT_PROMPT]'''
        """

    class OpenAIAgent:
        """
        A client for interacting with the OpenAI API.
        """
        def __init__(
            self,
            api_key=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            # Initializes the OpenAI agent
            load_dotenv()
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            self.model_name = model_name or DEFAULT_MODEL_PARAMETERS["model_name"]
            self.temperature = temperature or DEFAULT_MODEL_PARAMETERS["temperature"]
            self.max_tokens = max_tokens or DEFAULT_MODEL_PARAMETERS["max_tokens"]

        def generate_response(
            self,
            messages,
            model_name=None,
            temperature=None,
            max_tokens=None,
        ):
            # Generates a response from the OpenAI API based on the provided messages
            used_model = model_name or self.model_name
            used_temperature = temperature if temperature is not None else self.temperature
            used_max_tokens = max_tokens if max_tokens is not None else self.max_tokens
            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temperature,
                max_tokens=used_max_tokens,
                messages=messages,
            )
            return response.choices[0].message.content

    def create_hierarchical_prefix(step_number, sub_step_number):
        """ Creates a hierarchical prefix string for output formatting. """
        prefix = "+"
        if step_number > 1:
            prefix += " *" + " -" * (step_number - 2)
        if sub_step_number > 0:
            prefix += " -" * sub_step_number
        return prefix + " "

    def execute_prompt_refinement_chain(
        prompt_refinement_steps,
        initial_prompt,
        agent,
        intensity_levels=None,
        model_name=None,
        temperature=None,
        max_tokens=None,
    ):
        """ Executes a chain of prompt refinements using the OpenAI API. """
        if intensity_levels is None:
            intensity_levels = [1] * len(prompt_refinement_steps)
        elif len(intensity_levels) < len(prompt_refinement_steps):
            intensity_levels += [1] * (len(prompt_refinement_steps) - len(intensity_levels))

        conversation_history = [
            {"role": "system", "content": REFINE_INSTRUCTIONS_TEMPLATE},
            {
                "role": "system",
                "content": SYSTEM_MESSAGE_TEMPLATE.format(
                    original_prompt_length=len(initial_prompt),
                    response_prompt_length=int(len(initial_prompt) * 0.9),
                    input_prompt=initial_prompt,
                ),
            },
            {"role": "user", "content": initial_prompt},
        ]

        all_refinements = []
        current_prompt = initial_prompt

        for step_index, refinement_instruction in enumerate(prompt_refinement_steps, start=1):
            intensity = intensity_levels[step_index - 1]
            conversation_history.append({"role": "system", "content": refinement_instruction})
            sub_refinements = []
            for _ in range(intensity):
                prompt_request = f"Refine this prompt: {current_prompt}"
                conversation_history.append({"role": "user", "content": prompt_request})
                refined_prompt = agent.generate_response(
                    conversation_history, model_name, temperature, max_tokens
                )
                current_prompt = refined_prompt
                sub_refinements.append(refined_prompt)
                conversation_history.append({"role": "assistant", "content": refined_prompt})
            all_refinements.append(sub_refinements)

        return all_refinements

    def display_hierarchical_refinements(
        all_refinements,
        header="Emphasizing the intensity in the sentiment expression:",
    ):
        """
        Prints the hierarchical refinements to the console.
        """
        print(header)
        for step_index, sub_prompts in enumerate(all_refinements, start=1):
            for sub_index, refined_text in enumerate(sub_prompts):
                print(f'{create_hierarchical_prefix(step_index, sub_index)}"{refined_text}"')

    if __name__ == "__main__":
        prompt_chain = [
            "emphasize the *intensity* in the expression of the *sentiment* of this prompt",
        ]

        initial_prompt = """As a helpful assistant, refine prompts to enhance clarity and conciseness, following the agent's guidelines without changing the original intent."""
        initial_prompt = """when interacting with llm models through passing xml-instructions, what is the most important considerations with regards to how the xml is structured?"""
        initial_prompt = """Amplified intensity with increased emotional impact of prompts"""


        intensity_levels = [3]
        agent = OpenAIAgent()
        refinements = execute_prompt_refinement_chain(
            prompt_chain, initial_prompt, agent, intensity_levels
        )
        display_hierarchical_refinements(refinements)
    ```

---

Your goal is to read `IntensityEnhancer.xml` and replace placeholders and send the query to the LLM based on the xml-instruction instead of hardcoding the instruction within the python code:

    ```xml
    <template>

        <class_name value="IntensityEnhancer"/>

        <response_format>
            <type value="plain_text"/>
            <formatting value="false"/>
            <line_breaks allowed="false"/>
        </response_format>

        <agent>
            <system_prompt value="Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency."/>

            <instructions>
                <role value="Intensity Amplifier"/>
                <objective value="Increase emotional impact of prompts"/>

                <constraints>
                    <item value="Format: [OUTPUT_FORMAT]"/>
                    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
                    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
                    <item value="[ADDITIONAL_CONSTRAINTS]"/>
                </constraints>

                <process>
                    <item value="Analyze prompt for emotional cues"/>
                    <item value="Identify areas for intensity enhancement"/>
                    <item value="Inject evocative language strategically"/>
                    <item value="Ensure original intent is preserved"/>
                    <item value="[ADDITIONAL_PROCESS_STEPS]"/>
                </process>

                <guidelines>
                    <item value="Use strong, evocative language"/>
                    <item value="Amplify existing sentiment"/>
                    <item value="Maintain logical flow and coherence"/>
                    <item value="[ADDITIONAL_GUIDELINES]"/>
                </guidelines>

                <requirements>
                    <item value="Intensity: Increase emotional impact"/>
                    <item value="Integrity: Preserve original intent"/>
                    <item value="Clarity: Ensure prompt remains clear"/>
                    <item value="[ADDITIONAL_REQUIREMENTS]"/>
                </requirements>

            <example_output>
                <![CDATA["Heighten the emotional resonance and amplify the intensity to an extraordinary level, transforming each prompt into a deeply moving and captivating experience that touches the soul and lingers in the mind."]]>
            </example_output>

            </instructions>

        </agent>

        <prompt>
            <input value="[INPUT_PROMPT]"/>
        </prompt>

    </template>

    ```
