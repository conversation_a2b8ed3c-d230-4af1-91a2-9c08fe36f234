# Rename With Editor

## Overview
Batch file renaming utility that opens a text editor interface for easy bulk renaming operations with comprehensive safety features.

## Features
- **Text editor interface**: Edit filenames in your preferred text editor
- **Safety validation**: Automatic filename sanitization and collision detection
- **Rich progress tracking**: Visual progress indicators with detailed feedback
- **File type detection**: Intelligent handling of different file types
- **Hash verification**: Optional file integrity checking with xxhash
- **Comprehensive logging**: Detailed operation logs with Loguru
- **Interactive CLI**: User-friendly prompts and confirmations

## Quick Start
Run `run.bat` to start the interactive renaming tool (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py [directory_path]
uv run python src/main.py --help
```

## Additional Tools
- `packages_upgrade.bat` - Upgrade all packages to latest versions
- `src/timestamptodatepattern.py` - Utility for timestamp-based renaming

## Dependencies
Managed via `pyproject.toml` with uv package manager.
