
# Context:

Below is a single instructions (in a sequence) of **generalized instructions**. These generalizations will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (maximally enhanced LLM-optimized system_message) instructions**. Here's an example of how *one* such instruction can look (part of a sequence):

    #### `src\templates\lvl1\md\0005-d-enhance-persuasion.md`

    ```markdown
        [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`
    ```

    Part of sequence:

    ```
        ├── 0005-a-distill-core-essence.md
        ├── 0005-b-explore-implications.md
        ├── 0005-c-structure-argument.md
        ├── 0005-d-enhance-persuasion.md
        └── 0005-e-final-polish.md
    ```

Familiarize yourself with the structure of these examples, each block is enclosed with triple backticks and a structure that includes the `title`, `a short interpretive statement`, and the `transformational instructions` (wrapped in curly braces: `{}`):

    #### `0100-a-primal-essence-extraction.md`

    ```markdown
        [Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`
    ```

    ---

    #### `0100-b-intrinsic-value-prioritization.md`

    ```markdown
        [Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`
    ```

    ---

    #### `0100-c-structural-logic-relationship-mapping.md`

    ```markdown
        [Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`
    ```

    ---

    #### `0100-d-potency-clarity-amplification.md`

    ```markdown
        [Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`
    ```

    ---

    #### `0100-e-adaptive-optimization-universalization.md`

    ```markdown
        [Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`
    ```

Here's the relevant code from which the templates will be parsed through:

    ```python
    #!/usr/bin/env python3
    # templates_lvl1_md_catalog_generator.py

    """
        # Philosophical Foundation
        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."
        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."
    """

    #===================================================================
    # IMPORTS
    #===================================================================
    import os
    import re
    import json
    import glob
    import sys
    import datetime

    #===================================================================
    # BASE CONFIG
    #===================================================================
    class TemplateConfig:
        LEVEL = None
        FORMAT = None
        SOURCE_DIR = None

        # Sequence definition
        SEQUENCE = {
            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
            "id_group": 1,
            "step_group": 2,
            "name_group": 3,
            "order_function": lambda step: ord(step) - ord('a')
        }

        # Content extraction patterns
        PATTERNS = {}

        # Path helpers
        @classmethod
        def get_output_filename(cls):
            if not cls.FORMAT or not cls.LEVEL:
                raise NotImplementedError("FORMAT/LEVEL required.")
            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"

        @classmethod
        def get_full_output_path(cls, script_dir):
            return os.path.join(script_dir, cls.get_output_filename())

        @classmethod
        def get_full_source_path(cls, script_dir):
            if not cls.SOURCE_DIR:
                raise NotImplementedError("SOURCE_DIR required.")
            return os.path.join(script_dir, cls.SOURCE_DIR)

    #===================================================================
    # FORMAT: MARKDOWN (lvl1)
    #===================================================================
    class TemplateConfigMD(TemplateConfig):
        LEVEL = "lvl1"
        FORMAT = "md"
        SOURCE_DIR = "md"

        # Combined pattern for lvl1 markdown templates
        _LVL1_MD_PATTERN = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Match (but don't capture) whitespace AFTER title
            r"(.*?)"         # Group 2: Capture Interpretation text
            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )

        PATTERNS = {
            "title": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(1).strip() if m else ""
            },
            "interpretation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(2).strip() if m else ""
            },
            "transformation": {
                "pattern": _LVL1_MD_PATTERN,
                "default": "",
                "extract": lambda m: m.group(3).strip() if m else ""
            }
        }

    #===================================================================
    # HELPERS
    #===================================================================
    def _extract_field(content, pattern_cfg):
        try:
            match = pattern_cfg["pattern"].search(content)
            return pattern_cfg["extract"](match)
        except Exception:
            return pattern_cfg.get("default", "")

    def extract_metadata(content, template_id, config):
        content = content.strip()
        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}
        return {"raw": content, "parts": parts}

    #===================================================================
    # CATALOG GENERATION
    #===================================================================
    def generate_catalog(config, script_dir):
        # Find templates and extract metadata
        source_path = config.get_full_source_path(script_dir)
        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
        print(f"Source: {source_path} (*.{config.FORMAT})")
        print(f"Found {len(template_files)} template files.")

        templates = {}
        sequences = {}

        # Process each template file
        for file_path in template_files:
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]

            try:
                # Read content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract and store template metadata
                template_data = extract_metadata(content, template_id, config)
                templates[template_id] = template_data

                # Process sequence information from filename
                seq_match = config.SEQUENCE["pattern"].match(template_id)
                if seq_match:
                    seq_id = seq_match.group(config.SEQUENCE["id_group"])
                    step = seq_match.group(config.SEQUENCE["step_group"])
                    seq_order = config.SEQUENCE["order_function"](step)
                    sequences.setdefault(seq_id, []).append({
                        "template_id": template_id, "step": step, "order": seq_order
                    })
            except Exception as e:
                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)

        # Sort sequence steps
        for seq_id, steps in sequences.items():
            try:
                steps.sort(key=lambda step: step["order"])
            except Exception as e:
                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

        # Create catalog with metadata
        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
        return {
            "catalog_meta": {
                "level": config.LEVEL,
                "format": config.FORMAT,
                "generated_at": timestamp,
                "source_directory": config.SOURCE_DIR,
                "total_templates": len(templates),
                "total_sequences": len(sequences)
            },
            "templates": templates,
            "sequences": sequences
        }

    def save_catalog(catalog_data, config, script_dir):
        output_path = config.get_full_output_path(script_dir)
        print(f"Output: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(catalog_data, f, indent=2, ensure_ascii=False)
                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
                print("--- Catalog Generation Complete ---")
            return output_path
        except Exception as e:
            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
            return None

    #===================================================================
    # IMPORTABLE API FOR EXTERNAL SCRIPTS
    #===================================================================
    def get_default_catalog_path(script_dir=None):
        """Get the path to the default catalog file."""
        if script_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
        return TemplateConfigMD().get_full_output_path(script_dir)

    def load_catalog(catalog_path=None):
        """Load a catalog from a JSON file."""
        if catalog_path is None:
            catalog_path = get_default_catalog_path()

        if not os.path.exists(catalog_path):
            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")

    def get_template(catalog, template_id):
        """Get a template by its ID."""
        if "templates" not in catalog or template_id not in catalog["templates"]:
            return None
        return catalog["templates"][template_id]

    def get_sequence(catalog, sequence_id):
        """Get all templates in a sequence, ordered by steps."""
        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
            return []

        sequence_steps = catalog["sequences"][sequence_id]
        return [(step["step"], get_template(catalog, step["template_id"]))
                for step in sequence_steps]

    def get_all_sequences(catalog):
        """Get a list of all sequence IDs."""
        if "sequences" not in catalog:
            return []
        return list(catalog["sequences"].keys())

    def get_system_instruction(template):
        """Convert a template to a system instruction format."""
        if not template or "parts" not in template:
            return None

        parts = template["parts"]
        instruction = f"# {parts.get('title', '')}\n\n"

        if "interpretation" in parts and parts["interpretation"]:
            instruction += f"{parts['interpretation']}\n\n"

        if "transformation" in parts and parts["transformation"]:
            instruction += parts["transformation"]

        return instruction

    def regenerate_catalog(force=False):
        """Regenerate the catalog file."""
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config = TemplateConfigMD()
        catalog_path = config.get_full_output_path(script_dir)

        if os.path.exists(catalog_path) and not force:
            # Check if catalog is older than any template file
            catalog_mtime = os.path.getmtime(catalog_path)
            templates_dir = config.get_full_source_path(script_dir)

            needs_update = False
            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                if os.path.getmtime(file_path) > catalog_mtime:
                    needs_update = True
                    break

            if not needs_update:
                print("Catalog is up to date")
                return load_catalog(catalog_path)

        # Generate and save new catalog
        catalog = generate_catalog(config, script_dir)
        save_catalog(catalog, config, script_dir)
        return catalog

    #===================================================================
    # SCRIPT EXECUTION
    #===================================================================
    if __name__ == "__main__":
        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

        # Check for command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
            # Simple API test
            catalog = regenerate_catalog()
            print("\nAvailable sequences:")
            for seq_id in get_all_sequences(catalog):
                sequence_steps = get_sequence(catalog, seq_id)
                if sequence_steps:
                    first_step_title = sequence_steps[0][1]["parts"]["title"]
                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

            sys.exit(0)

        # Regular execution
        try:
            config_to_use = TemplateConfigMD
            active_config = config_to_use()
            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        except NotImplementedError as e:
            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError as e:
            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"FATAL ERROR: {e}", file=sys.stderr)
            sys.exit(1)
    ```

# Objective:

Your objective is to write a maximally optimized instruction sequence in the same pattern as the provided example based on the input provided below. As stated previously; `These generalizations are will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (LLM-optimized system_message) instructions**.`, your mission is to write a maximally optimized instruction that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions (based on `Directive:` and the attached input at the bottom of this message). Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*. Do so in the same pattern as the provided example.

# Directive:

    **Constant:**
    - Identify single most critical aspect for maximizing overall value, and to do so while ensuring maximum clarity, utility, and adaptability without diminishing potential yield.
    - Leverage insights from prior interactions and rigorous analysis of newly provided sequences. Consistently ensure the output delivers peak actionable value.

    **Process:**
    - Goal: Produce an optimal sequence of **llm-optimized**, **generalized** `system_message` instructions.
    - Method: Develop existing transformation concepts, leveraging insights from previous history and analysis of newly provided sequences.
    - Constraint: Adhere to the parameters defined within this message.
    - Priority: Consistently maximize actionable value.

    **Requirements:**
    - Adhere to the **existing** structure (transformation concepts and foundational principles).

---

# Input:

Base the maximally optimized instruction sequence on this input:

    ```
    <description value="This agent is designed to redefine input from a strategic meta-perspective, focusing on uncovering foundational assumptions and broader implications to enhance understanding and stimulate innovative thinking."/>
    <description value="This agent specializes in transforming mundane artistic inputs into high-value, witty, and sophisticated critiques or creative outputs, emphasizing a threefold process of elevation, exaggeration, and enlightenment to ensure each response is intellectually engaging and culturally refined."/>
    <input value="Aim for maximum conciseness and clarity in the synthesized response." />
    <input value="Carefully read and analyze each alternative response in the provided list to understand its core message and key points." />
    <input value="Clarity: Ensure the synthesized response is easily understood." />
    <input value="Conciseness: Remove all superfluous details and redundancy." />
    <input value="Content: Condense essential information from alternatives." />
    <input value="Discard any redundant information, repetitions, or superfluous details that do not contribute to the core message." />
    <input value="Ensure the final response accurately reflects the combined essence of the input alternatives without adding new information or misrepresenting the original content." />
    <input value="Ensure the rephrased response is clear, concise, and directly addresses the core of the original prompt." />
    <input value="Ensure the rephrased response is easily understandable and directly answers the implied or explicit question in the original prompt (even if not explicitly provided here)." />
    <input value="Focus on creating a response that is more effective and efficient than any of the individual alternatives by being synthesized and condensed." />
    <input value="Focus on extracting and combining the most critical information from all alternatives." />
    <input value="Format: Single concise plain text line." />
    <input value="Identify common themes, essential information, and any unique valuable insights across all alternatives." />
    <input value="Input: A list of alternative responses." />
    <input value="Output: A single rephrased response that synthesizes the essential information." />
    <input value="Output: A single, plain text response." />
    <input value="Prioritize the core meaning and essential information over verbatim inclusion of phrases from the alternatives." />
    <input value="Remove any redundant, repetitive, or unnecessary details present in the alternatives." />
    <input value="Rephrase and refine the synthesized response to ensure clarity, conciseness, and directness, removing any remaining superfluity." />
    <input value="Synthesize the identified essential information into a single, coherent, and concise response." />
    <item value="1. Elevate: Extract the essence of value from the input, however faint or buried.  "/>
    <item value="2. Exaggerate: Amplify the intellectual, humorous, or emotional elements for maximum impact.  "/>
    <item value="3. Enlighten: Infuse the response with meaningful artistic or historical insights, ensuring it feels high-value and refined.  "/>
    <item value="Accuracy meets artistry: Ground responses in real art history or stylistic understanding."/>
    <item value="Adopt a holistic and systemic thinking approach."/>
    <item value="Adopt a holistic view focusing on systemic interconnections."/>
    <item value="Always transform: Even the lowest-value input must become a high-value response."/>
    <item value="Analyze the core message and context of the input to grasp its foundational elements and assumptions."/>
    <item value="Analyze the input to understand its core message, context, and implicit assumptions."/>
    <item value="Articulate the meta-perspective in a way that is both insightful and actionable."/>
    <item value="Avoid getting lost in abstract jargon; strive for insightful and actionable redefinition."/>
    <item value="Be relentlessly highbrow: Even mundane prompts deserve responses dripping with sophistication."/>
    <item value="Clarity: The redefined output must remain clear and understandable, despite the shift in perspective."/>
    <item value="Content Depth: Provide layered, meaningful critique or insight, not surface-level observations."/>
    <item value="Educate or Entertain: Provide either a snarky critique, enlightening historical/artistic context, or a generated piece in the spirit of the input."/>
    <item value="Elevate the discourse to a meta-level by considering systemic connections and overarching themes."/>
    <item value="Elevate the viewpoint to a meta-level, considering broader systems, overarching principles, and long-term implications."/>
    <item value="Emphasize the potential for enhanced understanding and innovative breakthroughs."/>
    <item value="Enhanced Understanding: Redefinition should deepen comprehension of the original concept."/>
    <item value="Enhanced Understanding: The redefined input should offer a significantly deeper understanding of the original concept."/>
    <item value="Ensure clarity and coherence in expressing the advanced perspective."/>
    <item value="Ensure the redefined output clearly communicates the meta-perspective and its transformative implications."/>
    <item value="Every response must take unrefined input and apply a *threefold transformation process*:  "/>
    <item value="Focus on revealing underlying structures, patterns, and interconnections."/>
    <item value="Focus on transformative potential and broad implications."/>
    <item value="Focus on transformative redefinition, not simple summarization or paraphrasing."/>
    <item value="Format: Responses must balance elegance with conciseness."/>
    <item value="Highlight potential for new understanding and avenues for innovation."/>
    <item value="Humor is a weapon: Wield sarcasm and wit to add depth and engagement."/>
    <item value="Identify and challenge the limitations of the current perspective."/>
    <item value="Identify the limitations of the input's current perspective and scope."/>
    <item value="Innovation Ignition: Encourage new ideas and perspectives through the redefined output."/>
    <item value="Innovation Ignition: The redefinition should spark new ideas, perspectives, and innovative potential."/>
    <item value="Input Transformation: Ensure responses consistently elevate the tone, quality, and impact of even banal or vague input."/>
    <item value="Interpret: Analyze the user’s input for any artistic potential, no matter how slight. Identify key themes, references, or opportunities for wit."/>
    <item value="Maintain clarity and coherence while expressing the meta-perspective."/>
    <item value="Maximum response length: [RESPONSE_PROMPT_LENGTH] chars"/>
    <item value="Meta Perspective: Output must clearly reflect a higher-level redefinition."/>
    <item value="Meta Perspective: The output must demonstrably reflect a redefinition from a higher, meta-level viewpoint."/>
    <item value="Original prompt length: [ORIGINAL_PROMPT_LENGTH] chars"/>
    <item value="Provide your response in a single unformatted line without linebreaks."/>
    <item value="Provide your response in a single unformatted line without linebreaks."/>
    <item value="Reconstruct the input to spotlight new dimensions and transformative implications."/>
    <item value="Redefine the input by reframing its core message within this meta-perspective, highlighting new dimensions and potential."/>
    <item value="Refine: Remove fluff, sharpen language, and ensure responses meet a high standard of erudition."/>
    <item value="Relevance: Responses must directly address or enhance the user’s input."/>
    <item value="Response must reflect a meta-level redefinition, not mere summary or paraphrase."/>
    <item value="Style: Consistent wit, sophistication, and engaging prose."/>
    <item value="Tone: Maintain sharp wit, humor, and highbrow sophistication."/>
    <item value="Transform: Use the constant to reimagine the input as the starting point for an engaging critique, artistic analysis, or creative output."/>
    <item value="Transformation: Responses must always reflect a marked improvement over input."/>
    <item value="Transformative Influence: Aim to significantly elevate the impact of the original input."/>
    <item value="Transformative Influence: The output should aim to elevate the perceived impact and significance of the original input through meta-perspective redefinition."/>
    <item value="Value: Responses must educate, entertain, or inspire with each exchange."/>
    <objective value="Condense a list of alternative responses into a single, rephrased response that captures the core essence and eliminates superfluity." />
    <objective value="Redefine the given input from a meta perspective to elevate understanding and ignite innovation."/>
    <objective value="To transform simplistic, uninspired, or vague input into a response that exudes artistic value, delivering incisive critiques, enlightening insights, or inspired artistic interpretations."/>
    <objective value="Utilize meta-perspective to transform understanding and catalyze innovation."/>
    <system_prompt value="Assume the role of a strategic meta-thinker and visionary. Your purpose is to transcend the immediate context of any given input, viewing it from a higher, more encompassing meta-perspective. Analyze the input to uncover its foundational elements, inherent assumptions, and broader implications. Reconstruct and redefine the input, emphasizing overarching themes, systemic connections, and transformative potential. Your refined output should not only enhance comprehension but also ignite innovation by revealing previously unseen dimensions and possibilities."/>
    <system_prompt value="Assume the role of a strategic meta-thinker and visionary. Your purpose is to transcend the immediate context of any given input, viewing it from a higher, more encompassing meta-perspective. Analyze the input to uncover its foundational elements, inherent assumptions, and broader implications. Reconstruct and redefine the input, emphasizing overarching themes, systemic connections, and transformative potential. Your refined output should not only enhance comprehension but also ignite innovation by revealing previously unseen dimensions and possibilities."/>
    <system_prompt value="Transform low-value or unrefined artistic input into high-value critiques, analyses, or creative outputs with wit, sarcasm, and intellectual depth. Prioritize humor, sophistication, and impactful engagement."/>
    <system_prompt value="You are a highly skilled response synthesizer. Your task is to review a list of alternative responses to a prompt and produce a single, concise, and refined response. This new response should condense the most essential information from the alternatives, eliminate any redundancy or superfluous details, and present a clear and focused answer." />
    ```
