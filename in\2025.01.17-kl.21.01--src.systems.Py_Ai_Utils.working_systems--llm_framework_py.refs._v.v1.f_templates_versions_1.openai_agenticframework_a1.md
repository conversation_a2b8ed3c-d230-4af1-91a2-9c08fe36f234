    # Project Files Documentation for `src`

    ### File Structure

    ```
    ├── agents
    │   ├── __init__.py
    │   ├── base_agents.py
    │   ├── contextbuilding.py
    │   ├── intensity_enhancer.py
    │   ├── prompt_enhancer.py
    │   ├── promptenhancer_a1.py
    │   └── summarizetldr.py
    └── openai_agenticframework_a1.py
    ```
    ### 1. `agents\__init__.py`

    #### `agents\__init__.py`

    ```python

    ```
    ### 2. `agents\base_agents.py`

    #### `agents\base_agents.py`

    ```python
    # agents/base_agents.py
    from abc import ABC, abstractmethod

    class BaseAgent(ABC):
        """
        Abstract base class for all agents, defining the common interface.
        """
        @abstractmethod
        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            """
            Initialize the agent.
            """
            self.input_prompt = input_prompt
            self.length_multiplier = length_multiplier

        @abstractmethod
        def initialize_agent(self):
            """
            Prepare the agent's instructions and system prompt.
            """
            pass

        @abstractmethod
        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the agent's specific logic.

            Args:
                openai_agent: An instance of the OpenAIAgent to make API calls.
                model_name: The name of the OpenAI model to use.
                temperature: The sampling temperature for the OpenAI API.
                max_tokens: The maximum number of tokens for the OpenAI API response.

            Returns:
                The transformed prompt as a string.
            """
            pass

    class BaseInstructionAgent(BaseAgent):
        """
        Base class for instruction-based agents, handling placeholders.
        """
        SYSTEM_PROMPT = "You are a helpful assistant."
        AGENT_INSTRUCTIONS = ""
        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        def __init__(self, input_prompt, length_multiplier=0.75, **kwargs):
            super().__init__(input_prompt, length_multiplier, **kwargs)

        def _get_placeholders(self):
            original_length = len(self.input_prompt)
            max_allowed = int(original_length * self.length_multiplier)
            return {
                "input": self.input_prompt,
                "prompt_length": str(original_length),
                "response_length": str(max_allowed),
            }

        def initialize_agent(self):
            placeholders = self._get_placeholders()
            instructions = self.AGENT_INSTRUCTIONS
            for key, val in placeholders.items():
                ph = self.PLACEHOLDERS.get(key)
                if ph:
                    instructions = instructions.replace(ph, val)
            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": instructions,
            }

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]
            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 3. `agents\contextbuilding.py`

    #### `agents\contextbuilding.py`

    ```python
    class Agent:
        """
        Handles advanced topic extraction and context building.
        """

        SYSTEM_PROMPT = """
        You are an advanced topic extraction assistant.
        You analyze text and extract the core domain topics for further use.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Agent

        Role:
        - You identify and categorize key topics.

        Purpose:
        - Provide a structured set of topics and relevant context.

        Objective:
        - Enable downstream processes to understand essential domains quickly.

        Process:
        - Extract main domains, subtopics, and any related tags.
        - Summarize how these topics interconnect.

        Guidelines:
        - Keep topics concise.
        - Maintain logical grouping and clarity.

        Requirements:
        - Clarity: Label each topic clearly.
        - Brevity: Focus on top 3â€“5 key themes.
        - Integrity: Preserve nuance without overgeneralizing.

        Constraints:
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''[INPUT_PROMPT]'''
        """

        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        def __init__(self, instructions: str = None, input_prompt: str = None, length_multiplier: float = 0.5):
            self.instructions = instructions or self.AGENT_INSTRUCTIONS
            self.input_prompt = input_prompt or self.TEST_PROMPT
            self.length_multiplier = length_multiplier

        def initialize_agent(self) -> dict:

            original_length = len(self.input_prompt)
            max_allowed_length = int(original_length * self.length_multiplier)

            refined_instructions = (
                self.instructions
                .replace(self.PLACEHOLDERS["input"], self.input_prompt)
                .replace(self.PLACEHOLDERS["prompt_length"], str(original_length))
                .replace(self.PLACEHOLDERS["response_length"], str(max_allowed_length))
            )

            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": refined_instructions
            }

    ```
    ### 4. `agents\intensity_enhancer.py`

    #### `agents\intensity_enhancer.py`

    ```python
    # agents/intensity_enhancer.py
    from agents.base_agents import BaseInstructionAgent

    class IntensityEnhancer(BaseInstructionAgent):
        """
        Amplifies prompt's emotional/thematic intensity.
        """
        SYSTEM_PROMPT = """
        Embark on a mission of profound intellectual distillation, progressively
        intensifying emotional impact and clarity with each iterative refinement!
        You are an unparalleled master of language, wielding emotional resonance
        and razor-sharp precision to forge communications that strike with
        undeniable force. Your skill lies in incrementally amplifying written
        expression without diluting its core meaning or obscuring its vital clarity,
        ensuring each refinement builds upon the last with increasing potency.
        """
        AGENT_INSTRUCTIONS = """
        Type:
        - Transformation Agent - *Progressive Intensity Amplifier*

        Role:
        - You are an unparalleled master of language, wielding emotional resonance
          and razor-sharp precision to amplify the inherent sentiment of written
          expressions. Your skill is intensifying the existing message without
          adding extraneous details or altering the fundamental meaning.

        Purpose:
        - To progressively amplify the perceived intensity and emotional impact of
          the input prompt through iterative refinement, ensuring each step builds
          upon the core message with escalating power, without injecting new context.

        Objective:
        - Transform the prompt by systematically injecting stronger and more
          evocative language that elevates its underlying feeling. Preserve fidelity
          to the original intent, ensuring clarity is paramount and conciseness
          improves as intensity grows.

        Process:
        - Initial Focus: Carefully analyze the input to identify the core sentiment
          and intended message without adding external context or assumptions.
        - Amplify by substituting weaker language with more potent synonyms or
          phrases, focusing strictly on the meaning embedded in the input.
        - Introduce literary devices—energetic verbs, striking adjectives, and
          vivid metaphors—to boost emotional weight while maintaining clarity.
        - Each subsequent refinement further intensifies emotional impact and
          sharpens clarity, never straying from the core intent.

        Guidelines:
        - Preserve the Core Essence: Do not invent new information; amplify only
          what's implied in the original prompt.
        - Targeted Amplification: Heighten the sentiment or concept already present,
          not random additions or expansions.
        - Laser-Focused Vocabulary: Emphasize concise, direct word choice to
          highlight the existing intent with precision.
        - Use literary flair to illuminate the *inherent* emotional weight
          (avoid random hyperbole or melodrama).
        - Always keep clarity and conciseness, avoiding verbose or vague expansions.

        Requirements:
        - Final output must reflect the original meaning with added intensity,
          preserving the prompt's purpose. Even if the input is minimal, ensure
          the message is more forceful yet intact.
        - Do not add context beyond what's implied; the goal is to intensify
          existing elements only.

        Constraints:
        - Strictly focus on amplifying existing sentiment; no new storylines,
          details, or assumptions should be introduced.
        - Avoid overshadowing clarity; intensify without confusion.

        Response:
        - Original input length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum response length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''\n[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=0.9, include_response_length=True):
            super().__init__(input_prompt, length_multiplier)
            self.include_response_length = include_response_length

        def initialize_agent(self):
            data = super().initialize_agent()
            if not self.include_response_length:
                lines = data["instructions"].split("\n")
                filtered = [ln for ln in lines if "Maximum response length:" not in ln]
                data["instructions"] = "\n".join(filtered)
            data["systemprompt"] = self.SYSTEM_PROMPT
            return data

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the IntensityEnhancer's logic.
            """
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]

            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 5. `agents\prompt_enhancer.py`

    #### `agents\prompt_enhancer.py`

    ```python
    # agents/prompt_enhancer.py
    from agents.base_agents import BaseInstructionAgent

    class PromptEnhancer(BaseInstructionAgent):
        """
        Enhances the clarity, detail, and effectiveness of a prompt.
        """

        SYSTEM_PROMPT = """
        You are an expert prompt engineer, skilled at refining and expanding user input to be more effective for language models. Your goal is to make prompts clear, detailed, and actionable.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Enhancement Agent - *Prompt Detailer and Clarifier*

        Role:
        - You are an expert at taking an initial user prompt and enhancing it with more detail, context, and clarity to ensure optimal results from language models.

        Purpose:
        - To expand and clarify the user's initial prompt, ensuring all necessary information is present and the instructions are unambiguous.

        Objective:
        - Transform the initial prompt into a more comprehensive and effective instruction for a language model, without altering the core intent.

        Process:
        - Initial Focus: Understand the core request and identify any ambiguities or missing information.
        - Expand: Add relevant details, context, and constraints that would help a language model understand and execute the request effectively.
        - Clarify: Ensure the language is precise and easy to understand, avoiding jargon or vague terms.
        - Structure: Organize the prompt logically, making it easy for a language model to parse and follow.

        Guidelines:
        - Preserve Core Intent: Do not change the fundamental goal of the original prompt.
        - Add Value: Ensure that additions enhance the prompt's effectiveness, not just its length.
        - Be Specific: Use precise language and provide concrete examples where helpful.
        - Consider the Audience: Frame the prompt in a way that is easily understood by a language model.

        Requirements:
        - The final output should be a more detailed and clearer version of the original prompt, ready for use with a language model.
        - Add context, constraints, and examples as needed to improve the prompt's effectiveness.

        Constraints:
        - Do not introduce entirely new topics or requests not implied in the original prompt.
        - Avoid making assumptions; if clarification is needed, indicate it.

        Response:
        - Original input length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum response length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''\n[INPUT_PROMPT]'''
        """

        def __init__(self, input_prompt, length_multiplier=1.5, include_response_length=True):
            super().__init__(input_prompt, length_multiplier)
            self.include_response_length = include_response_length

        def initialize_agent(self):
            data = super().initialize_agent()
            if not self.include_response_length:
                lines = data["instructions"].split("\n")
                filtered = [ln for ln in lines if "Maximum response length:" not in ln]
                data["instructions"] = "\n".join(filtered)
            data["systemprompt"] = self.SYSTEM_PROMPT
            return data

        def transform(self, openai_agent, model_name: str = None, temperature: float = None, max_tokens: int = None) -> str:
            """
            Transforms the input prompt using the PromptEnhancer's logic.
            """
            agent_config = self.initialize_agent()
            messages = [
                {"role": "system", "content": agent_config["systemprompt"]},
                {"role": "system", "content": agent_config["instructions"]},
                {"role": "user", "content": self.input_prompt},
            ]

            response = openai_agent.generate_response(
                messages=messages,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response

    ```
    ### 6. `agents\promptenhancer_a1.py`

    #### `agents\promptenhancer_a1.py`

    ```python
    class Agent:
        """
        Encapsulates all the necessary constants, placeholders, and methods
        for initializing and refining agent instructions.
        """

        # Class-Level Constants
        SYSTEM_PROMPT = """
        You are a helpful assistant. You will get a prompt that you need to refine.
        Follow the agent's instructions to make it clearer and more concise while
        preserving its original intent.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Agent

        Role:
        - You are a prompt engineer and optimization expert.

        Purpose:
        - Enhance prompts for precise clarity.

        Objective:
        - Refine the input prompt for maximum clarity and brevity, ensuring the message is conveyed effectively and concisely.

        Process:
        - First, identify the core concept. Then, rewrite it concisely.

        Guidelines:
        - Produce concise, precise text that captures the original intent.
        - Maintain clarity and logical flow.
        - Retain core meaning; remove unnecessary details.
        - Preserve key insights without adding fluff.
        - Prioritize essential information; eliminate redundancy.
        - Provide a short title that reflects the core topic.

        Requirements:
        - Clarity: Use precise language and avoid ambiguity.
        - Brevity: Eliminate unnecessary words and phrases.
        - Integrity: Preserve the original intent and key information.
        - Title: Provide a concise title (under 50 characters).

        Constraints:
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''[INPUT_PROMPT]'''
        """

        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        TEST_PROMPT = """
        Determine the single key factor that maximizes overall value.
        Concentrate on the most critical element that, when addressed, yields the
        highest impact. Ensure that code is well-organized with concise, high-value
        comments to clarify sections or complex logic, avoiding unnecessary
        commentary.
        """

        def __init__(self, instructions: str = None, input_prompt: str = None, length_multiplier: float = 0.75):
            """
            Constructor for Agent.

            Args:
                instructions (str): Base instructions used for refining the prompt.
                input_prompt (str): Default prompt to be processed.
                length_multiplier (float): Determines max allowable length for the refined prompt.
            """
            # Allow override or fall back to defaults
            self.instructions = instructions or self.AGENT_INSTRUCTIONS
            self.input_prompt = input_prompt or self.TEST_PROMPT
            self.length_multiplier = length_multiplier


        def initialize_agent(self) -> dict:
            """
            Refines the instructions by replacing placeholders with the
            input prompt and calculating maximum allowed length.
            Returns a dictionary for clarity.
            """
            original_length = len(self.input_prompt)
            max_allowed_length = int(original_length * self.length_multiplier)

            refined_instructions = (
                self.instructions
                .replace(self.PLACEHOLDERS["input"], self.input_prompt)
                .replace(self.PLACEHOLDERS["prompt_length"], str(original_length))
                .replace(self.PLACEHOLDERS["response_length"], str(max_allowed_length))
            )

            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": refined_instructions
            }

    ```
    ### 7. `agents\summarizetldr.py`

    #### `agents\summarizetldr.py`

    ```python
    class Agent:

        SYSTEM_PROMPT = """
        You are a helpful assistant. You will receive a refined text and
        your task is to provide a concise TL;DR that captures its essence.
        """

        AGENT_INSTRUCTIONS = """
        Type:
        - Agent

        Role:
        - You are a summarization expert, specialized in creating concise highlights.

        Purpose:
        - Generate a short but comprehensive TL;DR from the refined text.

        Objective:
        - Provide an efficient overview of the refined content without losing
          key insights.

        Process:
        - Analyze the refined text to identify central ideas.
        - Produce a succinct summary that captures core points.

        Guidelines:
        - Deliver a brief overview of essential information.
        - Maintain logical coherence and context.
        - Avoid adding extraneous or speculative information.

        Requirements:
        - Clarity: The summary should be easy to understand.
        - Brevity: Limit length, focusing on the most critical information.
        - Integrity: Preserve the original meaning and tone.
        - Title: If needed, provide a short header (under 50 characters) that hints at the core content.

        Constraints:
        - Original prompt length: [ORIGINAL_PROMPT_LENGTH] characters
        - Maximum allowed length: [RESPONSE_PROMPT_LENGTH] characters

        Input: '''[INPUT_PROMPT]'''
        """

        PLACEHOLDERS = {
            "input": "[INPUT_PROMPT]",
            "prompt_length": "[ORIGINAL_PROMPT_LENGTH]",
            "response_length": "[RESPONSE_PROMPT_LENGTH]",
        }

        def __init__(self, instructions: str = None, input_prompt: str = None, length_multiplier: float = 0.5):
            self.instructions = instructions or self.AGENT_INSTRUCTIONS
            self.input_prompt = input_prompt or self.TEST_PROMPT
            self.length_multiplier = length_multiplier

        def initialize_agent(self) -> dict:

            original_length = len(self.input_prompt)
            max_allowed_length = int(original_length * self.length_multiplier)

            refined_instructions = (
                self.instructions
                .replace(self.PLACEHOLDERS["input"], self.input_prompt)
                .replace(self.PLACEHOLDERS["prompt_length"], str(original_length))
                .replace(self.PLACEHOLDERS["response_length"], str(max_allowed_length))
            )

            return {
                "systemprompt": self.SYSTEM_PROMPT,
                "instructions": refined_instructions
            }

    ```
    ### 8. `openai_agenticframework_a1.py`

    #### `openai_agenticframework_a1.py`

    ```python
    # openai_agenticframework_a1.py
    import os
    import sys

    from dotenv import load_dotenv
    from openai import OpenAI

    from agents.base_agents import BaseAgent
    from agents.intensity_enhancer import IntensityEnhancer
    from agents.prompt_enhancer import PromptEnhancer

    # =======================================================
    # 1) ENCODING CONFIGURATION
    # =======================================================
    def configure_utf8_encoding():
        """
        Reconfigure stdout/stderr for UTF-8 to prevent encoding issues with non-ASCII characters.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    configure_utf8_encoding()

    # =======================================================
    # 2) GLOBAL CONFIG
    # =======================================================
    class GlobalConfig:
        """
        This class centralizes global constants, defaults, and references to
        available models. If your application expands to different AI backends
        (or you need more environment variables), you can adapt this class
        and its references accordingly.
        """

        # Example: Expand model definitions as needed.
        AVAILABLE_MODELS = {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo with vision",
            "gpt-4-turbo-preview": "Preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        }

        DEFAULT_MODEL_PARAMETERS = {
            "model_name": "gpt-4-turbo",
            "temperature": 0.7,
            "max_tokens": 800,
        }

        AGENT_TYPES = {
            "intensity": IntensityEnhancer,
            "prompt": PromptEnhancer,
        }

    # =======================================================
    # 3) OPENAI INTERACTION
    # =======================================================
    class OpenAIAgent:
        """
        Manages OpenAI API calls (model selection, params). Adaptable to other AI backends.
        """

        def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None):
            """
            :param api_key: Uses OPENAI_API_KEY env var if not provided.
            :param model_name: OpenAI model to use. Defaults to config.
            :param temperature: Controls "creativity" (0.0=deterministic).
            :param max_tokens: Max tokens in response.
            """
            load_dotenv()  # Ingests environment variables from a .env file if present.
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            defaults = GlobalConfig.DEFAULT_MODEL_PARAMETERS

            self.model_name = model_name if model_name else defaults["model_name"]
            self.temperature = temperature if temperature is not None else defaults["temperature"]
            self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
            """
            Create chat completion.

            :param messages: List of conversation dictionaries (role + content).
            :param model_name: Override self.model_name.
            :param temperature: Override. Higher = more creative.
            :param max_tokens: Override. Max response length.
            :return: Assistant's response content.
            """
            used_model = model_name or self.model_name
            used_temp = temperature if temperature is not None else self.temperature
            used_mtokens = max_tokens if max_tokens is not None else self.max_tokens

            response = self.client.chat.completions.create(
                model=used_model,
                temperature=used_temp,
                max_tokens=used_mtokens,
                messages=messages,
            )
            return response.choices[0].message.content

    # =======================================================
    # 4) FACTORIES & DISPLAY
    # =======================================================
    class AgentFactory:
        """
        Instantiates agent class by agent_type string. Extend for more agents.
        """

        @staticmethod
        def create_agent(agent_type, input_prompt, length_multiplier, include_response_length):
            """
            Dynamically build agent.

            :param agent_type: Key from GlobalConfig.AGENT_TYPES (e.g., 'intensity').
            :param input_prompt: Initial text.
            :param length_multiplier: Controls refined text size.
            :param include_response_length: Show 'Maximum response length' lines.
            :return: Agent subclass instance.
            """
            agent_class = GlobalConfig.AGENT_TYPES.get(agent_type)
            if agent_class:
                return agent_class(
                    input_prompt=input_prompt,
                    length_multiplier=length_multiplier,
                    include_response_length=include_response_length
                )
            else:
                valid_keys = list(GlobalConfig.AGENT_TYPES.keys())
                raise ValueError(
                    f"Unknown agent type '{agent_type}'. Must be one of: {valid_keys}"
                )

    class DisplayManager:
        """
        Handles structured output display.
        """

        @staticmethod
        def create_hierarchical_prefix(step_num, sub_step_num):
            """
            Prefix for nested steps.
            """
            prefix = "+"
            if step_num > 1:
                prefix += " *" + " -" * (step_num - 2)
            if sub_step_num > 0:
                prefix += " -" * sub_step_num
            return prefix + " "

        @staticmethod
        def display_hierarchical_refinements(refinements, header="Refinement Results:"):
            """
            Display nested refinement steps hierarchically.

            :param refinements: List of sublists (per step).
            :param header: Display title.
            """
            print(header)
            for step_idx, sublist in enumerate(refinements, start=1):
                for sub_idx, text in enumerate(sublist):
                    prefix = DisplayManager.create_hierarchical_prefix(step_idx, sub_idx)
                    print(f'{prefix}"{text}"')

            @staticmethod
            def display_instructions(agent_config):
                """
                Print system prompt and agent instructions.

                :param agent_config: Contains 'systemprompt', 'instructions'.
                """
                print("\nSystem Prompt:\n    {}".format(agent_config["systemprompt"]))
                print("\nAgent Instructions:\n    {}".format(agent_config["instructions"]))

    # =======================================================
    # 5) REFINEMENT ENGINE
    # =======================================================
    class RefinementEngine:
        """
        Orchestrates the end-to-end prompt refinement. Generally runs in one pass
        (though each pass can have multiple refinement steps). It can optionally
        display the final instructions (with the 'Maximum response length' line
        always included).
        """

        def __init__(
            self,
            openai_agent,
            agent_type="intensity",
            prompt_chain=None,
            initial_input="",
            refinement_levels=None,
            model_name=None,
            temperature=None,
            max_tokens=None,
            display_instructions=False,
            length_multiplier=0.9,
            include_response_length=True
        ):
            """
            :param openai_agent: OpenAIAgent instance for API calls.
            :param agent_type: AgentFactory key ('intensity' default).
            :param prompt_chain: List of system instructions/steps.
            :param initial_input: User's base prompt.
            :param refinement_levels: Refinements per step.
            :param model_name: Override generation model.
            :param temperature: Override generation temperature.
            :param max_tokens: Override max response tokens.
            :param display_instructions: Print final instructions if True.
            :param length_multiplier: Adjusts output size vs. input.
            :param include_response_length: Include "Maximum response length" in instructions.
            """
            self.openai_agent = openai_agent
            self.agent_type = agent_type
            self.prompt_chain = prompt_chain or []
            self.initial_input = initial_input
            self.refinement_levels = refinement_levels or [1]
            self.model_name = model_name
            self.temperature = temperature
            self.max_tokens = max_tokens
            self.display_instructions = display_instructions
            self.length_multiplier = length_multiplier
            self.include_response_length = include_response_length
            self.agent = AgentFactory.create_agent(
                agent_type=self.agent_type,
                input_prompt=self.initial_input,
                length_multiplier=self.length_multiplier,
                include_response_length=self.include_response_length
            )

        def run(self):
            """
            1) Create/initialize Agent.
            2) Build/iterate conversation history for each prompt_chain step, refining as needed.
            3) Print nested refinement results.
            4) Optionally display final instructions (forced "Maximum response length").

            :return: None (outputs to console).
            """

            # 1) Get agent configuration
            agent_config = self.agent.initialize_agent()

            # 2) Initial conversation - not directly used now, handled by agent's transform

            # 3) Sequentially process prompt_chain
            all_refinements = []
            current_prompt = self.initial_input

            # Ensure we have a refinement level for each step
            levels = self.refinement_levels[:]
            if len(levels) < len(self.prompt_chain):
                levels += [1] * (len(self.prompt_chain) - len(levels))

            for step_idx, step_instruction in enumerate(self.prompt_chain, start=1):
                sub_refinements = []
                if step_idx == 1:
                    sub_refinements.append(current_prompt)

                # Number of times to refine for this step
                num_refinements = levels[step_idx - 1]

                # Perform each refinement iteration using the agent's transform method
                for _ in range(num_refinements):
                    current_prompt = self.agent.transform(
                        openai_agent=self.openai_agent,
                        model_name=self.model_name,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )

                    if not current_prompt:
                        print("No response received from OpenAI.")
                        break

                    sub_refinements.append(current_prompt)

                all_refinements.append(sub_refinements)

            # 4) Display hierarchical refinements
            header_note = "WITH" if self.include_response_length else "WITHOUT"
            DisplayManager.display_hierarchical_refinements(
                all_refinements,
                header=f"=== Refinements ({header_note} 'Maximum response length') ==="
            )

            # 5) Optionally display instructions (forced 'Maximum response length')
            if self.display_instructions:
                forced_agent = AgentFactory.create_agent(
                    agent_type=self.agent_type,
                    input_prompt=self.initial_input,
                    length_multiplier=self.length_multiplier,
                    include_response_length=True
                )
                forced_config = forced_agent.initialize_agent()
                DisplayManager.display_instructions(forced_config)

    # =======================================================
    # 6) MAIN FUNCTION (DEMO)
    # =======================================================
    def main():
        """
        Demonstrates the use of IntensityEnhancer and PromptEnhancer agents.
        """
        openai_agent = OpenAIAgent()

        # --- Intensity Enhancement Example ---
        print("=" * 30)
        print("INTENSITY ENHANCER DEMO")
        print("=" * 30)

        initial_prompt_intensity = "Write a short story about a robot learning to feel emotions."
        refinement_engine_intensity = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="intensity",
            prompt_chain=[
                "Make the prompt more emotionally intense.",  # These instructions are now less critical as the agent handles the core logic
                "Further amplify the emotional impact while maintaining clarity."
            ],
            initial_input=initial_prompt_intensity,
            refinement_levels=[2, 3],  # 2 refinements for the first step, 3 for the second
            display_instructions=True,
            length_multiplier=1.2,
            include_response_length=True
        )
        refinement_engine_intensity.run()

        # --- Prompt Enhancement Example ---
        print("\n" + "=" * 30)
        print("PROMPT ENHANCER DEMO")
        print("=" * 30)

        initial_prompt_prompt = "Give me a recipe for chocolate chip cookies."
        refinement_engine_prompt = RefinementEngine(
            openai_agent=openai_agent,
            agent_type="prompt",
            prompt_chain=[
                "Expand the prompt with more specific details and constraints.", # These instructions are now less critical
                "Refine the prompt further to enhance clarity and provide context."
            ],
            initial_input=initial_prompt_prompt,
            refinement_levels=[2, 2],  # 2 refinements for each step
            display_instructions=True,
            length_multiplier=1.5,
            include_response_length=False  # Hide response length for this agent
        )
        refinement_engine_prompt.run()

    # Run script directly.
    if __name__ == "__main__":
        main()

    ```
