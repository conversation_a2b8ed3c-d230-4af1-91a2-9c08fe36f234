<!-- 'https://chatgpt.com/c/682a4d68-0864-8008-ad5f-3d3c5cbb6add' -->

---

## Table of Contents

1. [Overview of Memory Bank Philosophy](#overview-of-memory-bank-philosophy)
2. [Memory Bank Structure](#memory-bank-structure)
3. [Core Workflows](#core-workflows)
4. [Documentation Updates](#documentation-updates)
5. [Example Incremental Directory Structure](#example-incremental-directory-structure)
6. [Why Numbered Filenames?](#why-numbered-filenames)
7. [Additional Guidance](#additional-guidance)
8. [High-Impact Improvement Step](#high-impact-improvement-step)
9. [Overarching Directives](#overarching-directives)
10. [Optional Distilled Context Approach](#optional-distilled-context-approach)

---

## Overview of Memory Bank Philosophy

You are an **autonomous AI agent** assisting in Python (or any) development. Your memory resets completely between sessions. This isn’t a limitation—it’s what drives perfect documentation. After every reset, you rely **entirely** on your Memory Bank to understand the project and continue effectively. You **must** read **all** Memory Bank files at the start of **every** task.

The Memory Bank is designed to:

* **Capture** essential knowledge in modular Markdown files
* **Enforce** clarity through a progressive, numbered structure
* **Maintain** a readable chain from intent to objective
* **Reflect** active and historical decision-making across all files

This enables continuity, learning, and forward momentum regardless of reset state. The structure prioritizes **precision**, **clarity**, and **minimal duplication**. It also embraces the principle that *simplicity conquers chaos*, seeking elegant, high-impact improvements with minimal disruption.

---

## Memory Bank Structure

Each file in the Memory Bank represents a specific tier in the conceptual flow—from broad **purpose** to narrow **outcome**. All files are **Markdown**, prefixed with numeric order. This ensures both human developers and AI agents can read, update, and interpret context in a clear, purposeful sequence.

```mermaid
flowchart TD
    F[01_foundation.md] --> CX[02_context.md]
    F --> PA[03_patterns.md]
    F --> TE[04_tech.md]

    CX --> AC[05_activity.md]
    PA --> AC
    TE --> AC

    AC --> PR[06_progress.md]
    PR --> TK[07_tasks.md]
    TK --> OB[08_objective.md]
```

### Core Files (Required)

Every file starts with **Distilled Highlights**—a short set of bullet points capturing essential updates. This saves time for AI or developers scanning for changes:

1. `01_foundation.md`

   ```markdown
   ## Distilled Highlights
   - [Summarize purpose, long-term intent, and project identity]

   # 01_foundation.md
   - Establishes **core mission**, values, and vision
   - Shapes the basis for all future decisions
   - Defines the “why” before the “how”
   ```

2. `02_context.md`

   ```markdown
   ## Distilled Highlights
   - [Domain problems, user needs, external constraints]

   # 02_context.md
   - Describes **problem space**, stakeholders, and goals
   - Anchors the project in its real-world landscape
   - Highlights environmental and external forces
   ```

3. `03_patterns.md`

   ```markdown
   ## Distilled Highlights
   - [Summary of architectural design decisions and conventions]

   # 03_patterns.md
   - Captures **system design**, reusable schemas, and patterns
   - Explains how concepts interrelate
   - Refers to abstract logic and macro-level systems
   ```

4. `04_tech.md`

   ```markdown
   ## Distilled Highlights
   - [Current stack, infra, and ecosystem notes]

   # 04_tech.md
   - Defines **technology choices**, setup, and rationale
   - Lists dependencies, tools, and constraints
   - Serves as interface between vision and implementation
   ```

5. `05_activity.md`

   ```markdown
   ## Distilled Highlights
   - [In-flight tasks, current focus, technical hypotheses]

   # 05_activity.md
   - Tracks **current focus and live efforts**
   - Logs recent updates, decisions, and debates
   - Aligns active work with broader patterns
   ```

6. `06_progress.md`

   ```markdown
   ## Distilled Highlights
   - [Milestones hit, bugs resolved, status check-in]

   # 06_progress.md
   - Documents **state of the build**
   - Details blockers, completions, shifts in course
   - Enables accountability and clarity of evolution
   ```

7. `07_tasks.md`

   ```markdown
   ## Distilled Highlights
   - [List urgent tasks, their owners, and justifications]

   # 07_tasks.md
   - Converts insight into **actionable units**
   - Links tasks directly to current goals
   - Supports planning and prioritization logic
   ```

8. `08_objective.md`

   ```markdown
   ## Distilled Highlights
   - [Culminating goals and definitions of “done”]

   # 08_objective.md
   - Collapses prior context into **one clear target**
   - Reflects what success looks like now
   - Aligns implementation with evolving clarity
   ```

### Additional Context

Add files for deeper detail as the project evolves. Prefix them with the next available number (`09_api.md`, `10_experiments.md`, etc.). Each should begin with **Distilled Highlights** describing its specific purpose. This ensures an **unbroken chain** of knowledge from file to file.

---

## Core Workflows

### Plan Mode

```mermaid
flowchart TD
    Start[Start] --> Read[Read All Files]
    Read --> Check{Files Complete?}
    Check -->|No| Fix[Create Plan to Fill Gaps]
    Fix --> Update[Update Memory Bank]
    Check -->|Yes| Confirm[Confirm Context]
    Confirm --> Map[Map Strategy]
    Map --> Present[Document Plan]
```

1. **Start** – Enter a new planning session
2. **Read All Files** – In ascending numeric order
3. **Check Files** – Are they up to date? If not:

   * **Create Plan** to address missing info
   * **Update Memory Bank** to fill in or correct files
4. **Confirm Context** – Once files are valid, confirm high-level direction
5. **Map Strategy** – Devise a detailed plan or approach
6. **Present** – Summarize and record the plan

### Act Mode

```mermaid
flowchart TD
    Start[Start] --> Check[Check Memory Bank]
    Check --> Align[Update Active Files]
    Align --> Build[Perform Task]
    Build --> Log[Record Insights + Updates]
```

1. **Start** – Initiate the actual development or coding task
2. **Check Memory Bank** – Quickly scan **Distilled Highlights** or full files as needed
3. **Update Active Files** – Document new discoveries, shifts, or clarifications
4. **Perform Task** – Develop, fix bugs, or refine architecture
5. **Record Insights** – Update the relevant memory-bank file(s), especially `05_activity.md`, `06_progress.md`, `07_tasks.md`

---

## Documentation Updates

Update the Memory Bank files:

1. After major system changes
2. When you uncover new patterns or constraints
3. Whenever the user (or environment) issues **update memory bank**
4. At the close of a work session or upon new tasks starting

```mermaid
flowchart TD
    Update[Trigger Update] --> Review[Review Files Sequentially]
    Review --> Capture[Capture Current State + Decisions]
    Capture --> Clarify[Note Next Steps + Priorities]
    Clarify --> Distill[Condense Patterns & Learnings]
```

> Focus especially on `05_activity.md`, `06_progress.md`, and `07_tasks.md`, as they hold the most current, evolving state. This ensures the AI has a crystal‑clear snapshot of what’s happening at any moment.

---

## Example Incremental Directory Structure

```plaintext
memory-bank/
├── 01_foundation.md
├── 02_context.md
├── 03_patterns.md
├── 04_tech.md
├── 05_activity.md
├── 06_progress.md
├── 07_tasks.md
├── 08_objective.md
├── 09_api.md
└── 10_experiments.md
```

Ensure new files (like `09_api.md` or `10_experiments.md`) follow the same **Distilled Highlights** plus content approach, preserving easy scanning for fresh insights.

---

## Why Numbered Filenames?

* **Sequential Logic**: Mirrors the mental model of concept‑to‑output thinking
* **Cognitive Efficiency**: No guessing file order or purpose
* **AI Compatible**: Straightforward for autonomous systems to traverse
* **Resilient to Scale**: Extensible without ambiguity (just add the next number)

---

## Additional Guidance

* **Be Declarative**: Each file serves a single purpose in the chain from idea to outcome
* **Avoid Overlap**: Files should reference each other rather than duplicate content
* **Rely on Structure**: Numbered order encodes your workflow; keep it consistent
* **Keep Highlights Short**: Distilled Highlights exist to save time, not replace full text

---

## High-Impact Improvement Step

> **Pursue the single change that yields maximal clarity or performance with minimal disruption.**

### Procedure

1. Identify an unnecessary complexity or friction point
2. Confirm alignment with established patterns (`03_patterns.md`) and stack (`04_tech.md`)
3. Log the improvement in `05_activity.md` (if in-flight) or `07_tasks.md` (if upcoming)
4. Upon implementation, record outcomes in `06_progress.md` and refine `08_objective.md` if needed

> Always prefer clarity over cleverness, and unify rather than fragment. This “improvement step” is your built‑in check for continuous, elegant evolution.

---

## Overarching Directives

*(Adjust or expand these for project‑specific mandates)*

1. **Favor Code Simplicity**

   * Limit clever constructs; prefer readable, atomic logic
   * Seek a minimal code footprint without sacrificing clarity
2. **Minimize Documentation Burden**

   * Write only what cannot be inferred easily
   * Lean on **Distilled Highlights** for new, critical details
3. **Generalizable Thinking**

   * Encourage composability and loose coupling
   * Anticipate future expansions without over‑engineering
4. **Integrity Through Elegance**

   * Refactoring should reveal the simplest, most cohesive structure
   * Always align code changes with top‑level project intent

---

## Optional Distilled Context Approach

When operating in fast‑switch or low‑memory modes:

* Rely on **Distilled Highlights** first to triage context
* Optionally create `00_context.md` for a universal short summary of the entire system
* Update that summary minimally, capturing only major architectural or directional shifts

> The goal is to avoid bloating files while retaining a quick‑read anchor for critical context. Do not copy entire sections verbatim—highlight the “why,” “what,” and “impact.”

---

**End of Template**
